'use server';

import { revalidatePath } from "next/cache";
import { auth, currentUser } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { Attendance, Employee, Gender, Department, EmploymentType } from "@/types";
import { Note } from "@prisma/client"; // Add this import
import QRCode from 'qrcode';
import { startOfMonth, endOfMonth, subMonths } from 'date-fns';
import { ensureUserInDatabase } from "@/lib/ensure-user";

// Import the addEmployee function from the new location
import { addEmployee } from '@/components/AddEmployee/addEmployeeAction';

// Re-export the addEmployee function
export { addEmployee };

export async function updateEmployee(employeeData: Partial<Employee>): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return { success: false, error: "User not found" };
    }

    if (!employeeData.id) {
      return { success: false, error: "Employee ID is missing" };
    }

    const { id, createdAt, updatedAt, userId: _, ...updateData } = employeeData;

    // Create a type-safe way to handle numeric fields
    const processNumericField = (value: any): number | undefined => {
      if (value === '' || value === null || value === undefined) {
        return undefined; // Changed from null to undefined to match Partial<Employee>
      }
      const num = Number(value);
      return isNaN(num) ? undefined : num;
    };

    // Create a type for processed data that matches Partial<Employee>
    type ProcessedData = {
      [K in keyof Employee]?: Employee[K];
    };

    // Type-safe way to update specific fields
    const processedData: ProcessedData = {
      ...updateData,
      // Handle numeric fields explicitly
      ...(updateData.salaire !== undefined && {
        salaire: processNumericField(updateData.salaire)
      }),
      ...(updateData.salaireBrut !== undefined && {
        salaireBrut: processNumericField(updateData.salaireBrut)
      }),
      ...(updateData.salaireNet !== undefined && {
        salaireNet: processNumericField(updateData.salaireNet)
      }),
      ...(updateData.tauxIR !== undefined && {
        tauxIR: processNumericField(updateData.tauxIR)
      }),
      ...(updateData.tauxCNSS !== undefined && {
        tauxCNSS: processNumericField(updateData.tauxCNSS)
      }),
      ...(updateData.tauxAMO !== undefined && {
        tauxAMO: processNumericField(updateData.tauxAMO)
      }),
      // Handle date fields explicitly
      ...(updateData.dateNaissance && {
        dateNaissance: new Date(updateData.dateNaissance)
      }),
      ...(updateData.dateDebut && {
        dateDebut: new Date(updateData.dateDebut)
      }),
      ...(updateData.dateFinContrat && {
        dateFinContrat: new Date(updateData.dateFinContrat)
      }),
      ...(updateData.dateExpirationPermis && {
        dateExpirationPermis: new Date(updateData.dateExpirationPermis)
      }),
      ...(updateData.dDepart && {
        dDepart: new Date(updateData.dDepart)
      }),
      ...(updateData.dateRelica && {
        dateRelica: new Date(updateData.dateRelica)
      })
    };

    // Clean up undefined values to avoid Prisma errors
    const cleanedData = Object.fromEntries(
      Object.entries(processedData).filter(([_, value]) => value !== undefined)
    );

    await prisma.employee.update({
      where: { id: id },
      data: cleanedData,
    });

    revalidatePath(`/hr/employee/${id}`);
    return { success: true };
  } catch (error) {
    console.error('Error in updateEmployee:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An error occurred while updating the employee"
    };
  }
}

export async function getEmployeeById(id: string): Promise<Employee | null> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("Authentication required");
    }

    const employee = await prisma.employee.findFirst({
      where: {
        id: id,
        userId: user.id,
      },
    });

    return employee as Employee | null;
  } catch (error) {
    console.error('Error in getEmployeeById:', error);
    throw error;
  }
}

export async function getAttendanceByEmployeeId(employeeId: string): Promise<Attendance[]> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("Authentication required");
    }

    const attendances = await prisma.attendance.findMany({
      where: { employeeId: employeeId },
      orderBy: { date: 'desc' },
    });

    return attendances;
  } catch (error) {
    console.error('Error in getAttendanceByEmployeeId:', error);
    throw error;
  }
}

export async function addAttendance(
  employeeId: string,
  date: Date | null,  // Add this parameter
  status: string,
  checkInTime?: Date | null,
  checkOutTime?: Date | null,
  notes?: string | null
): Promise<{ success: boolean; error?: string; attendance?: Attendance }> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      return { success: false, error: "Vous devez être connecté pour ajouter une présence" };
    }

    const attendance = await prisma.attendance.create({
      data: {
        employeeId,
        date: date || new Date(), // Use the provided date or fallback to current date
        status,
        checkInTime,
        checkOutTime,
        notes,
      },
    });

    revalidatePath(`/hr/employee/${employeeId}/attend`);

    return { success: true, attendance };
  } catch (error) {
    console.error('Error in addAttendance:', error);
    return { success: false, error: "Une erreur s'est produite lors de l'ajout de la présence" };
  }
}

export async function updateAttendance(attendance: Attendance): Promise<{ success: boolean; error?: string; attendance?: Attendance }> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      return { success: false, error: "You must be logged in to update an attendance record" };
    }

    const updatedAttendance = await prisma.attendance.update({
      where: { id: attendance.id },
      data: {
        date: attendance.date,
        status: attendance.status,
        checkInTime: attendance.checkInTime,
        checkOutTime: attendance.checkOutTime,
        notes: attendance.notes,
      },
    });

    revalidatePath(`/hr/employee/${attendance.employeeId}/attend`);

    return { success: true, attendance: updatedAttendance };
  } catch (error) {
    console.error('Error in updateAttendance:', error);
    return { success: false, error: "An error occurred while updating the attendance record" };
  }
}

export async function deleteAttendance(attendanceId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      return { success: false, error: "You must be logged in to delete an attendance record" };
    }

    await prisma.attendance.delete({
      where: { id: attendanceId },
    });

    revalidatePath(`/hr/employee/[id]/attend`);

    return { success: true };
  } catch (error) {
    console.error('Error in deleteAttendance:', error);
    return { success: false, error: "An error occurred while deleting the attendance record" };
  }
}

export async function getUserProfile(userId: string): Promise<Employee | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      include: { employees: true },
    });

    if (!user || user.employees.length === 0) {
      return null;
    }

    return user.employees[0] as Employee;
  } catch (error) {
    console.error('Error in getUserProfile:', error);
    throw error;
  }
}

export async function getUserAttendance(userId: string, startDate: Date, endDate: Date): Promise<Attendance[]> {
  try {
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      include: { employees: true },
    });

    if (!user || user.employees.length === 0) {
      return [];
    }

    const employee = user.employees[0];

    return await prisma.attendance.findMany({
      where: {
        employeeId: employee.id,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: { date: 'asc' },
    });
  } catch (error) {
    console.error('Error in getUserAttendance:', error);
    throw error;
  }
}

export async function generateQRCode(data: string): Promise<string> {
  try {
    return await QRCode.toDataURL(data);
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw error;
  }
}

export async function getEmployees() {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("User not found");
    }

    // Only fetch employees for the current user/company
    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        prenom: true,
        nom: true,
        email: true,
        telephone: true,
        dateNaissance: true,
        genre: true,
        adresse: true,
        departement: true,
        poste: true,
        dateDebut: true,
        dateFinContrat: true,
        salaire: true,
        typeEmploi: true,
        contactUrgence: true,
        telephoneUrgence: true,
        competences: true,
        notesAdditionnelles: true,
        cin: true,
        cnss: true,
        rib: true,
        mutuelle: true,
        nationalite: true,
        numeroPasseport: true,
        permisTravaill: true,
        dateExpirationPermis: true,
        createdAt: true,
        updatedAt: true
      }
    });

    return employees.map(employee => ({
      ...employee,
      dateFinContrat: employee.dateFinContrat ? employee.dateFinContrat.toISOString() : null,
      dateNaissance: employee.dateNaissance ? employee.dateNaissance.toISOString() : null,
      dateDebut: employee.dateDebut ? employee.dateDebut.toISOString() : null,
      dateExpirationPermis: employee.dateExpirationPermis ? employee.dateExpirationPermis.toISOString() : null,
      salaire: typeof employee.salaire === 'number' ? employee.salaire : parseFloat(employee.salaire as string) || 0
    }));
  } catch (error) {
    console.error('Error in getEmployees:', error);
    throw new Error('Failed to fetch employees');
  }
}

export async function getEmployeesForSelection(): Promise<{ id: string; name: string; email: string; poste: string }[]> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("Authentication required");
    }

    const employees = await prisma.employee.findMany({
      select: {
        id: true,
        prenom: true,
        nom: true,
        email: true,
        poste: true,
      },
      orderBy: { nom: 'asc' },
    });

    return employees.map(emp => ({
      id: emp.id,
      name: `${emp.prenom} ${emp.nom}`,
      email: emp.email,
      poste: emp.poste
    }));
  } catch (error) {
    console.error('Error in getEmployeesForSelection:', error);
    throw new Error('Failed to fetch employees. Please try again later.');
  }
}

export async function getEmployeeAttendanceForMonth(employeeId: string, date: Date): Promise<Attendance[]> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("Authentication required");
    }

    const firstDayOfMonth = startOfMonth(date);
    const lastDayOfMonth = endOfMonth(date);

    return await prisma.attendance.findMany({
      where: {
        employeeId: employeeId,
        date: {
          gte: firstDayOfMonth,
          lte: lastDayOfMonth,
        },
      },
      orderBy: { date: 'asc' },
    });
  } catch (error) {
    console.error('Error in getEmployeeAttendanceForMonth:', error);
    throw new Error('Failed to fetch attendance data. Please try again later.');
  }
}

export async function generateAttendanceRecommendations(attendanceData: Attendance[]): Promise<string> {
  const totalDays = attendanceData.length;
  const presentDays = attendanceData.filter(a => a.status === 'Present').length;
  const attendanceRate = (presentDays / totalDays) * 100;

  if (attendanceRate >= 90) {
    return "Excellent attendance rate. Keep up the good work!";
  } else if (attendanceRate >= 80) {
    return "Good attendance rate. There's room for improvement.";
  } else {
    return "Attendance needs improvement. Consider discussing any issues with your supervisor.";
  }
}

export async function getEmployeeDetails(employeeId: string): Promise<Employee | null> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("Authentication required");
    }

    return await prisma.employee.findUnique({
      where: { id: employeeId },
    }) as Employee | null;
  } catch (error) {
    console.error('Error in getEmployeeDetails:', error);
    throw new Error('Failed to fetch employee details. Please try again later.');
  }
}

export async function getEmployeesWithAttendance() {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return []; // Return empty array if no user
    }

    // Only fetch employees for the current user
    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id // Filter by current user
      },
      include: {
        attendances: {
          where: {
            date: {
              gte: subMonths(new Date(), 1),
            },
          },
          orderBy: { date: 'desc' },
        },
      },
    });

    return employees.map(emp => ({
      employee: emp,
      attendances: emp.attendances,
    }));
  } catch (error) {
    console.error('Error in getEmployeesWithAttendance:', error);
    return []; // Return empty array instead of throwing
  }
}

export async function fetchDashboardData() {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return {
        employees: [],
        totalPayroll: 0,
        employeeCount: 0,
        avgSalary: 0,
        salaryDistribution: [],
        contractTypeData: [],
        genderData: [],
        departmentData: []
      };
    }

    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id // Only fetch current user's employees
      },
      select: {
        id: true,
        prenom: true,
        nom: true,
        poste: true,
        salaire: true,
        departement: true,
        dateDebut: true,
        dateFinContrat: true, // Added to fix type issue with employee status calculation
        typeEmploi: true,  // Added for contract type chart
        genre: true,       // Added for gender distribution chart
        contratType: true, // Added for more specific contract type
      },
    });

    const totalPayroll = employees.reduce((sum, emp) => sum + emp.salaire, 0);
    const avgSalary = employees.length > 0 ? totalPayroll / employees.length : 0;

    // Calculate salary distribution by department
    const departmentSalaries = employees.reduce((acc, emp) => {
      acc[emp.departement] = (acc[emp.departement] || 0) + emp.salaire;
      return acc;
    }, {} as Record<string, number>);

    const salaryDistribution = Object.entries(departmentSalaries).map(([name, value]) => ({
      name,
      value
    }));

    // Calculate contract type distribution
    const contractTypes = employees.reduce((acc, emp) => {
      // Use contratType if available, otherwise fall back to typeEmploi
      const contractType = emp.contratType || emp.typeEmploi || 'Unknown';
      acc[contractType] = (acc[contractType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const contractTypeData = Object.entries(contractTypes).map(([name, value]) => ({
      name,
      value
    }));

    // Calculate gender distribution
    const genders = employees.reduce((acc, emp) => {
      const gender = emp.genre || 'Unknown';
      acc[gender] = (acc[gender] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const genderData = Object.entries(genders).map(([name, value]) => ({
      name,
      value
    }));

    // Calculate department size distribution
    const departments = employees.reduce((acc, emp) => {
      const department = emp.departement || 'Unknown';
      acc[department] = (acc[department] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const departmentData = Object.entries(departments).map(([name, value]) => ({
      name,
      value
    }));

    return {
      employees,
      totalPayroll,
      employeeCount: employees.length,
      avgSalary,
      salaryDistribution,
      contractTypeData,
      genderData,
      departmentData
    };
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    // Return empty data instead of throwing
    return {
      employees: [],
      totalPayroll: 0,
      employeeCount: 0,
      avgSalary: 0,
      salaryDistribution: [],
      contractTypeData: [],
      genderData: [],
      departmentData: []
    };
  }
}

export async function getNote(id: string): Promise<Note | null> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      throw new Error("You must be logged in to fetch a note");
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      throw new Error("User not found in the database");
    }

    const note = await prisma.note.findUnique({
      where: { id: id, userId: dbUser.id },
    });

    // Handle content data type - keep as object for custom blocks
    return note;
  } catch (error) {
    console.error('Error in getNote:', error);
    throw error;
  }
}

export async function saveNote(
  id: string,
  title: string,
  content: string,
  coverImage: string | null,
  parentId?: string,
  mergedFromPublicId?: string,
  isMerged?: boolean,
  metadata?: string | null
): Promise<{ success: boolean; error?: string; noteId?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    // Parse content to JSON before saving
    let contentJson;
    try {
      contentJson = JSON.parse(content);

      // Process each block to ensure custom blocks are properly preserved
      contentJson = contentJson.map((block: any) => {
        if (block.type === 'dataAnalysis') {
          // For dataAnalysis blocks, preserve ALL properties exactly as they are
          return {
            ...block,
            props: {
              ...block.props,
              // BlockNote required props
              analysisType: block.props.analysisType || 'code',
              textAlignment: block.props.textAlignment || 'left',
              textColor: block.props.textColor || 'default',
              // Cell props
              savedQuery: block.props.savedQuery || '',
              language: block.props.language || 'sql',
              cellId: block.props.cellId || '',
              datasetId: block.props.datasetId || 'ds1',
              // Result data
              resultData: block.props.resultData || '[]',
              resultOutput: block.props.resultOutput || '',
              resultPlots: block.props.resultPlots || '[]',
              hasError: block.props.hasError || false,
              errorMessage: block.props.errorMessage || '',
              showGraphicWalker: block.props.showGraphicWalker || false,
              // Chart configuration
              chartType: block.props.chartType,
              chartConfig: block.props.chartConfig,
              // Add viewMode preservation
              viewMode: block.props.viewMode
            }
          };
        }
        return block;
      });
    } catch (error) {
      console.error('Error parsing content JSON:', error);
      return { success: false, error: "Failed to parse note content" };
    }

    // Determine metadata to save
    let metadataToSave = metadata;

    // If no explicit metadata but merging, create metadata
    if (!metadataToSave && isMerged) {
      const mergeMetadata = { mergedFromPublicId, mergedAt: new Date() };
      metadataToSave = JSON.stringify(mergeMetadata);
    }

    if (id) {
      // Update existing note
      await prisma.note.update({
        where: { id },
        data: {
          title,
          content: contentJson, // Store as JSON object
          coverImage,
          parentId,
          metadata: metadataToSave, // Use the processed metadata
          updatedAt: new Date(),
        },
      });
      return { success: true, noteId: id };
    } else {
      // Create new note with metadata
      const note = await prisma.note.create({
        data: {
          title,
          content: contentJson, // Store as JSON object
          coverImage,
          parentId,
          userId: dbUser.id,
          metadata: metadataToSave, // Use the processed metadata
        },
      });
      return { success: true, noteId: note.id };
    }
  } catch (error) {
    console.error('Error in saveNote:', error);
    return { success: false, error: "Failed to save note" };
  }
}

export async function getNotes() {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    const notes = await prisma.note.findMany({
      where: {
        userId: dbUser.id,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      include: {
        children: true,
      },
    });

    return {
      success: true,
      notes: notes.map(note => ({
        ...note,
        content: typeof note.content === 'object' ? JSON.stringify(note.content) : note.content
      }))
    };
  } catch (error) {
    console.error('Error in getNotes:', error);
    return { success: false, error: "Failed to fetch notes" };
  }
}

export async function createFolder(
  folderName: string,
  parentId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    await prisma.note.create({
      // @ts-ignore
      data: {
        title: folderName,
        content: JSON.stringify([]),
        isFolder: true,
        parentId,
        userId: dbUser.id,
      },
    });

    revalidatePath('/hr/workspace/note');
    return { success: true };
  } catch (error) {
    console.error('Error in createFolder:', error);
    return { success: false, error: "Failed to create folder" };
  }
}

export async function deleteNote(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "You must be logged in to delete a note" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      return { success: false, error: "User not found in the database" };
    }

    const note = await prisma.note.findUnique({
      where: { id: id, userId: dbUser.id },
      include: { children: true },
    });

    if (!note) {
      return { success: false, error: "Note not found" };
    }

    // If it's a folder, delete all children recursively
    if (note.isFolder) {
      await deleteNoteRecursive(id);
    } else {
      await prisma.note.delete({ where: { id: id } });
    }

    revalidatePath('/hr/workspace/note');
    return { success: true };
  } catch (error) {
    console.error('Error in deleteNote:', error);
    return { success: false, error: "An error occurred while deleting the note" };
  }
}

async function deleteNoteRecursive(noteId: string) {
  const note = await prisma.note.findUnique({
    where: { id: noteId },
    include: { children: true },
  });

  if (!note) return;

  for (const child of note.children) {
    await deleteNoteRecursive(child.id);
  }

  await prisma.note.delete({ where: { id: noteId } });
}

export async function publishNote(id: string) {
  try {
    const { userId: clerkUserId } = auth()
    if (!clerkUserId) {
      return { success: false, error: "Authentication required" }
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    })

    if (!dbUser) {
      return { success: false, error: "User not found" }
    }

    const note = await prisma.note.findUnique({
      where: { id, userId: dbUser.id },
    })

    if (!note) {
      return { success: false, error: "Note not found" }
    }

    // Generate a new publicId if not exists
    const newPublicId = note.publicId || `${id}-${crypto.randomUUID()}`

    // Check if publicId already exists (excluding current note)
    const existingNote = await prisma.note.findFirst({
      where: {
        publicId: newPublicId,
        NOT: {
          id: id
        }
      }
    })

    if (existingNote) {
      return { success: false, error: "Failed to generate unique public ID" }
    }

    await prisma.note.update({
      where: { id },
      data: {
        isPublished: true,
        publishedAt: new Date(),
        publicId: newPublicId,
      },
    })

    return { success: true, publicUrl: newPublicId }
  } catch (error) {
    console.error('Error publishing note:', error)
    return { success: false, error: "Failed to publish note" }
  }
}

export async function getPublicNote(publicId: string) {
  try {
    const note = await prisma.note.findFirst({
      where: {
        publicId,
        isPublished: true, // Only return published notes
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    // If note is found, ensure content is properly formatted
    if (note) {
      return {
        ...note,
        // Ensure content is parsed correctly for the client
        content: typeof note.content === "string"
          ? JSON.parse(note.content)
          : note.content,
        author: note.user,
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching public note:', error);
    return null;
  }
}

export async function getDetailedEmployeeData(): Promise<any[]> {
  try {
    const employees = await prisma.employee.findMany({
      select: {
        id: true,
        prenom: true,
        nom: true,
        email: true,
        telephone: true,
        dateNaissance: true,
        genre: true,
        adresse: true,
        departement: true,
        poste: true,
        dateDebut: true,
        salaire: true,
        typeEmploi: true,
        contactUrgence: true,
        telephoneUrgence: true,
        competences: true,
        notesAdditionnelles: true,
        cin: true,
        cnss: true,
        rib: true,
        mutuelle: true,
        numeroCIMR: true,
        nationalite: true,
        numeroPasseport: true,
        permisTravaill: true,
        dateExpirationPermis: true,
        statutMatrimonial: true,
        niveauEducation: true,
        diplomes: true,
        languesParlees: true,
        salaireBrut: true,
        salaireNet: true,
        tauxIR: true
      }
    });

    return employees.map(emp => ({
      ...emp,
      name: `${emp.prenom} ${emp.nom}`,
      // Format dates for display
      dateNaissance: emp.dateNaissance.toLocaleDateString(),
      dateDebut: emp.dateDebut.toLocaleDateString(),
      dateExpirationPermis: emp.dateExpirationPermis?.toLocaleDateString(),
      // Format arrays for display
      competences: emp.competences.join(', '),
      diplomes: emp.diplomes.join(', '),
      languesParlees: emp.languesParlees.join(', '),
      // Format salary numbers
      salaire: emp.salaire.toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' }),
      salaireBrut: emp.salaireBrut?.toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' }),
      salaireNet: emp.salaireNet?.toLocaleString('fr-MA', { style: 'currency', currency: 'MAD' }),
      tauxIR: emp.tauxIR ? `${emp.tauxIR}%` : null
    }));
  } catch (error) {
    console.error('Failed to fetch detailed employee data:', error);
    return [];
  }
}

export async function getDetailedNotesData(): Promise<any[]> {
  try {
    const notes = await prisma.note.findMany({
      select: {
        id: true,
        title: true,
        content: true,
        coverImage: true,
        isFolder: true,
        isPublished: true,
        publishedAt: true,
        publicId: true,
        createdAt: true,
        updatedAt: true,

        parentId: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return notes.map(note => ({
      ...note,
      createdAt: note.createdAt.toLocaleDateString(),
      updatedAt: note.updatedAt.toLocaleDateString(),
      publishedAt: note.publishedAt?.toLocaleDateString(),
      // Format content preview if it's a string
      contentPreview: typeof note.content === 'string'
        ? note.content.slice(0, 100) + (note.content.length > 100 ? '...' : '')
        : null
    }));
  } catch (error) {
    console.error('Failed to fetch detailed notes data:', error);
    return [];
  }
}

export async function getDetailedDiagramsData(): Promise<any[]> {
  try {
    const diagrams = await prisma.diagram.findMany({
      select: {
        id: true,
        title: true,


        content: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return diagrams.map(diagram => ({
      ...diagram,
      createdAt: diagram.createdAt.toLocaleDateString(),
      updatedAt: diagram.updatedAt.toLocaleDateString(),
      // Format content preview if it's a string
      contentPreview: typeof diagram.content === 'string'
        ? diagram.content.slice(0, 100) + (diagram.content.length > 100 ? '...' : '')
        : null
    }));
  } catch (error) {
    console.error('Failed to fetch detailed diagrams data:', error);
    return [];
  }
}

export async function deleteEmployees(employeeIds: string[]): Promise<{ success: boolean; error?: string; updatedEmployees?: any[] }> {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return { success: false, error: "User not found" };
    }

    // Verify all employees belong to the user before deletion
    const employees = await prisma.employee.findMany({
      where: {
        id: { in: employeeIds },
        userId: user.id
      }
    });

    if (employees.length !== employeeIds.length) {
      return {
        success: false,
        error: "Some employees could not be found or don't belong to your company"
      };
    }

    // Delete employees in a transaction
    await prisma.$transaction([
      prisma.attendance.deleteMany({
        where: { employeeId: { in: employeeIds } }
      }),
      prisma.employeeDocument.deleteMany({
        where: { employeeId: { in: employeeIds } }
      }),
      prisma.employee.deleteMany({
        where: { id: { in: employeeIds } }
      })
    ]);

    // Get the updated list of employees
    const updatedEmployees = await prisma.employee.findMany({
      where: {
        userId: user.id,
      },
      select: {
        id: true,
        matricule: true,
        prenom: true,
        nom: true,
        email: true,
        telephone: true,
        dateNaissance: true,
        genre: true,
        adresse: true,
        departement: true,
        poste: true,
        dateDebut: true,
        dateFinContrat: true,
        salaire: true,
        typeEmploi: true,
        contactUrgence: true,
        telephoneUrgence: true,
        competences: true,
        notesAdditionnelles: true,
        cin: true,
        cnss: true,
        rib: true,
        mutuelle: true,
        nationalite: true,
        numeroPasseport: true,
        permisTravaill: true,
        dateExpirationPermis: true,
        statutMatrimonial: true,
        niveauEducation: true,
        diplomes: true,
        languesParlees: true,
        salaireBrut: true,
        salaireNet: true,
        tauxIR: true,
        tauxCNSS: true,
        tauxAMO: true,
        banque: true,
        agenceBancaire: true,
        modePaiement: true,
        echelonSalaire: true,
        contratType: true,
        periodeEssai: true,
        congesPayes: true,
        congesMaladie: true,
        congesMaternite: true,
        estLocataire: true,
        possedeAppartement: true,
        utiliseTransport: true,
        typeTransport: true,
        distanceDomicileTravail: true,
        numeroCIMR: true,
        groupeSanguin: true,
        situationFamiliale: true,
        zoneResidence: true,
        formationsContinues: true,
        nombreEnfants: true,
        modeTravaill: true,
        societe: true,
        service: true,
        nomService: true,
        division: true,
        droitConge: true,
        dDepart: true,
        nomPaiement: true,
        grid: true,
        confIGR: true,
        confCNSS: true,
        confMutuel: true,
        confAT: true,
        confBourB: true,
        confALLFAM: true,
        confCIMR: true,
        confCos: true,
        anulAnc: true,
        ville: true,
        pays: true,
        mot: true,
        relicaC: true,
        dateRelica: true,
        cos: true,
        compteCompt: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        nom: 'asc',
      },
    });

    // Format dates and other fields for client-side use
    const formattedEmployees = updatedEmployees.map(employee => ({
      ...employee,
      dateFinContrat: employee.dateFinContrat ? employee.dateFinContrat.toISOString() : null,
      dateNaissance: employee.dateNaissance ? employee.dateNaissance.toISOString() : null,
      dateDebut: employee.dateDebut ? employee.dateDebut.toISOString() : null,
      dateExpirationPermis: employee.dateExpirationPermis ? employee.dateExpirationPermis.toISOString() : null,
      dDepart: employee.dDepart ? employee.dDepart.toISOString() : null,
      dateRelica: employee.dateRelica ? employee.dateRelica.toISOString() : null,
      salaire: typeof employee.salaire === 'number' ? employee.salaire : parseFloat(employee.salaire as string) || 0
    }));

    revalidatePath('/hr/employee');
    return { success: true, updatedEmployees: formattedEmployees };
  } catch (error) {
    console.error('Error deleting employees:', error);
    return { success: false, error: "Failed to delete employees" };
  }
}

export async function updateNoteParent(noteId: string, newParentId: string | null) {
  try {
    // Ensure this matches your database structure
    const updated = await prisma.note.update({
      where: { id: noteId },
      data: {
        parentId: newParentId,
        updatedAt: new Date()
      },
    });

    return {
      success: true,
      note: updated
    };
  } catch (error) {
    console.error("Error updating note parent:", error);
    return {
      success: false,
      error: "Failed to update note parent"
    };
  }
}