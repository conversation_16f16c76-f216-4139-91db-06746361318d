'use server';

import { revalidatePath } from "next/cache";
import { currentUser } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { ensureUserInDatabase } from "@/lib/ensure-user";

export type DetailedEmployeeData = {
  userId: string;
  clerkId: string;
  prenom: string;
  nom: string;
  email: string;
  telephone: string;
  dateNaissance: Date;
  genre: string;
  adresse: string;
  departement: string;
  poste: string;
  dateDebut: Date;
  salaire: number;
  typeEmploi: string;
  contactUrgence: string;
  telephoneUrgence: string;
  competences: string[];
  notesAdditionnelles: string;
  cin: string;
  cnss: string;
  rib: string;
  mutuelle: string;
  nationalite: string;
  numeroPasseport: string;
  permisTravaill: string;
  dateExpirationPermis: Date | null;
  statutMatrimonial: string;
  niveauEducation: string;
  diplomes: string[];
  languesParlees: string[];
  salaireBrut: number;
  salaireNet: number;
  tauxIR: number;
  tauxCNSS: number;
  tauxAMO: number;
  agenceBancaire: string;
  contratType: string;
  dateFinContrat: Date | null;
  periodeEssai: number;
  congesPayes: number;
  congesMaladie: number;
  congesMaternite: number;
  estLocataire: boolean;
  possedeAppartement: boolean;
  utiliseTransport: boolean;
  typeTransport: string;
  distanceDomicileTravail: number;
  numeroCIMR: string;
  groupeSanguin: string;
  situationFamiliale: string;
  zoneResidence: string;
  modePaiement: string;
  echelonSalaire: string;
  formationsContinues: string[];
  nombreEnfants: number;
  banque: string;
  modeTravaill: string;
};

export async function getDetailedEmployeeData(): Promise<DetailedEmployeeData[]> {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return [];
    }

    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id
      },
      select: {
        userId: true,
        // @ts-ignore
        clerkId: true,
        prenom: true,
        nom: true,
        email: true,
        telephone: true,
        dateNaissance: true,
        genre: true,
        adresse: true,
        departement: true,
        poste: true,
        dateDebut: true,
        salaire: true,
        typeEmploi: true,
        contactUrgence: true,
        telephoneUrgence: true,
        competences: true,
        notesAdditionnelles: true,
        cin: true,
        cnss: true,
        rib: true,
        mutuelle: true,
        nationalite: true,
        numeroPasseport: true,
        permisTravaill: true,
        dateExpirationPermis: true,
        statutMatrimonial: true,
        niveauEducation: true,
        diplomes: true,
        languesParlees: true,
        salaireBrut: true,
        salaireNet: true,
        tauxIR: true,
        tauxCNSS: true,
        tauxAMO: true,
        agenceBancaire: true,
        contratType: true,
        dateFinContrat: true,
        periodeEssai: true,
        congesPayes: true,
        congesMaladie: true,
        congesMaternite: true,
        estLocataire: true,
        possedeAppartement: true,
        utiliseTransport: true,
        typeTransport: true,
        distanceDomicileTravail: true,
        numeroCIMR: true,
        groupeSanguin: true,
        situationFamiliale: true,
        zoneResidence: true,
        modePaiement: true,
        echelonSalaire: true,
        formationsContinues: true,
        nombreEnfants: true,
        banque: true,
        modeTravaill: true
      }
    });
    // @ts-ignore
    return employees;
  } catch (error) {
    console.error('Failed to fetch detailed employee data:', error);
    return [];
  }
}

export async function getDetailedDiagramsData(): Promise<any[]> {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return [];
    }

    // Fetch all employee data
    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id
      },
      select: {
        id: true,
        prenom: true,
        nom: true,
        email: true,
        telephone: true,
        dateNaissance: true,
        genre: true,
        adresse: true,
        departement: true,
        poste: true,
        dateDebut: true,
        salaire: true,
        typeEmploi: true,
        contactUrgence: true,
        telephoneUrgence: true,
        competences: true,
        notesAdditionnelles: true,
        cin: true,
        cnss: true,
        rib: true,
        mutuelle: true,
        nationalite: true,
        numeroPasseport: true,
        permisTravaill: true,
        dateExpirationPermis: true,
        statutMatrimonial: true,
        niveauEducation: true,
        diplomes: true,
        languesParlees: true,
        salaireBrut: true,
        salaireNet: true,
        tauxIR: true,
        tauxCNSS: true,
        tauxAMO: true,
        agenceBancaire: true,
        contratType: true,
        dateFinContrat: true,
        periodeEssai: true,
        congesPayes: true,
        congesMaladie: true,
        congesMaternite: true,
        estLocataire: true,
        possedeAppartement: true,
        utiliseTransport: true,
        typeTransport: true,
        distanceDomicileTravail: true,
        numeroCIMR: true,
        groupeSanguin: true,
        situationFamiliale: true,
        zoneResidence: true,
        modePaiement: true,
        echelonSalaire: true,
        formationsContinues: true,
        nombreEnfants: true,
        banque: true,
        modeTravaill: true
      }
    });

    // Transform the data into a hierarchical structure with all details
    // @ts-ignore
    const diagrams = employees.map(emp => ({
      id: emp.id,
      type: 'employee',
      title: `${emp.prenom} ${emp.nom}`,
      description: emp.poste,
      department: emp.departement,
      personalInfo: {
        email: emp.email,
        phone: emp.telephone,
        birthDate: emp.dateNaissance,
        gender: emp.genre,
        address: emp.adresse,
        nationality: emp.nationalite,
        passportNumber: emp.numeroPasseport,
        maritalStatus: emp.situationFamiliale,
        numberOfChildren: emp.nombreEnfants,
        bloodGroup: emp.groupeSanguin,
        residenceZone: emp.zoneResidence
      },
      employmentDetails: {
        position: emp.poste,
        department: emp.departement,
        startDate: emp.dateDebut,
        contractType: emp.contratType,
        contractEndDate: emp.dateFinContrat,
        probationPeriod: emp.periodeEssai,
        workPermit: emp.permisTravaill,
        workPermitExpiry: emp.dateExpirationPermis,
        workMode: emp.modeTravaill
      },
      financialInfo: {
        grossSalary: emp.salaireBrut,
        netSalary: emp.salaireNet,
        irRate: emp.tauxIR,
        cnssRate: emp.tauxCNSS,
        amoRate: emp.tauxAMO,
        bank: emp.banque,
        bankBranch: emp.agenceBancaire,
        rib: emp.rib,
        salaryScale: emp.echelonSalaire,
        paymentMethod: emp.modePaiement
      },
      benefits: {
        paidLeave: emp.congesPayes,
        sickLeave: emp.congesMaladie,
        maternityLeave: emp.congesMaternite
      },
      identificationDocs: {
        cin: emp.cin,
        cnss: emp.cnss,
        cimrNumber: emp.numeroCIMR,
        mutuelle: emp.mutuelle
      },
      qualifications: {
        educationLevel: emp.niveauEducation,
        diplomas: emp.diplomes,
        spokenLanguages: emp.languesParlees,
        skills: emp.competences,
        continuousTraining: emp.formationsContinues
      },
      transportation: {
        usesTransport: emp.utiliseTransport,
        transportType: emp.typeTransport,
        distanceToWork: emp.distanceDomicileTravail
      },
      housing: {
        isRenter: emp.estLocataire,
        ownsApartment: emp.possedeAppartement
      },
      emergencyContact: {
        name: emp.contactUrgence,
        phone: emp.telephoneUrgence
      },
      additionalNotes: emp.notesAdditionnelles
    }));

    // Group by department with enhanced metrics
    const departmentGroups = diagrams.reduce((groups: any, item:any) => {
      const dept = item.department;
      if (!groups[dept]) {
        groups[dept] = [];
      }
      groups[dept].push(item);
      return groups;
    }, {});

    // Create comprehensive organizational diagrams
    const organizationalDiagrams = Object.entries(departmentGroups).map(([dept, employees]) => ({
      id: `dept-${dept}`,
      type: 'department',
      title: dept,
      description: `${dept} Department`,
      employees: employees,
      metrics: {
        employeeCount: (employees as any[]).length,
        averageSalary: (employees as any[]).reduce((sum, emp) => sum + emp.financialInfo.grossSalary, 0) / (employees as any[]).length,
        salaryRange: {
          min: Math.min(...(employees as any[]).map(emp => emp.financialInfo.grossSalary)),
          max: Math.max(...(employees as any[]).map(emp => emp.financialInfo.grossSalary))
        },
        skillDistribution: (employees as any[]).reduce((skills: any, emp) => {
          emp.qualifications.skills.forEach((skill: string) => {
            skills[skill] = (skills[skill] || 0) + 1;
          });
          return skills;
        }, {}),
        languageDistribution: (employees as any[]).reduce((langs: any, emp) => {
          emp.qualifications.spokenLanguages.forEach((lang: string) => {
            langs[lang] = (langs[lang] || 0) + 1;
          });
          return langs;
        }, {}),
        contractTypes: (employees as any[]).reduce((types: any, emp) => {
          const type = emp.employmentDetails.contractType;
          types[type] = (types[type] || 0) + 1;
          return types;
        }, {}),
        workModeDistribution: (employees as any[]).reduce((modes: any, emp) => {
          const mode = emp.employmentDetails.workMode;
          modes[mode] = (modes[mode] || 0) + 1;
          return modes;
        }, {}),
        genderDistribution: (employees as any[]).reduce((genders: any, emp) => {
          const gender = emp.personalInfo.gender;
          genders[gender] = (genders[gender] || 0) + 1;
          return genders;
        }, {})
      }
    }));

    return organizationalDiagrams;
  } catch (error) {
    console.error('Error in getDetailedDiagramsData:', error);
    return [];
  }
}
