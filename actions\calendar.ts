'use server';

import type { 
  Holiday 
} from "@/types/calendar";


export async function getHolidays(year: number): Promise<Holiday[]> {
  return [
    { name: "Nouvel An", type: 'fixed', date: new Date(year, 0, 1) },
    { name: "Manifeste de l'Indépendance", type: 'fixed', date: new Date(year, 0, 11) },
    { name: "Fête du Travail", type: 'fixed', date: new Date(year, 4, 1) },
    { name: "Fête du Trône", type: 'fixed', date: new Date(year, 6, 30) },
    { name: "Révolution du Roi et du Peuple", type: 'fixed', date: new Date(year, 7, 20) },
    { name: "<PERSON><PERSON><PERSON> de la Jeunesse", type: 'fixed', date: new Date(year, 7, 21) },
    { name: "Marche Verte", type: 'fixed', date: new Date(year, 10, 6) },
    { name: "Fête de l'Indépendance", type: 'fixed', date: new Date(year, 10, 18) },

    // Islamic holidays (dates should be dynamically calculated based on the Hijri calendar)
    { name: "<PERSON><PERSON><PERSON> du Ramadan", type: 'islamic', date: new Date(year, 2, 10) }, // Placeholder
    { name: "<PERSON><PERSON> al-Qadr", type: 'islamic', date: new Date(year, 3, 7) }, // Placeholder
    { name: "Aïd al-Fitr", type: 'islamic', date: new Date(year, 3, 21) }, // Placeholder
    { name: "Aïd al-Adha", type: 'islamic', date: new Date(year, 5, 28) }, // Placeholder
    { name: "Nouvel An Musulman (1er Muharram)", type: 'islamic', date: new Date(year, 6, 19) }, // Placeholder
    { name: "Achoura (10 Muharram)", type: 'islamic', date: new Date(year, 6, 28) }, // Placeholder
    { name: "Al Mawlid (12 Rabi' al-Awwal)", type: 'islamic', date: new Date(year, 8, 27) }, // Placeholder
  ];
}
