'use server';

import { revalidatePath } from "next/cache";
import { auth } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { ensureUserInDatabase } from "@/lib/ensure-user";
import { EventEmitter } from 'events';
import { pusherServer } from "@/lib/pusher";

// Add this interface at the top
interface MessageResponse {
  id: string;
  content: string;
  fileUrl?: string;
  fileKey?: string;
  fileType?: string;
  user: string;
  userId: string;
  channelId: string;
  avatar: string;
  timestamp: string;
  reactions: any[];
}

// Create an event emitter for real-time updates
const chatEmitter = new EventEmitter();

// Add these event types at the top
type ChatEvent = {
  type: 'message' | 'messageDeleted';
  channelId: string;
  data: any;
};

const UPLOADTHING_URL = "https://uploadthing.com/f/";

// Add this helper function at the top of the file
const getFileTypeFromUrl = (url: string, providedType?: string): string => {
  // If we have a provided type that starts with 'image/', use it
  if (providedType?.startsWith('image/')) {
    return 'image';
  }

  // Check file extension
  const extension = url.split('.').pop()?.toLowerCase();
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
    return 'image';
  }
  if (extension === 'pdf') {
    return 'pdf';
  }
  if (extension === 'csv' || providedType?.includes('spreadsheet')) {
    return 'spreadsheet';
  }
  return providedType || 'other';
};

export async function createChannel(name: string, description?: string) {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      throw new Error("User not found");
    }

    // First create the channel
    const channel = await prisma.channel.create({
      data: {
        name,
        description,
        creator: { 
          connect: { id: user.id } 
        },
        members: {
          connect: [{ id: user.id }]
        }
      }
    });

    // Then create the UserRole separately
    await prisma.userRole.create({
      data: {
        user: { connect: { id: user.id } },
        channel: { connect: { id: channel.id } },
        role: "OWNER"
      }
    });

    // Update the channel to include the creator in memberIds
    const updatedChannel = await prisma.channel.update({
      where: { id: channel.id },
      data: {
        memberIds: [user.id]
      },
      include: {
        creator: true,
        userRoles: true,
        members: true
      }
    });

    revalidatePath('/hr/workspace/chats');
    return { success: true, channel: updatedChannel };
  } catch (error) {
    console.error('Error in createChannel:', error);
    return { success: false, error: "An error occurred while creating the channel" };
  }
}

export async function getChannels() {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return { success: true, channels: [] };
    }

    const channels = await prisma.channel.findMany({
      where: {
        OR: [
          { creatorId: user.id },
          { memberIds: { has: user.id } }
        ]
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        userRoles: {
          where: { userId: user.id }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log('Found channels:', channels);

    return { 
      success: true, 
      channels: channels.map(channel => ({
        id: channel.id,
        name: channel.name,
        description: channel.description,
        role: channel.userRoles[0]?.role || 'MEMBER',
        isCreator: channel.creatorId === user.id,
        createdAt: channel.createdAt,
        memberCount: channel.memberIds.length
      }))
    };
  } catch (error) {
    console.error('Error in getChannels:', error);
    return { success: true, channels: [] };
  }
}

export async function sendMessage({ 
  channelId, 
  content, 
  userId, 
  fileKey,
  fileType,
  fileUrl,
  replyTo 
}: {
  channelId: string;
  content: string;
  userId: string;
  fileKey?: string;
  fileType?: string;
  fileUrl?: string;
  replyTo?: string;
}) {
  try {
    const { userId: clerkId } = auth();
    const user = await ensureUserInDatabase();
    if (!user) {
      throw new Error("User not found");
    }

    // Construct the file URL and determine file type
    const constructedFileUrl = fileKey 
      ? `${UPLOADTHING_URL}${fileKey}` 
      : fileUrl;

    // Determine the correct file type
    const detectedFileType = getFileTypeFromUrl(constructedFileUrl || '', fileType);

    // Create base message data
    const messageData: any = {
      content,
      user: { connect: { id: user.id } },
      channel: { connect: { id: channelId } },
      fileKey,
      fileType: detectedFileType, // Use the detected file type
      fileUrl: constructedFileUrl
    };

    // Add reply data if present
    if (replyTo) {
      messageData.parent = { connect: { id: replyTo } };
    }

    const message = await prisma.message.create({
      data: messageData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            clerkId: true
          }
        }
      }
    });

    const responseData = {
      id: message.id,
      content: message.content,
      fileUrl: constructedFileUrl,
      fileType: detectedFileType, // Use the detected file type
      user: message.user.name || 'Anonymous',
      userId: message.user.clerkId,
      channelId,
      avatar: `https://api.dicebear.com/7.x/initials/svg?seed=${message.user.name || 'Anonymous'}`,
      timestamp: message.createdAt.toISOString(),
      reactions: []
    };

    console.log('Message response data:', responseData);

    // Trigger Pusher event for real-time updates
    await pusherServer.trigger(`chat-${channelId}`, 'message', {
      type: 'message',
      channelId,
      data: responseData
    });

    return { success: true, message: responseData };
  } catch (error) {
    console.error('Error sending message:', error);
    return { success: false, error: 'Failed to send message' };
  }
}

export async function getMessages(channelId: string) {
  try {
    const { userId: clerkId } = auth();
    if (!clerkId) {
      throw new Error("Not authenticated");
    }

    const messages = await prisma.message.findMany({
      where: { channelId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            clerkId: true
          }
        },
        reactions: {
          include: { 
            user: {
              select: {
                id: true,
                name: true
              }
            } 
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    return { 
      success: true, 
      messages: messages.map(message => ({
        id: message.id,
        content: message.content,
        // @ts-ignore
        fileUrl: message.fileUrl || (message.fileKey ? `${UPLOADTHING_URL}${message.fileKey}` : null),
        fileType: message.fileType,
        fileKey: message.fileKey,
        user: message.user.name || 'Anonymous',
        userId: message.user.clerkId,
        avatar: `https://api.dicebear.com/7.x/initials/svg?seed=${message.user.name || 'Anonymous'}`,
        timestamp: message.createdAt.toISOString(),
        reactions: message.reactions.map(reaction => ({
          emoji: reaction.emoji,
          count: 1,
          users: [reaction.user.name || 'Anonymous']
        }))
      } as MessageResponse))
    };
  } catch (error) {
    console.error('Error in getMessages:', error);
    return { success: false, error: "Failed to fetch messages" };
  }
}

export async function addReaction(messageId: string, emoji: string) {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      throw new Error("Not authenticated");
    }

    const reaction = await prisma.reaction.create({
      // @ts-ignore
      data: {
        emoji,
        user: { connect: { id: user.id } },
        userId: user.id,
        message: { connect: { id: messageId } }
      }
    });

    revalidatePath('/hr/workspace/chats');
    return { success: true, reaction };
  } catch (error) {
    console.error('Error in addReaction:', error);
    return { success: false, error: "Failed to add reaction" };
  }
}

export async function generateInviteLink(channelId: string) {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      throw new Error("Not authenticated");
    }

    // Verify channel ownership
    const channel = await prisma.channel.findFirst({
      where: {
        id: channelId,
        creatorId: user.id
      }
    });

    if (!channel) {
      return { success: false, error: "Channel not found or not authorized" };
    }

    const inviteToken = Buffer.from(`${channelId}-${Date.now()}-${Math.random()}`)
      .toString('base64')
      .replace(/[+/=]/g, '');

    await prisma.channel.update({
      where: { id: channelId },
      data: { inviteToken }
    });

    return { 
      success: true, 
      inviteLink: `/hr/workspace/chats/join/${inviteToken}` 
    };
  } catch (error) {
    console.error('Error generating invite link:', error);
    return { success: false, error: "Failed to generate invite link" };
  }
}

export async function joinChannelByInvite(inviteToken: string, userName: string) {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    // Find the channel with the invite token
    const channel = await prisma.channel.findFirst({
      where: { inviteToken }
    });

    if (!channel) {
      return { success: false, error: "Invalid or expired invite link" };
    }

    // Check if user is already a member
    const isMember = channel.memberIds.includes(user.id);
    if (isMember) {
      return { 
        success: true, 
        channelId: channel.id, 
        message: "You're already a member of this channel" 
      };
    }

    // Add user to channel
    await prisma.channel.update({
      where: { id: channel.id },
      data: {
        memberIds: {
          push: user.id
        },
        userRoles: {
          create: {
            userId: user.id,
            role: "MEMBER"
          }
        }
      }
    });

    return { 
      success: true, 
      channelId: channel.id,
      message: "Successfully joined the channel!"
    };
  } catch (error) {
    console.error('Error joining channel:', error);
    return { success: false, error: "Failed to join channel" };
  }
}

export async function getChannelMembers(channelId: string) {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    const channel = await prisma.channel.findFirst({
      where: {
        id: channelId,
        OR: [
          { creatorId: user.id },
          { memberIds: { has: user.id } }
        ]
      },
      include: {
        members: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!channel) {
      return { success: false, error: "Channel not found or access denied" };
    }

    return { 
      success: true, 
      members: channel.members.map(member => ({
        id: member.id,
        name: member.name || 'Anonymous',
        email: member.email
      }))
    };
  } catch (error) {
    console.error('Error fetching channel members:', error);
    return { success: false, error: "Failed to fetch channel members" };
  }
}

export async function subscribeToMessages(channelId: string) {
  const stream = new ReadableStream({
    start(controller) {
      const listener = (message: any) => {
        if (message.channelId === channelId) {
          controller.enqueue(`data: ${JSON.stringify(message)}\n\n`);
        }
      };

      chatEmitter.on('newMessage', listener);

      return () => {
        chatEmitter.off('newMessage', listener);
      };
    }
  });

  return stream;
}

export async function getRecentNotifications() {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return { success: false, notifications: [] };
    }

    // Get channels the user is a member of
    const channels = await prisma.channel.findMany({
      where: {
        OR: [
          { creatorId: user.id },
          { memberIds: { has: user.id } }
        ]
      },
      select: { id: true }
    });

    const channelIds = channels.map(channel => channel.id);

    // Get recent messages from all channels user is part of
    const recentMessages = await prisma.message.findMany({
      where: {
        channelId: { in: channelIds },
        userId: { not: user.id }, // Exclude user's own messages
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          }
        },
        channel: {
          select: {
            id: true,
            name: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5 // Get last 5 messages
    });

    return {
      success: true,
      notifications: recentMessages.map(msg => ({
        id: msg.id,
        title: `New message in ${msg.channel.name}`,
        message: `${msg.user.name}: ${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}`,
        time: msg.createdAt,
        channelId: msg.channel.id
      }))
    };
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return { success: false, notifications: [] };
  }
}

export async function deleteMessage(messageId: string, channelId: string) {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      throw new Error("Not authenticated");
    }

    const message = await prisma.message.findFirst({
      where: {
        id: messageId,
        user: {
          clerkId: user.clerkId
        }
      }
    });

    if (!message) {
      return { success: false, error: "Message not found or unauthorized" };
    }

    await prisma.message.delete({
      where: { id: messageId }
    });

    chatEmitter.emit('messageDeleted', {
      type: 'messageDeleted',
      channelId,
      data: { messageId }
    });

    revalidatePath('/hr/workspace/chats');

    // Trigger Pusher event
    await pusherServer.trigger(`chat-${channelId}`, 'messageDeleted', {
      type: 'messageDeleted',
      channelId,
      data: { messageId }
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting message:', error);
    return { success: false, error: 'Failed to delete message' };
  }
}