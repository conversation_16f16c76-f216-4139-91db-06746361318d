'use server'



import { getNotes } from './actions'



interface TreeNode {

  id: string

  name: string

  isFolder?: boolean

  isPublished?: boolean

  createdAt?: string

  updatedAt?: string

  children?: TreeNode[]

}



export async function fetchNotesTreeData(): Promise<TreeNode | null> {

  try {

    const result = await getNotes()

    

    if (!result.success || !result.notes) {

      return null

    }



    // Transform notes into tree structure

    const rootNode: TreeNode = {

      id: 'root',

      name: 'All Notes',

      children: []

    }



    // Group notes by folders

    const folderNotes = result.notes.filter(note => note.isFolder)

    const regularNotes = result.notes.filter(note => !note.isFolder && !note.parentId)

    const childNotes = result.notes.filter(note => !note.isFolder && note.parentId)



    // Add folders

    folderNotes.forEach(folder => {

      const folderNode: TreeNode = {

        id: folder.id,

        name: folder.title,

        isFolder: true,

        createdAt: folder.createdAt?.toISOString(),

        updatedAt: folder.updatedAt?.toISOString(),

        children: childNotes

          .filter(note => note.parentId === folder.id)

          .map(note => ({

            id: note.id,

            name: note.title,

            isFolder: false,

            isPublished: note.isPublished,

            createdAt: note.createdAt?.toISOString(),

            updatedAt: note.updatedAt?.toISOString(),

          }))

      }

      rootNode.children?.push(folderNode)

    })



    // Add regular notes without folders

    regularNotes.forEach(note => {

      rootNode.children?.push({

        id: note.id,

        name: note.title,

        isFolder: false,

        isPublished: note.isPublished,

        createdAt: note.createdAt?.toISOString(),

        updatedAt: note.updatedAt?.toISOString(),

      })

    })



    return rootNode

  } catch (error) {

    console.error('Error fetching notes tree data:', error)

    return null

  }

} 
