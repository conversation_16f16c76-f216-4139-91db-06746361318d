'use server'

import { auth } from "@clerk/nextjs/server"
import prisma from "@/lib/db"
import { eachDayOfInterval, format } from 'date-fns'

interface ActivityData {
  date: string;
  count: number;
}

interface ActivityResponse {
  success: boolean;
  data?: {
    notes: ActivityData[];
    workflows: ActivityData[];
    chats: ActivityData[];
  };
  error?: string;
  hasActivity: boolean;
}

// Function to get all historical activity data
export async function getAllHistoricalActivityData(): Promise<ActivityResponse> {
  try {
    const { userId: clerkId } = auth()
    if (!clerkId) {
      return { success: false, error: "Unauthorized", hasActivity: false };
    }

    // First get the user's internal ID from their clerkId
    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true }
    });

    if (!user) {
      return { success: false, error: "User not found", hasActivity: false };
    }

    // Get ALL historical data (no date restrictions)
    const [notes, workflows, channels] = await Promise.all([
      prisma.note.findMany({
        where: {
          userId: user.id, // Use internal userId
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.diagram.findMany({
        where: {
          clerkId, // Use clerkId directly for diagrams
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.channel.findMany({
        where: {
          creatorId: user.id, // Use internal userId
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      })
    ]);

    // Check if user has any activity
    const hasActivity = notes.length > 0 || workflows.length > 0 || channels.length > 0;

    // If no activity, return early
    if (!hasActivity) {
      return {
        success: true,
        data: {
          notes: [],
          workflows: [],
          chats: []
        },
        hasActivity: false
      };
    }

    // Get the earliest and latest dates
    const allDates = [
      ...notes.map(n => n.createdAt),
      ...workflows.map(w => w.createdAt),
      ...channels.map(c => c.createdAt)
    ];

    if (allDates.length === 0) {
      return {
        success: true,
        data: { notes: [], workflows: [], chats: [] },
        hasActivity: false
      };
    }

    const startDate = new Date(Math.min(...allDates.map(d => d.getTime())));
    const endDate = new Date();

    // Generate daily counts for the entire historical period
    const days = eachDayOfInterval({ start: startDate, end: endDate })

    const activityByDate = days.map(date => {
      const dateStr = format(date, 'yyyy-MM-dd')
      return {
        date: dateStr,
        notes: notes.filter(n => format(n.createdAt, 'yyyy-MM-dd') === dateStr).length,
        workflows: workflows.filter(w => format(w.createdAt, 'yyyy-MM-dd') === dateStr).length,
        chats: channels.filter(c => format(c.createdAt, 'yyyy-MM-dd') === dateStr).length
      }
    })

    // Return actual daily counts to preserve historical data
    const dailyData = activityByDate.map(day => ({
      date: day.date,
      notes: day.notes,
      workflows: day.workflows,
      chats: day.chats
    }))

    return {
      success: true,
      data: {
        notes: dailyData.map(d => ({ date: d.date, count: d.notes })),
        workflows: dailyData.map(d => ({ date: d.date, count: d.workflows })),
        chats: dailyData.map(d => ({ date: d.date, count: d.chats }))
      },
      hasActivity: true
    }
  } catch (error) {
    console.error('Error fetching historical activity data:', error)
    return {
      success: false,
      error: 'Failed to fetch historical activity data',
      hasActivity: false
    }
  }
}

export async function getActivityData(): Promise<ActivityResponse> {
  try {
    const { userId: clerkId } = auth()
    if (!clerkId) {
      return { success: false, error: "Unauthorized", hasActivity: false };
    }

    // First get the user's internal ID from their clerkId
    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true }
    });

    if (!user) {
      return { success: false, error: "User not found", hasActivity: false };
    }

    // Get the date range (last 365 days to preserve historical data)
    const endDate = new Date()
    const startDate = new Date(endDate)
    startDate.setDate(startDate.getDate() - 365) // Changed from 30 to 365 days

    // Get all items within date range for the specific user
    const [notes, workflows, channels] = await Promise.all([
      prisma.note.findMany({
        where: { 
          userId: user.id, // Use internal userId
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.diagram.findMany({
        where: { 
          clerkId, // Use clerkId directly for diagrams
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.channel.findMany({
        where: { 
          creatorId: user.id, // Use internal userId
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      })
    ]);

    // Check if user has any activity
    const hasActivity = notes.length > 0 || workflows.length > 0 || channels.length > 0;

    // If no activity, return early
    if (!hasActivity) {
      return {
        success: true,
        data: {
          notes: [],
          workflows: [],
          chats: []
        },
        hasActivity: false
      };
    }

    // Generate daily counts for the entire period
    const days = eachDayOfInterval({ start: startDate, end: endDate })

    const activityByDate = days.map(date => {
      const dateStr = format(date, 'yyyy-MM-dd')
      return {
        date: dateStr,
        notes: notes.filter(n => format(n.createdAt, 'yyyy-MM-dd') === dateStr).length,
        workflows: workflows.filter(w => format(w.createdAt, 'yyyy-MM-dd') === dateStr).length,
        chats: channels.filter(c => format(c.createdAt, 'yyyy-MM-dd') === dateStr).length
      }
    })

    // Return actual daily counts instead of cumulative to preserve historical data
    const dailyData = activityByDate.map(day => ({
      date: day.date,
      notes: day.notes,
      workflows: day.workflows,
      chats: day.chats
    }))

    return {
      success: true,
      data: {
        notes: dailyData.map(d => ({ date: d.date, count: d.notes })),
        workflows: dailyData.map(d => ({ date: d.date, count: d.workflows })),
        chats: dailyData.map(d => ({ date: d.date, count: d.chats }))
      },
      hasActivity: true
    }
  } catch (error) {
    console.error('Error fetching activity data:', error)
    return {
      success: false,
      error: 'Failed to fetch activity data',
      hasActivity: false
    }
  }
}
