'use server'

import db from "@/lib/db";
import { auth } from "@clerk/nextjs/server"
import { revalidatePath } from "next/cache"
import { nanoid } from 'nanoid'

export async function saveDiagram(data: {
  title: string;
  nodes: any[];
  edges: any[];
}) {
  try {
    const { userId } = auth()

    if (!userId) {
      throw new Error("Unauthorized")
    }

    if (!data.title) {
      throw new Error("Title is required")
    }

    const diagram = await db.diagram.create({
      data: {
        title: data.title,
        clerkId: userId,
        content: {
          nodes: data.nodes,
          edges: data.edges
        }
      }
    })

    revalidatePath('/hr/workspace/workflow')
    return { success: true, data: diagram }
  } catch (error) {
    console.error('Save Error:', error)
    if (error instanceof Error) {
      return { success: false, error: error.message }
    }
    return { success: false, error: 'Failed to save diagram' }
  }
}

export async function getSavedDiagrams() {
  try {
    const { userId } = auth()

    if (!userId) {
      throw new Error("Unauthorized")
    }

    const diagrams = await db.diagram.findMany({
      where: {
        clerkId: userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, data: diagrams }
  } catch (error) {
    console.error('Get Diagrams Error:', error)
    return { success: false, error: 'Failed to get diagrams' }
  }
}

export async function toggleDiagramPublic(id: string) {
  try {
    const { userId } = auth()

    if (!userId) {
      throw new Error("Unauthorized")
    }

    const diagram = await db.diagram.findFirst({
      where: {
        id,
        clerkId: userId
      }
    })

    if (!diagram) {
      throw new Error("Diagram not found")
    }

    const updatedDiagram = await db.diagram.update({
      where: { id },
      data: {
        isPublic: !diagram.isPublic,
        publicId: !diagram.isPublic ? nanoid() : null
      }
    })

    revalidatePath('/hr/workspace/workflow')
    return {
      success: true,
      isPublic: updatedDiagram.isPublic,
      publicUrl: updatedDiagram.isPublic ? `/workflow/${updatedDiagram.publicId}` : null
    }
  } catch (error) {
    console.error('Toggle Public Error:', error)
    return { success: false, error: 'Failed to toggle public status' }
  }
}

export async function getPublicDiagram(publicId: string) {
  try {
    const diagram = await db.diagram.findFirst({
      where: {
        publicId,
        isPublic: true
      },
      select: {
        id: true,
        title: true,
        content: true,
        isPublic: true,
        publicId: true,
        createdAt: true,
      }
    })

    if (!diagram) {
      throw new Error("Diagram not found or not public")
    }

    return { success: true, data: diagram }
  } catch (error) {
    console.error('Public Diagram Error:', error)
    return { success: false, error: 'Failed to fetch public diagram' }
  }
}