'use client';

import React from 'react';
import AgentSetChatInterface from '@/components/AgentSetRag/AgentSetChatInterface';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

const AgentSetRagPage = () => {
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AgentSet RAG</h1>
          <p className="text-muted-foreground mt-2">
            Interact with your documents using AgentSet&apos;s Retrieval Augmented Generation
          </p>
        </div>
        
        <Separator />
        
        <Tabs defaultValue="chat" className="w-full">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="chat">Chat Interface</TabsTrigger>
            <TabsTrigger value="about">About AgentSet</TabsTrigger>
          </TabsList>
          
          <TabsContent value="chat" className="mt-4">
            <AgentSetChatInterface />
          </TabsContent>
          
          <TabsContent value="about" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>About AgentSet RAG</CardTitle>
                <CardDescription>
                  Learn how AgentSet&apos;s RAG implementation works
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold">What is AgentSet?</h3>
                  <p className="text-muted-foreground">
                    AgentSet is a platform that provides a complete RAG (Retrieval Augmented Generation) 
                    solution through a set of REST APIs. It handles document ingestion, embedding, 
                    storage, and retrieval.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold">How it Works</h3>
                  <ol className="list-decimal pl-5 space-y-2 text-muted-foreground">
                    <li>
                      <strong>Namespaces:</strong> Organizational units for storing documents in vector databases
                    </li>
                    <li>
                      <strong>Document Ingestion:</strong> Documents are chunked, embedded, and stored
                    </li>
                    <li>
                      <strong>Search:</strong> Semantic search with optional reranking and filtering
                    </li>
                    <li>
                      <strong>Integration with LLMs:</strong> Retrieved context is used to enhance LLM responses
                    </li>
                  </ol>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold">Benefits</h3>
                  <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                    <li>Managed infrastructure for vector databases and embeddings</li>
                    <li>Simplified API compared to complex LangChain components</li>
                    <li>Automatic document chunking with configurable parameters</li>
                    <li>Advanced reranking for better search results</li>
                    <li>Metadata filtering for precise document retrieval</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AgentSetRagPage;
