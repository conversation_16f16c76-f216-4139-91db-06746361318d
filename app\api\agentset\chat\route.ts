import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Base URL for AgentSet API
const AGENTSET_API_URL = process.env.AGENTSET_API_URL || 'https://api.agentset.ai';
const AGENTSET_API_KEY = process.env.AGENTSET_API_KEY || '';

// LLM API keys
const COHERE_API_KEY = process.env.COHERE_API_KEY || '';
const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY || '';

// Model providers
type ModelProvider = 'cohere' | 'together';

// Model mapping
const TOGETHER_MODELS = {
  'llama-3-70b': 'meta-llama/Llama-3-70b-chat-hf',
  'llama-3-8b': 'meta-llama/Llama-3-8b-chat-hf',
  'mixtral-8x7b': 'mistralai/Mixtral-8x7B-Instruct-v0.1',
  'mistral-7b': 'mistralai/Mistral-7B-Instruct-v0.2',
  'qwen-72b': 'Qwen/Qwen-72B-Chat',
  'meta':'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'
};

const COHERE_MODELS = {
  'command-r': 'command-r',
  'command-r-plus': 'command-r-plus'
};

// Helper function to determine model provider
function getModelProvider(model: string): ModelProvider {
  if (Object.keys(COHERE_MODELS).includes(model)) {
    return 'cohere';
  }
  return 'together';
}

// Helper function to get actual model ID
function getModelId(model: string, provider: ModelProvider): string {
  if (provider === 'cohere') {
    return COHERE_MODELS[model as keyof typeof COHERE_MODELS] || 'command-r-plus';
  } else {
    return TOGETHER_MODELS[model as keyof typeof TOGETHER_MODELS] || 'meta-llama/Llama-3-70b-chat-hf';
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const {
      messages,
      namespaceId,
      model = 'command-r-plus',
      temperature = 0.7,
      isReasoningMode = false,
      maxTokens = 2048
    } = body;

    // Determine model provider
    const provider = getModelProvider(model);
    const modelId = getModelId(model, provider);

    console.log(`Using ${provider} model: ${modelId}`);
    console.log(`Reasoning mode: ${isReasoningMode ? 'enabled' : 'disabled'}`);

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Invalid messages format' }, { status: 400 });
    }

    if (!namespaceId) {
      return NextResponse.json({ error: 'Namespace ID is required' }, { status: 400 });
    }

    // Get the last user message to use as the search query
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    if (!lastUserMessage) {
      return NextResponse.json({ error: 'No user message found' }, { status: 400 });
    }

    // 1. Search AgentSet for relevant context
    const searchResponse = await fetch(`${AGENTSET_API_URL}/v1/namespace/${namespaceId}/search`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AGENTSET_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: lastUserMessage.content,
        topK: 5,
        rerank: true,
        includeMetadata: true
      })
    });

    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error('AgentSet search error:', errorText);
      return NextResponse.json({
        error: `AgentSet search failed: ${searchResponse.status}`
      }, { status: 500 });
    }

    const searchData = await searchResponse.json();

    // Format the context from search results
    let context = '';
    if (searchData.success && searchData.data && searchData.data.length > 0) {
      context = searchData.data.map((result: any) => {
        const source = result.metadata?.filename || 'Unknown source';
        return `Content: ${result.text}\nSource: ${source}\nRelevance: ${result.score.toFixed(2)}`;
      }).join('\n\n');
    } else {
      context = 'No relevant information found in the knowledge base.';
    }

    // 2. Create a system message with the context
    let systemContent = '';

    if (isReasoningMode) {
      systemContent = `You are an advanced AI assistant with reasoning and agentic capabilities. Your task is to provide detailed, comprehensive answers based on the provided context from the AgentSet knowledge base. You should think step-by-step, analyze the information thoroughly, and provide well-structured, detailed responses.

IMPORTANT INSTRUCTIONS:
1. ALWAYS use the provided context to answer the question. Do not use your general knowledge unless the context is insufficient.
2. ALWAYS cite your sources using the format [Source: X] where X is the source information provided in the context.
3. If multiple sources are used, cite each one at the appropriate point in your answer.
4. Be accurate and faithful to the provided context.
5. If no relevant information is found in the context, explicitly state that no relevant information was found in the knowledge base.
6. REASONING PROCESS: First analyze the question, then examine the available context, identify relevant information, and reason through to a comprehensive answer.
7. DETAILED RESPONSE: Provide thorough explanations with examples, implications, and nuances when possible.
8. STRUCTURE: Use clear headings, bullet points, and paragraphs to organize long responses.
9. REFERENCES: At the end of your response, include a "References" section that lists all the sources you used.

Context:
${context}

Remember to think step-by-step and provide a detailed, well-structured response with proper citations.`;
    } else {
      systemContent = `You are a helpful assistant. Use the following context from the AgentSet knowledge base to answer the user's question. If the context doesn't contain relevant information, say so clearly and suggest what kind of information might be needed.

IMPORTANT INSTRUCTIONS:
1. ALWAYS use the provided context to answer the question. Do not use your general knowledge unless the context is insufficient.
2. ALWAYS cite your sources using the format [Source: X] where X is the source information provided in the context.
3. If multiple sources are used, cite each one.
4. Be accurate and faithful to the provided context.
5. If no relevant information is found in the context, explicitly state that no relevant information was found in the knowledge base.
6. Provide a concise but complete answer.

Context:
${context}`;
    }

    const systemMessage = {
      role: 'system',
      content: systemContent
    };

    // 3. Format the messages based on provider
    const filteredMessages = messages.filter(msg => msg.role !== 'system');

    if (provider === 'cohere') {
      // Create chat history in Cohere format
      const chatHistory = filteredMessages.map(msg => ({
        role: msg.role === 'user' ? 'USER' : 'CHATBOT',
        message: msg.content
      }));

      // 4. Call Cohere API
      const cohereResponse = await fetch('https://api.cohere.ai/v1/chat', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${COHERE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: modelId,
          message: lastUserMessage.content,
          chat_history: chatHistory,
          preamble: systemMessage.content, // Use our system message as preamble
          temperature: temperature,
          max_tokens: maxTokens,
        })
      });

      if (!cohereResponse.ok) {
        const errorText = await cohereResponse.text();
        console.error('Cohere API error:', errorText);
        return NextResponse.json({
          error: `Cohere API failed: ${cohereResponse.status}`
        }, { status: 500 });
      }

      // 5. Return the response
      const cohereData = await cohereResponse.json();
      return NextResponse.json({
        content: cohereData.text || 'Sorry, I could not generate a response.',
        model: model,
        provider: provider
      });
    } else {
      // Format for Together AI
      const togetherMessages = [
        { role: 'system', content: systemMessage.content },
        ...filteredMessages
      ];

      // Call Together AI API
      const togetherResponse = await fetch('https://api.together.xyz/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TOGETHER_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: modelId,
          messages: togetherMessages,
          temperature: temperature,
          max_tokens: maxTokens
        })
      });

      if (!togetherResponse.ok) {
        const errorText = await togetherResponse.text();
        console.error('Together AI API error:', errorText);
        return NextResponse.json({
          error: `Together AI API failed: ${togetherResponse.status}`
        }, { status: 500 });
      }

      // Return the response
      const togetherData = await togetherResponse.json();
      return NextResponse.json({
        content: togetherData.choices?.[0]?.message?.content || 'Sorry, I could not generate a response.',
        model: model,
        provider: provider
      });
    }

  } catch (error: any) {
    console.error('Error in AgentSet chat API:', error);
    return NextResponse.json({
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
