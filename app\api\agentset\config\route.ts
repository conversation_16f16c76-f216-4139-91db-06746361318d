import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Return public config (no sensitive API keys)
    return NextResponse.json({
      apiUrl: process.env.AGENTSET_API_URL || 'https://api.agentset.ai',
      hasApiKey: !!process.env.AGENTSET_API_KEY,
    });
  } catch (error) {
    console.error('Error fetching AgentSet config:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
