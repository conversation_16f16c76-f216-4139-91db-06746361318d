import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Base URL for AgentSet API
const AGENTSET_API_URL = process.env.AGENTSET_API_URL || 'https://api.agentset.ai';
const AGENTSET_API_KEY = process.env.AGENTSET_API_KEY || '';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the path from the URL
    const url = new URL(req.url);
    const path = url.searchParams.get('path');
    
    if (!path) {
      return NextResponse.json({ error: 'Missing path parameter' }, { status: 400 });
    }

    // Forward the request to AgentSet
    const response = await fetch(`${AGENTSET_API_URL}${path}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AGENTSET_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error proxying AgentSet request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the path and body from the request
    const url = new URL(req.url);
    const path = url.searchParams.get('path');
    
    if (!path) {
      return NextResponse.json({ error: 'Missing path parameter' }, { status: 400 });
    }

    // Get the request body
    const body = await req.json();

    // Forward the request to AgentSet
    const response = await fetch(`${AGENTSET_API_URL}${path}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AGENTSET_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error proxying AgentSet request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
