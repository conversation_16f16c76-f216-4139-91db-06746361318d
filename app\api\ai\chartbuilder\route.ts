import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Initialize with updated API configuration
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { prompt, language, datasets } = await req.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    // Use the latest Gemini model with improved capabilities
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash-002",
      generationConfig: {
        temperature: 0.1, // Lower temperature for more consistent code generation
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 2048,
      },
    });

    // Create enhanced dataset context with better data understanding
    const datasetContext = datasets?.map((ds: any, index: number) => {
      const safeName = ds.name
        .replace(/\.[^/.]+$/, '') // Remove file extension
        .replace(/[^a-zA-Z0-9_]/g, '_') // Replace special chars
        .toLowerCase();

      // Analyze column types and provide insights
      const columnAnalysis = ds.columns?.map((col: string) => {
        const sampleValues = ds.sampleData?.map((row: any) => row[col]).filter((val: any) => val != null) || [];
        const isNumeric = sampleValues.every((val: any) => !isNaN(Number(val)) && val !== '');
        const isDate = sampleValues.some((val: any) => !isNaN(Date.parse(val)));

        let type = 'text';
        if (isNumeric) type = 'numeric';
        else if (isDate) type = 'date';

        return `${col} (${type})`;
      }).join(', ') || 'Unknown columns';

      // Create sample data in markdown table format for better readability
      const sampleDataTable = ds.sampleData?.length > 0 ?
        `| ${ds.columns?.join(' | ') || ''} |
| ${ds.columns?.map(() => '---').join(' | ') || ''} |
${ds.sampleData.slice(0, 3).map((row: any) =>
  `| ${ds.columns?.map((col: string) => row[col] || '').join(' | ') || ''} |`
).join('\n')}` : 'No sample data available';

      return `## Dataset ${index + 1}: ${ds.name}
**Table/Variable Name:** ${safeName}
**Total Rows:** ${ds.rowCount || ds.data?.length || 'Unknown'}
**Columns:** ${columnAnalysis}

**Sample Data:**
${sampleDataTable}

**Data Summary:**
- This dataset contains ${ds.rowCount || ds.data?.length || 'unknown number of'} records
- Key columns for analysis: ${ds.columns?.slice(0, 5).join(', ') || 'Unknown'}
- Data types: ${ds.columns?.length || 0} total columns`;
    }).join('\n\n') || 'No datasets provided';

    // Create language-specific system prompt
    const getLanguageInstructions = (lang: string) => {
      switch (lang) {
        case 'sql':
          return `Generate SQL code using these guidelines:
- Use actual dataset names as table names (e.g., employees, sales, etc.)
- Write standard SQL that works with most databases
- Include helpful comments
- Use proper SQL formatting and indentation
- For joins, use meaningful aliases
- Always end statements with semicolons`;

        case 'python':
          return `Generate Python code using these guidelines:
- Datasets are available as CSV files: 'dataset1.csv', 'dataset2.csv', etc.
- Use pd.read_csv('dataset1.csv') to load the first dataset
- Available libraries: pandas as pd, numpy as np, matplotlib.pyplot as plt, seaborn as sns
- For data analysis: use descriptive statistics, groupby operations, filtering
- For visualizations: create clear, well-labeled charts with plt.title(), plt.xlabel(), plt.ylabel()
- Always use plt.tight_layout() and plt.show() for plots
- Include helpful comments explaining each step
- Use proper Python formatting and best practices
- For displaying results: use print() statements or assign to 'result' variable
- Handle missing data appropriately with dropna() or fillna()
- Use meaningful variable names and follow PEP 8 style guidelines`;

        case 'javascript':
          return `Generate JavaScript code using these guidelines:
- Use modern ES6+ syntax
- Include helpful comments
- Use proper formatting and indentation
- Work with the provided dataset objects`;

        default:
          return `Generate ${lang} code with proper formatting and helpful comments.`;
      }
    };

    const systemPrompt = `You are an expert data analyst and AI assistant, similar to Databricks AI Assistant. Generate clean, efficient, and well-documented ${language.toUpperCase()} code based on the user's request.

# AVAILABLE DATASETS
${datasetContext}

# LANGUAGE-SPECIFIC INSTRUCTIONS
${getLanguageInstructions(language)}

# CODE GENERATION RULES
1. **Data Access**: For Python, use pd.read_csv('dataset1.csv'), pd.read_csv('dataset2.csv'), etc.
2. **Code Quality**: Generate production-ready code with proper error handling
3. **Documentation**: Include clear, helpful comments explaining the logic
4. **Best Practices**: Follow language-specific conventions and best practices
5. **Output Format**: Generate ONLY executable code, no markdown or explanations
6. **Data Insights**: When analyzing data, provide meaningful insights through the code
7. **Visualization**: For charts, ensure they are well-labeled and publication-ready
8. **Performance**: Write efficient code that handles data appropriately

# USER REQUEST
${prompt}

# CONTEXT AWARENESS
- Understand the data structure and types from the sample data provided
- Consider the business context and analytical goals
- Suggest appropriate analytical approaches based on the data characteristics
- Ensure the code addresses the specific user request comprehensively

Generate the ${language.toUpperCase()} code:`;

    const result = await model.generateContent(systemPrompt);
    const response = await result.response;
    let text = response.text();

    // Clean up the response to extract just the code
    text = text.trim();
    
    // Remove markdown code blocks if present
    const codeBlockRegex = new RegExp(`\`\`\`${language}\\n([\\s\\S]*?)\`\`\``, 'i');
    const match = text.match(codeBlockRegex);
    
    if (match) {
      text = match[1].trim();
    } else {
      // Try general code block pattern
      const generalCodeRegex = /```[\w]*\n([\s\S]*?)```/;
      const generalMatch = text.match(generalCodeRegex);
      if (generalMatch) {
        text = generalMatch[1].trim();
      }
    }

    // Remove any remaining markdown or explanatory text
    const lines = text.split('\n');
    const codeLines = lines.filter(line => {
      const trimmed = line.trim();
      // Keep lines that look like code (not explanatory text)
      return !trimmed.startsWith('Here') && 
             !trimmed.startsWith('This') && 
             !trimmed.startsWith('The above') &&
             !trimmed.startsWith('Note:') &&
             !trimmed.startsWith('Explanation:');
    });

    const cleanedCode = codeLines.join('\n').trim();

    return NextResponse.json({
      success: true,
      code: cleanedCode,
      language: language
    });

  } catch (error) {
    console.error('Gemini API error:', error);
    return NextResponse.json({
      error: 'Failed to generate code. Please try again.',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
