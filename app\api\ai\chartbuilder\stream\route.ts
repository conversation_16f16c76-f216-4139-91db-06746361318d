import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Initialize with updated API configuration
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }), 
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const { prompt, language, datasets } = await req.json();

    if (!prompt) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Use the latest Gemini model with streaming
    const model = genAI.getGenerativeModel({ 
      model: "gemini-1.5-flash-002",
      generationConfig: {
        temperature: 0.1,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 2048,
      },
    });

    // Create enhanced dataset context
    const datasetContext = datasets?.map((ds: any, index: number) => {
      const safeName = ds.name
        .replace(/\.[^/.]+$/, '')
        .replace(/[^a-zA-Z0-9_]/g, '_')
        .toLowerCase();
      
      const columnAnalysis = ds.columns?.map((col: string) => {
        const sampleValues = ds.sampleData?.map((row: any) => row[col]).filter((val: any) => val != null) || [];
        const isNumeric = sampleValues.every((val: any) => !isNaN(Number(val)) && val !== '');
        const isDate = sampleValues.some((val: any) => !isNaN(Date.parse(val)));
        
        let type = 'text';
        if (isNumeric) type = 'numeric';
        else if (isDate) type = 'date';
        
        return `${col} (${type})`;
      }).join(', ') || 'Unknown columns';

      const sampleDataTable = ds.sampleData?.length > 0 ? 
        `| ${ds.columns?.join(' | ') || ''} |
| ${ds.columns?.map(() => '---').join(' | ') || ''} |
${ds.sampleData.slice(0, 3).map((row: any) => 
  `| ${ds.columns?.map((col: string) => row[col] || '').join(' | ') || ''} |`
).join('\n')}` : 'No sample data available';
      
      return `## Dataset ${index + 1}: ${ds.name}
**Table/Variable Name:** ${safeName}
**Total Rows:** ${ds.rowCount || ds.data?.length || 'Unknown'}
**Columns:** ${columnAnalysis}

**Sample Data:**
${sampleDataTable}`;
    }).join('\n\n') || 'No datasets provided';

    const getLanguageInstructions = (lang: string) => {
      switch (lang) {
        case 'sql':
          return `Generate SQL code using these guidelines:
- Use standard SQL syntax compatible with most databases
- Table names should match the dataset names provided
- Include helpful comments explaining complex queries
- Use proper SQL formatting and indentation
- For aggregations, use GROUP BY appropriately
- Always end statements with semicolons`;
        case 'python':
          return `Generate Python code using these guidelines:
- Datasets are available as CSV files: 'dataset1.csv', 'dataset2.csv', etc.
- Use pd.read_csv('dataset1.csv') to load the first dataset
- Available libraries: pandas as pd, numpy as np, matplotlib.pyplot as plt, seaborn as sns
- For data analysis: use descriptive statistics, groupby operations, filtering
- For visualizations: create clear, well-labeled charts with plt.title(), plt.xlabel(), plt.ylabel()
- Always use plt.tight_layout() and plt.show() for plots
- Include helpful comments explaining each step
- Use proper Python formatting and best practices`;
        case 'javascript':
          return `Generate JavaScript code using these guidelines:
- Use modern ES6+ syntax
- Assume data is available as JSON arrays
- Use array methods like map, filter, reduce for data manipulation
- For visualizations, use D3.js or Chart.js syntax
- Include helpful comments
- Use proper JavaScript formatting`;
        default:
          return `Generate clean, well-documented ${lang} code with helpful comments.`;
      }
    };

    const systemPrompt = `You are an expert data analyst and AI assistant. Generate clean, efficient, and well-documented ${language.toUpperCase()} code based on the user's request.

# AVAILABLE DATASETS
${datasetContext}

# LANGUAGE-SPECIFIC INSTRUCTIONS
${getLanguageInstructions(language)}

# CODE GENERATION RULES
1. **Data Access**: For Python, use pd.read_csv('dataset1.csv'), pd.read_csv('dataset2.csv'), etc.
2. **Code Quality**: Generate production-ready code with proper error handling
3. **Documentation**: Include clear, helpful comments explaining the logic
4. **Best Practices**: Follow language-specific conventions and best practices
5. **Output Format**: Generate ONLY executable code, no markdown or explanations
6. **Data Insights**: When analyzing data, provide meaningful insights through the code
7. **Visualization**: For charts, ensure they are well-labeled and publication-ready
8. **Performance**: Write efficient code that handles data appropriately

# USER REQUEST
${prompt}

Generate the ${language.toUpperCase()} code:`;

    // Generate content with streaming
    const result = await model.generateContentStream(systemPrompt);

    // Create a readable stream
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          let fullText = '';
          
          for await (const chunk of result.stream) {
            const chunkText = chunk.text();
            fullText += chunkText;
            
            // Send chunk to client
            const data = JSON.stringify({ code: chunkText });
            controller.enqueue(encoder.encode(`data: ${data}\n\n`));
          }

          // Clean up the final code
          let cleanedCode = fullText.trim();
          
          // Remove markdown code blocks if present
          const codeBlockRegex = new RegExp(`\`\`\`${language}\\n([\\s\\S]*?)\`\`\``, 'i');
          const match = cleanedCode.match(codeBlockRegex);
          
          if (match) {
            cleanedCode = match[1].trim();
          } else {
            // Try general code block pattern
            const generalCodeRegex = /```[\w]*\n([\s\S]*?)```/;
            const generalMatch = cleanedCode.match(generalCodeRegex);
            if (generalMatch) {
              cleanedCode = generalMatch[1].trim();
            }
          }

          // Send final cleaned code and completion signal
          const finalData = JSON.stringify({ 
            code: '', // Empty since we already sent the content
            finalCode: cleanedCode,
            done: true 
          });
          controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
          
          controller.close();
        } catch (error) {
          console.error('Streaming error:', error);
          const errorData = JSON.stringify({ 
            error: 'Failed to generate code',
            done: true 
          });
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Error in streaming endpoint:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to generate code' }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
