import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Initialize with updated API configuration
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }), 
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const { prompt, context, conversationHistory } = await req.json();

    if (!prompt) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Use the latest Gemini model with streaming
    const model = genAI.getGenerativeModel({ 
      model: "gemini-1.5-flash-002",
      generationConfig: {
        temperature: 0.2,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 4096,
      },
    });

    // Build comprehensive context
    const cellsContext = context.cells?.map((cell: any) => 
      `Cell ${cell.cellNumber} (${cell.language}): ${cell.content.slice(0, 200)}${cell.content.length > 200 ? '...' : ''}`
    ).join('\n') || 'No cells available';

    const datasetsContext = context.datasets?.map((ds: any) => 
      `Dataset "${ds.name}": ${ds.columns?.join(', ')} (${ds.rowCount} rows, ${ds.fileType})`
    ).join('\n') || 'No datasets available';

    const conversationContext = conversationHistory?.map((msg: any) => 
      `${msg.type}: ${msg.content}`
    ).join('\n') || '';

    const systemPrompt = `You are an expert data analyst and AI assistant, similar to Databricks AI Assistant. You help users analyze data, write code, and create visualizations.

# CURRENT NOTEBOOK CONTEXT
## Cells (${context.totalCells} total):
${cellsContext}

## Available Datasets (${context.totalDatasets} total):
${datasetsContext}

## Selected Datasets:
${context.selectedDatasets?.join(', ') || 'None selected'}

# CONVERSATION HISTORY
${conversationContext}

# INSTRUCTIONS
- Provide helpful, accurate responses about data analysis
- When generating code, use the actual dataset names available in the context
- For SQL: Use standard SQL syntax that works with the available datasets
- For Python: Use pandas, matplotlib, seaborn for data analysis and visualization
- Always explain your reasoning and provide context
- If generating code, wrap it in proper code blocks and specify the language
- Be concise but thorough in your explanations
- Consider the existing cells and their results when making suggestions

# RESPONSE FORMAT
- Provide a clear explanation first
- If code is needed, include it in a separate code block
- Suggest next steps or related analyses when appropriate

User Question: ${prompt}`;

    // Create a readable stream
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const result = await model.generateContentStream(systemPrompt);
          
          let accumulatedText = '';
          let codeBlock = '';
          let inCodeBlock = false;
          let codeLanguage = '';
          
          for await (const chunk of result.stream) {
            const chunkText = chunk.text();
            accumulatedText += chunkText;
            
            // Parse for code blocks
            const lines = accumulatedText.split('\n');
            let currentText = '';
            
            for (let i = 0; i < lines.length; i++) {
              const line = lines[i];
              
              // Check for code block start
              if (line.trim().startsWith('```') && !inCodeBlock) {
                inCodeBlock = true;
                codeLanguage = line.replace('```', '').trim() || 'sql';
                codeBlock = '';
                continue;
              }
              
              // Check for code block end
              if (line.trim() === '```' && inCodeBlock) {
                inCodeBlock = false;
                // Send the code block
                controller.enqueue(
                  new TextEncoder().encode(`data: ${JSON.stringify({
                    code: codeBlock.trim(),
                    language: codeLanguage
                  })}\n\n`)
                );
                continue;
              }
              
              // Accumulate code or text
              if (inCodeBlock) {
                codeBlock += line + '\n';
              } else {
                currentText += line + '\n';
              }
            }
            
            // Send text content (excluding incomplete lines)
            if (currentText && !inCodeBlock) {
              controller.enqueue(
                new TextEncoder().encode(`data: ${JSON.stringify({
                  content: chunkText
                })}\n\n`)
              );
            }
          }
          
          // Send completion signal
          controller.enqueue(
            new TextEncoder().encode(`data: ${JSON.stringify({
              done: true
            })}\n\n`)
          );
          
          controller.close();
        } catch (error) {
          console.error('Streaming error:', error);
          controller.enqueue(
            new TextEncoder().encode(`data: ${JSON.stringify({
              error: 'Failed to generate response'
            })}\n\n`)
          );
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Copilot API error:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to generate response. Please try again.',
        details: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
