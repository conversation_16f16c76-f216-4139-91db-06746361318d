import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(req: Request) {
  try {
    const { prompt } = await req.json();

    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    // Updated prompt to include explanatory notes
    const systemPrompt = `
      Create a diagram or mindmap based on this prompt: "${prompt}"
      You must return a valid JSON object that includes both the diagram and an explanatory note.
      The diagram should follow this exact structure:
      {
        "nodes": [
          {
            "id": "node-1", 
            "type": "icon",
            "position": { "x": 100, "y": 100 },
            "data": {
              "label": "Example Node",
              "icon": "User" // Use any Lucide icon name based on the context
            }
          },
          {
            "id": "note-1",
            "type": "note",
            "position": { "x": 50, "y": 50 },
            "data": {
              "label": "Explanation",
              "note": "Detailed explanation of the diagram..."
            }
          }
        ],
        "edges": [
          {
            "id": "edge-1",
            "source": "node-1",
            "target": "node-2",
            "type": "smoothstep"
          }
        ]
      }

      Rules:
      1. Position values should be between 0 and 800
      2. Type should be one of: icon, square, circle, triangle, diamond, database, http, note
      3. For icon type nodes, use appropriate Lucide icon names that match the node's purpose. Examples:
         - User/Users for people/roles
         - Database for storage
         - Server for backend services
         - Globe for web/internet
         - Settings for configuration
         - Bell for notifications
         - Mail for email systems
         - Lock for security
         - And any other contextually appropriate Lucide icons
      4. Generate a meaningful diagram with proper connections based on the prompt
      5. Always include at least one note node with a detailed explanation of the diagram
      6. The explanation should describe:
         - The purpose of the diagram
         - Key components and their relationships
         - Any important architectural decisions
         - Best practices implemented
      7. Return ONLY the JSON object, no additional text
    `;

    const result = await model.generateContent(systemPrompt);
    const response = await result.response;
    const text = response.text();
    
    // Clean and validate the response
    let cleanedText = text.trim();
    cleanedText = cleanedText.replace(/```json\n?|\n?```/g, '');
    
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("No valid JSON found in response");
    }

    try {
      const flowData = JSON.parse(jsonMatch[0]);
      
      // Validate the structure
      if (!flowData.nodes || !flowData.edges || !Array.isArray(flowData.nodes) || !Array.isArray(flowData.edges)) {
        throw new Error("Invalid diagram structure");
      }

      // Ensure there's at least one note node
      const hasNoteNode = flowData.nodes.some((node: any) => node.type === 'note');
      if (!hasNoteNode) {
        // Add a default note node if none exists
        flowData.nodes.push({
          id: `note-${Date.now()}`,
          type: 'note',
          position: { x: 50, y: 50 },
          data: {
            label: 'Diagram Explanation',
            note: 'This diagram represents the system architecture based on your requirements.'
          }
        });
      }

      // Process nodes to ensure valid positions and types
      flowData.nodes = flowData.nodes.map((node: any, index: number) => ({
        ...node,
        id: node.id || `node-${index + 1}`,
        position: {
          x: Math.min(Math.max(node.position?.x || index * 150, 0), 800),
          y: Math.min(Math.max(node.position?.y || index * 100, 0), 600)
        },
        type: node.type || 'icon',
        data: {
          ...node.data,
          label: node.data?.label || `Node ${index + 1}`,
          note: node.type === 'note' ? (node.data?.note || 'Diagram explanation') : undefined,
          icon: node.type === 'icon' ? (node.data?.icon || 'Circle') : undefined
        }
      }));

      // Process edges to ensure valid connections
      flowData.edges = flowData.edges.map((edge: any, index: number) => ({
        ...edge,
        id: edge.id || `edge-${index + 1}`,
        type: 'smoothstep'
      }));

      // Validate note content
      flowData.nodes.forEach((node: any) => {
        if (node.type === 'note' && (!node.data.note || node.data.note.length < 50)) {
          node.data.note = `
            Diagram Overview:
            - Purpose: ${prompt}
            - Components: ${flowData.nodes.length - 1} components connected through ${flowData.edges.length} relationships
            - Architecture: The diagram shows the system's main components and their interactions
            - Implementation: Following best practices for system design and architecture
          `;
        }
      });

      return Response.json(flowData);
    } catch (parseError) {
      console.error('Parse error:', parseError, 'Raw text:', cleanedText);
      throw new Error("Failed to parse diagram data");
    }
  } catch (error) {
    console.error('AI Error:', error);
    return Response.json(
      { 
        error: 'Failed to generate diagram',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
} 