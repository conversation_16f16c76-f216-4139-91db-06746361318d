import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user
    const { userId: clerkUserId } = auth();
    if (!clerkUserId) {
      return NextResponse.json({ success: false, error: "Authentication required" }, { status: 401 });
    }

    // Get the note ID from the URL
    const { searchParams } = new URL(request.url);
    const noteId = searchParams.get("noteId");
    
    if (!noteId) {
      return NextResponse.json({ success: false, error: "Missing note ID" }, { status: 400 });
    }

    // Get the user ID from the database
    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 });
    }

    // Find the note
    const note = await prisma.note.findUnique({
      where: { id: noteId },
      select: { id: true, userId: true, title: true, metadata: true }
    });

    if (!note) {
      return NextResponse.json({ success: false, error: "Note not found" }, { status: 404 });
    }

    // If the user is the note owner, grant access
    if (note.userId === dbUser.id) {
      return NextResponse.json({ success: true, isOwner: true });
    }

    // Otherwise, check if the user has collaboration access
    let metadata = {};
    if (note.metadata) {
      try {
        metadata = JSON.parse(note.metadata as string);
      } catch (e) {
        console.error("Failed to parse metadata", e);
      }
    }

    // Check if the user is a collaborator
    // @ts-ignore
    const collaborators = metadata.collaborators || [];
    if (!collaborators.includes(clerkUserId)) {
      // Add the user as a collaborator
      collaborators.push(clerkUserId);
      
      // Update the note metadata
      await prisma.note.update({
        where: { id: noteId },
        data: {
          metadata: JSON.stringify({
            ...metadata,
            collaborators
          }),
        }
      });
    }

    return NextResponse.json({ success: true, isCollaborator: true });
  } catch (error) {
    console.error("Error joining collaboration:", error);
    return NextResponse.json({ success: false, error: "Failed to join collaboration" }, { status: 500 });
  }
} 