import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/db";

export async function GET(
  req: Request,
  { params }: { params: { id: string; version: string } }
) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const versionNumber = parseInt(params.version);
    if (isNaN(versionNumber)) {
      return NextResponse.json(
        { error: "Invalid version number" },
        { status: 400 }
      );
    }

    const dataset = await prisma.dataSet.findUnique({
      where: { id: params.id },
      include: {
        changes: {
          where: { version: versionNumber },
          take: 1
        }
      }
    });

    if (!dataset) {
      return NextResponse.json(
        { error: "Dataset not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: dataset.data,
      headers: dataset.headers
    });
  } catch (error) {
    console.error('Error loading version:', error);
    return NextResponse.json(
      { error: 'Failed to load version' },
      { status: 500 }
    );
  }
} 