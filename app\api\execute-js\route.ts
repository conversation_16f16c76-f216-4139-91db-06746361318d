import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/db";

type DataRow = Record<string, any>;
type AnalysisResult = {
  data: DataRow[];
  output?: string;
};

interface DataFrame {
  data: DataRow[];
  select(columns: string[]): AnalysisResult;
  analyze(column: string): AnalysisResult;
  filter(predicate: (row: DataRow) => boolean): AnalysisResult;
  groupBy(column: string, aggregations?: Record<string, 'sum' | 'avg' | 'count'>): AnalysisResult;
  compare(column: string, values: any[]): AnalysisResult;
  head(n?: number): AnalysisResult;
  tail(n?: number): AnalysisResult;
  value_counts(column: string): AnalysisResult;
}

function createDataFrame(data: DataRow[]): DataFrame {
  return {
    data,

    select(columns: string[]) {
      const filtered = data.map(row => {
        const newRow: DataRow = {};
        columns.forEach(col => {
          if (col in row) newRow[col] = row[col];
        });
        return newRow;
      });
      return { data: filtered };
    },

    analyze(column: string) {
      const values = data.map(row => row[column]).filter(v => v != null);
      const numericValues = values.map(Number).filter(v => !isNaN(v));

      const analysis: DataRow = {
        column,
        total_count: data.length,
        non_null_count: values.length,
        null_count: data.length - values.length,
        unique_values: new Set(values).size,
      };

      if (numericValues.length > 0) {
        const sorted = [...numericValues].sort((a, b) => a - b);
        Object.assign(analysis, {
          min: sorted[0],
          max: sorted[sorted.length - 1],
          mean: sorted.reduce((a, b) => a + b, 0) / sorted.length,
          median: sorted[Math.floor(sorted.length / 2)],
          sum: sorted.reduce((a, b) => a + b, 0)
        });
      }

      return { data: [analysis] };
    },

    filter(predicate: (row: DataRow) => boolean) {
      return { data: data.filter(predicate) };
    },

    groupBy(column: string, aggregations?: Record<string, 'sum' | 'avg' | 'count'>) {
      const groups = new Map<string, DataRow[]>();
      
      // Group the data
      data.forEach(row => {
        const key = String(row[column]);
        if (!groups.has(key)) groups.set(key, []);
        groups.get(key)!.push(row);
      });

      // Process each group
      const result = Array.from(groups.entries()).map(([key, rows]) => {
        const groupResult: DataRow = { [column]: key, count: rows.length };

        if (aggregations) {
          Object.entries(aggregations).forEach(([field, op]) => {
            const values = rows.map(r => Number(r[field])).filter(v => !isNaN(v));
            const aggKey = `${field}_${op}`;
            
            switch (op) {
              case 'sum':
                groupResult[aggKey] = values.reduce((a, b) => a + b, 0);
                break;
              case 'avg':
                groupResult[aggKey] = values.reduce((a, b) => a + b, 0) / values.length;
                break;
              case 'count':
                groupResult[aggKey] = values.length;
                break;
            }
          });
        }

        return groupResult;
      });

      return { data: result };
    },

    compare(column: string, values: any[]) {
      return { data: data.filter(row => values.includes(row[column])) };
    },

    head(n = 5) {
      return { data: data.slice(0, n) };
    },

    tail(n = 5) {
      return { data: data.slice(-n) };
    },

    value_counts(column: string) {
      const counts = new Map<string, number>();
      data.forEach(row => {
        const value = String(row[column]);
        counts.set(value, (counts.get(value) || 0) + 1);
      });

      const result = Array.from(counts.entries()).map(([value, count]) => ({
        value,
        count,
        percentage: (count / data.length * 100).toFixed(2) + '%'
      }));

      return { data: result.sort((a, b) => b.count - a.count) };
    }
  };
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { code, datasetId } = body;

    // Validate required fields
    if (!code || !datasetId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // For now, we'll mock the JS execution response
    // TODO: Replace with actual JS code execution
    const mockResponse = {
      data: [{ result: 'JavaScript execution successful' }],
      output: 'Code executed successfully',
    };

    return NextResponse.json(mockResponse);
  } catch (error) {
    console.error('JS execution error:', error);
    return NextResponse.json(
      { error: 'Failed to execute JavaScript code' },
      { status: 500 }
    );
  }
}
