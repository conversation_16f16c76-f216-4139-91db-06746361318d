// Model mapping
export const TOGETHER_MODELS: Record<string, string> = {
  'llama-3-70b': 'meta-llama/Llama-3-70b-chat-hf',
  'llama-3-8b': 'meta-llama/Llama-3-8b-chat-hf',
  'mixtral-8x7b': 'mistralai/Mixtral-8x7B-Instruct-v0.1',
  'mistral-7b': 'mistralai/Mistral-7B-Instruct-v0.2',
  'qwen-72b': 'Qwen/Qwen-72B-Chat',
  'meta': 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'
};

export const COHERE_MODELS: Record<string, string> = {
  'command-r': 'command-r',
  'command-r-plus': 'command-r-plus'
};

export const GEMINI_MODELS: Record<string, string> = {
  'gemini-1.5-pro': 'gemini-1.5-pro',
  'gemini-1.5-flash': 'gemini-1.5-flash',
  'gemini-1.0-pro': 'gemini-1.0-pro'
};

// Helper function to determine model provider
export function getModelProvider(model: string): 'cohere' | 'together' | 'gemini' {
  if (Object.keys(COHERE_MODELS).includes(model)) {
    return 'cohere';
  }
  if (Object.keys(GEMINI_MODELS).includes(model)) {
    return 'gemini';
  }
  return 'together';
}

// Helper function to get actual model ID
export function getModelId(model: string, provider: 'cohere' | 'together' | 'gemini'): string {
  if (provider === 'cohere') {
    return COHERE_MODELS[model] || 'command-r-plus';
  } else if (provider === 'gemini') {
    return GEMINI_MODELS[model] || 'gemini-1.5-pro';
  } else {
    return TOGETHER_MODELS[model] || 'meta-llama/Llama-3-70b-chat-hf';
  }
}
