import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { BM25Retriever } from '@langchain/community/retrievers/bm25';
import { PineconeStore } from '@langchain/pinecone';
import { ChatCohere } from '@langchain/cohere';
import { ChatTogetherAI } from '@langchain/community/chat_models/togetherai';
import { StringOutputParser } from '@langchain/core/output_parsers';
import {
  ChatPromptTemplate,
  HumanMessagePromptTemplate,
  SystemMessagePromptTemplate,
} from '@langchain/core/prompts';

// Import model mappings from the chat route
import { TOGETHER_MODELS, COHERE_MODELS, getModelProvider, getModelId } from '../chat/models';

// Define interfaces for better type safety
interface RequestBody {
  messages: Array<{ role: string; content: string }>;
  datasetId?: string;
  pdfId?: string;
  model?: string;
  namespace?: string;
  useNamespaceOnly?: boolean;
  maxIterations?: number;
}

interface DocumentMetadata {
  source: string;
  datasetId?: string;
  pdfId?: string;
  [key: string]: any;
}

interface Document {
  pageContent: string;
  metadata: DocumentMetadata;
}

interface IterationResult {
  iteration: number;
  query: string;
  docsRetrieved: number;
  subQuestions: string[];
}

interface DeepResearchResponse {
  content: string;
  model: string;
  provider: string;
  sourceDocuments: Array<{
    content: string;
    metadata: DocumentMetadata;
    index: number;
  }>;
  deepResearch: {
    iterations: IterationResult[];
    totalIterations: number;
    subQuestions: string[];
  };
}

// Initialize Pinecone client
const getPineconeClient = (): Pinecone => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings
const getEmbeddings = (): CohereEmbeddings => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0',
    inputType: 'search_query',
    // @ts-ignore - truncate is a valid parameter but not in the type definitions
    truncate: 'END',
    embeddingFormat: 'float'
  });
};

// Get LLM based on selected model
const getLLM = (model: string): ChatCohere | ChatTogetherAI => {
  const provider = getModelProvider(model);
  const modelId = getModelId(model, provider);

  if (provider === 'cohere') {
    const apiKey = process.env.COHERE_API_KEY;
    if (!apiKey) {
      throw new Error('COHERE_API_KEY is not defined in environment variables');
    }

    return new ChatCohere({
      apiKey,
      model: modelId,
      temperature: 0.3,
      // @ts-ignore - maxTokens is a valid parameter but not in the type definitions
      maxTokens: 2000,
    });
  } else {
    const apiKey = process.env.TOGETHER_API_KEY;
    if (!apiKey) {
      throw new Error('TOGETHER_API_KEY is not defined in environment variables');
    }

    return new ChatTogetherAI({
      apiKey,
      modelName: modelId,
      temperature: 0.3,
      maxTokens: 2000,
    });
  }
};

// Deep Research implementation
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body: RequestBody = await req.json();
    const {
      messages,
      datasetId,
      pdfId,
      model = 'command-r-plus',
      namespace = 'adeloop',
      useNamespaceOnly = false,
      maxIterations = 3 // Default to 3 iterations for deep research
    } = body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Messages are required' }, { status: 400 });
    }

    // Get the last user message
    const lastUserMessage = messages[messages.length - 1];
    if (lastUserMessage.role !== 'user') {
      return NextResponse.json({ error: 'Last message must be from user' }, { status: 400 });
    }

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = process.env.PINECONE_INDEX || 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Create vector store
    const vectorStore = await PineconeStore.fromExistingIndex(embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
    });

    // Get LLM
    const llm = getLLM(model);

    // Initialize variables for deep research
    let currentQuery = lastUserMessage.content;
    let accumulatedContext = '';
    let allRetrievedDocs: Document[] = [];
    let subQuestions: string[] = [];
    let iterationResults: Array<{
      iteration: number;
      query: string;
      docsRetrieved: number;
      subQuestions: string[];
    }> = [];
    
    // Expand the original query to improve retrieval
    const expandQueryPrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(
        `You are a query expansion assistant. Your job is to take a user's query and expand it into multiple alternative queries that capture different aspects and phrasings of the original question. This helps improve retrieval from vector databases.
        
        For the given query, generate 3 alternative phrasings or expansions that:
        1. Use different synonyms for key terms
        2. Rephrase the question structure
        3. Add potential context or implied information
        
        Format your response as a numbered list with each query on a new line, starting with 1.
        Do NOT include any explanations, just the expanded queries.`
      ),
      HumanMessagePromptTemplate.fromTemplate('{query}')
    ]);
    
    // Generate expanded queries
    const expandedQueriesResponse = await llm.invoke(await expandQueryPrompt.format({
      query: currentQuery
    }));
    
    // Parse expanded queries
    const expandedQueriesText = typeof expandedQueriesResponse.content === 'string' 
      ? expandedQueriesResponse.content 
      : JSON.stringify(expandedQueriesResponse.content);
    
    // Extract queries (one per line, removing numbers and any other formatting)
    const expandedQueries = expandedQueriesText
      .split('\n')
      .map(line => line.replace(/^\d+\.\s*/, '').trim())
      .filter(line => line.length > 10);
    
    console.log('Expanded queries:', expandedQueries);

    // Deep Research: Perform multiple iterations of retrieval and reasoning
    for (let iteration = 0; iteration < maxIterations; iteration++) {
      console.log(`Deep Research Iteration ${iteration + 1}/${maxIterations} - Query: ${currentQuery}`);

      // Create filter based on dataset or PDF
      let metadataFilter: Record<string, any> | undefined = undefined;
      if (!useNamespaceOnly) {
        if (datasetId) {
          metadataFilter = { source: 'dataset', datasetId: datasetId };
        } else if (pdfId) {
          metadataFilter = { source: 'pdf', pdfId: pdfId };
        }
      }

      // Create a hybrid search function
      const hybridSearch = async (query: string, k: number = 10): Promise<any[]> => {
        // Get vector search results
        const vectorResults = await vectorStore.similaritySearch(
          query,
          k,
          metadataFilter
        );
        
        // Create a simple BM25 retriever from existing documents
        // This helps with exact keyword matching
        const bm25Retriever = new BM25Retriever({
          docs: allRetrievedDocs.length > 0 ? allRetrievedDocs : vectorResults,
          k: k,
        });
        
        // Get keyword search results
        const keywordResults = await bm25Retriever.getRelevantDocuments(query);
        
        // Combine and deduplicate results
        const uniqueDocsMap = new Map();
        
        // Add vector results with higher weight
        vectorResults.forEach((doc, i) => {
          const key = `${doc.metadata.source}-${doc.metadata.datasetId || ''}-${doc.pageContent.substring(0, 50)}`;
          uniqueDocsMap.set(key, { 
            doc, 
            score: (vectorResults.length - i) * 1.5 // Weight vector results higher
          });
        });
        
        // Add keyword results
        keywordResults.forEach((doc, i) => {
          const key = `${doc.metadata.source}-${doc.metadata.datasetId || ''}-${doc.pageContent.substring(0, 50)}`;
          if (uniqueDocsMap.has(key)) {
            // Add to existing score if document already exists
            uniqueDocsMap.get(key).score += (keywordResults.length - i);
          } else {
            uniqueDocsMap.set(key, { 
              doc, 
              score: keywordResults.length - i 
            });
          }
        });
        
        // Convert map to array and sort by score
        const scoredDocs = Array.from(uniqueDocsMap.values())
          .sort((a, b) => b.score - a.score)
          .slice(0, k);
        
        // Return just the documents
        return scoredDocs.map(item => item.doc);
      };
      
      // Retrieve documents for the current query and all expanded queries
      const [mainQueryDocs, ...expandedQueryDocs] = await Promise.all([
        hybridSearch(currentQuery, 8),
        ...expandedQueries.slice(0, 2).map(query => hybridSearch(query, 5))
      ]);
      
      // Combine all retrieved documents
      let allQueryDocs = [...mainQueryDocs];
      expandedQueryDocs.forEach(docs => allQueryDocs.push(...docs));
      
      // Deduplicate documents
      const uniqueDocs = new Map();
      allQueryDocs.forEach(doc => {
        const key = `${doc.metadata.source}-${doc.metadata.datasetId || ''}-${doc.pageContent.substring(0, 50)}`;
        if (!uniqueDocs.has(key)) {
          uniqueDocs.set(key, doc);
        }
      });
      
      // Convert back to array
      const dedupedDocs = Array.from(uniqueDocs.values());
      
      // Re-rank documents using relevance to the query
      const reRankPrompt = ChatPromptTemplate.fromMessages([
        SystemMessagePromptTemplate.fromTemplate(
          `You are a document re-ranking assistant. Your job is to score how relevant each document is to the user's query on a scale of 0-10, where 10 is extremely relevant and 0 is completely irrelevant.
          
          Query: {query}
          
          For each document, output ONLY a number from 0-10 representing its relevance score. Do not include any other text.
          Document: {document}`
        )
      ]);
      
      // Re-rank the top documents for efficiency
      const topDocsToReRank = dedupedDocs.slice(0, 15);
      const reRankingScores = await Promise.all(
        topDocsToReRank.map(async (doc) => {
          const scoreResponse = await llm.invoke(await reRankPrompt.format({
            query: currentQuery,
            document: doc.pageContent.substring(0, 500) // Limit document size for efficiency
          }));
          
          // Extract score from response
          const scoreText = typeof scoreResponse.content === 'string' 
            ? scoreResponse.content 
            : JSON.stringify(scoreResponse.content);
          
          // Parse score - extract first number found in the response
          const scoreMatch = scoreText.match(/\d+(\.\d+)?/);
          const score = scoreMatch ? parseFloat(scoreMatch[0]) : 0;
          
          return { doc, score };
        })
      );
      
      // Sort by score and get the top documents
      const reRankedDocs = reRankingScores
        .sort((a, b) => b.score - a.score)
        .slice(0, 10)
        .map(item => item.doc);

      console.log(`Iteration ${iteration + 1}: Retrieved ${reRankedDocs.length} documents for query: ${currentQuery}`);

      // Process documents to ensure they are strings
      const stringifiedDocs = reRankedDocs.map(doc => {
        const docCopy = { ...doc };
        if (typeof docCopy.pageContent !== 'string') {
          docCopy.pageContent = JSON.stringify(docCopy.pageContent);
        }
        return docCopy;
      });

      // Add documents to the accumulated list
      allRetrievedDocs.push(...reRankedDocs);

      // Format documents for context
      const formattedDocsText = reRankedDocs.map((doc, i) => {
        const source = doc.metadata.source === 'dataset' ? 'Dataset' : 'PDF';
        const name = doc.metadata.datasetName || doc.metadata.pdfName || 'Unknown';
        return `[${i + 1}] ${source}: ${name}\n${doc.pageContent}\n\n`;
      }).join('');

      // Add to accumulated context
      accumulatedContext += `\n\n--- Iteration ${iteration + 1} Query: ${currentQuery} ---\n\n${formattedDocsText}`;

      // Add expanded queries to the context
      if (iteration === 0 && expandedQueries.length > 0) {
        accumulatedContext += `\n\nExpanded Queries:\n${expandedQueries.map((q, i) => `[${i+1}] ${q}`).join('\n')}\n\n`;  
      }

      // If this is the last iteration, break out of the loop
      if (iteration === maxIterations - 1) {
        break;
      }

      // Send an interim response with the current iteration count
      // This would be ideal with server-sent events, but we'll use a workaround
      console.log(`Deep Research iteration ${iteration + 1}/${maxIterations} completed`);

      // Generate sub-questions based on retrieved information
      const subQuestionPromptTemplate = ChatPromptTemplate.fromMessages([
        SystemMessagePromptTemplate.fromTemplate(
          `You are a research assistant that helps break down complex questions into specific sub-questions.
          Based on the original question and the information retrieved so far, generate 2-3 specific follow-up questions
          that would help gather more relevant information to answer the original question comprehensively.
          
          Each sub-question should:
          1. Focus on a specific aspect not fully covered in the retrieved information
          2. Be clear and specific
          3. Be answerable from the available data sources
          
          Format your response as a numbered list with each question on a new line, starting with 1.
          Do NOT include any explanations, just the questions.`
        ),
        HumanMessagePromptTemplate.fromTemplate(
          `Original question: {originalQuestion}\n\nRetrieved information so far:\n{retrievedInfo}`
        ),
      ]);

      // Generate sub-questions
      const subQuestionResponse = await llm.invoke(await subQuestionPromptTemplate.format({
        originalQuestion: lastUserMessage.content,
        retrievedInfo: formattedDocsText
      }));
      const subQuestionText = typeof subQuestionResponse.content === 'string'
        ? subQuestionResponse.content
        : JSON.stringify(subQuestionResponse.content);

      // Parse sub-questions (one per line)
      const newSubQuestions = subQuestionText
        .split('\n')
        .map(q => q.trim())
        .filter(q => q && q.length > 10 && (q.endsWith('?') || q.includes('?')));

      // If we couldn't generate valid sub-questions, break out of the loop
      if (newSubQuestions.length === 0) {
        console.log('No valid sub-questions generated, ending deep research');
        break;
      }

      // Add to sub-questions list
      subQuestions = [...subQuestions, ...newSubQuestions];

      // Use the first sub-question as the next query
      currentQuery = newSubQuestions[0];

      // Store iteration results
      iterationResults.push({
        iteration: iteration + 1,
        query: currentQuery,
        docsRetrieved: dedupedDocs.length,
        subQuestions: newSubQuestions
      });

      console.log(`Generated sub-questions for next iteration:`, newSubQuestions);
    }

    // Final synthesis with all accumulated context
    const finalPromptTemplate = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(
        `You are an AI assistant that answers questions based on the provided context.
        Use ONLY the information from the context to answer the question.
        If you don't know the answer based on the context, say "I don't have enough information in the provided data to answer this question."
        Always include relevant details from the context in your answer.
        Be specific and reference the source of information in your answer (e.g., "According to the dataset..." or "As mentioned in the PDF...").

        The context contains information from datasets and/or PDF documents that the user has selected.
        Each piece of context is labeled with its source.

        IMPORTANT INSTRUCTIONS FOR CSV DATA:
        - When working with CSV data, pay special attention to exact values and numbers
        - If asked about specific IDs, employee numbers, or exact values, search carefully through all rows
        - For numerical questions, provide precise answers with calculations when needed
        - If you can't find an exact match for a specific ID or value, say so clearly
        - When analyzing CSV data, consider all relevant columns and rows before answering

        IMPORTANT: You must ONLY use the information in the context below. Do NOT use any prior knowledge.
        If the context doesn't contain relevant information, admit that you don't have enough information.

        Context:
        {context}`
      ),
      HumanMessagePromptTemplate.fromTemplate(lastUserMessage.content),
    ]);

    // Execute LLM with the final prompt
    const llmResponse = await llm.invoke(await finalPromptTemplate.format({
      context: accumulatedContext
    }));

    // Extract the text content
    let response = '';
    if (typeof llmResponse.content === 'string') {
      response = llmResponse.content;
    } else {
      response = JSON.stringify(llmResponse.content);
    }

    // Prepare source documents for the response
    const sourceDocuments = allRetrievedDocs.map((doc, i) => ({
      content: doc.pageContent,
      metadata: doc.metadata,
      index: i + 1
    }));

    // Return the response with deep research metadata
    return NextResponse.json({
      content: response,
      model: model,
      provider: getModelProvider(model),
      sourceDocuments: sourceDocuments,
      deepResearch: {
        iterations: iterationResults,
        totalIterations: iterationResults.length + 1,
        subQuestions: subQuestions
      }
    });
  } catch (error: any) {
    console.error('Error in Deep Research API:', error);
    return NextResponse.json({
      success: false,
      error: `Internal server error: ${error.message}`,
      content: `Error occurred: ${error.message}`,
      provider: '',
      model: '',
      sourceDocuments: [],
      deepResearch: {
        iterations: [],
        totalIterations: 0,
        subQuestions: []
      }
    }, { status: 500 });
  }
}
