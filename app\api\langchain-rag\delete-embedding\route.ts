import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import prisma from '@/lib/db';

// Initialize Pinecone client
const getPineconeClient = () => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request body
    const body = await req.json();
    const { datasetId, pdfId, namespace = 'adeloop' } = body;

    if (!datasetId && !pdfId) {
      return NextResponse.json({ error: 'Either datasetId or pdfId is required' }, { status: 400 });
    }

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = process.env.PINECONE_INDEX || 'adeloop';
    const index = pinecone.index(indexName);

    // Prepare filter based on what we're deleting
    let filter: any = {};
    let entityType: 'dataset' | 'pdf' = 'dataset';
    let entityId: string = '';

    if (datasetId) {
      // Verify the dataset belongs to the user
      const dataset = await prisma.dataSet.findUnique({
        where: { id: datasetId },
        select: { id: true, userId: true }
      });

      if (!dataset) {
        return NextResponse.json({ error: 'Dataset not found' }, { status: 404 });
      }

      if (dataset.userId !== user.id) {
        return NextResponse.json({ error: 'Unauthorized access to dataset' }, { status: 403 });
      }

      filter = { source: 'dataset', datasetId: datasetId };
      entityType = 'dataset';
      entityId = datasetId;
    } else if (pdfId) {
      // Verify the PDF document belongs to the user
      const pdfDocument = await prisma.pDFDocument.findUnique({
        where: { id: pdfId },
        select: { id: true, userId: true }
      });

      if (!pdfDocument) {
        return NextResponse.json({ error: 'PDF document not found' }, { status: 404 });
      }

      if (pdfDocument.userId !== user.id) {
        return NextResponse.json({ error: 'Unauthorized access to PDF document' }, { status: 403 });
      }

      filter = { source: 'pdf', pdfId: pdfId };
      entityType = 'pdf';
      entityId = pdfId;
    }

    // Delete vectors from Pinecone
    try {
      // Use deleteMany with filter to delete all vectors matching the filter
      console.log('Attempting to delete vectors with filter:', filter, 'in namespace:', namespace);

      try {
        // First try the deleteMany method (newer Pinecone SDK)
        await index.deleteMany({
          filter: filter,
          namespace: namespace
        });
      } catch (innerError) {
        console.error('Error with deleteMany, trying alternative method:', innerError);

        // Fallback to older method if available
        // @ts-ignore - Handle potential API differences
        if (typeof index.delete === 'function') {
          // @ts-ignore - Handle potential API differences
          await index.delete({
            filter: filter,
            namespace: namespace
          });
        } else {
          throw new Error('No compatible delete method found in Pinecone SDK');
        }
      }

      console.log(`Deleted vectors with filter:`, filter);

      // Update the database to mark the entity as no longer embedded
      if (entityType === 'dataset') {
        await prisma.dataSet.update({
          where: { id: entityId },
          data: {
            embedding: false,
            embeddingModel: null
          }
        });
      } else if (entityType === 'pdf') {
        await prisma.pDFDocument.update({
          where: { id: entityId },
          data: {
            embedding: false,
            embeddingModel: null,
            vectorId: null
          }
        });
      }

      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${entityType} embeddings from Pinecone`
      });
    } catch (deleteError: any) {
      console.error('Error deleting vectors from Pinecone:', deleteError);
      return NextResponse.json({
        success: false,
        error: `Error deleting vectors: ${deleteError.message}`
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in delete embedding API:', error);
    return NextResponse.json({
      success: false,
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
