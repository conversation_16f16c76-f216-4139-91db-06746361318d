import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { Document } from '@langchain/core/documents';
import { PDFLoader } from "langchain/document_loaders/fs/pdf";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { ChatTogetherAI } from '@langchain/community/chat_models/togetherai';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts';
import prisma from '@/lib/db';

// Define interfaces for better type safety
interface RequestBody {
  pdfId: string;
}

interface PDFDocument {
  id: string;
  fileName: string;
  userId: string;
}

interface PDFChunk {
  pageContent: string;
  metadata: {
    [key: string]: any;
  };
}

interface ProcessedDocument extends Document {
  metadata: {
    pdfId: string;
    pdfName: string;
    fileName: string;
    contentType: string;
    contentPreview: string;
    contentFeatures: string;
    source: string;
    [key: string]: any;
  };
}

interface EmbeddingResponse {
  success: boolean;
  message?: string;
  namespace?: string;
  error?: string;
}

// Initialize Pinecone client
const getPineconeClient = (): Pinecone => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings
const getEmbeddings = (): CohereEmbeddings => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_document', // For document embeddings
    embeddingTypes: ['float'] // Use float format for better precision
  });
};

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get request body
    const body = await req.json();
    const { pdfId } = body;

    if (!pdfId) {
      return NextResponse.json({ success: false, error: 'No pdfId provided' }, { status: 400 });
    }

    // Fetch the PDF document
    const pdfDocument = await prisma.pDFDocument.findUnique({
      where: { id: pdfId },
      select: {
        id: true,
        fileName: true,
        userId: true
      }
    });

    if (!pdfDocument) {
      return NextResponse.json({ success: false, error: 'PDF document not found' }, { status: 404 });
    }

    // Verify the PDF document belongs to the user
    if (pdfDocument.userId !== user.id) {
      return NextResponse.json({ success: false, error: 'Unauthorized access to PDF document' }, { status: 403 });
    }

    // Use the adeloop namespace directly
    const namespace = 'adeloop';

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Get the PDF file from the request
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'PDF file is required' }, { status: 400 });
    }

    // Convert the file to an ArrayBuffer and then to Blob
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const blob = new Blob([buffer], { type: 'application/pdf' });

    // Load the PDF using PDFLoader
    const loader = new PDFLoader(blob);
    const rawDocs = await loader.load();

    // Initialize a model for content analysis
    const llm = new ChatTogetherAI({
      apiKey: process.env.TOGETHER_API_KEY,
      modelName: 'meta-llama/Llama-3-8b-chat-hf',
      temperature: 0.1,
      maxTokens: 500
    });
    
    // Create a more sophisticated text splitter that respects semantic boundaries
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 800,       // Smaller chunks for more precise retrieval
      chunkOverlap: 150,    // Ensure context continuity between chunks
      separators: [         // Custom separators to respect document structure
        "\n\n",            // Double line breaks often indicate section boundaries
        "\n",              // Single line breaks
        ".",               // Sentence boundaries
        "!",               // Exclamation marks
        "?",               // Question marks
        ";",               // Semicolons
        ":",               // Colons
        " ",               // Spaces (last resort)
        ""                 // Empty string as final fallback
      ],
    });
    
    // Process raw documents to identify tables and structured content
    const processedDocs: Document[] = [];

    for (const doc of rawDocs) {
      // Check if the content might contain tables or structured data
      const contentAnalysisPrompt = ChatPromptTemplate.fromMessages([
        SystemMessagePromptTemplate.fromTemplate(
          `You are a document structure analyzer. Examine the following text from a PDF document and determine if it contains tables, lists, or other structured data.
          If it does, respond with "STRUCTURED". If it's primarily regular text, respond with "TEXT".
          Provide ONLY one of these two responses without any additional text.`
        ),
      ]);
      
      // Analyze a sample of the content (first 1000 chars) to identify structure
      const contentSample = doc.pageContent.substring(0, 1000);
      const structureAnalysis = await llm.invoke(await contentAnalysisPrompt.format({
        text: contentSample
      }));
      
      const analysisResult = typeof structureAnalysis.content === 'string' 
        ? structureAnalysis.content.trim().toUpperCase()
        : 'TEXT';
      
      // Process differently based on content type
      if (analysisResult.includes('STRUCTURED')) {
        // For structured content, use smaller chunks with less overlap
        const structuredSplitter = new RecursiveCharacterTextSplitter({
          chunkSize: 500,
          chunkOverlap: 50,
        });
        
        const structuredChunks = await structuredSplitter.splitText(doc.pageContent);
        
        // Add each chunk with special metadata for structured content
        structuredChunks.forEach((chunk, idx) => {
          processedDocs.push(new Document({
            pageContent: chunk,
            metadata: {
              ...doc.metadata,
              contentType: 'structured',
              chunkIndex: idx,
              totalChunks: structuredChunks.length,
              isTable: analysisResult.includes('TABLE')
            }
          }));
        });
      } else {
        // For regular text, use standard chunking
        const textChunks = await textSplitter.splitText(doc.pageContent);
        
        textChunks.forEach((chunk, idx) => {
          processedDocs.push(new Document({
            pageContent: chunk,
            metadata: {
              ...doc.metadata,
              contentType: 'text',
              chunkIndex: idx,
              totalChunks: textChunks.length
            }
          }));
        });
      }
    }
    
    // Use the processed documents instead of standard chunking
    const docs = processedDocs;

    // Add enhanced metadata to each document
    const documents = docs.map((doc, index) => {
      // Extract potential key information from the content
      const hasNumbers = /\d+/.test(doc.pageContent);
      const hasNames = /[A-Z][a-z]+ [A-Z][a-z]+/.test(doc.pageContent);
      const hasDates = /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/.test(doc.pageContent) || 
                     /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b/.test(doc.pageContent);
      
      // Create a content summary for better retrieval
      const contentPreview = doc.pageContent.substring(0, 100).replace(/\n/g, ' ');
      
      return new Document({
        pageContent: doc.pageContent,
        metadata: {
          ...doc.metadata,
          pdfId: pdfDocument.id,
          pdfName: pdfDocument.fileName,
          fileName: pdfDocument.fileName,
          chunkIndex: index,
          source: 'pdf',
          contentPreview,
          contentFeatures: JSON.stringify({
            hasNumbers,
            hasNames,
            hasDates,
            length: doc.pageContent.length
          })
        }
      });
    });

    // Log the documents being embedded for debugging
    console.log(`Embedding ${documents.length} chunks from PDF "${pdfDocument.fileName}" with metadata:`,
      documents.slice(0, 2).map(doc => doc.metadata)
    );

    // Create vector store with enhanced metadata
    await PineconeStore.fromDocuments(documents, embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
      // Add additional metadata to improve filtering capabilities
      filter: {
        documentType: 'pdf',
        fileName: pdfDocument.fileName,
        embeddingDate: new Date().toISOString(),
        totalChunks: documents.length,
        embeddingModel: 'cohere-embed-english-v3.0'
      }
    });

    // Log success message
    console.log(`Successfully embedded ${documents.length} chunks from PDF "${pdfDocument.fileName}" into Pinecone namespace "${namespace}"`);

    // Update PDF document in database to mark as embedded
    await prisma.pDFDocument.update({
      where: { id: pdfId },
      data: {
        embedding: true,
        embeddingModel: 'cohere-embed-english-v3.0',
        vectorId: namespace
      }
    });

    return NextResponse.json({
      success: true,
      message: `Embedded ${documents.length} chunks from PDF "${pdfDocument.fileName}" into Pinecone`,
      namespace
    });
  } catch (error: any) {
    console.error('Error in PDF embedding API:', error);
    return NextResponse.json({
      success: false,
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
