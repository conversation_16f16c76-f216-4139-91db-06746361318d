import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { CohereEmbeddings } from '@langchain/cohere';
import { PineconeStore } from '@langchain/pinecone';
import { Document } from '@langchain/core/documents';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import prisma from '@/lib/db';

// Define interfaces for better type safety
interface RequestBody {
  datasetId: string;
}

interface DataSet {
  id: string;
  name: string;
  data: any[];
  headers: string[];
  fileType: string;
  userId: string;
}

interface DocumentMetadata {
  datasetId: string;
  datasetName: string;
  rowIndex: number;
  source: string;
  [key: string]: any;
}

interface EmbeddingResponse {
  success: boolean;
  message?: string;
  namespace?: string;
  error?: string;
}

// Initialize Pinecone client
const getPineconeClient = (): Pinecone => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

// Initialize Cohere embeddings with Cohere's latest model
const getEmbeddings = (): CohereEmbeddings => {
  const apiKey = process.env.COHERE_API_KEY;

  if (!apiKey) {
    throw new Error('COHERE_API_KEY is not defined in environment variables');
  }

  return new CohereEmbeddings({
    apiKey,
    model: 'embed-english-v3.0', // Using Cohere's latest embedding model
    inputType: 'search_document', // For document embeddings
    embeddingTypes: ['float'] // Use float format for better precision
  });
};

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({ success: false, error: 'Failed to embed dataset: User not found' }, { status: 404 });
    }

    // Get request body
    const body = await req.json();
    const { datasetId } = body;

    if (!datasetId) {
      return NextResponse.json({ success: false, error: 'Dataset ID is required' }, { status: 400 });
    }

    // Fetch the dataset
    const dataset = await prisma.dataSet.findUnique({
      where: { id: datasetId },
      select: {
        id: true,
        name: true,
        data: true,
        headers: true,
        fileType: true,
        userId: true
      }
    });

    if (!dataset) {
      return NextResponse.json({ success: false, error: 'Dataset not found' }, { status: 404 });
    }

    // Verify the dataset belongs to the user
    if (dataset.userId !== user.id) {
      return NextResponse.json({ success: false, error: 'Unauthorized access to dataset' }, { status: 403 });
    }

    // Use the adeloop namespace directly
    const namespace = 'adeloop';

    // Initialize Pinecone client
    const pinecone = getPineconeClient();
    const indexName = 'adeloop';
    const index = pinecone.index(indexName);

    // Initialize embeddings
    const embeddings = getEmbeddings();

    // Convert dataset to documents for embedding
    const documents: Document[] = [];
    
    // Create a text splitter for chunking large text
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });

    // Handle array data (typical for CSV/Excel datasets)
    if (Array.isArray(dataset.data)) {
      // Extract all column names for metadata
      const allHeaders = dataset.headers || [];
      
      // Identify potential key columns (IDs, unique identifiers, etc.)
      const potentialKeyColumns = allHeaders.filter(header => {
        const headerLower = header.toLowerCase();
        return headerLower.includes('id') || 
               headerLower.includes('key') || 
               headerLower.includes('code') || 
               headerLower.includes('number') ||
               headerLower.includes('name');
      });
      
      // Process each row with enhanced metadata
      dataset.data.forEach(async (row: any, rowIndex: number) => {
        // Extract key values for better retrieval
        const keyValues: Record<string, any> = {};
        potentialKeyColumns.forEach(keyCol => {
          if (row[keyCol] !== undefined && row[keyCol] !== null) {
            keyValues[keyCol] = row[keyCol];
          }
        });
        
        // Create a structured content format that's easier to search
        const rowContent = dataset.headers
          .map(header => `${header}: ${row[header]}`)
          .join('\n');
        
        // Create a searchable title that includes key values
        const keyValuesString = Object.entries(keyValues)
          .map(([key, value]) => `${key}=${value}`)
          .join(', ');
        
        const rowTitle = keyValuesString ? 
          `Row ${rowIndex + 1} (${keyValuesString})` : 
          `Row ${rowIndex + 1}`;
        
        // Split large rows into chunks if needed
        if (rowContent.length > 1500) {
          // For large rows, split into chunks with overlapping content
          const chunks = await textSplitter.splitText(rowContent);
          
          chunks.forEach((chunk: any, chunkIndex: any) => {
            documents.push(
              new Document({
                pageContent: chunk,
                metadata: {
                  datasetId: dataset.id,
                  datasetName: dataset.name,
                  rowIndex,
                  chunkIndex,
                  totalChunks: chunks.length,
                  rowTitle,
                  keyValues: JSON.stringify(keyValues),
                  source: 'dataset',
                  contentType: 'row_chunk'
                }
              })
            );
          });
        } else {
          // For smaller rows, keep as a single document
          documents.push(
            new Document({
              pageContent: rowContent,
              metadata: {
                datasetId: dataset.id,
                datasetName: dataset.name,
                rowIndex,
                rowTitle,
                keyValues: JSON.stringify(keyValues),
                source: 'dataset',
                contentType: 'row_complete'
              }
            })
          );
        }
      });
    }

    // Log the documents being embedded for debugging
    console.log(`Embedding ${documents.length} documents from dataset "${dataset.name}" with metadata:`,
      documents.slice(0, 2).map(doc => doc.metadata)
    );

    // Create vector store with improved metadata handling
    await PineconeStore.fromDocuments(documents, embeddings, {
      pineconeIndex: index,
      namespace,
      textKey: 'text',
      // Add additional metadata to improve filtering capabilities
      filter: {
        datasetType: dataset.fileType || 'unknown',
        embeddingDate: new Date().toISOString(),
        totalRows: Array.isArray(dataset.data) ? dataset.data.length : 0,
        totalDocuments: documents.length
      }
    });

    // Log success message
    console.log(`Successfully embedded ${documents.length} documents from dataset "${dataset.name}" into Pinecone namespace "${namespace}"`);

    // Update dataset in database to mark as embedded
    await prisma.dataSet.update({
      where: { id: datasetId },
      data: {
        embedding: true,
        embeddingModel: 'cohere-embed-english-v3.0' // Updated to match the actual model being used
      }
    });

    return NextResponse.json({ success: true, message: `Embedded ${dataset.name} with ${documents.length} documents`, namespace });
  } catch (error: any) {
    console.error('Error in embedding API:', error);
    return NextResponse.json({ success: false, error: `Internal server error: ${error.message}` }, { status: 500 });
  }
}




