import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Pinecone } from '@pinecone-database/pinecone';

// Type definition for Pinecone namespace stats
type PineconeStats = {
  namespaces?: Record<string, { vectorCount: number }>;
};

// Initialize Pinecone client
const getPineconeClient = (): Pinecone => {
  const apiKey = process.env.PINECONE_API_KEY;

  if (!apiKey) {
    throw new Error('PINECONE_API_KEY is not defined in environment variables');
  }

  return new Pinecone({
    apiKey,
  });
};

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use the adeloop namespace directly
    const namespace = 'adeloop';

    // Initialize Pinecone client
    const pinecone = getPineconeClient();

    // Get the index
    const indexName = 'adeloop';
    const index = pinecone.index(indexName);

    try {
      // Check if the index exists and get stats
      const stats = await index.describeIndexStats();

      // Get namespace stats if available
      const namespaceStats = stats.namespaces?.[namespace];
      const recordCount = namespaceStats?.recordCount || 0;

      return NextResponse.json({
        success: true,
        isInitialized: true,
        recordCount,
        namespace,
        indexName
      });
    } catch (error) {
      console.error('Error checking Pinecone index:', error);
      return NextResponse.json({
        success: true,
        isInitialized: false,
        recordCount: 0,
        namespace,
        error: 'Failed to connect to Pinecone index'
      });
    }
  } catch (error: any) {
    console.error('Error in Pinecone API:', error);
    return NextResponse.json({
      success: false,
      error: `Internal server error: ${error.message}`
    }, { status: 500 });
  }
}
