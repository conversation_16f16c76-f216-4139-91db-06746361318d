import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { auth } from '@clerk/nextjs/server';

// Define the directory where PDF files are stored
const PDF_DIRECTORY = path.join(process.cwd(), 'uploads', 'pdfs');

export async function GET(
  request: NextRequest,
  { params }: { params: { fileName: string } }
) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the file name from the URL params
    const fileName = decodeURIComponent(params.fileName);
    
    // Construct the file path
    const filePath = path.join(PDF_DIRECTORY, fileName);
    
    // Check if the file exists
    try {
      await fs.access(filePath);
    } catch (error) {
      console.error(`File not found: ${filePath}`, error);
      return new NextResponse('PDF file not found', { status: 404 });
    }
    
    // Read the file
    const fileBuffer = await fs.readFile(filePath);
    
    // Return the file with appropriate headers
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${fileName}"`,
        'Cache-Control': 'public, max-age=3600',
      },
    });
  } catch (error) {
    console.error('Error serving PDF file:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
