import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import prisma from '@/lib/db';

// Route Segment Config
export const dynamic = 'force-dynamic'; // Disable static rendering
export const maxDuration = 30; // Set maximum duration for the route (in seconds)

// GET: Fetch all PDF documents for the current user
export async function GET(req: NextRequest) {
  try {
    const { userId: clerkId } = auth();

    if (!clerkId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const documents = await prisma.pDFDocument.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        fileName: true,
        fileSize: true,
        createdAt: true,
        embedding: true,
        embeddingModel: true,
        vectorId: true
      }
    });

    // Transform documents to include proper embedding status
    const transformedDocuments = documents.map(doc => ({
      id: doc.id,
      name: doc.fileName, // Map fileName to name for consistency
      fileName: doc.fileName,
      fileSize: doc.fileSize,
      createdAt: doc.createdAt,
      isEmbedded: doc.embedding || false, // Map embedding to isEmbedded
      embeddingModel: doc.embeddingModel,
      vectorId: doc.vectorId,
      // Calculate estimated page count (rough estimate: 1 page per 2KB)
      pageCount: doc.fileSize ? Math.ceil(doc.fileSize / 2048) : undefined
    }));

    return NextResponse.json({
      success: true,
      documents: transformedDocuments
    });
  } catch (error) {
    console.error('Error fetching PDF documents:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch PDF documents' },
      { status: 500 }
    );
  }
}

// POST: Upload a new PDF document
export async function POST(req: NextRequest) {
  try {
    const { userId: clerkId } = auth();

    if (!clerkId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the database user ID
    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Handle form data request
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    const fileName = file.name;
    const fileSize = file.size;

    // Check if a document with the same name already exists
    const existingDocument = await prisma.pDFDocument.findFirst({
      where: {
        fileName: fileName,
        userId: user.id
      }
    });

    if (existingDocument) {
      return NextResponse.json(
        { error: 'A document with this name already exists' },
        { status: 409 }
      );
    }

    // Save the PDF document without processing the content
    const savedDocument = await prisma.pDFDocument.create({
      data: {
        fileName: fileName,
        fileSize: fileSize,
        userId: user.id,
      }
    });

    return NextResponse.json({
      success: true,
      document: savedDocument,
      metadata: {
        filename: fileName,
        fileSize: fileSize,
      }
    });
  } catch (error) {
    console.error('Error saving PDF:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save PDF'
      },
      { status: 500 }
    );
  }
}

// DELETE: Delete a PDF document
export async function DELETE(req: NextRequest) {
  try {
    const { userId: clerkId } = auth();

    if (!clerkId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(req.url);
    const fileName = searchParams.get('fileName');
    const id = searchParams.get('id');

    if (!fileName && !id) {
      return NextResponse.json(
        { error: 'Either fileName or id must be provided' },
        { status: 400 }
      );
    }

    const whereClause = id
      ? { id, userId: user.id }
      : { fileName: fileName as string, userId: user.id };

    await prisma.pDFDocument.deleteMany({
      where: whereClause as any,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting PDF document:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete PDF document' },
      { status: 500 }
    );
  }
}
