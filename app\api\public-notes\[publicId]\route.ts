import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { publicId: string } }
) {
  try {
    const publicId = params.publicId;
    
    const note = await prisma.note.findFirst({
      where: {
        publicId,
        isPublished: true,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      ...note,
      // Ensure content is parsed correctly for the client
      content: typeof note.content === "string" 
        ? JSON.parse(note.content) 
        : note.content,
      author: note.user,
    });
  } catch (error) {
    console.error('Error fetching public note:', error);
    return NextResponse.json(
      { error: 'Failed to fetch note' },
      { status: 500 }
    );
  }
} 