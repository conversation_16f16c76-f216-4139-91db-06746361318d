import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

interface RouteParams {
  params: {
    publicId: string;
  };
}

// GET /api/public/workspaces/[publicId] - Get a public workspace (read-only)
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { publicId } = params;

    if (!publicId) {
      return NextResponse.json(
        { error: 'Public ID is required' },
        { status: 400 }
      );
    }

    const workspace = await prisma.workspace.findFirst({
      where: {
        publicId,
        isPublic: true // Only return if workspace is marked as public
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        notebooks: {
          include: {
            cells: {
              orderBy: { order: 'asc' },
              select: {
                id: true,
                cellType: true,
                language: true,
                content: true,
                result: true,
                error: true,
                executionTime: true,
                isSuccess: true,
                selectedDatasetIds: true,
                notes: true,
                order: true,
                createdAt: true,
                updatedAt: true
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        },
        dashboards: {
          include: {
            items: {
              orderBy: [
                { gridRow: 'asc' },
                { gridColumn: 'asc' }
              ]
            }
          },
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json(
        { error: 'Public workspace not found or not accessible' },
        { status: 404 }
      );
    }

    // Remove sensitive information for public access
    const publicWorkspace = {
      ...workspace,
      user: {
        name: workspace.user.name || 'Anonymous User',
        email: workspace.user.email
      }
    };

    return NextResponse.json({
      success: true,
      workspace: publicWorkspace,
      isPublic: true,
      readOnly: true
    });
  } catch (error) {
    console.error('Error fetching public workspace:', error);
    return NextResponse.json(
      { error: 'Failed to fetch public workspace' },
      { status: 500 }
    );
  }
}

// POST /api/public/workspaces/[publicId]/fork - Fork a public workspace to current user's account
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { publicId } = params;

    if (!publicId) {
      return NextResponse.json(
        { error: 'Public ID is required' },
        { status: 400 }
      );
    }

    // This endpoint would require authentication to fork
    // For now, return a message about signing in
    return NextResponse.json({
      success: false,
      message: 'Please sign in to fork this workspace to your account',
      requiresAuth: true
    });
  } catch (error) {
    console.error('Error forking public workspace:', error);
    return NextResponse.json(
      { error: 'Failed to fork workspace' },
      { status: 500 }
    );
  }
}
