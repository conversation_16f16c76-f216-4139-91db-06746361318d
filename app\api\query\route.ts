import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/db";
// Direct import of SQL.js won't work in API routes due to WebAssembly
// We'll use a simplified SQL parser for simple queries
// For complex queries, we'll rely on the client-side SQL.js

// Import the server-specific alasql utilities
import { setupServerTable, executeServerQuery, executeCalculation } from "@/lib/server-alasql";

// Import alasql only on the server-side
// This is safe because this code only runs on the server
import alasql from 'alasql';

function parseQuery(query: string, data: any[]) {
  if (!query) {
    throw new Error("Query is undefined");
  }
  
  const normalizedQuery = query.toLowerCase().trim();
  console.log('Parsing query:', normalizedQuery);

  // Check if it's a calculation query (no FROM clause)
  if (!normalizedQuery.includes('from')) {
    return executeSqlCalculator(query);
  }

  try {
    let result = [...data];

    const parts = {
      select: /select\s+(.*?)\s+from/i,
      where: /where\s+(.*?)(?:\s+(?:order by|group by|limit|$))/i,
      orderBy: /order by\s+(.*?)(?:\s+(?:limit|$))/i,
      groupBy: /group by\s+(.*?)(?:\s+(?:having|order by|limit|$))/i,
      limit: /limit\s+(\d+)(?:\s*$)/i
    };

    // Handle SELECT
    const selectMatch = normalizedQuery.match(parts.select);
    if (selectMatch) {
      const fields = selectMatch[1].split(',')
        .map(f => f.trim())
        .map(f => f.toLowerCase() === '*' ? '*' : f);
        
      if (!fields.includes('*')) {
        result = result.map(row => {
          const newRow: any = {};
          fields.forEach(field => {
            const cleanField = field.split(' as ')[0].trim();
            if (cleanField.includes('+') || cleanField.includes('-') || 
                cleanField.includes('*') || cleanField.includes('/')) {
              try {
                const calcValue = evaluateExpression(cleanField, row);
                const fieldName = field.includes(' as ') ? 
                  field.split(' as ')[1].trim() : cleanField;
                newRow[fieldName] = calcValue;
              } catch (e) {
                newRow[field] = null;
              }
            } else {
              const fieldName = field.includes(' as ') ? 
                field.split(' as ')[1].trim() : field;
              newRow[fieldName] = row[cleanField];
            }
          });
          return newRow;
        });
      }
    }

    // Handle WHERE
    const whereMatch = normalizedQuery.match(parts.where);
    if (whereMatch) {
      const condition = whereMatch[1]
        .replace(/=(?!=)/g, '===')
        .replace(/!==/g, '!==')
        .replace(/\blike\b/gi, 'includes')
        .replace(/\bin\b/gi, 'includes')
        .replace(/\bnot in\b/gi, '!includes')
        .replace(/(\w+)\s*(===|!==|>|<|>=|<=)\s*(['"])(.*?)\3/g, 
          (_, col, op, q, val) => `(row["${col}"] !== undefined && row["${col}"] ${op} ${q}${val}${q})`)
        .replace(/(\w+)\s*(===|!==|>|<|>=|<=)\s*(\d+\.?\d*)/g,
          (_, col, op, val) => `(row["${col}"] !== undefined && row["${col}"] ${op} ${val})`);

      result = result.filter(row => {
        try {
          return new Function('row', `return ${condition}`)(row);
        } catch (e) {
          console.error('Error in WHERE clause:', e, 'for row:', row);
          return false;
        }
      });
    }

    // Handle GROUP BY
    const groupMatch = normalizedQuery.match(parts.groupBy);
    if (groupMatch) {
      const groupFields = groupMatch[1].split(',').map(f => f.trim());
      const groups = new Map();
      result.forEach(row => {
        const key = groupFields.map(f => row[f]).join('|');
        if (!groups.has(key)) {
          groups.set(key, []);
        }
        groups.get(key).push(row);
      });
      result = Array.from(groups.values()).map(group => {
        const groupRow: any = {};
        groupFields.forEach(field => {
          groupRow[field] = group[0][field];
        });
        return groupRow;
      });
    }

    // Handle ORDER BY
    const orderMatch = normalizedQuery.match(parts.orderBy);
    if (orderMatch) {
      const orderClauses = orderMatch[1].split(',').map(c => {
        const [field, dir] = c.trim().split(/\s+/);
        return { field: field.trim(), desc: dir?.toLowerCase() === 'desc' };
      });

      result.sort((a, b) => {
        for (const { field, desc } of orderClauses) {
          if (a[field] < b[field]) return desc ? 1 : -1;
          if (a[field] > b[field]) return desc ? -1 : 1;
        }
        return 0;
      });
    }

    // Handle LIMIT
    const limitMatch = normalizedQuery.match(parts.limit);
    if (limitMatch) {
      const limit = parseInt(limitMatch[1], 10);
      result = result.slice(0, limit);
    }

    console.log(`Query executed successfully, returned ${result.length} rows`);
    return result;

  } catch (error: any) {
    console.error('SQL parsing error:', error);
    throw new Error(`SQL Error: ${error.message}`);
  }
}

function evaluateExpression(expr: string, row: any) {
  // Replace column names with their values
  let evaluation = expr;
  Object.keys(row).forEach(key => {
    const regex = new RegExp(`\\b${key}\\b`, 'g');
    evaluation = evaluation.replace(regex, row[key]);
  });
  
  // Evaluate the expression
  try {
    return new Function(`return ${evaluation}`)();
  } catch (e) {
    console.error('Expression evaluation error:', e);
    return null;
  }
}

function executeSqlCalculator(query: string) {
  const selectMatch = query.match(/select\s+(.*?)(?:\s+from|\s*$)/i);
  if (!selectMatch) {
    throw new Error('Invalid calculator query: Must include SELECT clause');
  }
  
  const expressions = selectMatch[1].split(',').map(expr => expr.trim());
  const result: Record<string, number> = {};
  
  expressions.forEach(expr => {
    let fieldName = expr;
    let calculation = expr;
    
    if (expr.toLowerCase().includes(' as ')) {
      const parts = expr.split(/\s+as\s+/i);
      calculation = parts[0].trim();
      fieldName = parts[1].trim();
    }
    
    const jsExpr = calculation
      .replace(/power\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)/gi, 'Math.pow($1, $2)')
      .replace(/sqrt\s*\(\s*([^)]+)\s*\)/gi, 'Math.sqrt($1)')
      .replace(/round\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)/gi, 'Math.round($1 * Math.pow(10, $2)) / Math.pow(10, $2)')
      .replace(/pi\s*\(\s*\)/gi, 'Math.PI')
      .replace(/sum\s*\(\s*([^)]+)\s*\)/gi, '($1).reduce((a,b) => a+b, 0)')
      .replace(/avg\s*\(\s*([^)]+)\s*\)/gi, '($1).reduce((a,b) => a+b, 0) / ($1).length')
      .replace(/count\s*\(\s*([^)]+)\s*\)/gi, '($1).length');
    
    try {
      const value = new Function(`return ${jsExpr}`)();
      result[fieldName] = value;
    } catch (error) {
      console.error(`Error evaluating expression "${jsExpr}":`, error);
      result[fieldName] = NaN;
    }
  });
  
  return [result];
}

export async function POST(req: Request) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    console.log("Received body:", body);

    const query = body.query || body.code;
    const datasets = body.datasets; // Now we receive multiple datasets
    const startTime = Date.now();

    if (!query) {
      return NextResponse.json({ 
        error: "No query provided" 
      }, { status: 400 });
    }

    if (!datasets || !Array.isArray(datasets) || datasets.length === 0) {
      return NextResponse.json({ 
        error: "No datasets provided" 
      }, { status: 400 });
    }

    console.log("Processing query with multiple datasets:", query);
    
    // Check for GraphicWalker or loopchart comments
    const hasVisualizationComment = query.includes("--#graphicwalker") || 
                                  query.includes("-- #graphicwalker") ||
                                  query.includes("--loopchart") ||
                                  query.includes("-- loopchart");

    // Set up tables for all provided datasets
    try {
      // Set up each dataset as a separate table using actual names
      datasets.forEach((dataset, index) => {
        // Create safe table name from dataset name
        let tableName = dataset.name || `dataset${index + 1}`;

        // Clean the table name to be SQL-safe
        tableName = tableName
          .replace(/\.[^/.]+$/, '') // Remove file extension
          .replace(/[^a-zA-Z0-9_]/g, '_') // Replace special chars with underscore
          .toLowerCase();

        // Ensure it doesn't start with a number
        if (/^\d/.test(tableName)) {
          tableName = `dataset_${tableName}`;
        }

        // Ensure it's not empty
        if (!tableName || tableName === '_') {
          tableName = `dataset${index + 1}`;
        }

        setupServerTable(tableName, dataset.data);

        // Also set up numbered fallbacks for compatibility
        setupServerTable(`dataset${index + 1}`, dataset.data);

        // Also set up the first dataset as 'dataset' for backward compatibility
        if (index === 0) {
          setupServerTable('dataset', dataset.data);
        }
      });
      
      // Execute the query
      const result = executeServerQuery(query);
      
      const executionTime = Date.now() - startTime;
      
      return NextResponse.json({
        data: result,
        output: `Query executed successfully with ${datasets.length} datasets. Returned ${result.length} rows.`,
        showGraphicWalker: hasVisualizationComment,
        executionTime: executionTime,
        lastRun: new Date().toISOString()
      });
    } catch (alasqlError: any) {
      console.warn('AlasQL execution failed:', alasqlError);
      
      // Return the error with details
      return NextResponse.json({
        error: `SQL error: ${alasqlError.message}`,
        errorDetails: {
          message: alasqlError.message,
          code: alasqlError.code || 'ALASQL_ERROR',
          stack: process.env.NODE_ENV === 'development' ? alasqlError.stack : undefined,
          serverTrace: process.env.NODE_ENV === 'development' ? 
            `Error executing SQL query with multiple datasets.\nQuery: ${query}\nError: ${alasqlError.message}` : 
            'Error details not available in production mode.'
        },
        executionTime: Date.now() - startTime
      }, { status: 400 });
    }
  } catch (error: any) {
    const errorStartTime = Date.now();
    console.error('Query execution error:', error);
    return NextResponse.json({
      error: error.message || 'Failed to execute query',
      errorDetails: {
        message: error.message,
        code: error.code || 'SERVER_ERROR',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        serverTrace: process.env.NODE_ENV === 'development' ? 
          `Server error occurred during query execution.\nError: ${error.message}` : 
          'Error details not available in production mode.'
      },
      executionTime: 0
    }, { status: 500 });
  }
}