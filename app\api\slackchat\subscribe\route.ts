"use server"

import { NextRequest } from 'next/server';
import { chatEmitter } from '@/lib/chat-emitter';
import { NextResponse } from 'next/server';
import { pusherServer } from '@/lib/pusher';


export async function GET(request: NextRequest) {
  const channelId = request.nextUrl.searchParams.get('channelId');
  
  if (!channelId) {
    return new Response('Channel ID is required', { status: 400 });
  }

  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start: (controller) => {
      // Send initial connection message
      controller.enqueue(encoder.encode('data: {"type":"connected"}\n\n'));
      
      // Keep connection alive with comment
      const keepAlive = setInterval(() => {
        controller.enqueue(encoder.encode(': keep-alive\n\n'));
      }, 15000);

      const messageListener = (event: any) => {
        if (event.channelId === channelId) {
          const data = `data: ${JSON.stringify(event)}\n\n`;
          controller.enqueue(encoder.encode(data));
        }
      };

      chatEmitter.on('message', messageListener);
      chatEmitter.on('messageDeleted', messageListener);

      return () => {
        clearInterval(keepAlive);
        chatEmitter.off('message', messageListener);
        chatEmitter.off('messageDeleted', messageListener);
      };
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
    },
  });
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { type, channelId, data } = body;

    // Trigger the event via Pusher
    await pusherServer.trigger(`chat-${channelId}`, type, {
      type,
      channelId,
      data,
      timestamp: new Date().toISOString(),
      shouldNotify: type === 'message' // Add notification flag for new messages
    });

    // Also emit via chat emitter for SSE connections
    chatEmitter.emit(type, {
      type,
      channelId,
      data,
      timestamp: new Date().toISOString(),
      shouldNotify: type === 'message'
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Subscribe API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to send message' },
      { status: 500 }
    );
  }
}