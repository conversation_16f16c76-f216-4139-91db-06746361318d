import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import prisma from "@/lib/db";

export async function GET(req: Request) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    
    // If no auth header, use Clerk auth
    let clerkId;
    if (!authHeader) {
      const { userId } = auth();
      clerkId = userId;
    } else {
      // Extract the token from the Authorization header
      const token = authHeader.replace('Bearer ', '');
      clerkId = token;
    }
    
    if (!clerkId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the user from the database
    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true, name: true, email: true }
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error getting user:', error);
    return NextResponse.json(
      { error: 'Failed to get user' },
      { status: 500 }
    );
  }
}
