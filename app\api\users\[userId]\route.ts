import { auth } from "@clerk/nextjs/server";
import { clerk<PERSON><PERSON> } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Get authenticated user
    const { userId: currentUserId } = auth();
    if (!currentUserId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get user from Clerk
    const clerkUser = await clerkClient.users.getUser(params.userId);
    if (!clerkUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Return user info
    return NextResponse.json({
      id: clerkUser.id,
      name: clerkUser.firstName && clerkUser.lastName 
        ? `${clerkUser.firstName} ${clerkUser.lastName}`
        : clerkUser.username || clerkUser.emailAddresses[0]?.emailAddress || "Anonymous",
      avatarUrl: clerkUser.imageUrl,
    });
  } catch (error) {
    console.error("Error fetching user:", error);
    return new NextResponse("Error fetching user", { status: 500 });
  }
} 