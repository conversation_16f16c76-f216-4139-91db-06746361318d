import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';

interface RouteParams {
  params: {
    workspaceId: string;
    dashboardId: string;
    itemId: string;
  };
}

// DELETE /api/workspaces/[workspaceId]/dashboards/[dashboardId]/items/[itemId] - Delete a dashboard item
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, dashboardId, itemId } = params;

    // Verify ownership
    const dashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    // Delete the item
    await prisma.dashboardItem.delete({
      where: { id: itemId }
    });

    return NextResponse.json({
      success: true,
      message: 'Dashboard item deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting dashboard item:', error);
    return NextResponse.json(
      { error: 'Failed to delete dashboard item' },
      { status: 500 }
    );
  }
}

// PUT /api/workspaces/[workspaceId]/dashboards/[dashboardId]/items/[itemId] - Update a dashboard item
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, dashboardId, itemId } = params;
    const body = await req.json();
    const {
      type,
      title,
      description,
      data,
      config,
      content,
      gridColumn,
      gridRow,
      width,
      height,
      sourceNotebookId,
      sourceCellId
    } = body;

    // Verify ownership
    const dashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    // Update the item
    const updatedItem = await prisma.dashboardItem.update({
      where: { id: itemId },
      data: {
        ...(type && { type }),
        ...(title !== undefined && { title: title?.trim() || null }),
        ...(description !== undefined && { description: description?.trim() || null }),
        ...(data !== undefined && { data }),
        ...(config !== undefined && { config }),
        ...(content !== undefined && { content: content?.trim() || null }),
        ...(gridColumn !== undefined && { gridColumn }),
        ...(gridRow !== undefined && { gridRow }),
        ...(width !== undefined && { width }),
        ...(height !== undefined && { height }),
        ...(sourceNotebookId !== undefined && { sourceNotebookId }),
        ...(sourceCellId !== undefined && { sourceCellId })
      }
    });

    return NextResponse.json({
      success: true,
      item: updatedItem
    });
  } catch (error) {
    console.error('Error updating dashboard item:', error);
    return NextResponse.json(
      { error: 'Failed to update dashboard item' },
      { status: 500 }
    );
  }
}
