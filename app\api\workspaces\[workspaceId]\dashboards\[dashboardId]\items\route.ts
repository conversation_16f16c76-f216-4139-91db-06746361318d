import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';

interface RouteParams {
  params: {
    workspaceId: string;
    dashboardId: string;
  };
}

// GET /api/workspaces/[workspaceId]/dashboards/[dashboardId]/items - Get all items in a dashboard
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, dashboardId } = params;

    // Verify ownership
    const dashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    const items = await prisma.dashboardItem.findMany({
      where: { dashboardId },
      orderBy: [
        { gridRow: 'asc' },
        { gridColumn: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      items
    });
  } catch (error) {
    console.error('Error fetching dashboard items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard items' },
      { status: 500 }
    );
  }
}

// POST /api/workspaces/[workspaceId]/dashboards/[dashboardId]/items - Create a new dashboard item
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, dashboardId } = params;
    const body = await req.json();
    const {
      type,
      title,
      description,
      data,
      config,
      content,
      gridColumn = 1,
      gridRow = 1,
      width = 1,
      height = 1,
      sourceNotebookId,
      sourceCellId
    } = body;

    if (!type) {
      return NextResponse.json(
        { error: 'Item type is required' },
        { status: 400 }
      );
    }

    // Verify ownership
    const dashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    const item = await prisma.dashboardItem.create({
      data: {
        dashboardId,
        type,
        title: title?.trim() || null,
        description: description?.trim() || null,
        data,
        config,
        content: content?.trim() || null,
        gridColumn,
        gridRow,
        width,
        height,
        sourceNotebookId: sourceNotebookId || null,
        sourceCellId: sourceCellId || null
      }
    });

    return NextResponse.json({
      success: true,
      item
    });
  } catch (error) {
    console.error('Error creating dashboard item:', error);
    return NextResponse.json(
      { error: 'Failed to create dashboard item' },
      { status: 500 }
    );
  }
}

// PUT /api/workspaces/[workspaceId]/dashboards/[dashboardId]/items - Bulk update dashboard items (for layout changes)
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, dashboardId } = params;
    const body = await req.json();
    const { items } = body;

    if (!Array.isArray(items)) {
      return NextResponse.json(
        { error: 'Items array is required' },
        { status: 400 }
      );
    }

    // Verify ownership
    const dashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    // Update items in a transaction
    const updatedItems = await prisma.$transaction(
      items.map((item: any) =>
        prisma.dashboardItem.update({
          where: { id: item.id },
          data: {
            gridColumn: item.gridColumn,
            gridRow: item.gridRow,
            width: item.width,
            height: item.height
          }
        })
      )
    );

    return NextResponse.json({
      success: true,
      items: updatedItems
    });
  } catch (error) {
    console.error('Error updating dashboard items:', error);
    return NextResponse.json(
      { error: 'Failed to update dashboard items' },
      { status: 500 }
    );
  }
}
