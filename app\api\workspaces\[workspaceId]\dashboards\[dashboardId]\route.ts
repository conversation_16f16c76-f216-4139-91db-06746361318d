import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';
import { auth } from "@clerk/nextjs/server";

interface RouteParams {
  params: {
    workspaceId: string;
    dashboardId: string;
  };
}

// GET /api/workspaces/[workspaceId]/dashboards/[dashboardId] - Get a specific dashboard
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, dashboardId } = params;

    // Verify ownership and get dashboard
    const dashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      },
      include: {
        items: {
          orderBy: [
            { gridRow: 'asc' },
            { gridColumn: 'asc' }
          ]
        },
        workspace: {
          select: {
            id: true,
            name: true,
            userId: true
          }
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      dashboard
    });
  } catch (error) {
    console.error('Error fetching dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard' },
      { status: 500 }
    );
  }
}

// PUT /api/workspaces/[workspaceId]/dashboards/[dashboardId] - Update dashboard
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { workspaceId, dashboardId } = params;
    const body = await req.json();
    const { name, description, layout, settings } = body;

    // Verify ownership
    const existingDashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId
        }
      }
    });

    if (!existingDashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim() || null;
    if (layout !== undefined) updateData.layout = layout;
    if (settings !== undefined) updateData.settings = settings;

    const updatedDashboard = await prisma.dashboard.update({
      where: { id: dashboardId },
      data: updateData,
      include: {
        items: {
          orderBy: [
            { gridRow: 'asc' },
            { gridColumn: 'asc' }
          ]
        }
      }
    });

    return NextResponse.json({
      success: true,
      dashboard: updatedDashboard
    });
  } catch (error) {
    console.error('Error updating dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to update dashboard' },
      { status: 500 }
    );
  }
}

// DELETE /api/workspaces/[workspaceId]/dashboards/[dashboardId] - Delete dashboard
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { workspaceId, dashboardId } = params;

    // Verify ownership
    const existingDashboard = await prisma.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspaceId,
        workspace: {
          userId
        }
      }
    });

    if (!existingDashboard) {
      return NextResponse.json(
        { error: 'Dashboard not found' },
        { status: 404 }
      );
    }

    // Delete the dashboard (cascade will handle items)
    await prisma.dashboard.delete({
      where: { id: dashboardId }
    });

    return NextResponse.json({
      success: true,
      message: 'Dashboard deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to delete dashboard' },
      { status: 500 }
    );
  }
}
