import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';

interface RouteParams {
  params: {
    workspaceId: string;
  };
}

// GET /api/workspaces/[workspaceId]/dashboards - Get all dashboards in a workspace
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId } = params;

    // Verify workspace ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id
      }
    });

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    const dashboards = await prisma.dashboard.findMany({
      where: { workspaceId },
      include: {
        items: {
          orderBy: [
            { gridRow: 'asc' },
            { gridColumn: 'asc' }
          ]
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    return NextResponse.json({
      success: true,
      dashboards
    });
  } catch (error) {
    console.error('Error fetching dashboards:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboards' },
      { status: 500 }
    );
  }
}

// POST /api/workspaces/[workspaceId]/dashboards - Create a new dashboard
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId } = params;
    const body = await req.json();
    const { name, description, layout, settings } = body;

    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Dashboard name is required' },
        { status: 400 }
      );
    }

    // Verify workspace ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id
      }
    });

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    const dashboard = await prisma.dashboard.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        workspaceId,
        layout: layout || {
          columns: 12,
          rowHeight: 100,
          margin: [10, 10]
        },
        settings: settings || {
          autoRefresh: false,
          refreshInterval: 30000
        }
      },
      include: {
        items: true
      }
    });

    return NextResponse.json({
      success: true,
      dashboard
    });
  } catch (error) {
    console.error('Error creating dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to create dashboard' },
      { status: 500 }
    );
  }
}
