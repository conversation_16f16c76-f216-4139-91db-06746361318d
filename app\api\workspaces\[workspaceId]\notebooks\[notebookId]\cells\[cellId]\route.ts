import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';
import { auth } from '@clerk/nextjs/server';
interface RouteParams {
  params: {
    workspaceId: string;
    notebookId: string;
    cellId: string;
  };
}

// GET /api/workspaces/[workspaceId]/notebooks/[notebookId]/cells/[cellId] - Get a specific cell
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, notebookId, cellId } = params;

    // Verify ownership and get cell
    const cell = await prisma.notebookCell.findFirst({
      where: {
        id: cellId,
        notebookId,
        notebook: {
          workspaceId,
          workspace: {
            userId: user.id
          }
        }
      }
    });

    if (!cell) {
      return NextResponse.json(
        { error: 'Cell not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      cell
    });
  } catch (error) {
    console.error('Error fetching cell:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cell' },
      { status: 500 }
    );
  }
}

// PUT /api/workspaces/[workspaceId]/notebooks/[notebookId]/cells/[cellId] - Update cell
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { workspaceId, notebookId, cellId } = params;
    const body = await req.json();
    const { 
      content, 
      language, 
      cellType, 
      selectedDatasetIds, 
      result, 
      error, 
      errorDetails, 
      executionTime, 
      isSuccess, 
      notes 
    } = body;

    // Verify ownership
    const existingCell = await prisma.notebookCell.findFirst({
      where: {
        id: cellId,
        notebookId,
        notebook: {
          workspaceId,
          workspace: {
            userId
          }
        }
      }
    });

    if (!existingCell) {
      return NextResponse.json(
        { error: 'Cell not found' },
        { status: 404 }
      );
    }

    const updateData: any = {};
    
    if (content !== undefined) updateData.content = content;
    if (language !== undefined) updateData.language = language;
    if (cellType !== undefined) updateData.cellType = cellType;
    if (selectedDatasetIds !== undefined) updateData.selectedDatasetIds = selectedDatasetIds;
    if (result !== undefined) updateData.result = result;
    if (error !== undefined) updateData.error = error;
    if (errorDetails !== undefined) updateData.errorDetails = errorDetails;
    if (executionTime !== undefined) updateData.executionTime = executionTime;
    if (isSuccess !== undefined) updateData.isSuccess = isSuccess;
    if (notes !== undefined) updateData.notes = notes;

    const updatedCell = await prisma.notebookCell.update({
      where: { id: cellId },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      cell: updatedCell
    });
  } catch (error) {
    console.error('Error updating cell:', error);
    return NextResponse.json(
      { error: 'Failed to update cell' },
      { status: 500 }
    );
  }
}

// DELETE /api/workspaces/[workspaceId]/notebooks/[notebookId]/cells/[cellId] - Delete cell
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { workspaceId, notebookId, cellId } = params;

    // Verify ownership
    const existingCell = await prisma.notebookCell.findFirst({
      where: {
        id: cellId,
        notebookId,
        notebook: {
          workspaceId,
          workspace: {
            userId
          }
        }
      }
    });

    if (!existingCell) {
      return NextResponse.json(
        { error: 'Cell not found' },
        { status: 404 }
      );
    }

    // Get the notebook to update cellOrder
    const notebook = await prisma.notebook.findUnique({
      where: { id: notebookId }
    });

    if (notebook) {
      // Remove cell from cellOrder
      const updatedCellOrder = notebook.cellOrder.filter(id => id !== cellId);
      
      await prisma.notebook.update({
        where: { id: notebookId },
        data: { cellOrder: updatedCellOrder }
      });
    }

    // Update order of subsequent cells
    await prisma.notebookCell.updateMany({
      where: {
        notebookId,
        order: {
          gt: existingCell.order
        }
      },
      data: {
        order: {
          decrement: 1
        }
      }
    });

    // Delete the cell
    await prisma.notebookCell.delete({
      where: { id: cellId }
    });

    return NextResponse.json({
      success: true,
      message: 'Cell deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting cell:', error);
    return NextResponse.json(
      { error: 'Failed to delete cell' },
      { status: 500 }
    );
  }
}
