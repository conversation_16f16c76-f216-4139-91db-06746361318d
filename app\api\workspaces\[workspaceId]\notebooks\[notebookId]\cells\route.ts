import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';

interface RouteParams {
  params: {
    workspaceId: string;
    notebookId: string;
  };
}

// GET /api/workspaces/[workspaceId]/notebooks/[notebookId]/cells - Get all cells in a notebook
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, notebookId } = params;

    // Verify ownership
    const notebook = await prisma.notebook.findFirst({
      where: {
        id: notebookId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!notebook) {
      return NextResponse.json(
        { error: 'Notebook not found' },
        { status: 404 }
      );
    }

    const cells = await prisma.notebookCell.findMany({
      where: { notebookId },
      orderBy: { order: 'asc' }
    });

    return NextResponse.json({
      success: true,
      cells
    });
  } catch (error) {
    console.error('Error fetching cells:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cells' },
      { status: 500 }
    );
  }
}

// POST /api/workspaces/[workspaceId]/notebooks/[notebookId]/cells - Create a new cell
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, notebookId } = params;
    const body = await req.json();
    const {
      cellType = 'code',
      language = 'sql',
      content = '',
      afterCellId,
      selectedDatasetIds = [],
      result,
      error: cellError,
      errorDetails,
      executionTime,
      isSuccess = false,
      notes
    } = body;

    // Verify ownership
    const notebook = await prisma.notebook.findFirst({
      where: {
        id: notebookId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      },
      include: {
        cells: {
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!notebook) {
      return NextResponse.json(
        { error: 'Notebook not found' },
        { status: 404 }
      );
    }

    // Determine the order for the new cell
    let newOrder = 0;
    if (afterCellId) {
      const afterCell = notebook.cells.find(cell => cell.id === afterCellId);
      if (afterCell) {
        newOrder = afterCell.order + 1;
        // Update order of subsequent cells
        await prisma.notebookCell.updateMany({
          where: {
            notebookId,
            order: {
              gte: newOrder
            }
          },
          data: {
            order: {
              increment: 1
            }
          }
        });
      }
    } else {
      // Add at the end
      newOrder = notebook.cells.length;
    }

    // Set default content based on cell type and language
    let defaultContent = content;
    if (!content) {
      if (cellType === 'markdown') {
        defaultContent = '# New Markdown Cell\n\nEdit this cell to add your content.';
      } else if (language === 'sql') {
        defaultContent = '-- Select datasets using the database icon, then write your SQL query\n-- Selected datasets are available using their actual names\n-- Example: SELECT * FROM employees LIMIT 5;';
      } else if (language === 'python') {
        defaultContent = '# Selected datasets are available using their actual names\n# Available libraries: pandas as pd, numpy as np, matplotlib.pyplot as plt, seaborn as sns\n\n# Check what datasets are available\nshow_datasets()  # This will show you the exact variable names and filenames\n\n# Display first dataset\nresult = df.head()';
      }
    }

    const newCell = await prisma.notebookCell.create({
      data: {
        notebookId,
        cellType,
        language: cellType === 'markdown' ? 'markdown' : language,
        content: defaultContent,
        selectedDatasetIds,
        result,
        error: cellError,
        errorDetails,
        executionTime,
        isSuccess,
        notes,
        order: newOrder
      }
    });

    // Update notebook's cellOrder array
    const updatedCellOrder = [...notebook.cellOrder];
    if (afterCellId) {
      const afterIndex = updatedCellOrder.indexOf(afterCellId);
      updatedCellOrder.splice(afterIndex + 1, 0, newCell.id);
    } else {
      updatedCellOrder.push(newCell.id);
    }

    await prisma.notebook.update({
      where: { id: notebookId },
      data: { cellOrder: updatedCellOrder }
    });

    return NextResponse.json({
      success: true,
      cell: newCell
    });
  } catch (error) {
    console.error('Error creating cell:', error);
    return NextResponse.json(
      { error: 'Failed to create cell' },
      { status: 500 }
    );
  }
}
