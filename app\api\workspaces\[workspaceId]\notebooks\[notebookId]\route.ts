import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthenticatedUser } from '@/lib/auth-helpers';

interface RouteParams {
  params: {
    workspaceId: string;
    notebookId: string;
  };
}

// GET /api/workspaces/[workspaceId]/notebooks/[notebookId] - Get a specific notebook
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, notebookId } = params;

    // Verify workspace ownership and get notebook
    const notebook = await prisma.notebook.findFirst({
      where: {
        id: notebookId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      },
      include: {
        cells: {
          orderBy: { order: 'asc' }
        },
        workspace: {
          select: {
            id: true,
            name: true,
            userId: true
          }
        }
      }
    });

    if (!notebook) {
      return NextResponse.json(
        { error: 'Notebook not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      notebook
    });
  } catch (error) {
    console.error('Error fetching notebook:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notebook' },
      { status: 500 }
    );
  }
}

// PUT /api/workspaces/[workspaceId]/notebooks/[notebookId] - Update notebook
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, notebookId } = params;
    const body = await req.json();
    const { name, description, cellOrder, settings } = body;

    // Verify ownership
    const existingNotebook = await prisma.notebook.findFirst({
      where: {
        id: notebookId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!existingNotebook) {
      return NextResponse.json(
        { error: 'Notebook not found' },
        { status: 404 }
      );
    }

    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim() || null;
    if (cellOrder !== undefined) updateData.cellOrder = cellOrder;
    if (settings !== undefined) updateData.settings = settings;

    const updatedNotebook = await prisma.notebook.update({
      where: { id: notebookId },
      data: updateData,
      include: {
        cells: {
          orderBy: { order: 'asc' }
        }
      }
    });

    return NextResponse.json({
      success: true,
      notebook: updatedNotebook
    });
  } catch (error) {
    console.error('Error updating notebook:', error);
    return NextResponse.json(
      { error: 'Failed to update notebook' },
      { status: 500 }
    );
  }
}

// DELETE /api/workspaces/[workspaceId]/notebooks/[notebookId] - Delete notebook
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const { workspaceId, notebookId } = params;

    // Verify ownership
    const existingNotebook = await prisma.notebook.findFirst({
      where: {
        id: notebookId,
        workspaceId,
        workspace: {
          userId: user.id
        }
      }
    });

    if (!existingNotebook) {
      return NextResponse.json(
        { error: 'Notebook not found' },
        { status: 404 }
      );
    }

    // Delete the notebook (cascade will handle cells)
    await prisma.notebook.delete({
      where: { id: notebookId }
    });

    return NextResponse.json({
      success: true,
      message: 'Notebook deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting notebook:', error);
    return NextResponse.json(
      { error: 'Failed to delete notebook' },
      { status: 500 }
    );
  }
}
