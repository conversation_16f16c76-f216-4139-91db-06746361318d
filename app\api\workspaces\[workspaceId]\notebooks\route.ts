import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import prisma from '@/lib/db';

interface RouteParams {
  params: {
    workspaceId: string;
  };
}

// GET /api/workspaces/[workspaceId]/notebooks - Get all notebooks in a workspace
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId: clerkUserId } = auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the user record using Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { workspaceId } = params;

    // Verify workspace ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id
      }
    });

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    const notebooks = await prisma.notebook.findMany({
      where: { workspaceId },
      include: {
        cells: {
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    return NextResponse.json({
      success: true,
      notebooks
    });
  } catch (error) {
    console.error('Error fetching notebooks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notebooks' },
      { status: 500 }
    );
  }
}

// POST /api/workspaces/[workspaceId]/notebooks - Create a new notebook
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId: clerkUserId } = auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the user record using Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { workspaceId } = params;
    const body = await req.json();
    const { name, description } = body;

    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Notebook name is required' },
        { status: 400 }
      );
    }

    // Verify workspace ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id
      }
    });

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    const notebook = await prisma.notebook.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        workspaceId,
        cellOrder: [],
        settings: {
          defaultLanguage: 'sql',
          autoExecute: false
        }
      },
      include: {
        cells: {
          orderBy: { order: 'asc' }
        }
      }
    });

    // Create a default cell for the new notebook
    const defaultCell = await prisma.notebookCell.create({
      data: {
        notebookId: notebook.id,
        cellType: 'code',
        language: 'sql',
        content: '-- Select datasets using the database icon, then write your SQL query\n-- Selected datasets are available using their actual names\n-- Example: SELECT * FROM employees LIMIT 5;',
        selectedDatasetIds: [],
        order: 0
      }
    });

    // Update the notebook's cellOrder
    await prisma.notebook.update({
      where: { id: notebook.id },
      data: {
        cellOrder: [defaultCell.id]
      }
    });

    return NextResponse.json({
      success: true,
      notebook: {
        ...notebook,
        cells: [defaultCell]
      }
    });
  } catch (error) {
    console.error('Error creating notebook:', error);
    return NextResponse.json(
      { error: 'Failed to create notebook' },
      { status: 500 }
    );
  }
}
