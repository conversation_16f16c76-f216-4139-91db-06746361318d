import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import prisma from '@/lib/db';


interface RouteParams {
  params: {
    workspaceId: string;
  };
}

// GET /api/workspaces/[workspaceId] - Get a specific workspace with full details
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId: clerkUserId } = auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the user record using Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { workspaceId } = params;

    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id // Ensure user owns this workspace
      },
      include: {
        notebooks: {
          include: {
            cells: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { createdAt: 'asc' }
        },
        dashboards: {
          include: {
            items: {
              orderBy: [
                { gridRow: 'asc' },
                { gridColumn: 'asc' }
              ]
            }
          },
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      workspace
    });
  } catch (error) {
    console.error('Error fetching workspace:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workspace' },
      { status: 500 }
    );
  }
}

// PUT /api/workspaces/[workspaceId] - Update workspace details
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId: clerkUserId } = auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the user record using Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { workspaceId } = params;
    const body = await req.json();
    const { name, description, isPublic, settings } = body;

    // Verify workspace ownership
    const existingWorkspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id
      }
    });

    if (!existingWorkspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim() || null;
    if (isPublic !== undefined) updateData.isPublic = isPublic;
    if (settings !== undefined) updateData.settings = settings;

    const updatedWorkspace = await prisma.workspace.update({
      where: { id: workspaceId },
      data: updateData,
      include: {
        notebooks: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true
          }
        },
        dashboards: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      workspace: updatedWorkspace
    });
  } catch (error) {
    console.error('Error updating workspace:', error);
    return NextResponse.json(
      { error: 'Failed to update workspace' },
      { status: 500 }
    );
  }
}

// DELETE /api/workspaces/[workspaceId] - Delete a workspace
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId: clerkUserId } = auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the user record using Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { workspaceId } = params;

    // Verify workspace ownership
    const existingWorkspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        userId: user.id
      }
    });

    if (!existingWorkspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Delete the workspace (cascade will handle notebooks, dashboards, etc.)
    await prisma.workspace.delete({
      where: { id: workspaceId }
    });

    return NextResponse.json({
      success: true,
      message: 'Workspace deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting workspace:', error);
    return NextResponse.json(
      { error: 'Failed to delete workspace' },
      { status: 500 }
    );
  }
}
