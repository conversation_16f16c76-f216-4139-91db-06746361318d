import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { nanoid } from 'nanoid';
import { getAuthenticatedUser } from '@/lib/auth-helpers';

// GET /api/workspaces - Get all workspaces for the current user
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const workspaces = await prisma.workspace.findMany({
      where: { userId: user.id },
      include: {
        notebooks: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: { cells: true }
            }
          }
        },
        dashboards: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: { items: true }
            }
          }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      workspaces
    });
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workspaces' },
      { status: 500 }
    );
  }
}

// POST /api/workspaces - Create a new workspace
export async function POST(req: NextRequest) {
  try {
    const { user, error } = await getAuthenticatedUser();
    if (error) return error;

    const body = await req.json();
    const { name, description, isPublic = false } = body;

    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Workspace name is required' },
        { status: 400 }
      );
    }

    // Generate a unique public ID if the workspace is public
    const publicId = isPublic ? nanoid(12) : null;

    const workspace = await prisma.workspace.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        isPublic,
        publicId,
        userId: user.id,
        settings: {
          theme: 'system',
          autoSave: true,
          defaultNotebookLanguage: 'sql'
        }
      },
      include: {
        notebooks: true,
        dashboards: true
      }
    });

    // Create a default notebook and dashboard for the new workspace
    const [defaultNotebook, defaultDashboard] = await Promise.all([
      prisma.notebook.create({
        data: {
          name: 'Analysis Notebook',
          description: 'Default notebook for data analysis with SQL and Python',
          workspaceId: workspace.id,
          cellOrder: [],
          settings: {
            defaultLanguage: 'sql',
            autoExecute: false
          }
        }
      }),
      prisma.dashboard.create({
        data: {
          name: 'Analytics Dashboard',
          description: 'Default dashboard for data visualizations and insights',
          workspaceId: workspace.id,
          layout: {
            columns: 12,
            rowHeight: 100,
            margin: [10, 10]
          },
          settings: {
            autoRefresh: false,
            refreshInterval: 30000
          }
        }
      })
    ]);

    return NextResponse.json({
      success: true,
      workspace: {
        ...workspace,
        notebooks: [defaultNotebook],
        dashboards: [defaultDashboard]
      }
    });
  } catch (error) {
    console.error('Error creating workspace:', error);
    return NextResponse.json(
      { error: 'Failed to create workspace' },
      { status: 500 }
    );
  }
}
