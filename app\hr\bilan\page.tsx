"use client";

import { useState } from 'react';
import BilanRe from '@/components/BilanReport/BilanRe';
import { Card } from '@/components/ui/card';
import ViewDataset from '@/components/BilanReport/ViewDataset';
import { Code } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, Sheet<PERSON>rigger, <PERSON>etTitle, SheetHeader, SheetContent } from '@/components/ui/sheet';
import './scrollsheet.css';
import BilanMiniSidebar from '@/components/layout/BilanMiniSidebar';

export default function BilanPage() {
  const [selectedDataset, setSelectedDataset] = useState<any>(null);
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);

  // JSON structure for the required data format
const requiredJsonFormat = {
  matricule: "string",
  nom: "string",
  service: "string",
  sitfam: "string",
  NOMINDIC4: "string",
  sex: "string",
  ville: "string",
  Categorie: "string",
  Salaire: "number",
  mutuel: "string",
  cimr: "string",
  DroitConge: "number",
  Qualification: "string",
  Societe: "string",
  NomService: "string",
  PAYS: "string",
  cnss: "string",
  dentree: "string",
  nenf: "number",
  nat: "string",
  Division: "string",
  tauxcimr: "number",
  Tauxmutuel: "number",
  NOMBANQUE: "string",
  Dan: "string",
  CompteCompt: "string",
  DateRelica: "string",
};

  return (
    <div className="flex min-h-screen">
      <BilanMiniSidebar 
        isSidebarExpanded={isSidebarExpanded}
        setIsSidebarExpanded={setIsSidebarExpanded}
      />

      <div className={`flex-1 transition-margin duration-300 
        ${isSidebarExpanded ? 'ml-40' : 'ml-12'}`}> {/* Reduced margins */}
        <div className="container mx-auto px-1"> {/* Reduced padding */}
          <div className="space-y-1"> {/* Minimal spacing */}
            <ViewDataset 
              onDatasetSelect={setSelectedDataset}
              title="Bilan Social Dashboard"
            />
            
            <div className="flex justify-end -mt-2"> {/* Negative margin to reduce space */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Code className="h-4 w-4" />
                    View Data Format
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[400px] sm:w-[540px]">
                  <SheetHeader>
                    <SheetTitle>Required Data Format</SheetTitle>
                  </SheetHeader>
                  {/* Scrollable Container */}
                  <div className="mt-6 h-[calc(100vh-8rem)] overflow-y-auto">
                    <pre className="text-sm text-muted-foreground">
                      {JSON.stringify(requiredJsonFormat, null, 2)}
                    </pre>
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            {selectedDataset ? (
              <div className="mt-0.5"> {/* Minimal top margin */}
                <BilanRe data={selectedDataset.data} />
              </div>
            ) : (
              <Card className="mt-0.5 p-2 text-center text-muted-foreground text-sm">
                Please select a dataset
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}