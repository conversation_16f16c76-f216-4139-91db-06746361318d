'use client'

import dynamic from 'next/dynamic'
import { Loader2 } from 'lucide-react'
import ClientOnly from './ClientOnly'
import { useAuth } from "@clerk/nextjs"
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

const ChartBuilder = dynamic(
  () => import('@/components/ChartBuilder/ChartBuilder'),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading chart builder...</span>
      </div>
    )
  }
)

export default function ChartBuilderPage() {
  // const { isLoaded, userId } = useAuth();
  // const router = useRouter();

  // useEffect(() => {
  //   if (isLoaded && !userId) {
  //     router.push('/sign-in');
  //   }
  // }, [isLoaded, userId, router]);

  // if (!isLoaded) {
  // {
  //   return (
  //     <div className="flex items-center justify-center h-[60vh]">
  //       <Loader2 className="h-8 w-8 animate-spin text-primary" />
  //       <span className="ml-2">Loading...</span>
  //     </div>
  //   );
  // }

  // if (!userId) {
  //   return null;
  // }

  return (
    <div className="w-full h-screen overflow-hidden">
      <ClientOnly fallback={
        <div className="flex items-center justify-center h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading chart builder...</span>
        </div>
      }>
        <div className="w-full h-full overflow-hidden">
          <ChartBuilder />
        </div>
      </ClientOnly>
    </div>
  );
}

