'use client'

import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@clerk/nextjs'
import { toast } from 'sonner'
import { Loader2, ArrowLeft, Settings, Share2, Plus, Database, BarChart3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'

interface DataWorkspace {
  id: string
  name: string
  description?: string
  isPublic: boolean
  publicId?: string
  settings?: any
  notebooks: Notebook[]
  dashboards: Dashboard[]
  createdAt: string
  updatedAt: string
}

interface Notebook {
  id: string
  name: string
  description?: string
  cells: NotebookCell[]
  cellOrder: string[]
  settings?: any
  createdAt: string
  updatedAt: string
}

interface NotebookCell {
  id: string
  cellType: string
  language?: string
  content: string
  result?: any
  error?: string
  executionTime?: number
  isSuccess: boolean
  selectedDatasetIds: string[]
  notes?: string
  order: number
  createdAt: string
  updatedAt: string
}

interface Dashboard {
  id: string
  name: string
  description?: string
  items: DashboardItem[]
  layout?: any
  settings?: any
  createdAt: string
  updatedAt: string
}

interface DashboardItem {
  id: string
  type: string
  title?: string
  description?: string
  data?: any
  config?: any
  content?: string
  gridColumn: number
  gridRow: number
  width: number
  height: number
  sourceNotebookId?: string
  sourceCellId?: string
  createdAt: string
  updatedAt: string
}

export default function DataWorkspacePage() {
  const params = useParams()
  const router = useRouter()
  const { isLoaded, isSignedIn } = useAuth()
  const [workspace, setWorkspace] = useState<DataWorkspace | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('notebooks')

  const workspaceId = params.workspaceId as string

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in')
      return
    }

    if (isLoaded && isSignedIn && workspaceId) {
      fetchWorkspace()
    }
  }, [isLoaded, isSignedIn, workspaceId])

  const fetchWorkspace = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/workspaces/${workspaceId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Data workspace not found')
          router.push('/hr/chartbuilder')
          return
        }
        throw new Error('Failed to fetch data workspace')
      }

      const data = await response.json()
      if (data.success) {
        setWorkspace(data.workspace)
      } else {
        throw new Error(data.error || 'Failed to fetch data workspace')
      }
    } catch (error) {
      console.error('Error fetching data workspace:', error)
      toast.error('Failed to load data workspace')
      router.push('/hr/chartbuilder')
    } finally {
      setLoading(false)
    }
  }

  const openInChartBuilder = () => {
    router.push(`/hr/chartbuilder?workspace=${workspaceId}`)
  }

  const createNotebook = async () => {
    try {
      const response = await fetch(`/api/workspaces/${workspaceId}/notebooks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `Analysis Notebook ${(workspace?.notebooks.length || 0) + 1}`,
          description: 'New notebook for data analysis'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create notebook')
      }

      const data = await response.json()
      if (data.success) {
        toast.success('Analysis notebook created successfully')
        fetchWorkspace() // Refresh workspace data
      } else {
        throw new Error(data.error || 'Failed to create notebook')
      }
    } catch (error) {
      console.error('Error creating notebook:', error)
      toast.error('Failed to create analysis notebook')
    }
  }

  const createDashboard = async () => {
    try {
      const response = await fetch(`/api/workspaces/${workspaceId}/dashboards`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `Analytics Dashboard ${(workspace?.dashboards.length || 0) + 1}`,
          description: 'New dashboard for data visualizations'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create dashboard')
      }

      const data = await response.json()
      if (data.success) {
        toast.success('Analytics dashboard created successfully')
        fetchWorkspace() // Refresh workspace data
      } else {
        throw new Error(data.error || 'Failed to create dashboard')
      }
    } catch (error) {
      console.error('Error creating dashboard:', error)
      toast.error('Failed to create analytics dashboard')
    }
  }

  if (!isLoaded || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading Data Workspace...</p>
        </div>
      </div>
    )
  }

  if (!workspace) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Database className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-2xl font-bold mb-2">Data Workspace not found</h2>
          <p className="text-muted-foreground mb-4">The data workspace you're looking for doesn't exist or you don't have access to it.</p>
          <Button onClick={() => router.push('/hr/chartbuilder')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to ChartBuilder
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/hr/chartbuilder')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to ChartBuilder
          </Button>
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Database className="h-6 w-6 text-blue-600" />
              <h1 className="text-3xl font-bold">{workspace.name}</h1>
              {workspace.isPublic && (
                <Badge variant="secondary">Public</Badge>
              )}
            </div>
            {workspace.description && (
              <p className="text-muted-foreground">{workspace.description}</p>
            )}
            <p className="text-sm text-muted-foreground mt-1">
              Data Analytics Workspace • Created {new Date(workspace.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={openInChartBuilder} variant="default">
            <BarChart3 className="h-4 w-4 mr-2" />
            Open in ChartBuilder
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="notebooks">
            <Database className="h-4 w-4 mr-2" />
            Analysis Notebooks ({workspace.notebooks.length})
          </TabsTrigger>
          <TabsTrigger value="dashboards">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics Dashboards ({workspace.dashboards.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notebooks" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Analysis Notebooks</h2>
              <p className="text-sm text-muted-foreground">Create and manage your data analysis notebooks with SQL and Python</p>
            </div>
            <Button onClick={createNotebook}>
              <Plus className="h-4 w-4 mr-2" />
              New Analysis Notebook
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workspace.notebooks.map((notebook) => (
              <Card key={notebook.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Database className="h-5 w-5 text-blue-600" />
                    {notebook.name}
                  </CardTitle>
                  {notebook.description && (
                    <CardDescription>{notebook.description}</CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{notebook.cells.length} cells</span>
                    <span>Updated {new Date(notebook.updatedAt).toLocaleDateString()}</span>
                  </div>
                  <div className="mt-2">
                    <Button size="sm" variant="outline" className="w-full">
                      Open in ChartBuilder
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="dashboards" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Analytics Dashboards</h2>
              <p className="text-sm text-muted-foreground">View and manage your data visualization dashboards</p>
            </div>
            <Button onClick={createDashboard}>
              <Plus className="h-4 w-4 mr-2" />
              New Analytics Dashboard
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workspace.dashboards.map((dashboard) => (
              <Card key={dashboard.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                    {dashboard.name}
                  </CardTitle>
                  {dashboard.description && (
                    <CardDescription>{dashboard.description}</CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{dashboard.items.length} items</span>
                    <span>Updated {new Date(dashboard.updatedAt).toLocaleDateString()}</span>
                  </div>
                  <div className="mt-2">
                    <Button size="sm" variant="outline" className="w-full">
                      View Dashboard
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
