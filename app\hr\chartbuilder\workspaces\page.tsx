'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@clerk/nextjs'
import { toast } from 'sonner'
import { 
  Loader2, 
  Plus, 
  Database, 
  BarChart3, 
  Globe, 
  Lock, 
  Calendar,
  Users,
  ArrowRight,
  FolderPlus
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { WorkspaceModal } from '@/components/ChartBuilder/WorkspaceModal'

interface DataWorkspace {
  id: string
  name: string
  description?: string
  isPublic: boolean
  publicId?: string
  notebooks: Array<{
    id: string
    name: string
    _count?: { cells: number }
  }>
  dashboards: Array<{
    id: string
    name: string
    _count?: { items: number }
  }>
  createdAt: string
  updatedAt: string
}

export default function DataWorkspacesPage() {
  const router = useRouter()
  const { isLoaded, isSignedIn } = useAuth()
  const [workspaces, setWorkspaces] = useState<DataWorkspace[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in')
      return
    }

    if (isLoaded && isSignedIn) {
      fetchWorkspaces()
    }
  }, [isLoaded, isSignedIn])

  const fetchWorkspaces = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/workspaces')
      
      if (!response.ok) {
        throw new Error('Failed to fetch data workspaces')
      }

      const data = await response.json()
      if (data.success) {
        setWorkspaces(data.workspaces)
      } else {
        throw new Error(data.error || 'Failed to fetch data workspaces')
      }
    } catch (error) {
      console.error('Error fetching data workspaces:', error)
      toast.error('Failed to load data workspaces')
    } finally {
      setLoading(false)
    }
  }

  const handleWorkspaceCreated = (newWorkspace: DataWorkspace) => {
    setWorkspaces(prev => [newWorkspace, ...prev])
    toast.success('Data workspace created successfully!')
    // Redirect to the new workspace
    router.push(`/hr/chartbuilder?workspace=${newWorkspace.id}`)
  }

  const openWorkspace = (workspaceId: string) => {
    router.push(`/hr/chartbuilder?workspace=${workspaceId}`)
  }

  const viewWorkspaceDetails = (workspaceId: string) => {
    router.push(`/hr/chartbuilder/workspace/${workspaceId}`)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  if (!isLoaded || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading Data Workspaces...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <Database className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold">Data Workspaces</h1>
        </div>
        <p className="text-muted-foreground">
          Organize your data analysis projects with dedicated workspaces for notebooks and dashboards.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Database className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{workspaces.length}</p>
                <p className="text-sm text-muted-foreground">Data Workspaces</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <BarChart3 className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">
                  {workspaces.reduce((acc, ws) => acc + ws.notebooks.length, 0)}
                </p>
                <p className="text-sm text-muted-foreground">Analysis Notebooks</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <BarChart3 className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">
                  {workspaces.reduce((acc, ws) => acc + ws.dashboards.length, 0)}
                </p>
                <p className="text-sm text-muted-foreground">Analytics Dashboards</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workspaces Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Create New Workspace Card */}
        <Card 
          className="border-dashed border-2 hover:border-primary/50 transition-colors cursor-pointer group"
          onClick={() => setShowCreateModal(true)}
        >
          <CardContent className="flex flex-col items-center justify-center p-8 text-center min-h-[280px]">
            <div className="p-4 bg-primary/10 rounded-full mb-4 group-hover:bg-primary/20 transition-colors">
              <FolderPlus className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Create New Data Workspace</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Start a new data analysis project with notebooks and dashboards
            </p>
            <Button variant="outline" className="group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
              <Plus className="h-4 w-4 mr-2" />
              Create Workspace
            </Button>
          </CardContent>
        </Card>

        {/* Existing Workspaces */}
        {workspaces.map((workspace) => (
          <Card key={workspace.id} className="hover:shadow-lg transition-shadow group">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg line-clamp-1">{workspace.name}</CardTitle>
                </div>
                <div className="flex items-center gap-1">
                  {workspace.isPublic ? (
                    <Globe className="h-4 w-4 text-blue-500" />
                  ) : (
                    <Lock className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
              {workspace.description && (
                <CardDescription className="line-clamp-2">
                  {workspace.description}
                </CardDescription>
              )}
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <p className="text-lg font-semibold">{workspace.notebooks.length}</p>
                  <p className="text-xs text-muted-foreground">Notebooks</p>
                </div>
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <p className="text-lg font-semibold">{workspace.dashboards.length}</p>
                  <p className="text-xs text-muted-foreground">Dashboards</p>
                </div>
              </div>

              {/* Metadata */}
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Calendar className="h-3 w-3" />
                  <span>Updated {formatDate(workspace.updatedAt)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-3 w-3" />
                  <span>{workspace.isPublic ? 'Public' : 'Private'}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Button 
                  onClick={() => openWorkspace(workspace.id)}
                  className="flex-1"
                  size="sm"
                >
                  Open in ChartBuilder
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
                <Button 
                  onClick={() => viewWorkspaceDetails(workspace.id)}
                  variant="outline"
                  size="sm"
                >
                  Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {workspaces.length === 0 && (
        <div className="text-center py-12">
          <Database className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-semibold mb-2">No Data Workspaces Yet</h3>
          <p className="text-muted-foreground mb-6">
            Create your first data workspace to start analyzing data with notebooks and dashboards.
          </p>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Data Workspace
          </Button>
        </div>
      )}

      {/* Create Workspace Modal */}
      <WorkspaceModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onWorkspaceCreated={handleWorkspaceCreated}
      />
    </div>
  )
}
