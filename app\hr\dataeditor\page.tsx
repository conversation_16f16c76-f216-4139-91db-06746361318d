import { Suspense } from "react"
import DataEditor from "@/components/Datanalytics/DataEditor"
import { Loader2 } from "lucide-react"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"

// Loading component for Suspense
function DataEditorLoading() {
    return (
        <div className="flex items-center justify-center min-h-screen">
            <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary/80" />
                <p className="text-sm text-muted-foreground">Loading data editor...</p>
            </div>
        </div>
    )
}

async function DataEditorPage() {
    const { userId } = auth();

    if (!userId) {
        redirect('/sign-in');
    }

    return (
        <div className="w-full h-screen overflow-hidden">
            <Suspense fallback={<DataEditorLoading />}>
                <DataEditor />
            </Suspense>
        </div>
    )
}

export default DataEditorPage
