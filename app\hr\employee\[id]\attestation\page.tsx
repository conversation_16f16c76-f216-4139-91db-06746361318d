"use client";

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { getEmployeeById } from '@/actions/actions';
import { Button } from '@/components/ui/button';
import { Employee } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Document, Page, Text, View, StyleSheet, PDFViewer, Font, Image } from '@react-pdf/renderer';

// Register custom fonts
Font.register({
  family: 'Roboto',
  fonts: [
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-light-webfont.ttf', fontWeight: 300 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf', fontWeight: 400 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf', fontWeight: 500 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf', fontWeight: 700 },
  ],
});

// Define styles for the PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: 'Roboto',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  logo: {
    width: 100,
    height: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333333',
  },
  section: {
    margin: 10,
    padding: 10,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    width: '40%',
    fontWeight: 'medium',
    fontSize: 10,
  },
  value: {
    width: '60%',
    fontSize: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 10,
    color: '#666666',
  },
});

// Attestation component
const Attestation = ({ employee }: { employee: Employee }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={styles.header}>
        <Image style={styles.logo} src="/logo.png" />
        <Text>{new Date().toLocaleDateString()}</Text>
      </View>
      <Text style={styles.title}>Attestation of Employment</Text>
      <View style={styles.section}>
        <Text>This is to certify that:</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Full Name:</Text>
          <Text style={styles.value}>{`${employee.prenom} ${employee.nom}`}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>ID Number (CIN):</Text>
          <Text style={styles.value}>{employee.cin || 'N/A'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Date of Birth:</Text>
          <Text style={styles.value}>{new Date(employee.dateNaissance).toLocaleDateString()}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Nationality:</Text>
          <Text style={styles.value}>{employee.nationalite || 'N/A'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Position:</Text>
          <Text style={styles.value}>{employee.poste}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Department:</Text>
          <Text style={styles.value}>{employee.departement}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Employment Date:</Text>
          <Text style={styles.value}>{new Date(employee.dateDebut).toLocaleDateString()}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Employment Type:</Text>
          <Text style={styles.value}>{employee.typeEmploi}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Current Salary:</Text>
          <Text style={styles.value}>{`${employee.salaire} MAD`}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>CNSS Number:</Text>
          <Text style={styles.value}>{employee.cnss || 'N/A'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Work Mode:</Text>
          <Text style={styles.value}>{employee.modeTravaill || 'N/A'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Bank:</Text>
          <Text style={styles.value}>{employee.banque || 'N/A'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>RIB:</Text>
          <Text style={styles.value}>{employee.rib || 'N/A'}</Text>
        </View>
      </View>
      <View style={styles.section}>
        <Text>This attestation is issued upon the request of the employee for whatever legal purpose it may serve.</Text>
      </View>
      <View style={styles.footer}>
        <Text>This document is computer-generated and does not require a signature.</Text>
      </View>
    </Page>
  </Document>
);

export default function AttestationPage() {
  const { id } = useParams();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEmployee = async () => {
      try {
        const employeeData = await getEmployeeById(id as string);
        setEmployee(employeeData);
      } catch (error) {
        console.error('Error fetching employee:', error);
        toast.error('Failed to fetch employee data');
      } finally {
        setLoading(false);
      }
    };

    fetchEmployee();
  }, [id]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!employee) {
    return <div>Employee not found</div>;
  }

  return (
    <div className="container mx-auto p-4 ml-20">
      <Card>
        <CardHeader>
          <CardTitle>Employee Attestation</CardTitle>
        </CardHeader>
        <CardContent>
          <PDFViewer width="100%" height={600}>
            <Attestation employee={employee} />
          </PDFViewer>
          <Button onClick={() => window.print()} className="mt-4">Print Attestation</Button>
        </CardContent>
      </Card>
    </div>
  );
}
