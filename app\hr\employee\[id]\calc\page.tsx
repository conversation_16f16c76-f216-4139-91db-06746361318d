import { getEmployeeById } from '@/actions/actions';
import SalaryCalculation from '@/components/Empoyees/SalaryCalcualtionModal'
import { notFound } from 'next/navigation'

const EmployeePage = async ({ params }: { params: { id: string } }) => {
	try {
		const employee = await getEmployeeById(params.id);

		if (!employee) {
			notFound();
		}

		return (
			<div className='flex justify-center p-14'>
        {/* @ts-ignore */}
				<SalaryCalculation employee={employee} />
			</div>
		);
	} catch (error) {
		console.error('Error fetching employee:', error);
		return <div>Une erreur s'est produite lors du chargement des données de l'employé.</div>;
	}
}

export default EmployeePage;