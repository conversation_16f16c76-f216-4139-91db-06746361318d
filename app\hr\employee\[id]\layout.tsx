"use client"

import { useState, useEffect } from "react";
import ManagementMiniSidebar from "@/components/layout/ManagementMiniSidebar";
import { cn } from "@/lib/utils";
import { useSidebar } from "@/components/ui/sidebar";

export default function EmployeeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { state: mainSidebarState } = useSidebar();
  const isMainSidebarExpanded = mainSidebarState === "expanded";
  const [isMobile, setIsMobile] = useState(false);

  // Check if screen is mobile and adjust sidebar state
  useEffect(() => {
    const checkIfMobile = () => {
      const isMobileView = window.innerWidth < 1024;
      setIsMobile(isMobileView);

      // Auto-close sidebar on smaller screens to prevent overflow
      if (isMobileView || window.innerWidth < 1280) {
        setIsSidebarOpen(false);
      }
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, [setIsSidebarOpen]);

  return (
    <div className="relative min-h-screen bg-background max-w-full">
      <ManagementMiniSidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} />
      <main
        className={cn(
          "transition-all duration-300 ease-in-out",
          isMobile
            ? "ml-0 w-full" // Mobile view - full width
            : isMainSidebarExpanded
              ? isSidebarOpen
                ? "ml-[28rem] w-[calc(100%-28rem)]" // Main sidebar expanded + mini sidebar open (reduced width)
                : "ml-[20rem] w-[calc(100%-20rem)]" // Main sidebar expanded + mini sidebar closed
              : isSidebarOpen
                ? "ml-[16rem] w-[calc(100%-16rem)]" // Main sidebar collapsed + mini sidebar open (reduced width)
                : "ml-[8rem] w-[calc(100%-8rem)]" // Main sidebar collapsed + mini sidebar closed
        )}
      >
        <div className={cn(
          "w-full transition-all duration-300",
          isMobile ? "px-2 py-2" : "px-3 py-4",
          // Adjust padding based on available space
          !isMobile && (isMainSidebarExpanded && isSidebarOpen) && "px-2 py-2"
        )}>
          <div className="max-w-full">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}