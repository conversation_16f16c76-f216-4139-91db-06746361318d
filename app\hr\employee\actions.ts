// this action is used for employee import

'use server'

import { revalidatePath } from 'next/cache';
import { addEmployee } from '@/components/AddEmployee/addEmployeeAction';
import { ensureUserInDatabase } from '@/lib/ensure-user';
import prisma from '@/lib/db';



// Add type definitions for better type safety
type ValidationResult = {
  isValid: boolean;
  errors: string[];
  transformedData: any;
};

// Update the type to correctly represent the mapping
type MappedFields = {
  [key: string]: string;  // Maps source column names to our target field names
};

// Add validation helper functions
const validateAndTransformEmployee = (
  employeeData: any,
  mappedFields: MappedFields
): ValidationResult => {
  const errors: string[] = [];
  const transformed: any = {};
  const mappedTargetFields = new Set(Object.values(mappedFields));

  try {
    // Track which required fields have been mapped
    const hasMapping = {
      prenom: false,
      nom: false,
      email: false,
      matricule: false
    };

    // First, process all mapped values from the imported data
    // We need to map the source fields to our target fields using mappedFields
    Object.entries(employeeData).forEach(([sourceField, value]) => {
      // Get the corresponding target field from mappedFields
      const targetField = mappedFields[sourceField];

      // Skip if no mapping exists for this field
      if (!targetField) return;

      // Mark this field as having a mapping
      if (targetField in hasMapping) {
        hasMapping[targetField as keyof typeof hasMapping] = true;
      }

      // Mark that we have a mapping even if the value is empty
      if (value === undefined || value === '' || value === null) {
        // For empty values, we still want to record that this field was mapped
        // but we'll handle setting the value later
        return;
      }

      const stringValue = String(value); // Convert value to string to fix type issues

      switch (targetField) {
        case 'email':
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(stringValue)) {
            errors.push(`Invalid email format: ${stringValue}`);
            // Don't set the email, will use default later if needed
          } else {
            transformed.email = stringValue;
          }
          break;

        case 'dateNaissance':
        case 'dateDebut':
        case 'dateFinContrat':
        case 'dateExpirationPermis':
        case 'dDepart':
        case 'dateRelica':
          try {
            const date = new Date(stringValue);
            if (!isNaN(date.getTime())) {
              transformed[targetField] = date;
            } else {
              errors.push(`Invalid date format for ${targetField}: ${stringValue}`);
            }
          } catch (error) {
            errors.push(`Error parsing date for ${targetField}: ${stringValue}`);
          }
          break;

        case 'salaire':
        case 'salaireBrut':
        case 'salaireNet':
        case 'tauxIR':
        case 'tauxCNSS':
        case 'tauxAMO':
        case 'distanceDomicileTravail':
          const num = Number(stringValue);
          if (!isNaN(num)) {
            transformed[targetField] = num;
          } else {
            errors.push(`Invalid number format for ${targetField}: ${stringValue}`);
          }
          break;

        case 'competences':
        case 'diplomes':
        case 'languesParlees':
        case 'formationsContinues':
          transformed[targetField] = Array.isArray(value)
            ? value
            : typeof value === 'string'
              ? stringValue.split(',').map(item => item.trim())
              : [];
          break;

        case 'estLocataire':
        case 'possedeAppartement':
        case 'utiliseTransport':
        case 'confIGR':
        case 'confCNSS':
        case 'confMutuel':
        case 'confAT':
        case 'confBourB':
        case 'confCIMR':
        case 'confCos':
        case 'anulAnc':
          // Handle boolean fields
          transformed[targetField] =
            stringValue.toLowerCase() === 'true' ||
            stringValue.toLowerCase() === 'yes' ||
            stringValue.toLowerCase() === 'oui' ||
            stringValue === '1';
          break;

        case 'periodeEssai':
        case 'congesPayes':
        case 'congesMaladie':
        case 'congesMaternite':
        case 'nombreEnfants':
        case 'droitConge':
          // Handle integer fields
          const intValue = parseInt(stringValue);
          if (!isNaN(intValue)) {
            transformed[targetField] = intValue;
          } else {
            errors.push(`Invalid integer format for ${targetField}: ${stringValue}`);
          }
          break;

        default:
          transformed[targetField] = stringValue;
      }
    });

    // Now set default values ONLY for required fields that weren't mapped or had invalid values
    // For mapped fields that had empty values, use the actual value from the CSV if available

    // For first name (prenom)
    if (!transformed.prenom) {
      if (hasMapping.prenom) {
        // If prenom was mapped but empty or invalid, use the original value if possible
        const originalValue = Object.entries(mappedFields).find(([_, target]) => target === 'prenom')?.[0];
        if (originalValue && employeeData[originalValue]) {
          transformed.prenom = String(employeeData[originalValue]);
        } else {
          // Only use default if we really have nothing
          transformed.prenom = '';
        }
      } else {
        // If prenom wasn't mapped at all, use empty string to make it obvious it needs to be filled
        transformed.prenom = '';
      }
    }

    // For last name (nom)
    if (!transformed.nom) {
      if (hasMapping.nom) {
        // If nom was mapped but empty or invalid, use the original value if possible
        const originalValue = Object.entries(mappedFields).find(([_, target]) => target === 'nom')?.[0];
        if (originalValue && employeeData[originalValue]) {
          transformed.nom = String(employeeData[originalValue]);
        } else {
          // Only use default if we really have nothing
          transformed.nom = '';
        }
      } else {
        // If nom wasn't mapped at all, use empty string to make it obvious it needs to be filled
        transformed.nom = '';
      }
    }

    // For email
    if (!transformed.email) {
      if (hasMapping.email) {
        // If email was mapped but empty or invalid, use the original value if possible
        const originalValue = Object.entries(mappedFields).find(([_, target]) => target === 'email')?.[0];
        if (originalValue && employeeData[originalValue]) {
          // Try to use the original value even if it's not a valid email format
          transformed.email = String(employeeData[originalValue]);
        } else {
          // Generate a unique email only if we have nothing else
          transformed.email = `temp${Date.now()}_${Math.random().toString(36).substr(2, 9)}@example.com`;
        }
      } else {
        // If email wasn't mapped at all, generate a unique one
        transformed.email = `temp${Date.now()}_${Math.random().toString(36).substr(2, 9)}@example.com`;
      }
    }

    // For other fields, we'll handle them differently:
    // 1. If they were mapped but empty, try to use the original value
    // 2. If they weren't mapped at all, use a default value

    // Helper function to get the original field name from the mapping
    const getOriginalField = (targetField: string) => {
      return Object.entries(mappedFields).find(([_, target]) => target === targetField)?.[0];
    };

    // Helper function to get value from original data if available
    const getValueFromOriginal = (targetField: string) => {
      const originalField = getOriginalField(targetField);
      if (originalField && employeeData[originalField] !== undefined &&
          employeeData[originalField] !== null && employeeData[originalField] !== '') {
        return String(employeeData[originalField]);
      }
      return null;
    };

    // Telephone
    if (!transformed.telephone) {
      const originalValue = getValueFromOriginal('telephone');
      if (originalValue) {
        transformed.telephone = originalValue;
      } else if (!mappedTargetFields.has('telephone')) {
        transformed.telephone = '';
      }
    }

    // Date de naissance
    if (!transformed.dateNaissance) {
      const originalValue = getValueFromOriginal('dateNaissance');
      if (originalValue) {
        try {
          const date = new Date(originalValue);
          if (!isNaN(date.getTime())) {
            transformed.dateNaissance = date;
          } else {
            transformed.dateNaissance = new Date();
          }
        } catch (e) {
          transformed.dateNaissance = new Date();
        }
      } else if (!mappedTargetFields.has('dateNaissance')) {
        transformed.dateNaissance = new Date();
      }
    }

    // Genre
    if (!transformed.genre) {
      const originalValue = getValueFromOriginal('genre');
      if (originalValue) {
        transformed.genre = originalValue;
      } else if (!mappedTargetFields.has('genre')) {
        transformed.genre = '';
      }
    }

    // Adresse
    if (!transformed.adresse) {
      const originalValue = getValueFromOriginal('adresse');
      if (originalValue) {
        transformed.adresse = originalValue;
      } else if (!mappedTargetFields.has('adresse')) {
        transformed.adresse = '';
      }
    }

    // Departement
    if (!transformed.departement) {
      const originalValue = getValueFromOriginal('departement');
      if (originalValue) {
        transformed.departement = originalValue;
      } else if (!mappedTargetFields.has('departement')) {
        transformed.departement = '';
      }
    }

    // Poste
    if (!transformed.poste) {
      const originalValue = getValueFromOriginal('poste');
      if (originalValue) {
        transformed.poste = originalValue;
      } else if (!mappedTargetFields.has('poste')) {
        transformed.poste = '';
      }
    }

    // Date de début
    if (!transformed.dateDebut) {
      const originalValue = getValueFromOriginal('dateDebut');
      if (originalValue) {
        try {
          const date = new Date(originalValue);
          if (!isNaN(date.getTime())) {
            transformed.dateDebut = date;
          } else {
            transformed.dateDebut = new Date();
          }
        } catch (e) {
          transformed.dateDebut = new Date();
        }
      } else if (!mappedTargetFields.has('dateDebut')) {
        transformed.dateDebut = new Date();
      }
    }

    // Salaire
    if (!transformed.salaire && transformed.salaire !== 0) {
      const originalValue = getValueFromOriginal('salaire');
      if (originalValue) {
        const num = Number(originalValue);
        transformed.salaire = isNaN(num) ? 0 : num;
      } else if (!mappedTargetFields.has('salaire')) {
        transformed.salaire = 0;
      }
    }

    // Type d'emploi
    if (!transformed.typeEmploi) {
      const originalValue = getValueFromOriginal('typeEmploi');
      if (originalValue) {
        transformed.typeEmploi = originalValue;
      } else if (!mappedTargetFields.has('typeEmploi')) {
        transformed.typeEmploi = 'Temps plein';
      }
    }

    // Contact d'urgence
    if (!transformed.contactUrgence) {
      const originalValue = getValueFromOriginal('contactUrgence');
      if (originalValue) {
        transformed.contactUrgence = originalValue;
      } else if (!mappedTargetFields.has('contactUrgence')) {
        transformed.contactUrgence = '';
      }
    }

    // Téléphone d'urgence
    if (!transformed.telephoneUrgence) {
      const originalValue = getValueFromOriginal('telephoneUrgence');
      if (originalValue) {
        transformed.telephoneUrgence = originalValue;
      } else if (!mappedTargetFields.has('telephoneUrgence')) {
        transformed.telephoneUrgence = '';
      }
    }

    // Compétences
    if (!transformed.competences) {
      const originalValue = getValueFromOriginal('competences');
      if (originalValue) {
        transformed.competences = originalValue.split(',').map(item => item.trim());
      } else if (!mappedTargetFields.has('competences')) {
        transformed.competences = [];
      }
    }

    // Langues parlées
    if (!transformed.languesParlees) {
      const originalValue = getValueFromOriginal('languesParlees');
      if (originalValue) {
        transformed.languesParlees = originalValue.split(',').map(item => item.trim());
      } else if (!mappedTargetFields.has('languesParlees')) {
        transformed.languesParlees = [];
      }
    }

    // Diplômes
    if (!transformed.diplomes) {
      const originalValue = getValueFromOriginal('diplomes');
      if (originalValue) {
        transformed.diplomes = originalValue.split(',').map(item => item.trim());
      } else if (!mappedTargetFields.has('diplomes')) {
        transformed.diplomes = [];
      }
    }

    // Generate a unique matricule if not provided or mapped
    if (!transformed.matricule) {
      transformed.matricule = `EMP-${Date.now().toString().slice(-6)}`;
    }

  } catch (error) {
    errors.push('Error processing employee data');
  }

  // Determine if the record is valid based on errors and mappings
  // We'll consider it valid if:
  // 1. There are no critical errors
  // 2. At least one field was mapped (we're allowing partial mappings)
  const hasCriticalErrors = errors.some(e => e.includes('Error processing employee data'));
  const hasAtLeastOneMapping = Object.values(mappedFields).length > 0;

  // Valid if no critical errors AND at least one field was mapped
  const isValid = !hasCriticalErrors && hasAtLeastOneMapping;

  return {
    isValid,
    errors,
    transformedData: transformed
  };
};

// Define the result type
export interface SaveEmployeesResult {
  success: boolean;
  error?: string;
  message?: string;
  results: any[];
  errorDetails?: string[];
  updatedEmployees?: any[];
}

export async function saveEmployees(
  data: any[],
  mappedFields: MappedFields
): Promise<SaveEmployeesResult> {
  try {
    // Validate that we have data to import
    if (!data || data.length === 0) {
      return {
        success: false,
        error: "No data provided for import",
        results: []
      };
    }

    // Validate that we have at least one mapping
    if (!mappedFields || Object.keys(mappedFields).length === 0) {
      return {
        success: false,
        error: "No field mappings provided. Please map at least one field to import data.",
        results: []
      };
    }

    const user = await ensureUserInDatabase();
    if (!user) {
      throw new Error('Unauthorized - User not found in database');
    }

    const results = [];
    const BATCH_SIZE = 10;
    let successCount = 0;
    let errorCount = 0;
    let errorDetails: string[] = [];

    for (let i = 0; i < data.length; i += BATCH_SIZE) {
      const batch = data.slice(i, i + BATCH_SIZE);

      const batchResults = await Promise.all(
        batch.map(async (employeeData, index) => {
          try {
            const { transformedData, errors, isValid } = validateAndTransformEmployee(
              employeeData,
              mappedFields
            );

            // Add validation errors to error details
            if (errors.length > 0) {
              const recordNumber = i + index + 1;
              errorDetails.push(`Record #${recordNumber}: ${errors.join(', ')}`);
            }

            // Only proceed with adding the employee if validation passed
            if (isValid) {
              const formData = new FormData();
              Object.entries(transformedData).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                  if (Array.isArray(value)) {
                    formData.append(key, value.join(','));
                  } else if (value instanceof Date) {
                    formData.append(key, value.toISOString());
                  } else {
                    formData.append(key, String(value));
                  }
                }
              });

              const result = await addEmployee(formData);
              if (result.success) {
                successCount++;
                return {
                  success: true,
                  data: transformedData,
                  recordNumber: i + index + 1
                };
              } else {
                errorCount++;
                errorDetails.push(`Record #${i + index + 1}: ${result.error}`);
                return {
                  success: false,
                  error: result.error || "Failed to add employee",
                  data: employeeData,
                  recordNumber: i + index + 1
                };
              }
            } else {
              // If validation failed, return error
              errorCount++;
              return {
                success: false,
                error: "Validation failed",
                data: employeeData,
                recordNumber: i + index + 1
              };
            }
          } catch (error) {
            errorCount++;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            errorDetails.push(`Record #${i + index + 1}: ${errorMessage}`);
            return {
              success: false,
              error: errorMessage,
              data: employeeData,
              recordNumber: i + index + 1
            };
          }
        })
      );

      results.push(...batchResults);

      if (i + BATCH_SIZE < data.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    revalidatePath('/hr/employee');

    // Get the updated list of employees after import
    let updatedEmployees: any[] = [];

    if (user) {
      const employees = await prisma.employee.findMany({
        where: {
          userId: user.id,
        },
        select: {
          id: true,
          matricule: true,
          prenom: true,
          nom: true,
          email: true,
          telephone: true,
          dateNaissance: true,
          genre: true,
          adresse: true,
          departement: true,
          poste: true,
          dateDebut: true,
          dateFinContrat: true,
          salaire: true,
          typeEmploi: true,
          contactUrgence: true,
          telephoneUrgence: true,
          competences: true,
          notesAdditionnelles: true,
          cin: true,
          cnss: true,
          rib: true,
          mutuelle: true,
          nationalite: true,
          numeroPasseport: true,
          permisTravaill: true,
          dateExpirationPermis: true,
          statutMatrimonial: true,
          niveauEducation: true,
          diplomes: true,
          languesParlees: true,
          salaireBrut: true,
          salaireNet: true,
          tauxIR: true,
          tauxCNSS: true,
          tauxAMO: true,
          banque: true,
          agenceBancaire: true,
          modePaiement: true,
          echelonSalaire: true,
          contratType: true,
          periodeEssai: true,
          congesPayes: true,
          congesMaladie: true,
          congesMaternite: true,
          estLocataire: true,
          possedeAppartement: true,
          utiliseTransport: true,
          typeTransport: true,
          distanceDomicileTravail: true,
          numeroCIMR: true,
          groupeSanguin: true,
          situationFamiliale: true,
          zoneResidence: true,
          formationsContinues: true,
          nombreEnfants: true,
          modeTravaill: true,
          societe: true,
          service: true,
          nomService: true,
          division: true,
          droitConge: true,
          dDepart: true,
          nomPaiement: true,
          grid: true,
          confIGR: true,
          confCNSS: true,
          confMutuel: true,
          confAT: true,
          confBourB: true,
          confALLFAM: true,
          confCIMR: true,
          confCos: true,
          anulAnc: true,
          ville: true,
          pays: true,
          mot: true,
          relicaC: true,
          dateRelica: true,
          cos: true,
          compteCompt: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: {
          nom: 'asc',
        },
      });

      // Format dates and other fields for client-side use
      // @ts-ignore
      updatedEmployees = employees.map(employee => ({
        ...employee,
        dateFinContrat: employee.dateFinContrat ? employee.dateFinContrat.toISOString() : null,
        dateNaissance: employee.dateNaissance ? employee.dateNaissance.toISOString() : null,
        dateDebut: employee.dateDebut ? employee.dateDebut.toISOString() : null,
        dateExpirationPermis: employee.dateExpirationPermis ? employee.dateExpirationPermis.toISOString() : null,
        dDepart: employee.dDepart ? employee.dDepart.toISOString() : null,
        dateRelica: employee.dateRelica ? employee.dateRelica.toISOString() : null,
        salaire: typeof employee.salaire === 'number' ? employee.salaire : parseFloat(employee.salaire as string) || 0
      }));
    }

    if (successCount > 0) {
      return {
        success: true,
        message: `Successfully imported ${successCount} employees${errorCount > 0 ? `. Failed: ${errorCount}` : ''}`,
        results,
        errorDetails: errorDetails.length > 0 ? errorDetails : undefined,
        updatedEmployees
      };
    } else {
      // Provide detailed error information
      return {
        success: false,
        error: "No employees were imported successfully. Please check the error details.",
        errorDetails,
        results,
        updatedEmployees
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      results: []
    };
  }
}
