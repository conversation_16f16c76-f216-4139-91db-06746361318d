import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import prisma from '@/lib/db';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { Toaster } from 'sonner';
import { saveEmployees } from './actions';
import DataTableDemo from '@/components/Empoyees/EmployeeTable';
import ErrorBoundary from '@/components/ErrorBoundary';

async function getEmployees() {
  const { userId } = auth();

  if (!userId) {
    redirect('/sign-in');
  }

  try {
    const user = await prisma.user.findUnique({
      where: {
        clerkId: userId
      }
    });

    if (!user) {
      console.error('User not found in database');
      return [];
    }

    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id,
      },
      select: {
        id: true,
        matricule: true,  // Added
        prenom: true,
        nom: true,
        email: true,
        telephone: true,
        dateNaissance: true,
        genre: true,
        adresse: true,
        departement: true,
        poste: true,
        dateDebut: true,
        dateFinContrat: true,
        salaire: true,
        typeEmploi: true,
        contactUrgence: true,
        telephoneUrgence: true,
        competences: true,
        notesAdditionnelles: true,
        cin: true,
        cnss: true,
        rib: true,
        mutuelle: true,
        nationalite: true,
        numeroPasseport: true,
        permisTravaill: true,
        dateExpirationPermis: true,
        statutMatrimonial: true,
        niveauEducation: true,
        diplomes: true,
        languesParlees: true,
        salaireBrut: true,
        salaireNet: true,
        tauxIR: true,
        tauxCNSS: true,
        tauxAMO: true,
        banque: true,
        agenceBancaire: true,
        modePaiement: true,
        echelonSalaire: true,
        contratType: true,
        periodeEssai: true,
        congesPayes: true,
        congesMaladie: true,
        congesMaternite: true,
        estLocataire: true,
        possedeAppartement: true,
        utiliseTransport: true,
        typeTransport: true,
        distanceDomicileTravail: true,
        numeroCIMR: true,
        groupeSanguin: true,
        situationFamiliale: true,
        zoneResidence: true,
        formationsContinues: true,
        nombreEnfants: true,
        modeTravaill: true,
        societe: true,
        service: true,
        nomService: true,
        division: true,
        droitConge: true,
        dDepart: true,
        nomPaiement: true,
        grid: true,
        confIGR: true,
        confCNSS: true,
        confMutuel: true,
        confAT: true,
        confBourB: true,
        confALLFAM: true,
        confCIMR: true,
        confCos: true,
        anulAnc: true,
        ville: true,
        pays: true,
        mot: true,
        relicaC: true,
        dateRelica: true,
        cos: true,
        compteCompt: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        nom: 'asc',
      },
    });

    return employees.map(employee => ({
      ...employee,
      dateFinContrat: employee.dateFinContrat ? employee.dateFinContrat.toISOString() : null,
      dateNaissance: employee.dateNaissance ? employee.dateNaissance.toISOString() : null,
      dateDebut: employee.dateDebut ? employee.dateDebut.toISOString() : null,
      dateExpirationPermis: employee.dateExpirationPermis ? employee.dateExpirationPermis.toISOString() : null,
      dDepart: employee.dDepart ? employee.dDepart.toISOString() : null,
      dateRelica: employee.dateRelica ? employee.dateRelica.toISOString() : null,
      salaire: typeof employee.salaire === 'number' ? employee.salaire : parseFloat(employee.salaire as string) || 0
    }));

  } catch (error) {
    console.error('Error fetching employees:', error);
    return [];
  }
}

export default async function EmployeeList() {
  const employees = await getEmployees();

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 ml-20 mt-4">
        <Toaster />
        <div className="h-full max-w-[1400px] mx-auto px-4">
          <Card className="h-full flex flex-col">
            {/* Fixed Header */}
            <CardHeader className="flex-none border-b bg-card">
              <CardTitle className="flex items-center justify-between">
                <span>Employee Directory</span>
                <span className="text-sm text-muted-foreground">
                  Total: {employees.length} employees
                </span>
              </CardTitle>
            </CardHeader>

            {/* Scrollable Content */}
            <CardContent className="flex-1 overflow-hidden p-0">
              <div className="h-full">
                {/* @ts-ignore - Type mismatch is handled internally by the component */}
                <DataTableDemo data={employees} saveEmployees={saveEmployees} />

                {employees.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    No employees found. Start by adding your first employee or import employees using the button above.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ErrorBoundary>
  );
}