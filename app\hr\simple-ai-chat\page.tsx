"use client"

import React from 'react';
import { useSimpleAIChat } from '@/hooks/useSimpleAIChat';
import SimpleAIChatInterface from '@/components/SimpleAIChat/SimpleAIChatInterface';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, AlertCircle } from 'lucide-react';

const SimpleAIChatPage: React.FC = () => {
  const {
    datasets,
    selectedDatasets,
    isLoading,
    error,
    handleDatasetSelectionChange,
    refreshDatasets
  } = useSimpleAIChat();

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Card>
          <CardContent className="flex items-center gap-3 p-6">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading datasets...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Card>
          <CardContent className="flex flex-col items-center gap-3 p-6 text-center">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <div>
              <h3 className="font-medium">Failed to load datasets</h3>
              <p className="text-sm text-muted-foreground mt-1">{error}</p>
              <button 
                onClick={refreshDatasets}
                className="text-sm text-primary hover:underline mt-2"
              >
                Try again
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <SimpleAIChatInterface
        datasets={datasets}
        selectedDatasets={selectedDatasets}
        onDatasetSelectionChange={handleDatasetSelectionChange}
      />
    </div>
  );
};

export default SimpleAIChatPage;
