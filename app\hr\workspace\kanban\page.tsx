"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Users, Settings, Share, Filter, Plus } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import KanbanBoard from '@/components/Managements/KanbanBoard';

export default function KanbanPage() {
  const router = useRouter();

  return (
    <div className="h-screen bg-slate-900 text-white flex flex-col">
      {/* Header */}
      <div className="border-b border-slate-700 bg-slate-800">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="text-slate-400 hover:text-white hover:bg-slate-700"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            
            <div>
              <h1 className="text-xl font-bold text-white">Project Board</h1>
              <p className="text-sm text-slate-400">HR Atlas Team Workspace</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-slate-700 text-slate-300">
              <Users className="h-3 w-3 mr-1" />
              5 members
            </Badge>
            
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-400 hover:text-white hover:bg-slate-700"
            >
              <Filter className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-400 hover:text-white hover:bg-slate-700"
            >
              <Share className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-400 hover:text-white hover:bg-slate-700"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Project Stats */}
        <div className="px-4 pb-4">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-slate-500 rounded-full"></div>
              <span className="text-sm text-slate-400">To Do: 8</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-slate-400">In Progress: 3</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-slate-400">Done: 12</span>
            </div>
            
            <div className="ml-auto flex items-center gap-2">
              <span className="text-sm text-slate-400">Team:</span>
              <div className="flex -space-x-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <Avatar key={i} className="h-6 w-6 border-2 border-slate-800">
                    <AvatarImage src={`/placeholder.svg?height=24&width=24&text=${i}`} />
                    <AvatarFallback className="text-xs bg-slate-600 text-white">
                      U{i}
                    </AvatarFallback>
                  </Avatar>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full p-4">
          <Card className="h-full bg-slate-800 border-slate-700">
            <CardContent className="h-full p-0">
              <div className="h-full overflow-hidden">
                <KanbanBoard />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions Floating Button */}
      <div className="fixed bottom-6 right-6">
        <Button
          size="icon"
          className="h-12 w-12 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>
    </div>
  );
}
