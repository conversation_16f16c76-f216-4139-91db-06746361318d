import dynamic from "next/dynamic";
import { Toaster } from "@/components/ui/sonner";
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"

const Editor = dynamic(() => import("@/components/Note/Editor"), {
  ssr: false,
});

export default async function NotePage({ params }: { params: { id: string } }) {
  const { userId } = auth();

  if (!userId) {
    redirect('/sign-in');
  }

  return (
    <div className="h-screen">
      <Toaster />
      <Editor noteId={params.id} />
    </div>
  );
}