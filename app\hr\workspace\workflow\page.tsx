"use client";

import { useEffect, useState, useRef } from "react";
import NoteFlow from "@/components/Note/noteflow/noteflow";
import { getSavedDiagrams } from "@/app/actions/diagram";
import { WorkflowSidebar } from "@/components/workflow/WorkflowSidebar";
import { toast } from "sonner";

export default function WorkflowPage() {
  const [workflows, setWorkflows] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const noteFlowRef = useRef<any>(null);

  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        setIsLoading(true);
        const result = await getSavedDiagrams();
        if (result?.success && result.data) {
          setWorkflows(result.data);
        } else {
          setWorkflows([]);
        }
      } catch (error) {
        console.error("Error fetching workflows:", error);
        setWorkflows([]);
        toast.error("Failed to fetch workflows");
      } finally {
        setIsLoading(false);
      }
    };
    fetchWorkflows();
  }, []);

  const handleLoadWorkflow = (workflow: any) => {
    if (noteFlowRef.current?.loadSavedDiagram) {
      noteFlowRef.current.loadSavedDiagram(workflow);
    }
  };

  return (
    <div className="flex h-screen">
      <WorkflowSidebar 
        workflows={workflows} 
        isLoading={isLoading} 
        onLoadWorkflow={handleLoadWorkflow}
      />
      <div className="flex-1 p-6">
        <NoteFlow 
          // @ts-ignore
          ref={noteFlowRef} 
        />
      </div>
    </div>
  );
}
