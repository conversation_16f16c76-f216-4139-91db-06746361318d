import { getPublicDiagram } from "@/app/actions/diagram"
import { notFound } from "next/navigation"
import PublicFlow from '@/components/Note/noteflow/PublicFlow'
import { Node, Edge } from 'reactflow'

interface FlowData {
  nodes: Node[];
  edges: Edge[];
}

interface NodeData {
  label: string;
  content?: string;
  note?: string;
}

export const dynamic = 'force-dynamic'
export const fetchCache = 'force-no-store'
export const revalidate = 0

export default async function PublicDiagramPage({ 
  params: { publicId } 
}: { 
  params: { publicId: string } 
}) {
  const result = await getPublicDiagram(publicId)

  if (!result.success || !result.data) {
    notFound()
  }

  // Ensure we have valid flow data
  let flowData: FlowData;
  
  try {
    // Parse the content if it's a string
    const content = typeof result.data.content === 'string' 
      ? JSON.parse(result.data.content)
      : result.data.content;

    // Validate the flow data structure
    if (!content || !content.nodes || !content.edges || 
        !Array.isArray(content.nodes) || !Array.isArray(content.edges)) {
      throw new Error('Invalid flow data structure');
    }

    // Process nodes with proper typing
    const nodes = content.nodes.map((node: any) => ({
      id: node.id?.toString() || Math.random().toString(),
      type: node.type || 'custom',
      position: node.position || { x: 0, y: 0 },
      data: {
        label: node.data?.label || 'Untitled',
        content: node.data?.content || '',
        note: node.data?.note || '',
      } as NodeData,
    }));

    // Process edges with proper typing
    const edges = content.edges.map((edge: any) => ({
      id: edge.id?.toString() || `${edge.source}-${edge.target}`,
      source: edge.source?.toString() || '',
      target: edge.target?.toString() || '',
      type: edge.type || 'default',
    }));

    flowData = { nodes, edges };

  } catch (error) {
    console.error('Error processing flow data:', error);
    notFound();
  }

  return (
    <div className="w-screen h-screen bg-background">
      <PublicFlow 
        title={result.data.title} 
        initialData={flowData} 
      />
    </div>
  )
}