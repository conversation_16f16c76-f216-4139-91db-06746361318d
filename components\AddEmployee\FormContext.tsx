"use client"

import React, { createContext, useContext, useRef, useState } from 'react'

// Define the context type
type FormContextType = {
  formData: { [key: string]: any }
  updateFormData: (field: string, value: any) => void
  updateMultipleFields: (fields: { [key: string]: any }) => void
  getFormData: () => { [key: string]: any }
  handleCompetenceToggle: (competence: string) => void
  handleArrayChange: (field: string, value: string) => void
}

// Create the context with a default value
const FormContext = createContext<FormContextType | undefined>(undefined)

// Provider component
export const FormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use a ref to store form data
  const formDataRef = useRef<{ [key: string]: any }>({
    competences: [],
    diplomes: [],
    languesParlees: [],
    formationsContinues: [],
  })

  // Update a single field
  const updateFormData = (field: string, value: any) => {
    formDataRef.current[field] = value
  }

  // Update multiple fields at once
  const updateMultipleFields = (fields: { [key: string]: any }) => {
    Object.entries(fields).forEach(([key, value]) => {
      formDataRef.current[key] = value
    })
  }

  // Get the current form data
  const getFormData = () => {
    return formDataRef.current
  }

  // Handle competence toggle (for checkboxes)
  const handleCompetenceToggle = (competence: string) => {
    const competences = formDataRef.current.competences || []
    formDataRef.current.competences = competences.includes(competence)
      ? competences.filter((c: string) => c !== competence)
      : [...competences, competence]
  }

  // Handle array changes (for comma-separated inputs)
  const handleArrayChange = (field: string, value: string) => {
    const arrayValue = value ? value.split(',').map(item => item.trim()) : []
    formDataRef.current[field] = arrayValue
  }

  // Create the context value
  const contextValue: FormContextType = {
    formData: formDataRef.current,
    updateFormData,
    updateMultipleFields,
    getFormData,
    handleCompetenceToggle,
    handleArrayChange
  }

  return (
    <FormContext.Provider value={contextValue}>
      {children}
    </FormContext.Provider>
  )
}

// Custom hook to use the form context
export const useFormContext = () => {
  const context = useContext(FormContext)
  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider')
  }
  return context
}
