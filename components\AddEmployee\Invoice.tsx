"use client"

import React from 'react'
import AddEmployee from '.';

// Field descriptions object
const fieldDescriptions = {
  personal: {
    prenom: "Prénom de l'employé",
    nom: "Nom de famille de l'employé",
    email: "Adresse email professionnelle",
    telephone: "Numéro de téléphone principal",
    contactUrgence: "Personne à contacter en cas d'urgence",
    telephoneUrgence: "Numéro de téléphone d'urgence",
    dateNaissance: "Date de naissance de l'employé",
    genre: "Genre de l'employé",
    nationalite: "Nationalité de l'employé",
    numeroPasseport: "Numéro de passeport si applicable",
    adresse: "Adresse complète du domicile",
    ville: "Ville de résidence",
    pays: "Pays de résidence"
  },
  professional: {
    societe: "Nom ou code de la société",
    service: "Service ou département principal",
    nomService: "Nom complet du service",
    division: "Division ou unité organisationnelle",
    matricule: "Numéro d'identification unique de l'employé",
    departement: "Département de rattachement",
    poste: "Intitulé du poste occupé",
    dateDebut: "Date de début du contrat",
    typeEmploi: "Type de contrat de travail",
    dateFinContrat: "Date de fin pour les CDD",
    periodeEssai: "Durée de la période d'essai en jours",
    competences: "Compétences principales de l'employé",
    modeTravaill: "Mode de travail (présentiel, hybride, télétravail)"
  },
  financial: {
    salaire: "Salaire de base",
    salaireBrut: "Salaire brut avant déductions",
    salaireNet: "Salaire net après déductions",
    tauxIR: "Taux d'imposition sur le revenu",
    tauxCNSS: "Taux de cotisation CNSS",
    tauxAMO: "Taux de l'Assurance Maladie Obligatoire",
    rib: "Relevé d'Identité Bancaire",
    modePaiement: "Mode de versement du salaire",
    banque: "Établissement bancaire",
    compteCompt: "Numéro de compte comptable",
    grid: "Grille salariale",
    echelonSalaire: "Échelon dans la grille salariale",
    congesMaladie: "Nombre de jours de congés maladie",
    congesPayes: "Nombre de jours de congés payés",
    congesMaternite: "Nombre de jours de congés maternité",
  },
  benefits: {
    estLocataire: "Indique si l'employé est locataire de son logement",
    possedeAppartement: "Indique si l'employé est propriétaire de son logement",
    utiliseTransport: "Indique si l'employé utilise un moyen de transport",
    typeTransport: "Type de transport utilisé (public, privé, entreprise)",
    distanceDomicileTravail: "Distance en kilomètres entre le domicile et le lieu de travail",
    numeroCIMR: "Numéro d'affiliation à la CIMR",
    groupeSanguin: "Groupe sanguin de l'employé (important pour urgences médicales)",
    droitConge: "Nombre de jours de congés auxquels l'employé a droit",
    dDepart: "Date de départ prévue ou effective",
    confIGR: "Configuration de l'Impôt Général sur le Revenu",
    confCNSS: "Configuration des cotisations CNSS",
    confMutuel: "Configuration de la mutuelle de santé",
    confAT: "Configuration de l'assurance accidents de travail",
    confBourB: "Configuration des bourses et bonus",
    confCIMR: "Configuration des cotisations CIMR",
    confCos: "Configuration des cotisations sociales",
    confALLFAM: "Configuration des allocations familiales",
    dateDu: "Date de début de validité des configurations",
    dateAu: "Date de fin de validité des configurations"
  },
  additional: {
    niveauEducation: "Plus haut niveau d'études atteint",
    diplomes: "Liste des diplômes obtenus",
    languesParlees: "Langues maîtrisées par l'employé",
    formationsContinues: "Formations professionnelles suivies",
    situationFamiliale: "Situation matrimoniale actuelle",
    nombreEnfants: "Nombre d'enfants à charge",
    zoneResidence: "Zone géographique de résidence",
    nomPaiement: "Libellé du mode de paiement",
    anulAnc: "Annulation de l'ancienneté (oui/non)",
    grid: "Position dans la grille salariale",
    mot: "Motif des modifications de salaire",
    relicaC: "Informations sur les reliquats",
    dateRelica: "Date des derniers reliquats",
    cos: "Code des opérations sociales"
  },
  companyDetails: {
    societe: "Nom officiel de la société",
    service: "Service de rattachement",
    nomService: "Nom complet du service",
    division: "Division ou département",
    matricule: "Numéro d'identification interne",
    compteCompt: "Numéro de compte comptable"
  },
  configurations: {
    droitConge: "Nombre de jours de congés annuels",
    dDepart: "Date de départ prévue",
    nomPaiement: "Modalité de paiement du salaire",
    confIGR: "Application de l'IGR (oui/non)",
    confCNSS: "Application de la CNSS (oui/non)",
    confMutuel: "Couverture mutuelle (oui/non)",
    confAT: "Assurance accidents de travail (oui/non)",
    confBourB: "Application des bourses (oui/non)",
    confALLFAM: "Configuration allocations familiales",
    confCIMR: "Application CIMR (oui/non)",
    confCos: "Configuration COS (oui/non)"
  }
};

/**
 * Invoice component that uses the new AddEmployee component
 * This component serves as a wrapper for the AddEmployee component
 */
const Invoice: React.FC = () => {
  return <AddEmployee />;
}

export default Invoice;
