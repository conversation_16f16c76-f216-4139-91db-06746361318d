"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface StepLayoutProps {
  children: React.ReactNode
  currentStep: number
  totalSteps: number
  onNext: () => void
  onPrevious: () => void
  isLastStep: boolean
  isSubmitting: boolean
}

const StepLayout: React.FC<StepLayoutProps> = ({
  children,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  isLastStep,
  isSubmitting
}) => {
  // Step titles
  const stepTitles = [
    "Informations Personnelles",
    "Informations Professionnelles",
    "Informations Financières",
    "Avantages",
    "Informations Additionnelles"
  ]

  return (
    <div className="flex flex-col md:flex-row w-full gap-6">
      {/* Step indicator - right side on desktop, top on mobile */}
      <div className="md:order-2 md:w-64 flex-shrink-0">
        <Card className="sticky top-4">
          <CardContent className="p-4">
            <div className="space-y-2">
              <h3 className="font-medium text-sm mb-4">Étapes</h3>
              {stepTitles.map((title, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div 
                    className={cn(
                      "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                      currentStep === index 
                        ? "bg-primary text-primary-foreground" 
                        : index < currentStep 
                          ? "bg-primary/20 text-primary" 
                          : "bg-muted text-muted-foreground"
                    )}
                  >
                    {index + 1}
                  </div>
                  <span 
                    className={cn(
                      "text-sm",
                      currentStep === index 
                        ? "font-medium text-foreground" 
                        : "text-muted-foreground"
                    )}
                  >
                    {title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main content - left side */}
      <div className="md:order-1 flex-1">
        <Card>
          <CardContent className="p-6">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {children}
            </motion.div>

            {/* Navigation buttons */}
            <div className="flex justify-between mt-8">
              <Button 
                type="button" 
                onClick={onPrevious} 
                disabled={currentStep === 0}
                variant="outline"
              >
                Précédent
              </Button>
              <Button 
                type="button" 
                onClick={onNext}
                disabled={isSubmitting}
              >
                {isLastStep 
                  ? (isSubmitting ? "Soumission en cours..." : "Soumettre") 
                  : "Suivant"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default StepLayout
