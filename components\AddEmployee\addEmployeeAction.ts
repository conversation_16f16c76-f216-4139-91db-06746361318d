'use server';

import { revalidatePath } from "next/cache";
import prisma from "@/lib/db";
import { ensureUserInDatabase } from "@/lib/ensure-user";

type AddEmployeeResult = {
  success: boolean;
  error?: string;
  employeeId?: string;
};

export async function addEmployee(formData: FormData): Promise<AddEmployeeResult> {
  try {
    const user = await ensureUserInDatabase();

    if (!user) {
      return { success: false, error: "User not found" };
    }

    // Check if employee with same matricule already exists
    const existingEmployee = await prisma.employee.findFirst({
      where: {
        userId: user.id,
        OR: [
          { email: formData.get('email') as string },
          { matricule: formData.get('matricule') as string }
        ]
      }
    });

    if (existingEmployee) {
      if (existingEmployee.email === formData.get('email')) {
        return {
          success: false,
          error: `Un employé avec l'email ${formData.get('email')} existe déjà`
        };
      }
      if (existingEmployee.matricule === formData.get('matricule')) {
        return {
          success: false,
          error: `Un employé avec le matricule ${formData.get('matricule')} existe déjà`
        };
      }
    }

    // Helper function to safely split comma-separated strings
    const safeSplit = (value: string | null) => {
      if (!value) return [];
      return value.trim() ? value.split(',').map(item => item.trim()) : [];
    };

    // Helper function to safely parse float
    const safeParseFloat = (value: string | null) => {
      if (!value) return null;
      const num = parseFloat(value);
      return isNaN(num) ? null : num;
    };

    // Helper function to safely parse int
    const safeParseInt = (value: string | null) => {
      if (!value) return null;
      const num = parseInt(value, 10);
      return isNaN(num) ? null : num;
    };

    // Helper function to safely parse boolean
    const safeParseBoolean = (value: string | null) => {
      if (!value) return false;
      return value === 'true';
    };

    // Helper function to safely parse date
    const safeParseDate = (value: string | null) => {
      if (!value) return null;
      const date = new Date(value);
      return isNaN(date.getTime()) ? null : date;
    };

    const employee = await prisma.employee.create({
      data: {
        user: { connect: { id: user.id } },
        prenom: formData.get('prenom') as string || 'Unknown',
        nom: formData.get('nom') as string || 'Unknown',
        email: formData.get('email') as string || `temp${Date.now()}@example.com`,
        telephone: formData.get('telephone') as string || '',
        dateNaissance: new Date(formData.get('dateNaissance') as string || Date.now()),
        genre: formData.get('genre') as string || 'Non spécifié',
        adresse: formData.get('adresse') as string || '',
        departement: formData.get('departement') as string || 'Non assigné',
        poste: formData.get('poste') as string || 'Non assigné',
        dateDebut: new Date(formData.get('dateDebut') as string || Date.now()),
        salaire: safeParseFloat(formData.get('salaire') as string) || 0,
        typeEmploi: formData.get('typeEmploi') as string || 'Temps plein',
        contactUrgence: formData.get('contactUrgence') as string || '',
        telephoneUrgence: formData.get('telephoneUrgence') as string || '',
        competences: safeSplit(formData.get('competences') as string),
        notesAdditionnelles: formData.get('notesAdditionnelles') as string || null,
        cin: formData.get('cin') as string || null,
        cnss: formData.get('cnss') as string || null,
        rib: formData.get('rib') as string || null,
        mutuelle: formData.get('mutuelle') as string || null,
        numeroCIMR: formData.get('numeroCIMR') as string || null,
        nationalite: formData.get('nationalite') as string || null,
        numeroPasseport: formData.get('numeroPasseport') as string || null,
        permisTravaill: formData.get('permisTravaill') as string || null,
        dateExpirationPermis: safeParseDate(formData.get('dateExpirationPermis') as string),
        statutMatrimonial: formData.get('statutMatrimonial') as string || null,
        niveauEducation: formData.get('niveauEducation') as string || null,
        diplomes: safeSplit(formData.get('diplomes') as string),
        languesParlees: safeSplit(formData.get('languesParlees') as string),
        salaireBrut: safeParseFloat(formData.get('salaireBrut') as string),
        salaireNet: safeParseFloat(formData.get('salaireNet') as string),
        tauxIR: safeParseFloat(formData.get('tauxIR') as string),
        tauxCNSS: safeParseFloat(formData.get('tauxCNSS') as string),
        tauxAMO: safeParseFloat(formData.get('tauxAMO') as string),
        banque: formData.get('banque') as string || null,
        agenceBancaire: formData.get('agenceBancaire') as string || null,
        modePaiement: formData.get('modePaiement') as string || null,
        echelonSalaire: formData.get('echelonSalaire') as string || null,
        contratType: formData.get('contratType') as string || null,
        dateFinContrat: safeParseDate(formData.get('dateFinContrat') as string),
        periodeEssai: safeParseInt(formData.get('periodeEssai') as string),
        congesPayes: safeParseInt(formData.get('congesPayes') as string),
        congesMaladie: safeParseInt(formData.get('congesMaladie') as string),
        congesMaternite: safeParseInt(formData.get('congesMaternite') as string),
        estLocataire: safeParseBoolean(formData.get('estLocataire') as string),
        possedeAppartement: safeParseBoolean(formData.get('possedeAppartement') as string),
        utiliseTransport: safeParseBoolean(formData.get('utiliseTransport') as string),
        typeTransport: formData.get('typeTransport') as string || null,
        distanceDomicileTravail: safeParseFloat(formData.get('distanceDomicileTravail') as string),
        zoneResidence: formData.get('zoneResidence') as string || null,
        groupeSanguin: formData.get('groupeSanguin') as string || null,
        situationFamiliale: formData.get('situationFamiliale') as string || null,
        formationsContinues: safeSplit(formData.get('formationsContinues') as string),
        nombreEnfants: safeParseInt(formData.get('nombreEnfants') as string),
        modeTravaill: formData.get('modeTravaill') as string || null,
        societe: formData.get('societe') as string || null,
        service: formData.get('service') as string || null,
        nomService: formData.get('nomService') as string || null,
        division: formData.get('division') as string || null,
        matricule: formData.get('matricule') as string || null,
        droitConge: safeParseInt(formData.get('droitConge') as string),
        dDepart: safeParseDate(formData.get('dDepart') as string),
        nomPaiement: formData.get('nomPaiement') as string || null,
        confIGR: formData.get('confIGR') === 'true',
        confCNSS: formData.get('confCNSS') === 'true',
        confMutuel: formData.get('confMutuel') === 'true',
        confAT: formData.get('confAT') === 'true',
        confBourB: formData.get('confBourB') === 'true',
        confALLFAM: formData.get('confALLFAM') ? JSON.parse(formData.get('confALLFAM') as string) : null,
        confCIMR: formData.get('confCIMR') === 'true',
        confCos: formData.get('confCos') === 'true',
        anulAnc: formData.get('anulAnc') === 'true',
        ville: formData.get('ville') as string || null,
        pays: formData.get('pays') as string || null,
        grid: formData.get('grid') as string || null,
        mot: formData.get('mot') as string || null,
        relicaC: formData.get('relicaC') as string || null,
        dateRelica: formData.get('dateRelica') ? new Date(formData.get('dateRelica') as string) : null,
        cos: formData.get('cos') as string || null,
        compteCompt: formData.get('compteCompt') as string || null,
      }
    });

    revalidatePath('/hr/employee');
    return { success: true, employeeId: employee.id  };
  } catch (error) {
    console.error('Error in addEmployee:', error);
    return { success: false, error: "Failed to add employee" };
  }
}
