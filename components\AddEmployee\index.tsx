"use client"

import React, { useState } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { toast } from 'sonner'
import { addEmployee } from './addEmployeeAction'
import { FormProvider, useFormContext } from './FormContext'
import StepLayout from './StepLayout'
import PersonalInfoStep from './steps/PersonalInfoStep'
import ProfessionalInfoStep from './steps/ProfessionalInfoStep'
import FinancialInfoStep from './steps/FinancialInfoStep'
import BenefitsStep from './steps/BenefitsStep'
import AdditionalInfoStep from './steps/AdditionalInfoStep'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

// Main component wrapper with FormProvider
const AddEmployee: React.FC = () => {
  return (
    <FormProvider>
      <AddEmployeeForm />
    </FormProvider>
  )
}

// Form component that uses the form context
const AddEmployeeForm: React.FC = () => {
  const router = useRouter()
  const { getFormData } = useFormContext()
  const [currentStep, setCurrentStep] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Define the steps
  const steps = [
    <PersonalInfoStep key="personal" />,
    <ProfessionalInfoStep key="professional" />,
    <FinancialInfoStep key="financial" />,
    <BenefitsStep key="benefits" />,
    <AdditionalInfoStep key="additional" />
  ]

  // Handle next step
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleSubmit()
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Handle form submission
  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      const formData = new FormData()
      const formValues = getFormData()

      // Add all form values to FormData
      Object.entries(formValues).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(item => formData.append(key, item))
        } else if (value != null) {
          formData.append(key, String(value))
        }
      })

      // Submit the form
      const result = await addEmployee(formData)

      if (result.success && result.employeeId) {
        toast.success("Employé ajouté avec succès")
        router.push(`/hr/employee/${result.employeeId}`)
      } else {
        if (result.error?.includes('matricule')) {
          toast.error("Ce matricule existe déjà")
        } else {
          toast.error(result.error || "Échec de l'ajout de l'employé")
        }
      }
    } catch (error) {
      toast.error("Une erreur s'est produite")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="w-full max-w-6xl mx-auto py-6">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-2xl sm:text-3xl">Profil de l'Employé</CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Entrez les détails du nouvel employé ci-dessous.
          </CardDescription>
        </CardHeader>
      </Card>

      <StepLayout
        currentStep={currentStep}
        totalSteps={steps.length}
        onNext={handleNext}
        onPrevious={handlePrevious}
        isLastStep={currentStep === steps.length - 1}
        isSubmitting={isSubmitting}
      >
        {steps[currentStep]}
      </StepLayout>
    </div>
  )
}

export default AddEmployee
