"use client"

import React, { useState } from 'react'
import { useFormContext } from '../FormContext'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { InfoCircledIcon } from "@radix-ui/react-icons"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Field descriptions
const fieldDescriptions = {
  niveauEducation: "Plus haut niveau d'études atteint",
  diplomes: "Liste des diplômes obtenus",
  languesParlees: "Langues maîtrisées par l'employé",
  formationsContinues: "Formations professionnelles suivies",
  situationFamiliale: "Situation matrimoniale actuelle",
  nombreEnfants: "Nombre d'enfants à charge",
  zoneResidence: "Zone géographique de résidence",
  nomPaiement: "Libellé du mode de paiement",
  anulAnc: "Annulation de l'ancienneté (oui/non)",
  grid: "Position dans la grille salariale",
  mot: "Motif des modifications de salaire",
  relicaC: "Informations sur les reliquats",
  dateRelica: "Date des derniers reliquats",
  cos: "Code des opérations sociales"
}

// Helper component for field with tooltip
const FieldWithTooltip = ({ label, description, children }: { label: string; description: string; children: React.ReactNode }) => (
  <div className="space-y-2">
    <div className="flex items-center space-x-2">
      <Label htmlFor={label}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <InfoCircledIcon className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p>{description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    {children}
  </div>
)

const AdditionalInfoStep: React.FC = () => {
  const { updateFormData, getFormData, handleArrayChange } = useFormContext()
  const formData = getFormData()
  
  // Local state to force re-render for conditional fields
  const [situationFamiliale, setSituationFamiliale] = useState(formData.situationFamiliale || '')
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    updateFormData(name, type === 'number' ? (value === '' ? '' : Number(value)) : value)
  }
  
  // Check if nombre enfants should be shown
  const shouldShowNombreEnfants = ['marie', 'divorce'].includes(situationFamiliale)
  
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Informations Additionnelles</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Niveau d'Éducation" 
          description={fieldDescriptions.niveauEducation}
        >
          <input 
            id="niveauEducation" 
            name="niveauEducation" 
            placeholder="Niveau d'Éducation" 
            defaultValue={formData.niveauEducation || ''}
            onChange={handleChange}
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Diplômes" 
          description={fieldDescriptions.diplomes}
        >
          <Textarea 
            id="diplomes" 
            name="diplomes" 
            placeholder="Diplômes (séparés par des virgules)" 
            defaultValue={Array.isArray(formData.diplomes) ? formData.diplomes.join(', ') : ''} 
            onChange={(e) => handleArrayChange('diplomes', e.target.value)} 
            className="min-h-[80px]"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Langues Parlées" 
          description={fieldDescriptions.languesParlees}
        >
          <Textarea 
            id="languesParlees" 
            name="languesParlees" 
            placeholder="Langues parlées (séparées par des virgules)" 
            defaultValue={Array.isArray(formData.languesParlees) ? formData.languesParlees.join(', ') : ''} 
            onChange={(e) => handleArrayChange('languesParlees', e.target.value)} 
            className="min-h-[80px]"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Formations Continues" 
          description={fieldDescriptions.formationsContinues}
        >
          <Textarea 
            id="formationsContinues" 
            name="formationsContinues" 
            placeholder="Formations continues (séparées par des virgules)" 
            defaultValue={Array.isArray(formData.formationsContinues) ? formData.formationsContinues.join(', ') : ''} 
            onChange={(e) => handleArrayChange('formationsContinues', e.target.value)} 
            className="min-h-[80px]"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Situation Familiale" 
          description={fieldDescriptions.situationFamiliale}
        >
          <Select 
            name="situationFamiliale" 
            defaultValue={formData.situationFamiliale || ''}
            onValueChange={(value) => {
              updateFormData('situationFamiliale', value)
              setSituationFamiliale(value) // Update local state to force re-render
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Situation Familiale" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="celibataire">Célibataire</SelectItem>
              <SelectItem value="marie">Marié(e)</SelectItem>
              <SelectItem value="divorce">Divorcé(e)</SelectItem>
              <SelectItem value="veuf">Veuf/Veuve</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        {shouldShowNombreEnfants && (
          <FieldWithTooltip 
            label="Nombre d'Enfants" 
            description={fieldDescriptions.nombreEnfants}
          >
            <input 
              id="nombreEnfants" 
              name="nombreEnfants" 
              type="number" 
              placeholder="Nombre d'Enfants"
              defaultValue={formData.nombreEnfants || ''}
              onChange={handleChange}
              className="border p-2 rounded w-full"
            />
          </FieldWithTooltip>
        )}
        
        <FieldWithTooltip 
          label="Zone de Résidence" 
          description={fieldDescriptions.zoneResidence}
        >
          <input 
            id="zoneResidence" 
            name="zoneResidence" 
            placeholder="Zone de Résidence" 
            defaultValue={formData.zoneResidence || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Nom du Paiement" 
          description={fieldDescriptions.nomPaiement}
        >
          <input 
            id="nomPaiement" 
            name="nomPaiement" 
            placeholder="Nom du paiement" 
            defaultValue={formData.nomPaiement || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Annulation Ancienneté" 
          description={fieldDescriptions.anulAnc}
        >
          <Select 
            name="anulAnc" 
            defaultValue={formData.anulAnc || ''}
            onValueChange={(value) => updateFormData('anulAnc', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Annulation Ancienneté" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Grid" 
          description={fieldDescriptions.grid}
        >
          <input 
            id="grid" 
            name="grid" 
            placeholder="Grid" 
            defaultValue={formData.grid || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Motif" 
          description={fieldDescriptions.mot}
        >
          <input 
            id="mot" 
            name="mot" 
            placeholder="Motif" 
            defaultValue={formData.mot || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Relica C" 
          description={fieldDescriptions.relicaC}
        >
          <input 
            id="relicaC" 
            name="relicaC" 
            placeholder="Relica C" 
            defaultValue={formData.relicaC || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Date Relica" 
          description={fieldDescriptions.dateRelica}
        >
          <input 
            id="dateRelica" 
            name="dateRelica" 
            type="date" 
            defaultValue={formData.dateRelica || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="COS" 
          description={fieldDescriptions.cos}
        >
          <input 
            id="cos" 
            name="cos" 
            placeholder="COS" 
            defaultValue={formData.cos || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
    </div>
  )
}

export default AdditionalInfoStep
