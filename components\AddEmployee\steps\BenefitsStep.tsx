"use client"

import React, { useState } from 'react'
import { useFormContext } from '../FormContext'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { InfoCircledIcon } from "@radix-ui/react-icons"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Field descriptions
const fieldDescriptions = {
  estLocataire: "Indique si l'employé est locataire de son logement",
  possedeAppartement: "Indique si l'employé est propriétaire de son logement",
  utiliseTransport: "Indique si l'employé utilise un moyen de transport",
  typeTransport: "Type de transport utilisé (public, privé, entreprise)",
  distanceDomicileTravail: "Distance en kilomètres entre le domicile et le lieu de travail",
  numeroCIMR: "Numéro d'affiliation à la CIMR",
  groupeSanguin: "Groupe sanguin de l'employé (important pour urgences médicales)",
  droitConge: "Nombre de jours de congés auxquels l'employé a droit",
  dDepart: "Date de départ prévue ou effective",
  confIGR: "Configuration de l'Impôt Général sur le Revenu",
  confCNSS: "Configuration des cotisations CNSS",
  confMutuel: "Configuration de la mutuelle de santé",
  confAT: "Configuration de l'assurance accidents de travail",
  confBourB: "Configuration des bourses et bonus",
  confCIMR: "Configuration des cotisations CIMR",
  confCos: "Configuration des cotisations sociales",
  confALLFAM: "Configuration des allocations familiales",
  dateDu: "Date de début de validité des configurations",
  dateAu: "Date de fin de validité des configurations"
}

// Helper component for field with tooltip
const FieldWithTooltip = ({ label, description, children }: { label: string; description: string; children: React.ReactNode }) => (
  <div className="space-y-2">
    <div className="flex items-center space-x-2">
      <Label htmlFor={label}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <InfoCircledIcon className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p>{description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    {children}
  </div>
)

const BenefitsStep: React.FC = () => {
  const { updateFormData, getFormData } = useFormContext()
  const formData = getFormData()
  
  // Local state to force re-render for conditional fields
  const [utiliseTransport, setUtiliseTransport] = useState(formData.utiliseTransport || 'false')
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    updateFormData(name, type === 'number' ? (value === '' ? '' : Number(value)) : value)
  }
  
  // Check if transport details should be shown
  const shouldShowTransportDetails = utiliseTransport === 'true'
  
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Avantages et Sécurité Sociale</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Est Locataire" 
          description={fieldDescriptions.estLocataire}
        >
          <Select 
            name="estLocataire" 
            defaultValue={formData.estLocataire}
            onValueChange={(value) => updateFormData('estLocataire', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Est Locataire" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Utilise le Transport" 
          description={fieldDescriptions.utiliseTransport}
        >
          <Select 
            name="utiliseTransport" 
            defaultValue={formData.utiliseTransport}
            onValueChange={(value) => {
              updateFormData('utiliseTransport', value)
              setUtiliseTransport(value) // Update local state to force re-render
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Utilise le Transport" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        {shouldShowTransportDetails && (
          <>
            <FieldWithTooltip 
              label="Type de Transport" 
              description={fieldDescriptions.typeTransport}
            >
              <Select 
                name="typeTransport" 
                defaultValue={formData.typeTransport}
                onValueChange={(value) => updateFormData('typeTransport', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Type de Transport" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PUBLIC">Public</SelectItem>
                  <SelectItem value="PRIVATE">Privé</SelectItem>
                  <SelectItem value="COMPANY">Entreprise</SelectItem>
                </SelectContent>
              </Select>
            </FieldWithTooltip>
            
            <FieldWithTooltip 
              label="Distance Domicile-Travail (km)" 
              description={fieldDescriptions.distanceDomicileTravail}
            >
              <input 
                id="distanceDomicileTravail" 
                name="distanceDomicileTravail" 
                type="number" 
                step="0.1" 
                placeholder="Distance en km" 
                defaultValue={formData.distanceDomicileTravail || ''}
                onChange={handleChange} 
                className="border p-2 rounded w-full"
              />
            </FieldWithTooltip>
          </>
        )}
        
        <FieldWithTooltip 
          label="Numéro CIMR" 
          description={fieldDescriptions.numeroCIMR}
        >
          <input 
            id="numeroCIMR" 
            name="numeroCIMR" 
            placeholder="Numéro CIMR" 
            defaultValue={formData.numeroCIMR || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Groupe Sanguin" 
          description={fieldDescriptions.groupeSanguin}
        >
          <input 
            id="groupeSanguin" 
            name="groupeSanguin" 
            placeholder="Groupe Sanguin" 
            defaultValue={formData.groupeSanguin || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Droit aux Congés (jours)" 
          description={fieldDescriptions.droitConge}
        >
          <input 
            id="droitConge" 
            name="droitConge" 
            type="number" 
            placeholder="Nombre de jours" 
            defaultValue={formData.droitConge || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Date de Départ" 
          description={fieldDescriptions.dDepart}
        >
          <input 
            id="dDepart" 
            name="dDepart" 
            type="date" 
            defaultValue={formData.dDepart || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
      
      <h4 className="text-lg font-semibold mt-6">Configurations</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Configuration IGR" 
          description={fieldDescriptions.confIGR}
        >
          <Select 
            name="confIGR" 
            defaultValue={formData.confIGR}
            onValueChange={(value) => updateFormData('confIGR', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration IGR" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration CNSS" 
          description={fieldDescriptions.confCNSS}
        >
          <Select 
            name="confCNSS" 
            defaultValue={formData.confCNSS}
            onValueChange={(value) => updateFormData('confCNSS', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration CNSS" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration Mutuelle" 
          description={fieldDescriptions.confMutuel}
        >
          <Select 
            name="confMutuel" 
            defaultValue={formData.confMutuel}
            onValueChange={(value) => updateFormData('confMutuel', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration Mutuelle" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration AT" 
          description={fieldDescriptions.confAT}
        >
          <Select 
            name="confAT" 
            defaultValue={formData.confAT}
            onValueChange={(value) => updateFormData('confAT', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration AT" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration BourB" 
          description={fieldDescriptions.confBourB}
        >
          <Select 
            name="confBourB" 
            defaultValue={formData.confBourB}
            onValueChange={(value) => updateFormData('confBourB', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration BourB" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration CIMR" 
          description={fieldDescriptions.confCIMR}
        >
          <Select 
            name="confCIMR" 
            defaultValue={formData.confCIMR}
            onValueChange={(value) => updateFormData('confCIMR', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration CIMR" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration COS" 
          description={fieldDescriptions.confCos}
        >
          <Select 
            name="confCos" 
            defaultValue={formData.confCos}
            onValueChange={(value) => updateFormData('confCos', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Configuration COS" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Configuration ALLFAM" 
          description={fieldDescriptions.confALLFAM}
        >
          <input 
            id="confALLFAM" 
            name="confALLFAM" 
            placeholder="Configuration ALLFAM" 
            defaultValue={formData.confALLFAM || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Date Du" 
          description={fieldDescriptions.dateDu}
        >
          <input 
            id="dateDu" 
            name="dateDu" 
            type="date" 
            defaultValue={formData.dateDu || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Date Au" 
          description={fieldDescriptions.dateAu}
        >
          <input 
            id="dateAu" 
            name="dateAu" 
            type="date" 
            defaultValue={formData.dateAu || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
    </div>
  )
}

export default BenefitsStep
