"use client"

import React from 'react'
import { useFormContext } from '../FormContext'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { InfoCircledIcon } from "@radix-ui/react-icons"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Field descriptions
const fieldDescriptions = {
  salaire: "Salaire de base",
  salaireBrut: "Salaire brut avant déductions",
  salaireNet: "Salaire net après déductions",
  tauxIR: "Taux d'imposition sur le revenu",
  tauxCNSS: "Taux de cotisation CNSS",
  tauxAMO: "Taux de l'Assurance Maladie Obligatoire",
  rib: "Relevé d'Identité Bancaire",
  modePaiement: "Mode de versement du salaire",
  banque: "Établissement bancaire",
  compteCompt: "Numéro de compte comptable",
  grid: "Grille salariale",
  echelonSalaire: "Échelon dans la grille salariale",
  congesMaladie: "Nombre de jours de congés maladie",
  congesPayes: "Nombre de jours de congés payés",
  congesMaternite: "Nombre de jours de congés maternité",
}

// Moroccan banks list
const moroccanBanks = [
  "Attijariwafa Bank",
  "Banque Populaire",
  "BMCE Bank",
  "Société Générale Maroc",
  "BMCI",
  "CIH Bank",
  "Crédit Agricole du Maroc",
  "CFG Bank",
  "Al Barid Bank",
  "Bank Al-Maghrib",
  "Crédit du Maroc",
  "Bank of Africa",
]

// Helper component for field with tooltip
const FieldWithTooltip = ({ label, description, children }: { label: string; description: string; children: React.ReactNode }) => (
  <div className="space-y-2">
    <div className="flex items-center space-x-2">
      <Label htmlFor={label}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <InfoCircledIcon className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p>{description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    {children}
  </div>
)

const FinancialInfoStep: React.FC = () => {
  const { updateFormData, getFormData } = useFormContext()
  const formData = getFormData()
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    updateFormData(name, type === 'number' ? (value === '' ? '' : Number(value)) : value)
  }
  
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Rémunération et Avantages</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <FieldWithTooltip 
          label="Salaire" 
          description={fieldDescriptions.salaire}
        >
          <input 
            id="salaire" 
            name="salaire" 
            type="number" 
            step="0.01" 
            placeholder="Salaire" 
            defaultValue={formData.salaire || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Salaire Brut" 
          description={fieldDescriptions.salaireBrut}
        >
          <input 
            id="salaireBrut" 
            name="salaireBrut" 
            type="number" 
            step="0.01" 
            placeholder="Salaire Brut" 
            defaultValue={formData.salaireBrut || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Salaire Net" 
          description={fieldDescriptions.salaireNet}
        >
          <input 
            id="salaireNet" 
            name="salaireNet" 
            type="number" 
            step="0.01" 
            placeholder="Salaire Net" 
            defaultValue={formData.salaireNet || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Taux IR (%)" 
          description={fieldDescriptions.tauxIR}
        >
          <input 
            id="tauxIR" 
            name="tauxIR" 
            type="number" 
            step="0.01" 
            placeholder="Taux IR (%)" 
            defaultValue={formData.tauxIR || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Taux CNSS (%)" 
          description={fieldDescriptions.tauxCNSS}
        >
          <input 
            id="tauxCNSS" 
            name="tauxCNSS" 
            type="number" 
            step="0.01" 
            placeholder="Taux CNSS (%)" 
            defaultValue={formData.tauxCNSS || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Taux AMO (%)" 
          description={fieldDescriptions.tauxAMO}
        >
          <input 
            id="tauxAMO" 
            name="tauxAMO" 
            type="number" 
            step="0.01" 
            placeholder="Taux AMO (%)" 
            defaultValue={formData.tauxAMO || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Congés Payés (jours)" 
          description={fieldDescriptions.congesPayes}
        >
          <input 
            id="congesPayes" 
            name="congesPayes" 
            type="number" 
            placeholder="Congés Payés (jours)" 
            defaultValue={formData.congesPayes || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Congés Maladie (jours)" 
          description={fieldDescriptions.congesMaladie}
        >
          <input 
            id="congesMaladie" 
            name="congesMaladie" 
            type="number" 
            placeholder="Congés Maladie (jours)" 
            defaultValue={formData.congesMaladie || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Congés Maternité (jours)" 
          description={fieldDescriptions.congesMaternite}
        >
          <input 
            id="congesMaternite" 
            name="congesMaternite" 
            type="number" 
            placeholder="Congés Maternité (jours)" 
            defaultValue={formData.congesMaternite || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="RIB (Relevé d'Identité Bancaire)" 
          description={fieldDescriptions.rib}
        >
          <input 
            id="rib" 
            name="rib" 
            placeholder="RIB" 
            defaultValue={formData.rib || ''}
            onChange={handleChange}
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Mode de Paiement" 
          description={fieldDescriptions.modePaiement}
        >
          <Select 
            name="modePaiement" 
            defaultValue={formData.modePaiement}
            onValueChange={(value) => updateFormData('modePaiement', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Mode de Paiement" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BANK_TRANSFER">Virement Bancaire</SelectItem>
              <SelectItem value="CHEQUE">Chèque</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Banque" 
          description={fieldDescriptions.banque}
        >
          <Select 
            name="banque" 
            defaultValue={formData.banque}
            onValueChange={(value) => updateFormData('banque', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Sélectionnez une banque" />
            </SelectTrigger>
            <SelectContent>
              {moroccanBanks.map((bank) => (
                <SelectItem key={bank} value={bank}>{bank}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Compte Comptable" 
          description={fieldDescriptions.compteCompt}
        >
          <input 
            id="compteCompt" 
            name="compteCompt" 
            placeholder="Compte comptable" 
            defaultValue={formData.compteCompt || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
    </div>
  )
}

export default FinancialInfoStep
