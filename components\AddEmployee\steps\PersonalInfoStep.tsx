"use client"

import React, { useEffect } from 'react'
import { useFormContext } from '../FormContext'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Textarea } from '@/components/ui/textarea'
import { InfoCircledIcon } from "@radix-ui/react-icons"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Field descriptions
const fieldDescriptions = {
  prenom: "Prénom de l'employé",
  nom: "Nom de famille de l'employé",
  email: "Adresse email professionnelle",
  telephone: "Numéro de téléphone principal",
  contactUrgence: "Personne à contacter en cas d'urgence",
  telephoneUrgence: "Numéro de téléphone d'urgence",
  dateNaissance: "Date de naissance de l'employé",
  genre: "Genre de l'employé",
  nationalite: "Nationalité de l'employé",
  numeroPasseport: "Numéro de passeport si applicable",
  adresse: "Adresse complète du domicile",
  ville: "Ville de résidence",
  pays: "Pays de résidence"
}

// Helper component for field with tooltip
const FieldWithTooltip = ({ label, description, children }: { label: string; description: string; children: React.ReactNode }) => (
  <div className="space-y-2">
    <div className="flex items-center space-x-2">
      <Label htmlFor={label}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <InfoCircledIcon className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p>{description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    {children}
  </div>
)

const PersonalInfoStep: React.FC = () => {
  const { updateFormData, getFormData } = useFormContext()
  const formData = getFormData()

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    updateFormData(name, type === 'number' ? (value === '' ? '' : Number(value)) : value)
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Informations Personnelles</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Prénom" 
          description={fieldDescriptions.prenom}
        >
          <input 
            id="prenom" 
            name="prenom" 
            placeholder="Prénom" 
            defaultValue={formData.prenom || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Nom" 
          description={fieldDescriptions.nom}
        >
          <input 
            id="nom" 
            name="nom" 
            placeholder="Nom" 
            defaultValue={formData.nom || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Email" 
          description={fieldDescriptions.email}
        >
          <input 
            id="email" 
            name="email" 
            type="email" 
            placeholder="Email" 
            defaultValue={formData.email || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Téléphone" 
          description={fieldDescriptions.telephone}
        >
          <input 
            id="telephone" 
            name="telephone" 
            placeholder="Téléphone" 
            defaultValue={formData.telephone || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <div className="space-y-4 col-span-2">
          <h4 className="text-lg font-semibold">Contact d'Urgence</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FieldWithTooltip 
              label="Nom du Contact d'Urgence" 
              description={fieldDescriptions.contactUrgence}
            >
              <input 
                id="contactUrgence" 
                name="contactUrgence" 
                placeholder="Nom du contact d'urgence" 
                defaultValue={formData.contactUrgence || ''}
                onChange={handleChange}
                className="border p-2 rounded w-full"
              />
            </FieldWithTooltip>
            
            <FieldWithTooltip 
              label="Téléphone d'Urgence" 
              description={fieldDescriptions.telephoneUrgence}
            >
              <input 
                id="telephoneUrgence" 
                name="telephoneUrgence" 
                placeholder="Téléphone d'urgence" 
                defaultValue={formData.telephoneUrgence || ''}
                onChange={handleChange}
                className="border p-2 rounded w-full"
              />
            </FieldWithTooltip>
          </div>
        </div>
        
        <FieldWithTooltip 
          label="Date de Naissance" 
          description={fieldDescriptions.dateNaissance}
        >
          <input 
            id="dateNaissance" 
            name="dateNaissance" 
            type="date" 
            defaultValue={formData.dateNaissance || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Genre" 
          description={fieldDescriptions.genre}
        >
          <RadioGroup 
            name="genre" 
            className="flex space-x-4" 
            defaultValue={formData.genre}
            onValueChange={(value) => updateFormData('genre', value)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="homme" id="homme" />
              <Label htmlFor="homme">Homme</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="femme" id="femme" />
              <Label htmlFor="femme">Femme</Label>
            </div>
          </RadioGroup>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Nationalité" 
          description={fieldDescriptions.nationalite}
        >
          <input 
            id="nationalite" 
            name="nationalite" 
            placeholder="Nationalité" 
            defaultValue={formData.nationalite || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Numéro de Passeport" 
          description={fieldDescriptions.numeroPasseport}
        >
          <input 
            id="numeroPasseport" 
            name="numeroPasseport" 
            placeholder="Numéro de Passeport" 
            defaultValue={formData.numeroPasseport || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
      
      <FieldWithTooltip 
        label="Adresse" 
        description={fieldDescriptions.adresse}
      >
        <Textarea 
          id="adresse" 
          name="adresse" 
          placeholder="Adresse" 
          defaultValue={formData.adresse || ''}
          className="w-full" 
          onChange={handleChange} 
        />
      </FieldWithTooltip>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Ville" 
          description={fieldDescriptions.ville}
        >
          <input 
            id="ville" 
            name="ville" 
            placeholder="Ville" 
            defaultValue={formData.ville || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Pays" 
          description={fieldDescriptions.pays}
        >
          <input 
            id="pays" 
            name="pays" 
            placeholder="Pays" 
            defaultValue={formData.pays || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
    </div>
  )
}

export default PersonalInfoStep
