"use client"

import React, { useState, useEffect } from 'react'
import { useFormContext } from '../FormContext'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { InfoCircledIcon } from "@radix-ui/react-icons"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Field descriptions
const fieldDescriptions = {
  societe: "Nom ou code de la société",
  service: "Service ou département principal",
  nomService: "Nom complet du service",
  division: "Division ou unité organisationnelle",
  matricule: "Numéro d'identification unique de l'employé",
  departement: "Département de rattachement",
  poste: "Intitulé du poste occupé",
  dateDebut: "Date de début du contrat",
  typeEmploi: "Type de contrat de travail",
  dateFinContrat: "Date de fin pour les CDD",
  periodeEssai: "Durée de la période d'essai en jours",
  competences: "Compétences principales de l'employé",
  modeTravaill: "Mode de travail (présentiel, hybride, télétravail)"
}

// Helper component for field with tooltip
const FieldWithTooltip = ({ label, description, children }: { label: string; description: string; children: React.ReactNode }) => (
  <div className="space-y-2">
    <div className="flex items-center space-x-2">
      <Label htmlFor={label}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <InfoCircledIcon className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p>{description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    {children}
  </div>
)

const ProfessionalInfoStep: React.FC = () => {
  const { updateFormData, getFormData, handleCompetenceToggle } = useFormContext()
  const formData = getFormData()
  
  // Local state to force re-render for conditional fields
  const [typeEmploi, setTypeEmploi] = useState(formData.typeEmploi || '')
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    updateFormData(name, type === 'number' ? (value === '' ? '' : Number(value)) : value)
  }
  
  // Check if date fin contrat should be shown
  const shouldShowDateFinContrat = typeEmploi === 'cdd'
  
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Informations Professionnelles</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FieldWithTooltip 
          label="Société" 
          description={fieldDescriptions.societe}
        >
          <input 
            id="societe" 
            name="societe" 
            placeholder="Nom de la société" 
            defaultValue={formData.societe || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Service" 
          description={fieldDescriptions.service}
        >
          <input 
            id="service" 
            name="service" 
            placeholder="Service" 
            defaultValue={formData.service || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Nom du Service" 
          description={fieldDescriptions.nomService}
        >
          <input 
            id="nomService" 
            name="nomService" 
            placeholder="Nom du service" 
            defaultValue={formData.nomService || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Division" 
          description={fieldDescriptions.division}
        >
          <input 
            id="division" 
            name="division" 
            placeholder="Division" 
            defaultValue={formData.division || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Matricule" 
          description={fieldDescriptions.matricule}
        >
          <input 
            id="matricule" 
            name="matricule" 
            placeholder="Matricule" 
            defaultValue={formData.matricule || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Département" 
          description={fieldDescriptions.departement}
        >
          <Select 
            name="departement" 
            defaultValue={formData.departement}
            onValueChange={(value) => updateFormData('departement', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Département" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="rh">Ressources Humaines</SelectItem>
              <SelectItem value="it">Technologie de l'Information</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
              <SelectItem value="operations">Opérations</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Poste" 
          description={fieldDescriptions.poste}
        >
          <input 
            id="poste" 
            name="poste" 
            placeholder="Poste" 
            defaultValue={formData.poste || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Date de Début" 
          description={fieldDescriptions.dateDebut}
        >
          <input 
            id="dateDebut" 
            name="dateDebut" 
            type="date" 
            defaultValue={formData.dateDebut || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
        
        <FieldWithTooltip 
          label="Type d'Emploi" 
          description={fieldDescriptions.typeEmploi}
        >
          <Select 
            name="typeEmploi" 
            defaultValue={formData.typeEmploi} 
            onValueChange={(value) => {
              updateFormData('typeEmploi', value)
              setTypeEmploi(value) // Update local state to force re-render
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Type d'Emploi" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cdi">CDI</SelectItem>
              <SelectItem value="cdd">CDD</SelectItem>
              <SelectItem value="interim">Intérim</SelectItem>
              <SelectItem value="stage">Stage</SelectItem>
            </SelectContent>
          </Select>
        </FieldWithTooltip>
        
        {shouldShowDateFinContrat && (
          <FieldWithTooltip 
            label="Date de Fin de Contrat" 
            description={fieldDescriptions.dateFinContrat}
          >
            <input 
              id="dateFinContrat" 
              name="dateFinContrat" 
              type="date" 
              defaultValue={formData.dateFinContrat || ''} 
              onChange={handleChange}
              className="border p-2 rounded w-full" 
            />
          </FieldWithTooltip>
        )}
        
        <FieldWithTooltip 
          label="Période d'Essai (jours)" 
          description={fieldDescriptions.periodeEssai}
        >
          <input 
            id="periodeEssai" 
            name="periodeEssai" 
            type="number" 
            placeholder="Période d'Essai (jours)" 
            defaultValue={formData.periodeEssai || ''}
            onChange={handleChange} 
            className="border p-2 rounded w-full"
          />
        </FieldWithTooltip>
      </div>
      
      <FieldWithTooltip 
        label="Compétences" 
        description={fieldDescriptions.competences}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
          {['Communication', 'Leadership', 'Résolution de problèmes', 'Travail d\'équipe', 'Gestion du temps'].map((competence) => (
            <div key={competence} className="flex items-center space-x-2">
              <Checkbox
                id={competence}
                checked={formData.competences?.includes(competence)}
                onCheckedChange={() => handleCompetenceToggle(competence)}
              />
              <Label htmlFor={competence} className="text-sm">{competence}</Label>
            </div>
          ))}
        </div>
      </FieldWithTooltip>
      
      <FieldWithTooltip 
        label="Mode de Travail" 
        description={fieldDescriptions.modeTravaill}
      >
        <Select 
          name="modeTravaill" 
          defaultValue={formData.modeTravaill}
          onValueChange={(value) => updateFormData('modeTravaill', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Sélectionnez le mode de travail" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="remote">Télétravail</SelectItem>
            <SelectItem value="hybrid">Hybride</SelectItem>
            <SelectItem value="office">Bureau</SelectItem>
          </SelectContent>
        </Select>
      </FieldWithTooltip>
    </div>
  )
}

export default ProfessionalInfoStep
