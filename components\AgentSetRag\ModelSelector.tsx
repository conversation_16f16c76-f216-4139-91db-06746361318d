import React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, <PERSON>rkles, Zap, Stars, Cpu } from 'lucide-react';
import { cn } from '@/lib/utils';

export type AgentSetModel = 
  // Cohere models
  'command-r' | 'command-r-plus' |
  // Together AI models
  'llama-3-70b' | 'llama-3-8b' | 'mixtral-8x7b' | 'mistral-7b' | 'qwen-72b';

interface ModelInfo {
  id: AgentSetModel;
  name: string;
  description: string;
  icon: React.ElementType;
  color: string;
  provider: 'cohere' | 'together';
}

const MODELS: ModelInfo[] = [
  // Cohere models
  {
    id: 'command-r',
    name: 'Cohere Command-R',
    description: 'Fast and efficient for most tasks',
    icon: Brain,
    color: 'text-green-500',
    provider: 'cohere'
  },
  {
    id: 'command-r-plus',
    name: 'Cohere Command-R+',
    description: 'Enhanced reasoning and comprehension',
    icon: Stars,
    color: 'text-green-600',
    provider: 'cohere'
  },
  // Together AI models
  {
    id: 'llama-3-70b',
    name: 'Llama 3 (70B)',
    description: 'Meta\'s largest open model with strong reasoning',
    icon: Sparkles,
    color: 'text-blue-500',
    provider: 'together'
  },
  {
    id: 'llama-3-8b',
    name: 'Llama 3 (8B)',
    description: 'Efficient and fast open model',
    icon: Zap,
    color: 'text-blue-400',
    provider: 'together'
  },
  {
    id: 'mixtral-8x7b',
    name: 'Mixtral 8x7B',
    description: 'Powerful mixture-of-experts model',
    icon: Brain,
    color: 'text-purple-500',
    provider: 'together'
  },
  {
    id: 'mistral-7b',
    name: 'Mistral 7B',
    description: 'Efficient model with strong performance',
    icon: Cpu,
    color: 'text-purple-400',
    provider: 'together'
  },
  {
    id: 'qwen-72b',
    name: 'Qwen 72B',
    description: 'Large multilingual model with strong capabilities',
    icon: Stars,
    color: 'text-amber-500',
    provider: 'together'
  }
];

interface ModelSelectorProps {
  value: AgentSetModel;
  onChange: (value: AgentSetModel) => void;
}

export function ModelSelector({ value, onChange }: ModelSelectorProps) {
  return (
    <Select value={value} onValueChange={(v) => onChange(v as AgentSetModel)}>
      <SelectTrigger className="w-[250px]">
        <SelectValue placeholder="Select a model" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Cohere Models</SelectLabel>
          {MODELS.filter(m => m.provider === 'cohere').map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center gap-2">
                <model.icon className={cn("h-4 w-4", model.color)} />
                <span>{model.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
        <SelectGroup>
          <SelectLabel>Together AI Models</SelectLabel>
          {MODELS.filter(m => m.provider === 'together').map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center gap-2">
                <model.icon className={cn("h-4 w-4", model.color)} />
                <span>{model.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}

export default ModelSelector;
