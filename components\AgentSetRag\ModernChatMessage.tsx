"use client"

import React, { useState } from 'react';
import { AgentSetChatMessage } from '@/hooks/useAgentSetRag';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Copy, Check, Bot, User, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface ModernChatMessageProps {
  message: AgentSetChatMessage;
  isLast: boolean;
}

const ModernChatMessage: React.FC<ModernChatMessageProps> = ({ message, isLast }) => {
  const [copied, setCopied] = useState(false);
  
  const isUser = message.role === 'user';
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
   // Extract references from the message content using string split method
  const extractReferences = (content: string) => {
    // First split by [Source: to get all potential references
    const parts = content.split('[Source: ');
    
    if (parts.length <= 1) return null; // No sources found
    
    // Process each part to extract the source
    const sources = new Set<string>();
    for (let i = 1; i < parts.length; i++) {
      const end = parts[i].indexOf(']');
      if (end !== -1) {
        const source = parts[i].substring(0, end).trim();
        if (source) {
          sources.add(source);
        }
      }
    }
    
    return sources.size > 0 ? Array.from(sources) : null;
  };

  const references = !isUser ? extractReferences(message.content) : null;
  
  // Replace [Source: X] with a styled span
  const formatContentWithSources = (content: string) => {
    return content.replace(/\[Source: ([^\]]+)\]/g, '');
  };
  
  const formattedContent = !isUser ? formatContentWithSources(message.content) : message.content;
  
  return (
    <div className={cn(
      "group relative mb-4 flex items-start gap-3",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar */}
      <Avatar className={cn(
        "h-8 w-8 rounded-md",
        isUser ? "bg-primary" : "bg-secondary"
      )}>
        {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
      </Avatar>
      
      {/* Message Content */}
      <Card className={cn(
        "relative px-4 py-3 max-w-[85%] shadow-sm",
        isUser ? "bg-primary text-primary-foreground" : "bg-card"
      )}>
        {/* Copy button */}
        {!isUser && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={copyToClipboard}
          >
            {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
          </Button>
        )}
        
        {/* Message text */}
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              // @ts-ignore
              code({ node, inline, className, children, ...props }) {
                const match = /language-(\w+)/.exec(className || '');
                return !inline && match ? (
                  <SyntaxHighlighter
                  // @ts-ignore
                    style={vscDarkPlus}
                    language={match[1]}
                    PreTag="div"
                    {...props}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                ) : (
                  <code className={className} {...props}>
                    {children}
                  </code>
                );
              },
              h1: ({ children }) => <h1 className="text-xl font-bold mt-4 mb-2">{children}</h1>,
              h2: ({ children }) => <h2 className="text-lg font-bold mt-3 mb-2">{children}</h2>,
              h3: ({ children }) => <h3 className="text-md font-bold mt-2 mb-1">{children}</h3>,
              ul: ({ children }) => <ul className="list-disc pl-6 my-2">{children}</ul>,
              ol: ({ children }) => <ol className="list-decimal pl-6 my-2">{children}</ol>,
              li: ({ children }) => <li className="my-1">{children}</li>,
              p: ({ children }) => <p className="my-2">{children}</p>,
            }}
          >
            {formattedContent}
          </ReactMarkdown>
        </div>
        
        {/* References section */}
        {references && references.length > 0 && (
          <>
            <Separator className="my-2" />
            <div className="mt-2">
              <h4 className="text-xs font-semibold mb-1">References:</h4>
              <ul className="text-xs space-y-1">
                {references.map((source, index) => (
                  <li key={index} className="flex items-center gap-1">
                    <ExternalLink className="h-3 w-3" />
                    <span>{source}</span>
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default ModernChatMessage;
