'use client';

import { useEffect, useState } from 'react';
import { useAuth, useUser } from '@clerk/nextjs';
import { toast } from 'sonner';


export default function AuthProvider() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    if (isLoaded && isSignedIn && user && !hasChecked) {
      const checkAndEnsureUser = async () => {
        try {
          const response = await fetch('/api/auth/ensure-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: user.primaryEmailAddress?.emailAddress,
              name: `${user.firstName} ${user.lastName}`.trim(),
            }),
          });
          
          const data = await response.json();
          
          if (data.success) {
            toast.success('Successfully signed in and synced with database!');
          } else {
            toast.error('Error syncing user data');
          }
        } catch (error) {
          console.error('Error ensuring user:', error);
          toast.error('Error connecting to the database');
        } finally {
          setHasChecked(true);
        }
      };

      checkAndEnsureUser();
    }
  }, [isLoaded, isSignedIn, user, hasChecked]);

  return null;
} 