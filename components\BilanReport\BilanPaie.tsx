"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TableTab } from "../Datanalytics/TableTab";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "../ui/button";
import { TableIcon } from "lucide-react";
import { useState } from "react";
import { Pagination, PaginationContent, PaginationItem, PaginationPrevious, PaginationNext } from "../ui/pagination";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import dynamic from 'next/dynamic';
import { toast } from "sonner";
import { X } from "lucide-react";

// Dynamic imports
const ReactEChartsDynamic = dynamic(() => import('echarts-for-react'), {
  ssr: false,
  loading: () => <div className="h-[400px] flex items-center justify-center">Loading chart...</div>
});

const EmployeeSalaryAnalysisDynamic = dynamic(
  () => import('./EmployeeSalaryAnalysis'),
  {
    ssr: false,
    loading: () => <div className="p-8 text-center">Loading analysis...</div>
  }
);

// Update DialogContent styles or create custom dialog
const CustomDialog = ({
  isOpen,
  onClose,
  children
}: {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed inset-4 z-50 flex flex-col rounded-lg border bg-background shadow-lg">
        <div className="flex items-center justify-between p-2 border-b">
          <h2 className="text-lg font-semibold">Dataset View</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default function BilanPaie({ data }: { data: any[] }) {
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [editingData, setEditingData] = useState<any | null>(null);

  if (!data || data.length === 0) {
    return (
      <Card className="p-6 text-center text-muted-foreground">
        No payroll data available
      </Card>
    );
  }

  // Format number helper
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("fr-MA", {
      maximumFractionDigits: 2,
    }).format(num);
  };

  // Process data for charts
  const months = ["Jan-25", "Feb-25", "Mar-25", "Apr-25", "May-25", "Jun-25",
                 "Jul-25", "Aug-25", "Sep-25", "Oct-25", "Nov-25", "Dec-25"];

  // Calculate total salary per employee
  const employeeTotalSalaries = data.reduce((acc, item) => {
    if (item.Rubrique === "940") { // Changed from "001" to "940" for NET A PAYER
      acc[item["Nom & Prénom"]] = months.reduce((sum, month) => sum + Number(item[month]), 0);
    }
    return acc;
  }, {} as Record<string, number>);

  // Get unique employees
  const uniqueEmployees = Array.from(new Set(data.map(item => item["Nom & Prénom"])));

  // Pagination logic
  const totalPages = Math.ceil(uniqueEmployees.length / itemsPerPage);
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages;

  const paginatedEmployees = uniqueEmployees.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Employee Monthly Salary Bar Chart
  const employeeMonthlySalaryChart = {
    title: {
      text: 'Employee Total Salary Comparison',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any) => {
        const employee = params[0].name;
        const salary = params[0].value;
        return `${employee}<br/>Total Salary: ${formatNumber(salary)} MAD`;
      }
    },
    grid: {
      left: '15%',  // Increased to accommodate employee names
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => `${formatNumber(value)} MAD`
      }
    },
    yAxis: {
      type: 'category',
      data: uniqueEmployees,
      axisLabel: {
        width: 120,
        overflow: 'truncate',
        interval: 0
      }
    },
    series: [{
      name: 'Total Salary',
      type: 'bar',
      data: uniqueEmployees.map(employee => ({
        name: employee,
        value: employeeTotalSalaries[employee] || 0
      })),
      itemStyle: {
        color: '#4f46e5'
      },
      label: {
        show: true,
        position: 'right',
        formatter: (params: any) => formatNumber(params.value)
      }
    }],
    dataZoom: [{
      type: 'slider',
      yAxisIndex: 0,
      right: 0,
      start: 0,
      end: Math.min(100, (10 / uniqueEmployees.length) * 100), // Show first 10 employees
      zoomLock: true,
      handleSize: '80%',
      showDetail: false
    }]
  };

  // Employee Salary Components Breakdown
  const employeeComponentsChart = {
    title: {
      text: 'Salary Components by Employee',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      type: 'scroll',
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => `${formatNumber(value)} MAD`
      }
    },
    yAxis: {
      type: 'category',
      data: uniqueEmployees,
      axisLabel: {
        width: 100,
        overflow: 'truncate'
      }
    },
    series: Array.from(new Set(data.map(item => item.Libelle))).map(libelle => ({
      name: libelle,
      type: 'bar',
      stack: 'total',
      data: uniqueEmployees.map(emp =>
        data.find(item =>
          item["Nom & Prénom"] === emp &&
          item.Libelle === libelle
        )?.Total || 0
      )
    })),
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }, {
      start: 0,
      end: 100,
      handleSize: '80%'
    }]
  };

  // Salary Distribution by Component
  const componentDistributionChart = {
    title: {
      text: 'Salary Components Distribution',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} MAD ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false
      },
      emphasis: {
        label: {
          show: true,
          formatter: '{b}: {c} MAD'
        }
      },
      data: Object.entries(
        data.reduce((acc, item) => {
          acc[item.Libelle] = (acc[item.Libelle] || 0) + Number(item.Total);
          return acc;
        }, {} as Record<string, number>)
      ).map(([name, value]) => ({
        name,
        value,
      }))
    }]
  };

  // Monthly Trends Chart
  const monthlyTrendsChart = {
    title: {
      text: 'Monthly Payroll Trends',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['Total Payroll', 'Average Salary'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => `${formatNumber(value)} MAD`
      }
    },
    series: [
      {
        name: 'Total Payroll',
        type: 'line',
        smooth: true,
        data: months.map(month =>
          data.reduce((sum, item) => sum + Number(item[month]), 0)
        ),
        areaStyle: {
          opacity: 0.1
        }
      },
      {
        name: 'Average Salary',
        type: 'line',
        smooth: true,
        data: months.map(month =>
          data.reduce((sum, item) => sum + Number(item[month]), 0) / uniqueEmployees.length
        ),
        areaStyle: {
          opacity: 0.1
        }
      }
    ],
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }, {
      start: 0,
      end: 100,
      handleSize: '80%'
    }]
  };

  // Employee Monthly Salary Line Chart
  const employeeMonthlySalaryLineChart = {
    title: {
      text: `Monthly Salary for ${selectedEmployee}`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['Monthly Salary'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => `${formatNumber(value)} MAD`
      }
    },
    series: [
      {
        name: 'Monthly Salary',
        type: 'line',
        smooth: true,
        data: selectedEmployee ? months.map(month =>
          data.find(item =>
            item["Nom & Prénom"] === selectedEmployee &&
            item.Rubrique === "940" // Changed from "001" to "940"
          )?.[month] || 0
        ) : [],
        areaStyle: {
          opacity: 0.1
        }
      }
    ],
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }, {
      start: 0,
      end: 100,
      handleSize: '80%'
    }]
  };

  // Dataset Table Button Component
  const DatasetTableButton = () => {
    const handleSaveChanges = (updatedData: any[]) => {
      // Handle saving changes to the dataset
      console.log("Saving changes:", updatedData);
      toast.success("Changes saved successfully");
    };

    return (
      <>
        <Button
          variant="outline"
          className="fixed bottom-4 right-4 gap-2 shadow-lg z-40"
          onClick={() => setIsFullScreen(true)}
        >
          <TableIcon className="h-4 w-4" />
          View Dataset
        </Button>

        <CustomDialog
          isOpen={isFullScreen}
          onClose={() => setIsFullScreen(false)}
        >
          <div className="h-full overflow-hidden">
            <TableTab
              data={data}
              headers={Object.keys(data[0] || {})}
              onSave={handleSaveChanges}
            />
          </div>
        </CustomDialog>
      </>
    );
  };

  return (
    <div className="space-y-4"> {/* Reduced from space-y-6 */}
      <Tabs defaultValue="overview" className="space-y-4"> {/* Reduced from space-y-6 */}
        <TabsList className="h-8"> {/* Added height constraint */}
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="employee">Employee Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4"> {/* Reduced from space-y-6 */}
          <DatasetTableButton />

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2"> {/* Reduced from gap-4 */}
            <Card className="overflow-hidden"> {/* Added overflow-hidden */}
              <CardHeader className="p-3"> {/* Reduced from p-4 */}
                <CardTitle className="text-sm">Total Employees</CardTitle>
                <CardDescription className="text-xs">Number of unique employees</CardDescription>
              </CardHeader>
              <CardContent className="p-3 pt-0"> {/* Reduced padding */}
                <div className="text-xl font-bold">{uniqueEmployees.length}</div>
              </CardContent>
            </Card>
            <Card className="overflow-hidden"> {/* Added overflow-hidden */}
              <CardHeader className="p-3"> {/* Reduced from p-4 */}
                <CardTitle className="text-sm">Total Annual Payroll</CardTitle>
                <CardDescription className="text-xs">Total payroll cost for the year.</CardDescription>
              </CardHeader>
              <CardContent className="p-3 pt-0"> {/* Reduced padding */}
                <div className="text-xl font-bold">
                  {formatNumber(data.reduce((sum, item) => sum + (Number(item.Total) || 0), 0))} MAD
                </div>
              </CardContent>
            </Card>
            <Card className="overflow-hidden"> {/* Added overflow-hidden */}
              <CardHeader className="p-3"> {/* Reduced from p-4 */}
                <CardTitle className="text-sm">Average Monthly Salary</CardTitle>
                <CardDescription className="text-xs">Average monthly salary per employee.</CardDescription>
              </CardHeader>
              <CardContent className="p-3 pt-0"> {/* Reduced padding */}
                <div className="text-xl font-bold">
                  {formatNumber(
                    data.reduce((sum, item) => sum + (Number(item.Total) || 0), 0) /
                    (12 * uniqueEmployees.length)
                  )} MAD
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Employee Table and Selected Employee Chart */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2"> {/* Reduced from gap-6 */}
            <Card>
              <CardHeader className="p-3"> {/* Reduced padding */}
                <CardTitle className="text-sm">Employee Net Salary</CardTitle>
                <CardDescription className="text-xs">Click to view breakdown</CardDescription>
              </CardHeader>
              <CardContent className="p-2"> {/* Reduced padding */}
                <Table>
                  <TableHeader>
                    <TableRow className="h-8"> {/* Added height constraint */}
                      <TableHead className="py-1">Employee Name</TableHead>
                      <TableHead className="text-right py-1">Total Salary (MAD)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedEmployees.map(employee => (
                      <TableRow
                        key={employee}
                        onClick={() => setSelectedEmployee(employee)}
                        className={cn(
                          "cursor-pointer hover:bg-gray-100",
                          selectedEmployee === employee && "bg-gray-100"
                        )}
                      >
                        <TableCell>{employee}</TableCell>
                        <TableCell className="text-right">
                          {formatNumber(employeeTotalSalaries[employee])}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <Pagination className="mt-2"> {/* Reduced from mt-4 */}
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => !isFirstPage && setCurrentPage(prev => prev - 1)}
                        className={cn(
                          currentPage === 1 && "pointer-events-none opacity-50"
                        )}
                      />
                    </PaginationItem>
                    <PaginationItem>
                      <span className="px-4">{currentPage} of {totalPages}</span>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => !isLastPage && setCurrentPage(prev => prev + 1)}
                        className={cn(
                          currentPage === totalPages && "pointer-events-none opacity-50"
                        )}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </CardContent>
            </Card>

            {selectedEmployee ? (
              <Card>
                <CardHeader className="p-3"> {/* Reduced padding */}
                  <CardTitle className="text-sm">{`Monthly Salary for ${selectedEmployee}`}</CardTitle>
                  <CardDescription className="text-xs">Monthly breakdown</CardDescription>
                </CardHeader>
                <CardContent className="p-2"> {/* Reduced padding */}
                  <ReactEChartsDynamic
                    option={employeeMonthlySalaryLineChart}
                    style={{ height: '300px' }}
                  />
                </CardContent>
              </Card>
            ) : (
              <Card className="flex items-center justify-center">
                <CardContent className="py-4 px-2 text-center text-muted-foreground">
                  <TableIcon className="mx-auto h-6 w-6 mb-2 opacity-50" />
                  <p className="text-xs">Select an employee to view details</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Main Charts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2"> {/* Reduced from gap-6 */}
            <Card>
              <CardHeader className="p-3"> {/* Reduced padding */}
                <CardTitle className="text-sm">Monthly Payroll Overview</CardTitle>
                <CardDescription className="text-xs">Monthly distribution</CardDescription>
              </CardHeader>
              <CardContent className="p-2"> {/* Reduced padding */}
                <ReactEChartsDynamic
                  option={employeeMonthlySalaryChart}
                  style={{ height: '300px' }}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-3"> {/* Reduced padding */}
                <CardTitle className="text-sm">Employee Salary Breakdown</CardTitle>
                <CardDescription className="text-xs">Salary components per employee.</CardDescription>
              </CardHeader>
              <CardContent className="p-2"> {/* Reduced padding */}
                <ReactEChartsDynamic
                  option={employeeComponentsChart}
                  style={{ height: '300px' }}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-3"> {/* Reduced padding */}
                <CardTitle className="text-sm">Components Distribution</CardTitle>
                <CardDescription className="text-xs">Overall salary components breakdown.</CardDescription>
              </CardHeader>
              <CardContent className="p-2"> {/* Reduced padding */}
                <ReactEChartsDynamic
                  option={componentDistributionChart}
                  style={{ height: '300px' }}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-3"> {/* Reduced padding */}
                <CardTitle className="text-sm">Monthly Trends</CardTitle>
                <CardDescription className="text-xs">Total and average salary trends over time.</CardDescription>
              </CardHeader>
              <CardContent className="p-2"> {/* Reduced padding */}
                <ReactEChartsDynamic
                  option={monthlyTrendsChart}
                  style={{ height: '300px' }}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="employee">
          <EmployeeSalaryAnalysisDynamic data={data} />
        </TabsContent>
      </Tabs>
    </div>
  );
}