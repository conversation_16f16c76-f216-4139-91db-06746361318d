"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Users, Briefcase, Wallet, Calendar } from "lucide-react";
import ReactECharts from "echarts-for-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>et<PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import { TableTab } from "../Datanalytics/TableTab";
import { Button } from "@/components/ui/button";
import { Table as TableIcon } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import '../../app/hr/bilan/scrollsheet.css'
import { toast } from "sonner";
import { CustomDialog } from "./CustomDialog";


const formatNumber = (num: number): string => {
  return new Intl.NumberFormat("fr-MA", {
    maximumFractionDigits: 2,
  }).format(num);
};

interface BilanData {
  matricule: string;
  nom: string;
  service: string;
  sitfam: string;
  NOMINDIC4: string;
  sex: string;
  ville: string;
  Categorie: string;
  Salaire: number;
  mutuel: string;
  cimr: string;
  DroitConge: number;
  Qualification: string;
  Societe: string;
  NomService: string;
  PAYS: string;
  cnss: string;
  dentree: string;
  nenf: number;
  nat: string;
  Division: string;
  tauxcimr: number;
  Tauxmutuel: number;
  NOMBANQUE: string;
  Dan: string;
  CompteCompt: string;
  DateRelica: string;
}



export default function BilanRe({ data }: { data: BilanData[] }) {
  const [filterType, setFilterType] = useState<"department" | "city" | "contract" | "all">("all");
  const [filterValue, setFilterValue] = useState("all");
  const [filteredData, setFilteredData] = useState(data);
  const [isFullScreen, setIsFullScreen] = useState(false); // Add this line

  useEffect(() => {
    if (filterType === "all" || filterValue === "all") {
      setFilteredData(data);
      return;
    }

    setFilteredData(
      data.filter((emp) => {
        switch (filterType) {
          case "department":
            return emp.Qualification === filterValue;
          case "city":
            return emp.ville === filterValue;
          case "contract":
            return emp.NOMINDIC4 === filterValue;
          default:
            return true;
        }
      })
    );
  }, [filterType, filterValue, data]);

  // HR Analytics Functions
  const getHRStats = () => {
    const total = filteredData.length;
    return {
      total,
      gender: {
        male: filteredData.filter((emp) => emp.sex?.toLowerCase() === "masculin").length,
        female: filteredData.filter((emp) => emp.sex?.toLowerCase() === "féminin").length,
      },
      marital: {
        married: filteredData.filter((emp) => emp.sitfam?.toLowerCase().includes("marié")).length,
        single: filteredData.filter((emp) => emp.sitfam?.toLowerCase().includes("célibataire")).length,
        other:
          total -
          filteredData.filter((emp) => emp.sitfam?.toLowerCase().includes("marié") || emp.sitfam?.toLowerCase().includes("célibataire")).length,
      },
      benefits: {
        cimr: filteredData.filter((emp) => emp.cimr).length,
        mutuel: filteredData.filter((emp) => emp.mutuel).length,
      },
      contracts: {
        cdi: filteredData.filter((emp) => emp.NOMINDIC4 === "CDI").length,
        cdd: filteredData.filter((emp) => emp.NOMINDIC4 === "CDD").length,
      },
      categories: Object.entries(
        filteredData.reduce((acc, emp) => {
          acc[emp.Categorie] = (acc[emp.Categorie] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      ).sort(([, a], [, b]) => b - a),
      cities: Object.entries(
        filteredData.reduce((acc, emp) => {
          acc[emp.ville] = (acc[emp.ville] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      ).sort(([, a], [, b]) => b - a),
      salary: {
        avg: filteredData.reduce((acc, emp) => acc + (emp.Salaire || 0), 0) / total,
        max: Math.max(...filteredData.map((emp) => emp.Salaire || 0)),
        topEarners: filteredData.sort((a, b) => (b.Salaire || 0) - (a.Salaire || 0)).slice(0, 5),
      },
      leave: {
        avg: filteredData.reduce((acc, emp) => acc + (emp.DroitConge || 0), 0) / total,
      },
    };
  };

  // Salary Ranges Calculation
  const calculateSalaryRanges = () => {
    const ranges = [
      { min: 0, max: 5000 },
      { min: 5000, max: 10000 },
      { min: 10000, max: 15000 },
      { min: 15000, max: 20000 },
      { min: 20000, max: Infinity },
    ];

    const distribution = new Array(ranges.length).fill(0);

    filteredData.forEach((emp) => {
      const salary = emp.Salaire || 0;
      for (let i = 0; i < ranges.length; i++) {
        if (salary >= ranges[i].min && salary < ranges[i].max) {
          distribution[i]++;
          break;
        }
      }
    });

    return distribution;
  };

  // Role-based Salary Data
  const getRoleSalaryData = () => {
    const roleData = filteredData.reduce((acc, emp) => {
      if (!acc[emp.Categorie]) {
        acc[emp.Categorie] = {
          role: emp.Categorie,
          totalSalary: 0,
          count: 0,
          maxSalary: 0,
          minSalary: Infinity,
        };
      }
      acc[emp.Categorie].totalSalary += emp.Salaire || 0;
      acc[emp.Categorie].count++;
      acc[emp.Categorie].maxSalary = Math.max(acc[emp.Categorie].maxSalary, emp.Salaire || 0);
      acc[emp.Categorie].minSalary = Math.min(acc[emp.Categorie].minSalary, emp.Salaire || 0);
      return acc;
    }, {} as Record<string, { role: string; totalSalary: number; count: number; maxSalary: number; minSalary: number }>);

    return Object.values(roleData)
      .map((item) => ({
        role: item.role,
        avgSalary: item.totalSalary / item.count,
        maxSalary: item.maxSalary,
        minSalary: item.minSalary,
        difference: item.maxSalary - item.minSalary,
      }))
      .sort((a, b) => b.avgSalary - a.avgSalary);
  };



  const bankDistributionChart = {
    title: {
      text: 'Bank Distribution',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: { trigger: 'axis' },
    legend: { bottom: 'bottom' },
    xAxis: {
      type: 'category',
      data: Array.from(new Set(data.map(emp => emp.NOMBANQUE))).filter(Boolean),
      axisLabel: { rotate: 45 }
    },
    yAxis: { type: 'value' },
    series: [{
      name: 'Employees',
      type: 'bar',
      data: Array.from(new Set(data.map(emp => emp.NOMBANQUE)))
        .filter(Boolean)
        .map(bank => ({
          value: data.filter(emp => emp.NOMBANQUE === bank).length,
          itemStyle: { color: '#6366f1' }
        }))
    }]
  };

  const departmentChart = {
    title: { text: "Department Distribution", left: "center" },
    tooltip: { trigger: "axis" },
    legend: { data: ["Employees"], bottom: "bottom" },
    xAxis: {
      type: "category",
      data: getHRStats().categories.map(([dept]) => dept),
      axisLabel: { rotate: 45 },
    },
    yAxis: { type: "value" },
    series: [
      {
        name: "Employees",
        type: "bar",
        data: getHRStats().categories.map(([, count]) => count),
      },
    ],
  };

  const benefitsChart = {
    title: { text: "Benefits Coverage", left: "center" },
    tooltip: { trigger: "axis" },
    legend: { bottom: "bottom" },
    radar: {
      indicator: [
        { name: "CIMR", max: data.length },
        { name: "Mutuel", max: data.length },
        { name: "CNSS", max: data.length },
      ],
    },
    series: [
      {
        type: "radar",
        data: [
          {
            value: [getHRStats().benefits.cimr, getHRStats().benefits.mutuel, data.filter((emp) => emp.cnss).length],
            name: "Coverage",
          },
        ],
      },
    ],
  };

  const salaryDistributionChart = {
    title: { text: "Salary Distribution (MAD)", left: "center" },
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: ["0-5k", "5k-10k", "10k-15k", "15k-20k", "20k+"],
    },
    yAxis: {
      type: "value",
      name: "Number of Employees",
    },
    series: [
      {
        name: "Employees",
        type: "bar",
        data: calculateSalaryRanges(),
        itemStyle: {
          color: "#4f46e5",
        },
        label: {
          show: true,
          position: "top",
        },
      },
    ],
  };

  const roleSalaryChart = {
    title: { text: "Average Salary by Role", left: "center" },
    tooltip: { trigger: "axis" },
    legend: { bottom: 0 },
    xAxis: {
      type: "category",
      data: getRoleSalaryData().map((item) => item.role),
      axisLabel: { rotate: 45 },
    },
    yAxis: {
      type: "value",
      name: "Average Salary (MAD)",
    },
    series: [
      {
        name: "Average Salary",
        type: "bar",
        data: getRoleSalaryData().map((item) => item.avgSalary),
        itemStyle: {
          color: "#10b981",
        },
        label: {
          show: true,
          position: "top",
          formatter: (params: any) => formatNumber(params.value),
        },
      },
    ],
  };

  // Gender Distribution Chart
  const genderDistributionChart = {
    title: {
      text: 'Gender Distribution',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 'bottom'
    },
    series: [{
      type: 'pie',
      radius: '70%',
      data: [
        {
          value: filteredData.filter(emp => emp.sex?.toLowerCase() === 'masculin').length,
          name: 'Male',
          itemStyle: { color: '#3b82f6' }
        },
        {
          value: filteredData.filter(emp => emp.sex?.toLowerCase() === 'féminin').length,
          name: 'Female',
          itemStyle: { color: '#ec4899' }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };

  // Marital Status Chart
  const maritalStatusChart = {
    title: {
      text: 'Marital Status Distribution',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: { trigger: 'item' },
    legend: {
      orient: 'horizontal',
      bottom: 'bottom'
    },
    series: [{
      type: 'pie',
      radius: ['50%', '70%'],
      data: [
        {
          value: filteredData.filter(emp => emp.sitfam?.toLowerCase().includes('marié')).length,
          name: 'Married'
        },
        {
          value: filteredData.filter(emp => emp.sitfam?.toLowerCase().includes('célibataire')).length,
          name: 'Single'
        },
        {
          value: filteredData.filter(emp =>
            !emp.sitfam?.toLowerCase().includes('marié') &&
            !emp.sitfam?.toLowerCase().includes('célibataire')
          ).length,
          name: 'Other'
        }
      ]
    }]
  };

  // City Distribution Chart
  const cityDistributionChart = {
    title: {
      text: 'Employee Distribution by City',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: { trigger: 'axis' },
    legend: { bottom: 'bottom' },
    xAxis: {
      type: 'category',
      data: Object.keys(
        filteredData.reduce((acc, emp) => {
          acc[emp.ville] = (acc[emp.ville] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      ),
      axisLabel: { rotate: 45 }
    },
    yAxis: { type: 'value' },
    series: [{
      type: 'bar',
      data: Object.values(
        filteredData.reduce((acc, emp) => {
          acc[emp.ville] = (acc[emp.ville] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      ),
      itemStyle: {
        color: '#6366f1'
      }
    }]
  };

  // Department Stats
  const getDepartmentStats = () => {
    const departments = filteredData.reduce((acc, emp) => {
      if (!acc[emp.Qualification]) {
        acc[emp.Qualification] = {
          name: emp.Qualification,
          employees: [],
          count: 0,
          totalSalary: 0,
          cdiCount: 0,
        };
      }
      acc[emp.Qualification].employees.push(emp);
      acc[emp.Qualification].count++;
      acc[emp.Qualification].totalSalary += emp.Salaire || 0;
      if (emp.NOMINDIC4 === "CDI") {
        acc[emp.Qualification].cdiCount++;
      }
      return acc;
    }, {} as Record<string, { name: string; employees: BilanData[]; count: number; totalSalary: number; cdiCount: number }>);

    return Object.values(departments)
      .map((dept) => ({
        name: dept.name || "Unknown",
        count: dept.count,
        avgSalary: dept.totalSalary / dept.count,
        cdiPercentage: Math.round((dept.cdiCount / dept.count) * 100),
      }))
      .sort((a, b) => b.count - a.count);
  };


// Update the calculateWorkingYears function to handle YYYY-MM-DD format
const calculateWorkingYears = (dateString: string) => {
  if (!dateString) return 0;

  try {
    const [year, month, day] = dateString.split('-').map(Number);
    if (!year || !month || !day) return 0;

    const entryDate = new Date(year, month - 1, day);
    const today = new Date();
    let years = today.getFullYear() - entryDate.getFullYear();

    const currentMonth = today.getMonth();
    const entryMonth = entryDate.getMonth();
    if (currentMonth < entryMonth || (currentMonth === entryMonth && today.getDate() < entryDate.getDate())) {
      years--;
    }

    return years;
  } catch (error) {
    console.error('Error calculating working years:', dateString);
    return 0;
  }
};

  const EmployeeSheet = ({ employee }: { employee: BilanData }) => {
    const age = calculateAge(employee.Dan);
    const workingYears = calculateWorkingYears(employee.dentree);

    return (
      <SheetContent className="w-[300px] sm:w-[400px]"> {/* Reduced width */}
        <SheetHeader>
          <SheetTitle className="text-base">{employee.nom}</SheetTitle> {/* Reduced text size */}
        </SheetHeader>
        <div className="mt-2 space-y-3"> {/* Reduced spacing */}
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Personal Information</h3>
            <div className="grid grid-cols-2 gap-2 p-2 rounded-lg text-xs"> {/* Reduced padding and gap */}
              <p className="text-sm">Age: {age} years</p>
              <p className="text-sm">Gender: {employee.sex}</p>
              <p className="text-sm">Nationality: {employee.nat}</p>
              <p className="text-sm">Marital Status: {employee.sitfam}</p>
              <p className="text-sm">Children: {employee.nenf}</p>
              <p className="text-sm">Birth Date: {employee.Dan}</p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-500 mb-2">Work Information</h3>
            <div className="grid grid-cols-2 gap-4 p-4 rounded-lg">
              <p className="text-sm">Position: {employee.Categorie}</p>
              <p className="text-sm">Qualification: {employee.Qualification}</p>
              <p className="text-sm">Contract: {employee.NOMINDIC4}</p>
              <p className="text-sm">Entry Date: {employee.dentree}</p>
              <p className="text-sm">Years of Service: {workingYears} years</p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-500 mb-2">Benefits & Compensation</h3>
            <div className="grid grid-cols-2 gap-4 p-4 rounded-lg">
              <p className="text-sm">Salary: {formatNumber(employee.Salaire)} MAD</p>
              <p className="text-sm">CIMR: {employee.cimr ? "Yes" : "No"}</p>
              <p className="text-sm">Mutuel: {employee.mutuel ? "Yes" : "No"}</p>
              <p className="text-sm">CNSS: {employee.cnss ? "Yes" : "No"}</p>
              <p className="text-sm">Leave Balance: {employee.DroitConge} days</p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-500 mb-2">Contact & Location</h3>
            <div className="grid grid-cols-2 gap-4 p-4 rounded-lg">
              <p className="text-sm">City: {employee.ville}</p>
              <p className="text-sm">Country: {employee.PAYS}</p>
              <p className="text-sm">Bank: {employee.NOMBANQUE}</p>
            </div>
          </div>
        </div>
      </SheetContent>
    );
  };


  // Update the DatasetTableButton component
  const DatasetTableButton = () => {
    const handleSaveChanges = (updatedData: any[]) => {
      // Handle saving changes to the dataset
      console.log("Saving changes:", updatedData);
      toast.success("Changes saved successfully");
    };

    return (
      <>
        <Button
          variant="outline"
          className="fixed bottom-2 right-2 gap-1 shadow-lg z-40"
          onClick={() => setIsFullScreen(true)}
        >
          <TableIcon className="h-3 w-3" /> {/* Reduced icon size */}
          <span className="text-xs">View Dataset</span> {/* Reduced text size */}
        </Button>

        <CustomDialog
          isOpen={isFullScreen}
          onClose={() => setIsFullScreen(false)}
          title="Dataset Explorer"
        >
          <div className="h-full overflow-hidden">
            <TableTab
            // @ts-ignore
              data={data}
              headers={Object.keys(data[0] || {})}
              onSave={handleSaveChanges}
            />
          </div>
        </CustomDialog>
      </>
    );
  };

  // Add new contract distribution chart
  const contractDistributionChart = {
    title: {
      text: 'Contract Type Distribution',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 'bottom'
    },
    series: [{
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: true,
      label: {
        show: true,
        formatter: '{b}: {c} ({d}%)'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: [
        {
          value: filteredData.filter(emp => emp.NOMINDIC4 === 'CDI').length,
          name: 'CDI',
          itemStyle: { color: '#22c55e' }  // green
        },
        {
          value: filteredData.filter(emp => emp.NOMINDIC4 === 'CDD').length,
          name: 'CDD',
          itemStyle: { color: '#f59e0b' }  // amber
        }
      ]
    }]
  };


  const calculateAge = (dateInput: unknown): number => {
    // Handle null/undefined cases first
    if (!dateInput) return 0;

    let dateString: string;
    let date: Date;

    try {
      // Convert different input types to string
      if (typeof dateInput === 'string') {
        dateString = dateInput;

        // Handle Excel serial dates stored as strings (e.g., "44235")
        if (/^\d+$/.test(dateString)) {
          const excelSerial = parseInt(dateString, 10);
          date = new Date((excelSerial - 25569) * 86400 * 1000);
        } else {
          // Split date and time parts
          const [datePart] = dateString.split(' ');
          const [year, month, day] = datePart.split(/[-/]/).map(Number);
          date = new Date(year, month - 1, day);
        }
      } else if (dateInput instanceof Date) {
        date = dateInput;
      } else if (typeof dateInput === 'number') {
        // Handle Excel serial numbers directly
        date = new Date((dateInput - 25569) * 86400 * 1000);
      } else {
        console.error('Unsupported date format:', dateInput);
        return 0;
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        console.error('Invalid date:', dateInput);
        return 0;
      }

      // Calculate age
      const today = new Date();
      let age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
        age--;
      }

      return age;
    } catch (error) {
      console.error('Error calculating age:', error);
      return 0;
    }
  };

  // Simplified Retirement Analysis Table
  const RetirementAnalysisTable = () => {
    const getRetirementAge = (employee: BilanData) => {
      return employee.Qualification?.toLowerCase().includes("cadre") ? 65 : 60;
    };

    const retirementData = data
      .map((emp) => ({
        ...emp,
        age: calculateAge(emp.Dan),
      }))
      .filter((emp) => emp.age > 18 && emp.age < 100)
      .map((emp) => ({
        ...emp,
        yearsToRetirement: Math.max(0, getRetirementAge(emp) - emp.age),
        retirementYear: new Date().getFullYear() + Math.max(0, getRetirementAge(emp) - emp.age),
      }))
      .sort((a, b) => a.yearsToRetirement - b.yearsToRetirement);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Calculate pagination data
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = retirementData.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(retirementData.length / itemsPerPage);

    return (
      <Card>
        <CardHeader>
          <CardTitle>Retirement Projections</CardTitle>
          <CardDescription>Showing all employees with valid birth dates</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Age</TableHead>
                <TableHead>Years to Retirement</TableHead>
                <TableHead>Retirement Year</TableHead>
                <TableHead>Position</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentItems.map((emp) => (
                <TableRow key={emp.matricule}>
                  <TableCell>
                    <Sheet>
                      <SheetTrigger className="hover:text-blue-600 font-medium">
                        {emp.nom}
                      </SheetTrigger>
                      <EmployeeSheet employee={emp} />
                    </Sheet>
                  </TableCell>
                  <TableCell>{emp.age}</TableCell>
                  <TableCell>
                    <Badge variant={emp.yearsToRetirement <= 5 ? "destructive" : "secondary"}>
                      {emp.yearsToRetirement} years
                    </Badge>
                  </TableCell>
                  <TableCell>{emp.retirementYear}</TableCell>
                  <TableCell>{emp.Qualification}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {/* Pagination controls */}
          <div className="flex justify-between items-center mt-4">
            <button
              className="px-4 py-2 rounded disabled:opacity-50"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </button>
            <span>
              Page {currentPage} of {totalPages}
            </span>
            <button
              className="px-4 py-2 rounded disabled:opacity-50"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </div>
        </CardContent>
      </Card>
    );
  };
  return (
    <div className="space-y-2 p-2"> {/* Reduced from space-y-6 p-6 */}
      <DatasetTableButton />



      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-1"> {/* Reduced gap */}
        <QuickStatCard title="Total Employees" value={getHRStats().total} icon={<Users className="h-3 w-3" />} trend="+2.5%" />
        <QuickStatCard
          title="Average Salary"
          value={`${formatNumber(getHRStats().salary.avg)} MAD`}
          icon={<Wallet className="h-4 w-4" />}
          trend="+3.2%"
        />
        <QuickStatCard
          title="CDI Ratio"
          value={`${Math.round((getHRStats().contracts.cdi / getHRStats().total) * 100)}%`}
          icon={<Briefcase className="h-4 w-4" />}
          trend="+1.8%"
        />
        <QuickStatCard
          title="Avg Leave Balance"
          value={`${Math.round(getHRStats().leave.avg)} days`}
          icon={<Calendar className="h-4 w-4" />}
          trend="-0.5%"
        />
      </div>

      {/* Main Charts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-1"> {/* Reduced gap */}
        {/* Add the contract distribution chart at the start */}
        <Card className="overflow-hidden"> {/* Added overflow-hidden */}
          <CardHeader className="p-2"> {/* Reduced padding */}
            <CardTitle className="text-xs">Contract Types</CardTitle>
            <CardDescription className="text-xs">Distribution</CardDescription>
          </CardHeader>
          <CardContent className="p-1"> {/* Minimal padding */}
            <ReactECharts option={contractDistributionChart} style={{ height: '250px' }} /> {/* Reduced height */}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Bank Distribution</CardTitle>
            <CardDescription>Employee Count by Bank</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={bankDistributionChart} style={{ height: '300px' }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gender Distribution</CardTitle>
            <CardDescription>Male vs Female Ratio</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={genderDistributionChart} style={{ height: '300px' }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Marital Status</CardTitle>
            <CardDescription>Employee Marital Status Distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={maritalStatusChart} style={{ height: '300px' }} />
          </CardContent>
        </Card>

        <Card className="col-span-1 md:col-span-2">
          <CardHeader>
            <CardTitle>City Distribution</CardTitle>
            <CardDescription>Employee Count by City</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={cityDistributionChart} style={{ height: '300px' }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Salary Distribution</CardTitle>
            <CardDescription>Employee count by salary range</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={salaryDistributionChart} style={{ height: "300px" }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Average Salary by Role</CardTitle>
            <CardDescription>Salary comparison across roles</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={roleSalaryChart} style={{ height: "300px" }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Department Overview</CardTitle>
            <CardDescription>Employee count by department</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={departmentChart} style={{ height: "300px" }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Benefits Coverage</CardTitle>
            <CardDescription>CIMR, Mutuel, and CNSS coverage</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={benefitsChart} style={{ height: "300px" }} />
          </CardContent>
        </Card>

        {/* Add the new contract distribution chart */}
        <Card>
          <CardHeader>
            <CardTitle>Contract Types</CardTitle>
            <CardDescription>Distribution of CDI vs CDD</CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts option={cityDistributionChart} style={{ height: '300px' }} />
          </CardContent>
        </Card>
      </div>

      {/* Retirement Analysis with simplified table */}
      <RetirementAnalysisTable />

      {/* Detailed Tables Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-1"> {/* Reduced gap */}
        <Card>
          <CardHeader className="p-2">
            <CardTitle className="text-xs">Top Earners</CardTitle>
          </CardHeader>
          <CardContent className="p-1">
            <Table>
              <TableHeader>
                <TableRow className="h-6"> {/* Reduced row height */}
                  <TableHead>Name</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead className="text-right">Salary (MAD)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getHRStats().salary.topEarners.map((emp) => (
                  <TableRow key={emp.matricule}>
                    <TableCell className="font-medium">{emp.nom}</TableCell>
                    <TableCell>{emp.Qualification}</TableCell>
                    <TableCell>{emp.Categorie}</TableCell>
                    <TableCell className="text-right">{formatNumber(emp.Salaire)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Department Summary</CardTitle>
            <CardDescription>Key metrics by department</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Department</TableHead>
                  <TableHead>Employees</TableHead>
                  <TableHead>Avg Salary</TableHead>
                  <TableHead className="text-right">CDI %</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getDepartmentStats().map((dept) => (
                  <TableRow key={dept.name}>
                    <TableCell className="font-medium">{dept.name}</TableCell>
                    <TableCell>{dept.count}</TableCell>
                    <TableCell>{formatNumber(dept.avgSalary)} MAD</TableCell>
                    <TableCell className="text-right">{dept.cdiPercentage}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Quick Stat Card Component
function QuickStatCard({ title, value, icon, trend }: { title: string; value: string | number; icon: React.ReactNode; trend: string }) {
  const isPositive = trend.startsWith("+");

  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between p-2">
        <CardTitle className="text-xs font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent className="p-2">
        <div className="text-base font-bold">{value}</div>
        <p className={`text-[10px] mt-0.5 ${isPositive ? "text-green-600" : "text-red-600"}`}>{trend} from last month</p>
      </CardContent>
    </Card>
  );
}
