"use client"

import { useState, useMemo } from "react"
import { <PERSON>, Calculator, Plus, Minus, X as Multiply, Divide, Percent, X } from "lucide-react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogT<PERSON>le,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts"
import { Checkbox } from "@/components/ui/checkbox"

interface EmployeeData {
  Matricule: string
  "Nom & Prénom": string
  Rubrique: string
  Libelle: string
  Total: string
  [key: string]: string
}

const MONTHS = [
  "Jan-25", "Feb-25", "Mar-25", "Apr-25", "May-25", "Jun-25",
  "Jul-25", "Aug-25", "Sep-25", "Oct-25", "Nov-25", "Dec-25"
];

// Add this new type for formula steps
interface FormulaStep {
  type: 'rubrique' | 'operator' | 'percentage' | 'number';
  value: string;
}

interface CalculationResult {
  formula: string;
  results: Record<string, number>;
  rubriques: string[];
}

interface MonthlyData {
  [key: string]: number;
}

const RubriqueCalculator = ({
  onCalculate,
  onClose
}: {
  onCalculate: (result: { firstRub: string, secondRub: string, operation: string }) => void,
  onClose: () => void
}) => {
  const [firstRub, setFirstRub] = useState("")
  const [secondRub, setSecondRub] = useState("")
  const [operation, setOperation] = useState<'+' | '-' | '*' | '/' | '%'>('+')

  const handleCalculate = () => {
    if (!firstRub || !secondRub) {
      toast.error("Please enter both rubrique numbers")
      return
    }

    onCalculate({
      firstRub,
      secondRub,
      operation
    })
    onClose()
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4 items-end">
        <div className="space-y-2">
          <label className="text-sm font-medium">First Rubrique</label>
          <Input
            type="number"
            placeholder="e.g., 300"
            value={firstRub}
            onChange={(e) => setFirstRub(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <div className="grid grid-cols-5 gap-1">
            {['+', '-', '*', '/', '%'].map(op => (
              <Button
                key={op}
                size="sm"
                variant={operation === op ? "default" : "outline"}
                onClick={() => setOperation(op as any)}
              >
                {op}
        </Button>
            ))}
                </div>
              </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Second Rubrique</label>
          <Input
            type="number"
            placeholder="e.g., 600"
            value={secondRub}
            onChange={(e) => setSecondRub(e.target.value)}
          />
        </div>
      </div>

      <div className="pt-4 flex justify-end gap-2">
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={handleCalculate}>Calculate</Button>
      </div>
  </div>
  )
}

export default function EmployeeSalaryAnalysis({ data }: { data: EmployeeData[] }) {
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])
  const [selectedMonths, setSelectedMonths] = useState<string[]>(MONTHS)
  const [searchTerm, setSearchTerm] = useState("")
  const [formulaInput, setFormulaInput] = useState("")
  const [calculation, setCalculation] = useState<CalculationResult | null>(null)

  // Get unique employees and rubriques with proper typing
  const employees = useMemo(() => 
    Array.from(new Set(data.map(item => item["Nom & Prénom"]))).sort(),
    [data]
  )

  const rubriques = useMemo(() => {
    const uniqueRubriques = new Map<string, { code: string; label: string }>()
    data.forEach(item => {
      if (!uniqueRubriques.has(item.Rubrique)) {
        uniqueRubriques.set(item.Rubrique, {
          code: item.Rubrique,
          label: item.Libelle
        })
      }
    })
    return Array.from(uniqueRubriques.values())
  }, [data])

  const filteredRubriques = useMemo(() => 
    rubriques.filter(rub => {
      if (!searchTerm) return true
      const terms = searchTerm.toLowerCase().split(/\s+/)
      return terms.every(term => 
        rub.code.toLowerCase().includes(term) ||
        rub.label.toLowerCase().includes(term)
      )
    }),
    [rubriques, searchTerm]
  )

  const calculateFormula = () => {
    if (!selectedEmployees.length || !formulaInput.trim() || !selectedMonths.length) {
      toast.error("Please select employee(s), month(s) and enter a formula")
      return
    }

    try {
      const parts = formulaInput.match(/(\d+%?)|[+\-*/]/g) || []
      const usedRubs = new Set<string>()
      const results: Record<string, number> = {}

      selectedMonths.forEach(month => {
        let result = 0
        let currentOp = '+'
        let i = 0

        while (i < parts.length) {
          const part = parts[i]

          if ('+-*/'.includes(part)) {
            currentOp = part
            i++
            continue
          }

          let value = 0
          if (part.endsWith('%')) {
            const percent = parseFloat(part.slice(0, -1))
            value = (result * percent) / 100
          } else {
            const rubrique = part
            usedRubs.add(rubrique)
            
            // Calculate average across selected employees
            value = selectedEmployees.reduce((sum, emp) => {
              const row = data.find(d => 
                d["Nom & Prénom"] === emp && 
                d.Rubrique === rubrique
              )
              return sum + (row ? parseFloat(row[month]) || 0 : 0)
            }, 0) / selectedEmployees.length
          }

          switch (currentOp) {
            case '+': result += value; break
            case '-': result -= value; break
            case '*': result *= value; break
            case '/': result = value !== 0 ? result / value : 0; break
          }

          i++
        }

        results[month] = result
      })

      setCalculation({
        formula: formulaInput,
        results,
        rubriques: Array.from(usedRubs)
      })
      toast.success("Calculation completed")
    } catch (error) {
      toast.error("Invalid formula format")
    }
  }

    return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Salary Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-12 gap-6">
            {/* Left Column - Selections */}
            <div className="col-span-4 space-y-6">
              {/* Employee Selection */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Employees</label>
                  <span className="text-xs text-muted-foreground">
                    {selectedEmployees.length} selected
                  </span>
                </div>
                <ScrollArea className="h-[150px] border rounded-md p-2">
                  {employees.map(emp => (
                    <div key={emp} className="flex items-center space-x-2 py-1">
                      <Checkbox
                        checked={selectedEmployees.includes(emp)}
                        onCheckedChange={(checked) => {
                          setSelectedEmployees(prev =>
                            checked
                              ? [...prev, emp]
                              : prev.filter(e => e !== emp)
                          )
                        }}
                      />
                      <label className="text-sm">{emp}</label>
              </div>
            ))}
        </ScrollArea>
      </div>

              {/* Month Selection */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Months</label>
                  <span className="text-xs text-muted-foreground">
                    {selectedMonths.length} selected
                  </span>
                </div>
                <ScrollArea className="h-[150px] border rounded-md p-2">
                  {MONTHS.map(month => (
                    <div key={month} className="flex items-center space-x-2 py-1">
                      <Checkbox
                        checked={selectedMonths.includes(month)}
                        onCheckedChange={(checked) => {
                          setSelectedMonths(prev =>
                            checked
                              ? [...prev, month]
                              : prev.filter(m => m !== month)
                          )
                        }}
                      />
                      <label className="text-sm">{month}</label>
                    </div>
                  ))}
                </ScrollArea>
              </div>
            </div>

            {/* Middle Column - Rubriques */}
            <div className="col-span-4 space-y-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">Available Rubriques</label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                    placeholder="Search rubriques..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
              />
            </div>
                <ScrollArea className="h-[335px] border rounded-md p-2">
                  {filteredRubriques.map((rub) => (
                    <div key={rub.code} 
                      className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-sm cursor-pointer"
                      onClick={() => setFormulaInput(prev => prev + (prev ? ' + ' : '') + rub.code)}
                    >
                      <span className="font-mono">{rub.code}</span>
                      <span className="text-sm text-muted-foreground truncate ml-2">{rub.label}</span>
                  </div>
                  ))}
                </ScrollArea>
              </div>
          </div>

            {/* Right Column - Formula */}
            <div className="col-span-4 space-y-6">
          <div className="space-y-4">
              <div>
                  <label className="text-sm font-medium">Formula Builder</label>
                  <p className="text-xs text-muted-foreground mt-1">
                    Click rubriques to add or type directly
                </p>
              </div>

                <div className="space-y-2">
                  <Input
                    placeholder="e.g., 300 + 375 + 370 - 10%"
                    value={formulaInput}
                    onChange={(e) => setFormulaInput(e.target.value)}
                  />
                  <div className="grid grid-cols-5 gap-2">
                    {['+', '-', '*', '/', '%'].map(op => (
                <Button
                        key={op}
                        variant="outline"
                  size="sm"
                        onClick={() => setFormulaInput(prev => prev + ` ${op} `)}
                >
                        {op}
                </Button>
                    ))}
              </div>
            </div>

                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={calculateFormula}
                    disabled={!selectedEmployees.length || !formulaInput.trim()}
                  >
                    Calculate
                  </Button>
                  <p className="text-xs text-muted-foreground text-center">
                    Supports +, -, *, /, and percentages
                  </p>
                </div>
              </div>

              {calculation && (
                <div className="rounded-md border p-4 space-y-2">
                  <h4 className="font-medium text-sm">Current Formula</h4>
                  <p className="text-sm font-mono bg-muted p-2 rounded">
                    {calculation.formula}
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Display - Full Width */}
      {calculation && (
        <div className="grid gap-6">
          {/* Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Analysis</CardTitle>
            </CardHeader>
            <CardContent>
                <LineChart
                width={800}
                height={400}
                  data={selectedMonths.map(month => ({
                  name: month,
                  ...calculation.rubriques.reduce<MonthlyData>((acc, rub) => {
                    const value = selectedEmployees.reduce((sum, emp) => {
                      const row = data.find(d => 
                        d["Nom & Prénom"] === emp && 
                        d.Rubrique === rub
                      )
                      return sum + (row ? parseFloat(row[month]) || 0 : 0)
                    }, 0) / selectedEmployees.length
                    acc[rub] = value
                    return acc
                  }, {}),
                  Result: calculation.results[month]
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                {[...calculation.rubriques, 'Result'].map((key, index) => (
                  <Line
                    key={key}
                    type="monotone"
                    dataKey={key}
                    stroke={`hsl(${index * 360 / (calculation.rubriques.length + 1)}, 70%, 50%)`}
                    />
                  ))}
                </LineChart>
            </CardContent>
          </Card>

      {/* Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rubrique</TableHead>
                <TableHead>Description</TableHead>
                    <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
                  {[
                    ...calculation.rubriques.map(rub => {
                      const row = data.find(d => 
                        d["Nom & Prénom"] === selectedEmployees[0] && 
                        d.Rubrique === rub
                      )
                      return {
                        code: rub,
                        label: row?.Libelle || "",
                        total: parseFloat(row?.Total || "0")
                      }
                    }),
                    {
                      code: 'Result',
                      label: `Calculated (${calculation.formula})`,
                      total: Object.values(calculation.results).reduce((a, b) => a + b, 0)
                    }
                  ].map(item => (
                    <TableRow key={item.code}>
                      <TableCell>{item.code}</TableCell>
                      <TableCell>{item.label}</TableCell>
                    <TableCell className="text-right">
                        {new Intl.NumberFormat('fr-MA', {
                          style: 'currency',
                          currency: 'MAD'
                        }).format(item.total)}
                    </TableCell>
                  </TableRow>
                  ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
        </div>
      )}
    </div>
  )
} 