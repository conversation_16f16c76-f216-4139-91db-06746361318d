'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Send,
  X,
  Copy,
  Check,
  Code,
  Zap,
  Play,
  BarChart3,
  TrendingUp,
  Award,
  Shield,
  Wand2,
  Sparkles,
  Loader2
} from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'

interface AIAssistantProps {
  selectedDatasets: Dataset[]
  language: 'sql' | 'python' | 'javascript' | 'markdown'
  onCodeGenerated: (code: string) => void
  editorRef?: React.MutableRefObject<any>
}

export function AIAssistant({ selectedDatasets, language, onCodeGenerated, editorRef }: AIAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [prompt, setPrompt] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [streamedCode, setStreamedCode] = useState('')
  const [generatedCode, setGeneratedCode] = useState('')
  const [showPreview, setShowPreview] = useState(false)
  const [copied, setCopied] = useState(false)
  const [showQuickActions, setShowQuickActions] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const originalContentRef = useRef<string>('')

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Store original content and start streaming
  const startStreaming = useCallback(() => {
    if (editorRef?.current) {
      const editor = editorRef.current
      const model = editor.getModel()
      if (model) {
        // Store original content
        originalContentRef.current = model.getValue()

        // Add separator if there's existing content
        const currentContent = model.getValue()
        if (currentContent.trim()) {
          model.setValue(currentContent + '\n\n# AI Generated Code\n')
        } else {
          model.setValue('# AI Generated Code\n')
        }

        // Position cursor at end
        const lineCount = model.getLineCount()
        editor.setPosition({ lineNumber: lineCount + 1, column: 1 })
      }
    }
  }, [editorRef])

  // Stream code directly to editor
  const streamCodeToEditor = useCallback((code: string) => {
    if (editorRef?.current) {
      const editor = editorRef.current
      const model = editor.getModel()
      if (model) {
        const currentContent = model.getValue()
        model.setValue(currentContent + code)

        // Position cursor at end
        const lineCount = model.getLineCount()
        const lastLineLength = model.getLineLength(lineCount)
        editor.setPosition({ lineNumber: lineCount, column: lastLineLength + 1 })
      }
    }
  }, [editorRef])

  const handleGenerate = async (quickPrompt?: string) => {
    const currentPrompt = quickPrompt || prompt.trim()

    if (!currentPrompt) {
      toast.error('Please enter a prompt')
      return
    }

    if (selectedDatasets.length === 0) {
      toast.error('Please select at least one dataset first')
      return
    }

    // Abort any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setIsStreaming(true)
    setStreamedCode('')
    setGeneratedCode('')
    setShowPreview(false)

    // Start streaming to editor
    startStreaming()

    if (!quickPrompt) {
      setPrompt('') // Clear input only if not using quick action
    }

    try {
      // Prepare enhanced dataset information for the AI
      const datasetsInfo = selectedDatasets.map(ds => ({
        name: ds.name,
        columns: ds.headers,
        sampleData: ds.data.slice(0, 3),
        rowCount: ds.data.length,
        columnTypes: ds.headers?.map(header => {
          const sampleValues = ds.data.slice(0, 10).map(row => row[header]).filter(val => val != null)
          const isNumeric = sampleValues.every(val => !isNaN(Number(val)) && val !== '')
          const isDate = sampleValues.some(val => !isNaN(Date.parse(val)))
          return {
            name: header,
            type: isNumeric ? 'numeric' : isDate ? 'date' : 'text',
            sampleValues: sampleValues.slice(0, 3)
          }
        })
      }))

      const response = await fetch('/api/ai/chartbuilder/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          language: language,
          datasets: datasetsInfo
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error('Failed to generate code')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let accumulatedCode = ''
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.code) {
                accumulatedCode += data.code
                setStreamedCode(accumulatedCode)

                // Stream directly to editor
                streamCodeToEditor(data.code)
              }
              if (data.done) {
                setGeneratedCode(accumulatedCode)
                setShowPreview(true)
                toast.success('Code generated! Accept or reject the changes.')
                return
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info('Generation cancelled')
      } else {
        console.error('Error generating code:', error)
        toast.error('Failed to generate code. Please try again.')
      }
    } finally {
      setIsStreaming(false)
      abortControllerRef.current = null
    }
  }

  const handleCopy = async (code?: string) => {
    try {
      const textToCopy = code || streamedCode
      await navigator.clipboard.writeText(textToCopy)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success('Code copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy code')
    }
  }

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setIsStreaming(false)
    // Restore original content
    handleReject()
    toast.info('Generation stopped')
  }

  const handleAccept = () => {
    // Keep the generated code in the editor
    onCodeGenerated(generatedCode)
    setIsOpen(false)
    setShowPreview(false)
    setGeneratedCode('')
    setStreamedCode('')
    originalContentRef.current = ''
    toast.success('Code accepted and added to cell!')
  }

  const handleReject = () => {
    // Restore original content
    if (editorRef?.current && originalContentRef.current !== undefined) {
      const editor = editorRef.current
      const model = editor.getModel()
      if (model) {
        model.setValue(originalContentRef.current)
        // Position cursor at end
        const lineCount = model.getLineCount()
        const lastLineLength = model.getLineLength(lineCount)
        editor.setPosition({ lineNumber: lineCount, column: lastLineLength + 1 })
      }
    }
    setShowPreview(false)
    setGeneratedCode('')
    setStreamedCode('')
    originalContentRef.current = ''
    toast.info('Changes rejected, original content restored')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleGenerate()
    }
    if (e.key === 'Escape') {
      setIsOpen(false)
      setStreamedCode('')
    }
  }

  // Quick action prompts
  const quickActions = [
    { label: "Summary", prompt: "Show summary statistics for all columns", icon: BarChart3 },
    { label: "Chart", prompt: "Create a visualization showing the main trends", icon: TrendingUp },
    { label: "Top 10", prompt: "Show the top 10 records by the most important metric", icon: Award },
    { label: "Clean", prompt: "Check for missing values and data quality issues", icon: Shield }
  ]

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        size="sm"
        variant="ghost"
        className="gap-1.5 h-7 px-2 text-xs bg-gradient-to-r from-violet-50 to-blue-50 hover:from-violet-100 hover:to-blue-100 border border-violet-200/50 text-violet-700 hover:text-violet-800 transition-all duration-200 shadow-sm"
      >
        <Wand2 className="h-3.5 w-3.5" />
        AI
      </Button>
    )
  }

  return (
    <div className="w-full bg-gradient-to-r from-violet-50/50 to-blue-50/50 border border-violet-200/50 rounded-lg p-3 mb-2">
      {/* Inline AI Input */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2 flex-shrink-0">
          <div className="p-1 bg-gradient-to-r from-violet-500 to-blue-500 rounded">
            <Sparkles className="h-3 w-3 text-white" />
          </div>
          <span className="text-xs font-medium text-violet-700">AI Assistant</span>
          {selectedDatasets.length > 0 && (
            <Badge variant="outline" className="text-xs h-5 px-1.5">
              {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>

        {/* Quick Actions */}
        <div className="flex gap-1 flex-shrink-0">
          {quickActions.map((action, index) => {
            const IconComponent = action.icon
            return (
              <Button
                key={index}
                onClick={() => handleGenerate(action.prompt)}
                disabled={isStreaming}
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs text-violet-600 hover:text-violet-800 hover:bg-violet-100"
                title={action.prompt}
              >
                <IconComponent className="h-3 w-3" />
              </Button>
            )
          })}
        </div>

        {/* Input */}
        <div className="flex-1 flex gap-2">
          <Input
            ref={inputRef}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Ask AI to generate code..."
            className="h-7 text-xs border-violet-200 focus:border-violet-400 focus:ring-violet-400"
            disabled={isStreaming}
          />

          {isStreaming ? (
            <Button
              onClick={handleStop}
              size="sm"
              variant="ghost"
              className="h-7 px-2 text-xs text-red-600 hover:text-red-800 hover:bg-red-50"
            >
              <X className="h-3 w-3" />
            </Button>
          ) : (
            <Button
              onClick={() => handleGenerate()}
              disabled={!prompt.trim()}
              size="sm"
              className="h-7 px-2 bg-gradient-to-r from-violet-500 to-blue-500 hover:from-violet-600 hover:to-blue-600"
            >
              <Send className="h-3 w-3" />
            </Button>
          )}
        </div>

        <Button
          onClick={() => setIsOpen(false)}
          size="sm"
          variant="ghost"
          className="h-6 px-2 text-xs text-violet-600 hover:text-violet-800"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      {/* Streaming Status */}
      {isStreaming && (
        <div className="mt-2 flex items-center gap-2 text-xs text-violet-600">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Generating {language.toUpperCase()} code...</span>
          <span className="text-violet-400">({streamedCode.length} chars)</span>
        </div>
      )}

      {/* Accept/Reject Buttons */}
      {showPreview && !isStreaming && (
        <div className="mt-2 flex items-center justify-between p-2 bg-violet-50 dark:bg-violet-900/20 rounded border border-violet-200 dark:border-violet-700">
          <div className="flex items-center gap-2 text-xs text-violet-700 dark:text-violet-300">
            <Code className="h-3 w-3" />
            <span>Code generated ({generatedCode.length} chars)</span>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleReject}
              size="sm"
              variant="outline"
              className="h-6 px-3 text-xs border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
            >
              <X className="h-3 w-3 mr-1" />
              Reject
            </Button>
            <Button
              onClick={handleAccept}
              size="sm"
              className="h-6 px-3 text-xs bg-green-600 hover:bg-green-700 text-white"
            >
              <Check className="h-3 w-3 mr-1" />
              Accept
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
