'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Download, Upload, Copy, Check, Code } from "lucide-react"
import { toast } from "sonner"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"

export interface ChartConfigExport {
  version: string;
  timestamp: string;
  config: {
    type: string;
    xAxis: string;
    yAxis: string;
    title: string;
    description: string;
    color: string;
    showLegend: boolean;
    showLabels: boolean;
    showGrid: boolean;
    aggregation: string;
    groupBy: string;
    timeScale: string;
    customLabel: string;
    enableZoom: boolean;
    multiSeries: boolean;
  };
  series?: {
    id: string;
    field: string;
    color: string;
    label: string;
    visible: boolean;
  }[];
  filters?: {
    column: string;
    operator: string;
    value: string | number;
    enabled: boolean;
  }[];
  data: any[];
}

interface ChartConfigExporterProps {
  config: any;
  series?: any[];
  filters?: any[];
  data: any[];
  onImport: (importedConfig: ChartConfigExport) => void;
}

export function ChartConfigExporter({ 
  config, 
  series = [], 
  filters = [], 
  data, 
  onImport 
}: ChartConfigExporterProps) {
  const [copied, setCopied] = useState(false)
  const [importJson, setImportJson] = useState('')
  const [importError, setImportError] = useState('')

  // Create exportable config
  const chartConfigExport: ChartConfigExport = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    config,
    series: series.length > 0 ? series : undefined,
    filters: filters.length > 0 ? filters : undefined,
    data: data
  }

  // Convert to JSON string
  const jsonConfig = JSON.stringify(chartConfigExport, null, 2)

  // Copy to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonConfig)
    setCopied(true)
    toast.success('Configuration copied to clipboard')
    setTimeout(() => setCopied(false), 2000)
  }

  // Download as JSON file
  const downloadJson = () => {
    const blob = new Blob([jsonConfig], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chart-config-${new Date().getTime()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Configuration downloaded as JSON')
  }

  // Handle import
  const handleImport = () => {
    try {
      setImportError('')
      if (!importJson.trim()) {
        setImportError('Please paste a valid JSON configuration')
        return
      }
      
      const parsed = JSON.parse(importJson)
      
      // Basic validation
      if (!parsed.config || !parsed.data) {
        setImportError('Invalid configuration format: missing required fields')
        return
      }
      
      // Import into the chart
      onImport(parsed)
      toast.success('Configuration imported successfully')
      setImportJson('')
    } catch (error) {
      console.error('Import error:', error)
      setImportError('Failed to parse JSON configuration')
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8"
        >
          <Code className="h-4 w-4 mr-1" />
          <span className="hidden sm:inline">Export/Import</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Chart Configuration</DialogTitle>
          <DialogDescription>
            Export or import your chart configuration as JSON
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="export">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
          </TabsList>
          
          <TabsContent value="export" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-2">
              This JSON contains all the settings for your current chart, including data.
            </div>
            
            <ScrollArea className="h-[300px] w-full rounded-md border">
              <pre className="p-4 text-sm font-mono">{jsonConfig}</pre>
            </ScrollArea>
            
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={copyToClipboard}
              >
                {copied ? <Check className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                {copied ? 'Copied' : 'Copy'}
              </Button>
              
              <Button
                onClick={downloadJson}
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="import" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-2">
              Paste a previously exported JSON configuration to import
            </div>
            
            <Textarea
              placeholder="Paste chart configuration JSON here..."
              className="font-mono text-sm h-[300px]"
              value={importJson}
              onChange={(e) => setImportJson(e.target.value)}
            />
            
            {importError && (
              <div className="text-sm text-red-500">{importError}</div>
            )}
            
            <div className="flex justify-end">
              <Button
                onClick={handleImport}
              >
                <Upload className="h-4 w-4 mr-1" />
                Import Configuration
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 