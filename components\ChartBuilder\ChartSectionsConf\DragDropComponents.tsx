'use client'

import React, { ReactNode, useMemo, useState } from 'react'
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { X } from "lucide-react"
// Removed toast import for better performance
import { useDroppable } from '@dnd-kit/core'
import { useDraggable } from '@dnd-kit/core'
import { CSS } from '@dnd-kit/utilities'
import { ScrollArea } from "@/components/ui/scroll-area"
import { DraggableItem as DraggableItemType } from '../types'

// Re-export DraggableItem type
export type DraggableItem = DraggableItemType;

interface DropZoneProps {
  id: string
  title: string
  acceptTypes?: string[]
  items: DraggableItem[]
  onItemDrop: (item: DraggableItem, zoneId: string) => void
  onItemRemove: (itemId: string, zoneId: string) => void
  isRequired?: boolean
  maxItems?: number
  description?: string
  icon?: ReactNode
}

interface DraggableItemProps {
  item: DraggableItem
  onRemove?: () => void
  isDropped?: boolean
}

// Component for displaying a draggable column item
export const DraggableColumn = ({
  item,
  disabled = false,
  sourceZone
}: {
  item: DraggableItem,
  disabled?: boolean,
  sourceZone?: string
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `${sourceZone ? sourceZone + '-' : ''}${item.id}`,
    data: {
      ...item,
      sourceZone
    },
    disabled
  })

  // Performance optimized style with transform GPU acceleration
  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.8 : 1,
    cursor: disabled ? 'not-allowed' : 'grab',
    zIndex: isDragging ? 99999 : 'auto',
    boxShadow: isDragging ? '0 4px 8px rgba(0,0,0,0.2)' : 'none',
    position: isDragging ? 'relative' : 'static' as any,
    willChange: 'transform',
    transition: 'none',
    touchAction: 'none',
  }

  // Different color badges for different data types
  const getBadgeVariant = () => {
    switch (item.dataType) {
      case 'number': return 'default'
      case 'date': return 'secondary'
      case 'boolean': return 'destructive'
      default: return 'outline'
    }
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "flex items-center gap-1 px-2 py-1 rounded-md border bg-background text-xs",
        disabled ? "opacity-50 cursor-not-allowed" : "hover:bg-accent hover:text-accent-foreground",
        isDragging ? "bg-primary/10 border-primary shadow-lg" : "",
        sourceZone ? "bg-accent/20 hover:bg-accent/30" : ""
      )}
    >
      <span className="truncate">{item.name}</span>
      <Badge variant={getBadgeVariant()} className="text-[10px] h-4">
        {item.dataType ? item.dataType.charAt(0).toUpperCase() : 'S'}
      </Badge>
    </div>
  )
}

// Component for items that are already in a zone and can be dragged to other zones
export const ZoneItem = ({
  item,
  onRemove,
  sourceZone
}: {
  item: DraggableItem,
  onRemove: () => void,
  sourceZone: string
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `${sourceZone}-${item.id}`,
    data: {
      ...item,
      sourceZone
    }
  })

  // Performance optimized style with transform GPU acceleration
  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.8 : 1,
    cursor: 'grab',
    zIndex: isDragging ? 99999 : 'auto',
    boxShadow: isDragging ? '0 4px 8px rgba(0,0,0,0.2)' : 'none',
    position: isDragging ? 'relative' : 'static' as any,
    willChange: 'transform',
    transition: 'none',
    touchAction: 'none',
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "flex items-center gap-1 px-2 py-1 rounded-md border bg-accent/20 text-xs group",
        "hover:bg-accent/40 transition-colors duration-150",
        "animate-in fade-in-50 slide-in-from-top-5 duration-200",
        isDragging ? "shadow-lg ring-2 ring-primary/30 bg-primary/10" : ""
      )}
    >
      <span className="truncate">{item.name}</span>
      <button
        type="button"
        onClick={(e) => {
          e.stopPropagation();
          onRemove();
        }}
        className="w-4 h-4 rounded-full bg-muted hover:bg-destructive/20 flex items-center justify-center opacity-60 group-hover:opacity-100 transition-opacity"
        aria-label={`Remove ${item.name}`}
      >
        <X className="w-3 h-3" />
      </button>
    </div>
  )
}

// Component for a drop zone that accepts draggable items
export const DropZone = ({
  id,
  title,
  description,
  acceptTypes = [],
  items = [],
  onItemDrop,
  onItemRemove,
  isRequired = false,
  maxItems = 1,
  icon
}: DropZoneProps) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
    data: {
      accepts: acceptTypes
    }
  })

  const isFull = maxItems > 0 && items.length >= maxItems

  // Check if this is the Y-axis zone to add special indicator
  const isYAxis = id === 'y-axis';

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "border rounded-md p-2 transition-all duration-200",
        isOver && !isFull ? "border-primary bg-primary/10 shadow-sm ring-1 ring-primary/30" : "border-border",
        isRequired && items.length === 0 ? "border-dashed border-amber-500 border-2" : "",
        isFull ? "opacity-70" : "opacity-100"
      )}
    >
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-1">
          {icon}
          <h4 className="text-xs font-medium">
            {title}
            {isRequired && <span className="text-destructive ml-1">*</span>}
          </h4>
        </div>
        <Badge
          variant={items.length > 0 ? "default" : "outline"}
          className="text-[10px] px-1"
        >
          {items.length}/{maxItems > 0 ? maxItems : '∞'}
        </Badge>
      </div>

      {description && (
        <p className="text-[10px] text-muted-foreground mb-2">{description}</p>
      )}

      <div className="flex flex-wrap gap-1">
        {items.map(item => (
          <ZoneItem
            key={item.id}
            item={item}
            onRemove={() => onItemRemove(item.id, id)}
            sourceZone={id}
          />
        ))}

        {items.length === 0 && (
          <div className={cn(
            "w-full h-10 flex items-center justify-center text-[10px] text-muted-foreground border border-dashed rounded-md transition-colors",
            isOver ? "bg-primary/5 border-primary/40" : "border-muted"
          )}>
            {isOver ? "Drop here" : "Drop items here"}
          </div>
        )}
      </div>
    </div>
  )
}

// Component to display the list of available columns
export const ColumnsList = ({
  columns,
  usedColumnIds = []
}: {
  columns: DraggableItem[]
  usedColumnIds?: string[]
}) => {
  return (
    <ScrollArea className="h-[400px]">
      <div className="space-y-1.5 pr-3">
        {columns.map(column => {
          const isUsed = usedColumnIds.includes(column.id);
          return (
            <DraggableColumn
              key={column.id}
              item={column}
              disabled={isUsed}
            />
          );
        })}

        {columns.length === 0 && (
          <div className="p-4 text-center text-sm text-muted-foreground">
            No columns available
          </div>
        )}
      </div>
    </ScrollArea>
  )
}

// Component to display the list of available columns with drop functionality
export const AvailableColumnsDropZone = ({
  columns,
  usedColumnIds = []
}: {
  columns: DraggableItem[]
  usedColumnIds?: string[]
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const { isOver, setNodeRef } = useDroppable({
    id: 'available-columns',
    data: {
      accepts: ['column']
    }
  });
  
  // Filter columns based on search term
  const filteredColumns = useMemo(() => {
    if (!searchTerm) return columns;
    return columns.filter(col => 
      col.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      col.id.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [columns, searchTerm]);

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "col-span-2 border rounded-lg p-2 transition-all duration-200",
        isOver ? "border-primary bg-primary/10 shadow-sm ring-1 ring-primary/30" : "border-border"
      )}
    >
      <h3 className="text-sm font-medium mb-2">Available Columns</h3>
      <div className="relative mb-2">
        <input
          type="text"
          placeholder="Search columns..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full h-8 px-2 py-1 text-xs border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
        />
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        )}
      </div>
      <div className="h-[400px] overflow-y-auto">
        <ScrollArea className="h-full">
          <div className={cn(
            "space-y-1.5 pr-3",
            isOver ? "bg-primary/5" : ""
          )}>
            {filteredColumns.map(column => {
              const isUsed = usedColumnIds.includes(column.id);
              return (
                <DraggableColumn
                  key={column.id}
                  item={column}
                  disabled={isUsed}
                />
              );
            })}

            {filteredColumns.length === 0 && (
              <div className="p-4 text-center text-sm text-muted-foreground">
                No columns available
              </div>
            )}

            {isOver && (
              <div className="mt-2 p-4 border border-dashed border-primary/30 rounded-md text-center text-xs animate-pulse">
                Drop here to return to available columns
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}