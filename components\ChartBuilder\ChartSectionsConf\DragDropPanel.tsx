'use client'

import { useState, useEffect, useRef } from 'react'
import { DraggableItem, DropZone, ColumnsList, AvailableColumnsDropZone } from './DragDropComponents'
import { PanelTop, ArrowUpDown, Layers, Pin, Filter as FilterIcon, BarChart3, <PERSON><PERSON>hart as LineChartIcon, <PERSON><PERSON><PERSON> as <PERSON><PERSON>hart<PERSON><PERSON>, TrendingUp } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { FilterOperator, ChartConfig, Filter as FilterConfig, ChartType } from '../types'
import { toast } from "sonner"
import { ChartPreview } from './ChartPreview'

// Helper function to check if a chart type is supported in chartTypeStyles
const isSupportedChartType = (type: ChartType): 'line' | 'bar' | 'pie' | 'area' | undefined => {
  switch (type) {
    case 'line':
      return 'line';
    case 'bar':
      return 'bar';
    case 'pie':
      return 'pie';
    case 'area':
      return 'area';
    default:
      return undefined;
  }
};


// Simple color picker component with predefined colors
interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
}

// Debounced input component to prevent excessive re-renders
interface DebouncedInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const DebouncedInput = ({ value, onChange, placeholder, className }: DebouncedInputProps) => {
  const [localValue, setLocalValue] = useState(value);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set a new timeout to update the parent after 300ms of no typing
    timeoutRef.current = setTimeout(() => {
      onChange(newValue);
    }, 300);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <Input
      value={localValue}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
    />
  );
};

const ColorPicker = ({ value, onChange }: ColorPickerProps) => {
  const colors = [
    '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
    '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
  ];

  return (
    <div className="flex flex-wrap gap-2">
      {colors.map((color, i) => (
        <button
          key={i}
          type="button"
          className={`w-6 h-6 rounded-full ${value === color ? 'ring-2 ring-primary ring-offset-2' : ''}`}
          style={{ backgroundColor: color }}
          onClick={() => onChange(color)}
          aria-label={`Color ${i + 1}`}
        />
      ))}
    </div>
  );
};

interface DragDropPanelProps {
  activeTab: string
  allColumns: DraggableItem[]
  xAxisColumns: DraggableItem[]
  yAxisColumns: DraggableItem[]
  groupByColumns: DraggableItem[]
  filterColumns: DraggableItem[]
  labelColumns: DraggableItem[]
  usedColumnIds: string[]
  config: ChartConfig
  filters: FilterConfig[]
  handleItemDrop: (item: DraggableItem, zoneId: string) => void
  handleItemRemove: (itemId: string, zoneId: string) => void
  handleConfigChange: (updates: Partial<ChartConfig>) => void
  updateFilter: (index: number, updates: Partial<FilterConfig>) => void
  handleDragFromZone: (item: DraggableItem, sourceZone: string) => void
  chartData: Record<string, any>[]
  chartSeries: any[]
}

export function DragDropPanel({
  activeTab,
  allColumns,
  xAxisColumns,
  yAxisColumns,
  groupByColumns,
  filterColumns,
  labelColumns,
  usedColumnIds,
  config,
  filters,
  handleItemDrop,
  handleItemRemove,
  handleConfigChange,
  updateFilter,
  handleDragFromZone,
  chartData,
  chartSeries
}: DragDropPanelProps) {
  // Enhanced function to handle both drop and drag from zone
  const handleZoneItemRemove = (itemId: string, zoneId: string) => {
    // Find the item in the appropriate zone
    let item: DraggableItem | undefined;

    switch (zoneId) {
      case 'x-axis':
        item = xAxisColumns.find(col => col.id === itemId);
        break;
      case 'y-axis':
        item = yAxisColumns.find(col => col.id === itemId);
        break;
      case 'group-by':
        item = groupByColumns.find(col => col.id === itemId);
        break;
      case 'filter':
        item = filterColumns.find(col => col.id === itemId);
        break;
      case 'label':
        item = labelColumns.find(col => col.id === itemId);
        break;
    }

    // If the item is found, trigger the drag from zone action
    if (item) {
      // Removed toast notification for better performance during drag operations
      handleDragFromZone(item, zoneId);
    }

    // Always remove the item from its zone
    handleItemRemove(itemId, zoneId);
  };

  // Removed console logging for performance improvement

  return (
    <div className="grid grid-cols-12 gap-4">
      {/* Left Column - Available Columns (narrower) */}
      <AvailableColumnsDropZone
        columns={allColumns.map(col => ({...col}))}
        usedColumnIds={usedColumnIds}
      />

      {/* Middle Column - Chart Configuration (narrower) */}
      <div className="col-span-3 border rounded-lg p-2">
        {activeTab === 'chart' && (
          <>
            <h3 className="text-sm font-medium mb-2">Chart Layout</h3>
            <div className="space-y-3">
              <DropZone
                id="x-axis"
                title="X-Axis"
                acceptTypes={['column']}
                items={xAxisColumns}
                onItemDrop={handleItemDrop}
                onItemRemove={handleZoneItemRemove}
                isRequired={true}
                maxItems={5} // Allow multiple X-axis columns
                description="Multiple columns for multi-dimensional charts"
                icon={<PanelTop className="h-4 w-4" />}
              />

              <DropZone
                id="y-axis"
                title="Y-Axis"
                acceptTypes={['column']}
                items={yAxisColumns}
                onItemDrop={handleItemDrop}
                onItemRemove={handleZoneItemRemove}
                isRequired={true}
                maxItems={config.type === 'pie' ? 1 : 5}
                description={config.type !== 'pie' ? "Multi-series charts" : undefined}
                icon={<ArrowUpDown className="h-4 w-4" />}
              />

              <DropZone
                id="group-by"
                title="Group By"
                acceptTypes={['column']}
                items={groupByColumns}
                onItemDrop={handleItemDrop}
                onItemRemove={handleZoneItemRemove}
                maxItems={1}
                icon={<Layers className="h-4 w-4" />}
              />

              <DropZone
                id="label"
                title="Custom Label"
                acceptTypes={['column']}
                items={labelColumns}
                onItemDrop={handleItemDrop}
                onItemRemove={handleZoneItemRemove}
                maxItems={1}
                icon={<Pin className="h-4 w-4" />}
              />

              {/* Visual Enhancement Properties */}
              <div className="mt-4 pt-2 border-t border-dashed">
                <h4 className="text-xs font-medium mb-2">Visual Enhancements</h4>

                {/* Opacity Slider */}
                <div className="space-y-1 mb-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Opacity: {(config.opacity || 0.8).toFixed(1)}</Label>
                  </div>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={config.opacity || 0.8}
                    onChange={(e) => handleConfigChange({ opacity: parseFloat(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                </div>

                {/* Removed Shape and Size selectors for more architect-focused design */}
              </div>
            </div>
          </>
        )}

        {activeTab === 'style' && (
          <>
            <h3 className="text-sm font-medium mb-2">Chart Style</h3>
            <div className="space-y-3">
              {/* Global Chart Color */}
              <div className="space-y-1">
                <Label className="text-xs">Global Chart Color</Label>
                <ColorPicker
                  value={config.color}
                  onChange={(color: string) => {
                    // Update both the main color and the chart type styles
                    const updatedChartTypeStyles = { ...config.chartTypeStyles };

                    // Update the color for the current chart type
                    if (config.type) {
                      // Only update for supported chart types
                      const supportedType = isSupportedChartType(config.type);
                      if (supportedType) {
                        updatedChartTypeStyles[supportedType] = {
                          color: color,
                          opacity: (updatedChartTypeStyles[supportedType]?.opacity || 0.8)
                        };
                      }
                    }

                    // Update all selected chart types
                    if (config.chartTypes && config.chartTypes.length > 0) {
                      config.chartTypes.forEach(type => {
                        // Only update for supported chart types
                        const supportedType = isSupportedChartType(type);
                        if (supportedType) {
                          updatedChartTypeStyles[supportedType] = {
                            color: color,
                            opacity: (updatedChartTypeStyles[supportedType]?.opacity || 0.8)
                          };
                        }
                      });
                    }

                    handleConfigChange({
                      color,
                      chartTypeStyles: updatedChartTypeStyles
                    });
                  }}
                />
              </div>

              {/* Per Chart Type Styling */}
              <div className="space-y-2 pt-2 border-t border-dashed">
                <h4 className="text-xs font-medium">Chart Type Styles</h4>

                {/* Line Chart Style */}
                {(config.type === 'line' || config.chartTypes.includes('line')) && (
                  <div className="space-y-2 p-2 border rounded-md">
                    <div className="flex items-center gap-2">
                      <LineChartIcon className="h-4 w-4" />
                      <Label className="text-xs font-medium">Line Chart</Label>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-1">
                        <Label className="text-xs">Color</Label>
                        <ColorPicker
                          value={config.chartTypeStyles?.line?.color || config.color}
                          onChange={(color: string) => {
                            const updatedChartTypeStyles = { ...config.chartTypeStyles };
                            updatedChartTypeStyles.line = {
                              color: color || config.color,
                              opacity: updatedChartTypeStyles.line?.opacity || 0.8
                            };
                            handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                          }}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Opacity</Label>
                        <div className="flex items-center gap-2">
                          <input
                            type="range"
                            min="0.1"
                            max="1"
                            step="0.1"
                            value={config.chartTypeStyles?.line?.opacity || 0.8}
                            onChange={(e) => {
                              const updatedChartTypeStyles = { ...config.chartTypeStyles };
                              updatedChartTypeStyles.line = {
                                color: updatedChartTypeStyles.line?.color || config.color,
                                opacity: parseFloat(e.target.value)
                              };
                              handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                            }}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <span className="text-xs w-8 text-right">
                            {(config.chartTypeStyles?.line?.opacity || 0.8).toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Bar Chart Style */}
                {(config.type === 'bar' || config.chartTypes.includes('bar')) && (
                  <div className="space-y-2 p-2 border rounded-md">
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      <Label className="text-xs font-medium">Bar Chart</Label>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-1">
                        <Label className="text-xs">Color</Label>
                        <ColorPicker
                          value={config.chartTypeStyles?.bar?.color || config.color}
                          onChange={(color: string) => {
                            const updatedChartTypeStyles = { ...config.chartTypeStyles };
                            updatedChartTypeStyles.bar = {
                              color: color || config.color,
                              opacity: updatedChartTypeStyles.bar?.opacity || 0.8
                            };
                            handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                          }}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Opacity</Label>
                        <div className="flex items-center gap-2">
                          <input
                            type="range"
                            min="0.1"
                            max="1"
                            step="0.1"
                            value={config.chartTypeStyles?.bar?.opacity || 0.8}
                            onChange={(e) => {
                              const updatedChartTypeStyles = { ...config.chartTypeStyles };
                              updatedChartTypeStyles.bar = {
                                color: updatedChartTypeStyles.bar?.color || config.color,
                                opacity: parseFloat(e.target.value)
                              };
                              handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                            }}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <span className="text-xs w-8 text-right">
                            {(config.chartTypeStyles?.bar?.opacity || 0.8).toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Area Chart Style */}
                {(config.type === 'area' || config.chartTypes.includes('area')) && (
                  <div className="space-y-2 p-2 border rounded-md">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      <Label className="text-xs font-medium">Area Chart</Label>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-1">
                        <Label className="text-xs">Color</Label>
                        <ColorPicker
                          value={config.chartTypeStyles?.area?.color || config.color}
                          onChange={(color: string) => {
                            const updatedChartTypeStyles = { ...config.chartTypeStyles };
                            updatedChartTypeStyles.area = {
                              color: color || config.color,
                              opacity: updatedChartTypeStyles.area?.opacity || 0.8
                            };
                            handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                          }}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Opacity</Label>
                        <div className="flex items-center gap-2">
                          <input
                            type="range"
                            min="0.1"
                            max="1"
                            step="0.1"
                            value={config.chartTypeStyles?.area?.opacity || 0.8}
                            onChange={(e) => {
                              const updatedChartTypeStyles = { ...config.chartTypeStyles };
                              updatedChartTypeStyles.area = {
                                color: updatedChartTypeStyles.area?.color || config.color,
                                opacity: parseFloat(e.target.value)
                              };
                              handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                            }}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <span className="text-xs w-8 text-right">
                            {(config.chartTypeStyles?.area?.opacity || 0.8).toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Pie Chart Style */}
                {(config.type === 'pie' || config.chartTypes.includes('pie')) && (
                  <div className="space-y-2 p-2 border rounded-md">
                    <div className="flex items-center gap-2">
                      <PieChartIcon className="h-4 w-4" />
                      <Label className="text-xs font-medium">Pie Chart</Label>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-1">
                        <Label className="text-xs">Color</Label>
                        <ColorPicker
                          value={config.chartTypeStyles?.pie?.color || config.color}
                          onChange={(color: string) => {
                            const updatedChartTypeStyles = { ...config.chartTypeStyles };
                            updatedChartTypeStyles.pie = {
                              ...(updatedChartTypeStyles.pie || {}),
                              color: color,
                              opacity: updatedChartTypeStyles.pie?.opacity || 0.8
                            };
                            handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                          }}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Opacity</Label>
                        <div className="flex items-center gap-2">
                          <input
                            type="range"
                            min="0.1"
                            max="1"
                            step="0.1"
                            value={config.chartTypeStyles?.pie?.opacity || 0.8}
                            onChange={(e) => {
                              const updatedChartTypeStyles = { ...config.chartTypeStyles };
                              updatedChartTypeStyles.pie = {
                                color: updatedChartTypeStyles.pie?.color || config.color,
                                opacity: parseFloat(e.target.value)
                              };
                              handleConfigChange({ chartTypeStyles: updatedChartTypeStyles });
                            }}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <span className="text-xs w-8 text-right">
                            {(config.chartTypeStyles?.pie?.opacity || 0.8).toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Title</Label>
                <DebouncedInput
                  value={config.title}
                  onChange={(value) => handleConfigChange({ title: value })}
                  placeholder="Chart Title"
                  className="h-7 text-xs"
                />
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Description</Label>
                <DebouncedInput
                  value={config.description}
                  onChange={(value) => handleConfigChange({ description: value })}
                  placeholder="Chart Description"
                  className="h-7 text-xs"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="enable-zoom" className="text-xs">Enable Zoom</Label>
                <Switch
                  id="enable-zoom"
                  checked={config.enableZoom}
                  onCheckedChange={(checked) =>
                    handleConfigChange({ enableZoom: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-grid" className="text-xs">Show Grid</Label>
                <Switch
                  id="show-grid"
                  checked={config.showGrid}
                  onCheckedChange={(checked) =>
                    handleConfigChange({ showGrid: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-legend" className="text-xs">Show Legend</Label>
                <Switch
                  id="show-legend"
                  checked={config.showLegend}
                  onCheckedChange={(checked) =>
                    handleConfigChange({ showLegend: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-labels" className="text-xs">Show Data Labels</Label>
                <Switch
                  id="show-labels"
                  checked={config.showLabels}
                  onCheckedChange={(checked) =>
                    handleConfigChange({ showLabels: checked })
                  }
                />
              </div>
            </div>
          </>
        )}

        {activeTab === 'analytics' && (
          <>
            <h3 className="text-sm font-medium mb-2">Data Analysis</h3>
            <ScrollArea className="h-[450px]">
              <div className="space-y-4 pr-2">
                {/* Basic Analytics */}
                <div className="space-y-3">
                  <h4 className="text-xs font-medium">Basic Analytics</h4>

                  <div className="space-y-1">
                    <Label className="text-xs">Aggregation Method</Label>
                    <Select
                      value={config.aggregation}
                      onValueChange={(value) =>
                        handleConfigChange({ aggregation: value as any })
                      }
                    >
                      <SelectTrigger className="h-7 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="sum">Sum</SelectItem>
                        <SelectItem value="average">Average</SelectItem>
                        <SelectItem value="min">Minimum</SelectItem>
                        <SelectItem value="max">Maximum</SelectItem>
                        <SelectItem value="count">Count</SelectItem>
                        <SelectItem value="median">Median</SelectItem>
                        <SelectItem value="mode">Mode</SelectItem>
                        <SelectItem value="stddev">Standard Deviation</SelectItem>
                        <SelectItem value="variance">Variance</SelectItem>
                        <SelectItem value="percentile25">25th Percentile</SelectItem>
                        <SelectItem value="percentile75">75th Percentile</SelectItem>
                        <SelectItem value="percentile90">90th Percentile</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs">Time Scale (for date fields)</Label>
                    <Select
                      value={config.timeScale}
                      onValueChange={(value) =>
                        handleConfigChange({ timeScale: value as any })
                      }
                    >
                      <SelectTrigger className="h-7 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="day">Day</SelectItem>
                        <SelectItem value="week">Week</SelectItem>
                        <SelectItem value="month">Month</SelectItem>
                        <SelectItem value="year">Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Advanced Analytics */}
                <div className="space-y-3 pt-2 border-t border-dashed">
                  <h4 className="text-xs font-medium">Advanced Analytics</h4>

                  {/* Trendline */}
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-trendline" className="text-xs">Show Trendline</Label>
                    <Switch
                      id="show-trendline"
                      checked={config.showTrendline}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ showTrendline: checked })
                      }
                    />
                  </div>

                  {config.showTrendline && (
                    <div className="space-y-1 ml-4">
                      <Label className="text-xs">Trendline Type</Label>
                      <Select
                        value={config.trendlineType}
                        onValueChange={(value) =>
                          handleConfigChange({ trendlineType: value as any })
                        }
                      >
                        <SelectTrigger className="h-7 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="linear">Linear</SelectItem>
                          <SelectItem value="polynomial">Polynomial</SelectItem>
                          <SelectItem value="exponential">Exponential</SelectItem>
                          <SelectItem value="logarithmic">Logarithmic</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Moving Average */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Moving Average Period</Label>
                      <span className="text-xs">{config.movingAveragePeriod}</span>
                    </div>
                    <input
                      type="range"
                      min="2"
                      max="20"
                      step="1"
                      value={config.movingAveragePeriod}
                      onChange={(e) => handleConfigChange({ movingAveragePeriod: parseInt(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>

                  {/* Outliers */}
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-outliers" className="text-xs">Highlight Outliers</Label>
                    <Switch
                      id="show-outliers"
                      checked={config.showOutliers}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ showOutliers: checked })
                      }
                    />
                  </div>
                </div>

                {/* Statistical Indicators */}
                <div className="space-y-3 pt-2 border-t border-dashed">
                  <h4 className="text-xs font-medium">Statistical Indicators</h4>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-mean" className="text-xs">Show Mean</Label>
                    <Switch
                      id="show-mean"
                      checked={config.showMean}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ showMean: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-median" className="text-xs">Show Median</Label>
                    <Switch
                      id="show-median"
                      checked={config.showMedian}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ showMedian: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-stddev" className="text-xs">Show Standard Deviation</Label>
                    <Switch
                      id="show-stddev"
                      checked={config.showStdDev}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ showStdDev: checked })
                      }
                    />
                  </div>

                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Confidence Interval</Label>
                      <span className="text-xs">{(config.confidenceInterval * 100).toFixed(0)}%</span>
                    </div>
                    <input
                      type="range"
                      min="0.5"
                      max="0.99"
                      step="0.01"
                      value={config.confidenceInterval}
                      onChange={(e) => handleConfigChange({ confidenceInterval: parseFloat(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>
                </div>

                {/* Data Comparison */}
                <div className="space-y-3 pt-2 border-t border-dashed">
                  <h4 className="text-xs font-medium">Data Comparison</h4>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="normalize-data" className="text-xs">Normalize Data</Label>
                    <Switch
                      id="normalize-data"
                      checked={config.normalizeData}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ normalizeData: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-percentage" className="text-xs">Show % Change</Label>
                    <Switch
                      id="show-percentage"
                      checked={config.showPercentageChange}
                      onCheckedChange={(checked) =>
                        handleConfigChange({ showPercentageChange: checked })
                      }
                    />
                  </div>
                </div>

                {/* Forecasting */}
                <div className="space-y-3 pt-2 border-t border-dashed">
                  <h4 className="text-xs font-medium">Forecasting</h4>

                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Forecast Periods</Label>
                      <span className="text-xs">{config.forecastPeriods}</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="12"
                      step="1"
                      value={config.forecastPeriods}
                      onChange={(e) => handleConfigChange({ forecastPeriods: parseInt(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>

                  {config.forecastPeriods > 0 && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Seasonality Periods</Label>
                        <span className="text-xs">{config.seasonalityPeriods}</span>
                      </div>
                      <input
                        type="range"
                        min="1"
                        max="24"
                        step="1"
                        value={config.seasonalityPeriods}
                        onChange={(e) => handleConfigChange({ seasonalityPeriods: parseInt(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                      />
                    </div>
                  )}
                </div>
              </div>
            </ScrollArea>
          </>
        )}

        {activeTab === 'filters' && (
          <>
            <h3 className="text-sm font-medium mb-2">Filters</h3>

            <DropZone
              id="filter"
              title="Drag columns here to create filters"
              acceptTypes={['column']}
              items={filterColumns}
              onItemDrop={handleItemDrop}
              onItemRemove={handleZoneItemRemove}
              maxItems={10}
              icon={<FilterIcon className="h-4 w-4" />}
            />

            {filters.length > 0 && (
              <div className="mt-3 space-y-2">
                <h4 className="text-xs font-medium">Configure Filters</h4>

                <ScrollArea className="h-[180px]">
                  <div className="space-y-2 pr-2">
                    {filters.map((filter, index) => (
                      <div key={index} className="space-y-1 border rounded-md p-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs">{filter.column}</Label>
                          <Switch
                            checked={filter.enabled}
                            onCheckedChange={(checked) =>
                              updateFilter(index, { enabled: checked })
                            }
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <Select
                            value={filter.operator}
                            onValueChange={(value) =>
                              updateFilter(index, { operator: value as FilterOperator })
                            }
                          >
                            <SelectTrigger className="h-7 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="equals">=</SelectItem>
                              <SelectItem value="notEquals">≠</SelectItem>
                              <SelectItem value="greaterThan">&gt;</SelectItem>
                              <SelectItem value="lessThan">&lt;</SelectItem>
                              <SelectItem value="contains">Contains</SelectItem>
                            </SelectContent>
                          </Select>

                          <Input
                            value={filter.value}
                            onChange={(e) =>
                              updateFilter(index, { value: e.target.value })
                            }
                            placeholder="Value"
                            className="h-7 text-xs"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
          </>
        )}
      </div>

      {/* Right Column - Preview (larger) */}
      <div className="col-span-7 border rounded-lg p-2">
        <h3 className="text-sm font-medium mb-2">Chart Preview</h3>
        <div className="h-[450px] rounded-md">
          <ChartPreview
            config={config}
            series={chartSeries}
            data={chartData}
            isConfigMode={true}
            fullHeight={true}
          />
        </div>
      </div>
    </div>
  );
}