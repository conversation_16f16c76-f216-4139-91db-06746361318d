'use client'

import { useState, useEffect, useMemo, useRef } from 'react'
import { DndContext, DragEndEvent, DragStartEvent, DragOverEvent } from '@dnd-kit/core'
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import './styles/drag-performance.css'

// We'll use the Filter type from the types file instead of defining our own

import {
  <PERSON><PERSON><PERSON> as Line<PERSON>hartIcon, <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON> as Pie<PERSON><PERSON>Icon, TrendingUp,
  <PERSON><PERSON><PERSON>, Palette, Filter as FilterIcon,
  ArrowUpDown, PanelTop,
  Table
} from "lucide-react"
import { DraggableItem } from './DragDropComponents'
import { DragDropPanel } from './DragDropPanel'
import { ChartPreview } from './ChartPreview' // Using ECharts implementation
import { ChartConfigExporter } from './ChartConfigExporter'
import {
  ChartConfig,
  ChartType,
  AggregationType,
  TimeScaleType,
  FilterOperator,
  Series,
  Filter,
  ChartVisualizerProps,
  ChartTypeStyle
} from '../types'
import { createPortal } from 'react-dom'
import { createRoot } from 'react-dom/client'

// Chart color palette
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
];

// Simple portal component to inject content into the chart-preview element
const ChartPreviewPortal = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) return null;

  const chartPreviewElement = document.getElementById('chart-preview');
  if (!chartPreviewElement) return null;

  return createPortal(children, chartPreviewElement);
};

export function EnhancedChartVisualizer({
  data,
  initialChartType = 'line',
  chartConfig: initialConfig,
  showConfig = false,
  fullHeight = false,
  onConfigChange,
  cellId,
  ...restProps
}: ChartVisualizerProps) {
  const safeData = data || [];

  // Helper functions to validate enum types
  function validateAggregationType(value?: string): AggregationType {
    const validValues: AggregationType[] = [
      'sum', 'average', 'min', 'max', 'count', 'median', 'mode',
      'stddev', 'variance', 'percentile25', 'percentile75', 'percentile90',
      'cumulative', 'movingAverage', 'none'
    ];
    return validValues.includes(value as AggregationType)
      ? (value as AggregationType)
      : 'none';
  }

  function validateTimeScaleType(value?: string): TimeScaleType {
    const validValues: TimeScaleType[] = ['day', 'week', 'month', 'year', 'none'];
    return validValues.includes(value as TimeScaleType)
      ? (value as TimeScaleType)
      : 'none';
  }

  function validateChartType(value?: string): ChartType {
    const validValues: ChartType[] = [
      'line', 'bar', 'pie', 'area', 'scatter', 'bubble',
      'heatmap', 'radar', 'boxplot', 'candlestick', 'funnel', 'gauge'
    ];
    return validValues.includes(value as ChartType)
      ? (value as ChartType)
      : 'line';
  }

  function validateFilterOperator(value: string): FilterOperator {
    const validValues: FilterOperator[] = ['equals', 'notEquals', 'greaterThan', 'lessThan', 'contains'];
    return validValues.includes(value as FilterOperator)
      ? (value as FilterOperator)
      : 'equals';
  }

  // State for chart configuration
  const [config, setConfig] = useState<ChartConfig>({
    // Basic chart properties
    type: (initialConfig?.type as ChartType) || initialChartType as ChartType,
    chartTypes: initialConfig?.chartTypes as ChartType[] || [] as ChartType[], // Support for multiple chart types
    xAxis: initialConfig?.xAxis || '',
    xAxisColumns: initialConfig?.xAxisColumns || [], // Support for multiple X-axis columns
    yAxis: initialConfig?.yAxis || '',
    title: initialConfig?.title || 'Chart Visualization',
    description: initialConfig?.description || 'Data visualization',
    color: initialConfig?.color || COLORS[0],
    showLegend: initialConfig?.showLegend !== undefined ? initialConfig.showLegend : true,
    showLabels: initialConfig?.showLabels !== undefined ? initialConfig.showLabels : false,
    showGrid: initialConfig?.showGrid !== undefined ? initialConfig.showGrid : true,

    // Chart type specific styling
    // @ts-ignore
    chartTypeStyles: initialConfig?.chartTypeStyles || {
      line: { color: COLORS[0], opacity: 0.8 },
      bar: { color: COLORS[1], opacity: 0.8 },
      pie: { color: COLORS[2], opacity: 0.8 },
      area: { color: COLORS[3], opacity: 0.8 }
    } as {
      line: ChartTypeStyle;
      bar: ChartTypeStyle;
      pie: ChartTypeStyle;
      area: ChartTypeStyle;
    },

    // Visual properties
    opacity: initialConfig?.opacity !== undefined ? initialConfig.opacity : 0.8,

    // View options
    showAsTable: initialConfig?.showAsTable !== undefined ? initialConfig.showAsTable : false,

    // Data processing
    aggregation: validateAggregationType(initialConfig?.aggregation),
    groupBy: initialConfig?.groupBy || 'none',
    timeScale: validateTimeScaleType(initialConfig?.timeScale),
    customLabel: initialConfig?.customLabel || '',

    // Interaction
    enableZoom: initialConfig?.enableZoom !== undefined ? initialConfig.enableZoom : true,
    multiSeries: initialConfig?.multiSeries !== undefined ? initialConfig.multiSeries : false,

    // Advanced analytics
    showTrendline: initialConfig?.showTrendline !== undefined ? initialConfig.showTrendline : false,
    trendlineType: initialConfig?.trendlineType as any || 'linear',
    showOutliers: initialConfig?.showOutliers !== undefined ? initialConfig.showOutliers : false,
    confidenceInterval: initialConfig?.confidenceInterval !== undefined ? initialConfig.confidenceInterval : 0.95,
    movingAveragePeriod: initialConfig?.movingAveragePeriod !== undefined ? initialConfig.movingAveragePeriod : 3,

    // Statistical indicators
    showMean: initialConfig?.showMean !== undefined ? initialConfig.showMean : false,
    showMedian: initialConfig?.showMedian !== undefined ? initialConfig.showMedian : false,
    showStdDev: initialConfig?.showStdDev !== undefined ? initialConfig.showStdDev : false,

    // Comparison
    normalizeData: initialConfig?.normalizeData !== undefined ? initialConfig.normalizeData : false,
    showPercentageChange: initialConfig?.showPercentageChange !== undefined ? initialConfig.showPercentageChange : false,

    // Forecasting
    forecastPeriods: initialConfig?.forecastPeriods !== undefined ? initialConfig.forecastPeriods : 0,
    seasonalityPeriods: initialConfig?.seasonalityPeriods !== undefined ? initialConfig.seasonalityPeriods : 12
  });

  // State for filtering
  // Initialize filters from chartConfig if available
  const [filters, setFiltersState] = useState<Filter[]>(
    // Check if filters exist in the initial config
    initialConfig?.filters ? initialConfig.filters : []
  );
  
  // Create a function to update filters and notify parent component
  const setFilters = (newFilters: Filter[] | ((prev: Filter[]) => Filter[])) => {
    // Handle both direct assignment and function updater patterns
    if (typeof newFilters === 'function') {
      const updaterFn = newFilters;
      setFiltersState(prev => {
        const updatedFilters = updaterFn(prev);
        // If onConfigChange is provided, update the parent with the new filters
        if (onConfigChange && cellId) {
          const updatedConfig = {
            ...config,
            filters: updatedFilters
          };
          onConfigChange(updatedConfig);
        }
        return updatedFilters;
      });
    } else {
      setFiltersState(newFilters);
      
      // If onConfigChange is provided, update the parent with the new filters
      if (onConfigChange && cellId) {
        // Create a new config object that includes the filters
        const updatedConfig = {
          ...config,
          filters: newFilters
        };
        onConfigChange(updatedConfig);
      }
    }
  };

  // State for data series in multi-series mode
  const [series, setSeries] = useState<Series[]>([]);

  // UI state
  const [activeTab, setActiveTab] = useState('chart');

  // Dropzone states
  const [xAxisColumns, setXAxisColumns] = useState<DraggableItem[]>([]);
  const [yAxisColumns, setYAxisColumns] = useState<DraggableItem[]>([]);
  const [groupByColumns, setGroupByColumns] = useState<DraggableItem[]>([]);
  const [filterColumns, setFilterColumns] = useState<DraggableItem[]>([]);
  const [labelColumns, setLabelColumns] = useState<DraggableItem[]>([]);

  // To track all columns made available for dragging
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [availableColumns, setAvailableColumns] = useState<DraggableItem[]>([]);

  // Flag to prevent update loops
  const isUpdating = useRef(false);

  // Data columns detection
  const columns = useMemo(() =>
    safeData.length > 0 ? Object.keys(safeData[0]) : [],
    [safeData]
  );

  const numericColumns = useMemo(() =>
    safeData.length > 0
      ? columns.filter(col => typeof safeData[0][col] === 'number')
      : [],
    [safeData, columns]
  );

  const dateColumns = useMemo(() =>
    safeData.length > 0
      ? columns.filter(col => {
          const val = safeData[0][col];
          return typeof val === 'string' &&
                (val.match(/^\d{4}-\d{2}-\d{2}/) ||
                 val.match(/^\d{2}\/\d{2}\/\d{4}/));
        })
      : [],
    [safeData, columns]
  );

  // Convert columns to draggable items
  const draggableColumns = useMemo(() => {
    return columns.map(col => {
      let dataType: 'string' | 'number' | 'date' | 'boolean' = 'string';

      if (numericColumns.includes(col)) {
        dataType = 'number';
      } else if (dateColumns.includes(col)) {
        dataType = 'date';
      } else if (safeData.length > 0 && typeof safeData[0][col] === 'boolean') {
        dataType = 'boolean';
      }

      return {
        id: col,
        name: col,
        type: 'column',
        dataType
      };
    });
  }, [columns, numericColumns, dateColumns, safeData]);

  // Update available columns
  useEffect(() => {
    if (draggableColumns.length > 0) {
      setAvailableColumns(draggableColumns);
    }
  }, [draggableColumns]);

  // Track used column IDs to filter available ones
  const usedColumnIds = useMemo(() => {
    const ids: string[] = [];

    xAxisColumns.forEach(col => ids.push(col.id));
    yAxisColumns.forEach(col => ids.push(col.id));
    groupByColumns.forEach(col => ids.push(col.id));
    labelColumns.forEach(col => ids.push(col.id));

    // We don't add filter columns to this list because we allow the same column to be filtered multiple ways

    return ids;
  }, [xAxisColumns, yAxisColumns, groupByColumns, labelColumns]);

  // Update config when initialChartType changes
  useEffect(() => {
    setConfig(prev => ({
      ...prev,
      type: validateChartType(initialConfig?.type) || initialChartType
    }));
  }, [initialConfig, initialChartType]);

  // Sync drop zones with config on initial load
  useEffect(() => {
    // Initialize X axis
    if (config.xAxis && xAxisColumns.length === 0) {
      const xAxisCol = draggableColumns.find(col => col.id === config.xAxis);
      if (xAxisCol) {
        setXAxisColumns([{...xAxisCol, sourceZone: 'x-axis'}]);
      }
    }

    // Initialize Y axis
    if (config.yAxis && yAxisColumns.length === 0) {
      const yAxisCol = draggableColumns.find(col => col.id === config.yAxis);
      if (yAxisCol) {
        setYAxisColumns([{...yAxisCol, sourceZone: 'y-axis'}]);
      }
    }

    // Initialize Group By
    if (config.groupBy && config.groupBy !== 'none' && groupByColumns.length === 0) {
      const groupByCol = draggableColumns.find(col => col.id === config.groupBy);
      if (groupByCol) {
        setGroupByColumns([{...groupByCol, sourceZone: 'group-by'}]);
      }
    }
  }, [config, draggableColumns, xAxisColumns.length, yAxisColumns.length, groupByColumns.length]);

  // Sync config with drop zones
  useEffect(() => {
    if (isUpdating.current) return;

    // Only update when we have valid dropzone contents
    if (xAxisColumns.length === 0 || yAxisColumns.length === 0) return;

    isUpdating.current = true;

    const newConfig = {
      ...config,
      // For X-axis, we'll use the first column as the primary X-axis
      // but we'll keep track of all columns for multi-dimensional charts
      xAxis: xAxisColumns[0]?.id || config.xAxis,
      // Track all X-axis columns for multi-dimensional charts
      xAxisColumns: xAxisColumns.map(col => col.id),
      yAxis: yAxisColumns[0]?.id || config.yAxis,
      groupBy: groupByColumns[0]?.id || 'none',
      customLabel: labelColumns[0]?.id || config.customLabel
    };

    // Update multi-series config based on Y-axis columns
    if (yAxisColumns.length > 1) {
      newConfig.multiSeries = true;

      // Create or update series based on columns
      const newSeries = yAxisColumns.map((col, index) => {
        // Try to find existing series for this column
        const existingSeries = series.find(s => s.field === col.id);

        if (existingSeries) {
          return existingSeries;
        }

        // Create new series
        return {
          id: `series-${Date.now()}-${index}`,
          field: col.id,
          color: COLORS[index % COLORS.length],
          label: col.name,
          visible: true
        };
      });

      setSeries(newSeries);
    } else {
      newConfig.multiSeries = false;
    }

    // Update config and notify parent
    // Include filters in the config update
    const updatedConfig = {
      ...newConfig,
      filters: filters
    };
    
    setConfig(updatedConfig);

    if (onConfigChange) {
      onConfigChange(updatedConfig);
    }

    setTimeout(() => {
      isUpdating.current = false;
    }, 100);
  }, [xAxisColumns, yAxisColumns, groupByColumns, labelColumns]);

  // Handler for dropping items in drop zones
  const handleItemDrop = (item: DraggableItem, zoneId: string) => {
    const enhancedItem = { ...item, sourceZone: zoneId };

    // If the item is coming from another zone, remove it from that zone first
    if (item.sourceZone && item.sourceZone !== zoneId) {
      handleItemRemove(item.id, item.sourceZone);
    }

    switch (zoneId) {
      case 'x-axis':
        // Allow multiple X-axis columns similar to Y-axis
        // Don't add duplicates
        if (!xAxisColumns.some(col => col.id === item.id)) {
          setXAxisColumns(prev => [...prev, enhancedItem]);
        }
        break;
      case 'y-axis':
        // Allow any column type for Y-axis, including strings
        if (config.type === 'pie') {
          // For pie charts, only allow one Y-axis
          setYAxisColumns([enhancedItem]);
        } else {
          // For other charts, allow multiple Y-axes for multi-series
          // Don't add duplicates
          if (!yAxisColumns.some(col => col.id === item.id)) {
            setYAxisColumns(prev => [...prev, enhancedItem]);
          }
        }
        break;
      case 'group-by':
        setGroupByColumns([enhancedItem]);
        break;
      case 'filter':
        setFilterColumns(prev => [...prev, enhancedItem]);

        // Create a new filter only if one doesn't already exist for this column
        if (!filters.some(f => f.column === item.id)) {
          setFilters(prev => [
            ...prev,
            {
              column: item.id,
              operator: 'equals',
              value: '',
              enabled: true
            }
          ]);
        }
        break;
      case 'label':
        setLabelColumns([enhancedItem]);

        // Update custom label
        handleConfigChange({ customLabel: item.name });
        break;
    }
  };

  // Handler for dragging items from a zone back to available columns
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleDragFromZone = (item: DraggableItem, sourceZone: string) => {
    // The key is to make sure the item is removed from its current zone
    // handleItemRemove will handle this for us

    // Removed console.log and toast for better performance during drag operations
  };

  // Handler for removing items from drop zones
  const handleItemRemove = (itemId: string, zoneId: string) => {
    // Removed console.log for better performance

    switch (zoneId) {
      case 'x-axis':
        setXAxisColumns([]);
        // Update the config to reflect the removal
        if (config.xAxis === itemId) {
          handleConfigChange({ xAxis: '' });
        }
        // Removed toast notification for better performance during drag operations
        break;
      case 'y-axis':
        setYAxisColumns(prev => prev.filter(item => item.id !== itemId));
        // If this was the only Y axis item, update the config
        if (yAxisColumns.length === 1 && yAxisColumns[0].id === itemId) {
          handleConfigChange({ yAxis: '' });
        }
        // Update multi-series if needed
        if (config.multiSeries) {
          setSeries(prev => prev.filter(s => s.field !== itemId));
        }
        // Removed toast notification for better performance during drag operations
        break;
      case 'group-by':
        setGroupByColumns([]);
        handleConfigChange({ groupBy: 'none' });
        // Removed toast notification for better performance during drag operations
        break;
      case 'filter':
        setFilterColumns(prev => prev.filter(item => item.id !== itemId));

        // Remove associated filters
        setFilters(prev => prev.filter(f => f.column !== itemId));
        // Removed toast notification for better performance during drag operations
        break;
      case 'label':
        setLabelColumns([]);
        handleConfigChange({ customLabel: '' });
        // Removed toast notification for better performance during drag operations
        break;
    }
  };

  // Update config
  const handleConfigChange = (newPartialConfig: Partial<ChartConfig>) => {
    if (isUpdating.current) return;

    isUpdating.current = true;

    // Handle chart type changes that affect multi-series
    if (newPartialConfig.type === 'pie' && config.type !== 'pie' && yAxisColumns.length > 1) {
      // When switching to pie, keep only the first y-axis
      setYAxisColumns([yAxisColumns[0]]);
      toast.info("Pie charts support only one data series");
    }

    // Create the updated config
    const updatedConfig = { ...config, ...newPartialConfig };

    // Update local state
    setConfig(updatedConfig);

    // Notify parent if callback exists
    if (onConfigChange) {
      // Use a debounced approach to reduce update frequency
      const timeoutId = setTimeout(() => {
        onConfigChange(updatedConfig);
        isUpdating.current = false;
      }, 50);

      // Cleanup function to prevent memory leaks
      return () => {
        clearTimeout(timeoutId);
        isUpdating.current = false;
      };
    } else {
      isUpdating.current = false;
    }
  };

  // Update a filter
  const updateFilter = (index: number, partialFilter: Partial<Filter>) => {
    const updatedFilters = [...filters];
    // Ensure operator is always valid
    if (partialFilter.operator) {
      partialFilter.operator = validateFilterOperator(partialFilter.operator);
    }
    updatedFilters[index] = { ...updatedFilters[index], ...partialFilter };
    setFilters(updatedFilters);
  };

  // Apply filters to data
  const filteredData = useMemo(() => {
    if (!filters.length) return safeData;

    return safeData.filter(row => {
      return filters.every(filter => {
        if (!filter.enabled) return true;

        const value = row[filter.column];

        switch (filter.operator) {
          case 'equals':
            return value == filter.value;
          case 'notEquals':
            return value != filter.value;
          case 'greaterThan':
            return typeof value === 'number' && value > Number(filter.value);
          case 'lessThan':
            return typeof value === 'number' && value < Number(filter.value);
          case 'contains':
            return typeof value === 'string' &&
                  value.toLowerCase().includes(String(filter.value).toLowerCase());
          default:
            return true;
        }
      });
    });
  }, [safeData, filters]);

  // Apply aggregations to data
  const processedData = useMemo(() => {
    if (config.aggregation === 'none' && (config.groupBy === 'none' || !config.groupBy)) {
      return filteredData;
    }

    // Group data
    const groupedData: Record<string, any[]> = {};

    filteredData.forEach(row => {
      // Use 'all' as the group key when groupBy is 'none' or empty
      const groupKey = config.groupBy && config.groupBy !== 'none'
        ? String(row[config.groupBy])
        : 'all';

      if (!groupedData[groupKey]) {
        groupedData[groupKey] = [];
      }
      groupedData[groupKey].push(row);
    });

    // Apply aggregation
    return Object.entries(groupedData).map(([key, rows]) => {
      const result: Record<string, any> = {};

      // Set group by value
      if (config.groupBy && config.groupBy !== 'none') {
        result[config.groupBy] = key;
      }

      // Set x-axis value if different from group by
      if (config.xAxis !== config.groupBy) {
        result[config.xAxis] = rows[0]?.[config.xAxis] || key;
      }

      // Get all fields needed for multi-series
      const fieldsToAggregate = config.multiSeries
        ? series.map(s => s.field)
        : [config.yAxis];

      // Apply aggregation function to all required numeric columns
      if (config.aggregation !== 'none') {
        fieldsToAggregate.forEach(field => {
          const values = rows.map((r: Record<string, any>) => {
            const val = r[field];
            // Handle various non-numeric cases
            if (val === null || val === undefined || val === '') return NaN;

            // If it's already a number, return it
            if (typeof val === 'number') return val;

            // If it's a string that can be converted to a number, convert it
            if (typeof val === 'string') {
              // Try to extract numeric part if it's a string with mixed content
              const numericMatch = val.match(/-?\d+(\.\d+)?/);
              if (numericMatch) {
                return Number(numericMatch[0]);
              }

              // For strings that don't contain numbers, use string length as a numeric representation
              return val.length;
            }

            // For other types, try to convert to number
            const num = Number(val);
            return num;
          }).filter((v: number) => !isNaN(v));

          switch (config.aggregation) {
            case 'sum':
              result[field] = values.reduce((sum: number, val: number) => sum + val, 0);
              break;
            case 'average':
              result[field] = values.length ?
                values.reduce((sum: number, val: number) => sum + val, 0) / values.length : 0;
              break;
            case 'min':
              result[field] = values.length ? Math.min(...values) : 0;
              break;
            case 'max':
              result[field] = values.length ? Math.max(...values) : 0;
              break;
            case 'count':
              result[field] = values.length;
              break;
          }
        });
      } else {
        // If no aggregation, use first row values
        if (rows.length > 0) {
          Object.keys(rows[0] || {}).forEach(col => {
            result[col] = rows[0][col];
          });
        }
      }

      return result;
    });
  }, [filteredData, config.aggregation, config.groupBy, config.yAxis, config.xAxis, config.multiSeries, series]);

  // Handle imported configuration
  const handleImportConfig = (importedConfig: any) => {
    // Update chart configuration with proper type validation
    setConfig({
      ...config,
      type: validateChartType(importedConfig.config.type),
      chartTypes: Array.isArray(importedConfig.config.chartTypes) ?
                 importedConfig.config.chartTypes.map((type: any) => validateChartType(type)) as ChartType[] :
                 config.chartTypes,
      xAxis: importedConfig.config.xAxis || config.xAxis,
      yAxis: importedConfig.config.yAxis || config.yAxis,
      title: importedConfig.config.title || config.title,
      description: importedConfig.config.description || config.description,
      color: importedConfig.config.color || config.color,
      showLegend: importedConfig.config.showLegend !== undefined ?
                  importedConfig.config.showLegend : config.showLegend,
      showLabels: importedConfig.config.showLabels !== undefined ?
                  importedConfig.config.showLabels : config.showLabels,
      showGrid: importedConfig.config.showGrid !== undefined ?
                importedConfig.config.showGrid : config.showGrid,
      // Chart type specific styling
      chartTypeStyles: importedConfig.config.chartTypeStyles || config.chartTypeStyles,
      // View options
      showAsTable: importedConfig.config.showAsTable !== undefined ?
                  importedConfig.config.showAsTable : config.showAsTable,
      aggregation: validateAggregationType(importedConfig.config.aggregation),
      groupBy: importedConfig.config.groupBy || config.groupBy,
      timeScale: validateTimeScaleType(importedConfig.config.timeScale),
      customLabel: importedConfig.config.customLabel || config.customLabel,
      enableZoom: importedConfig.config.enableZoom !== undefined ?
                  importedConfig.config.enableZoom : config.enableZoom,
      multiSeries: importedConfig.config.multiSeries !== undefined ?
                  importedConfig.config.multiSeries : config.multiSeries,
      opacity: importedConfig.config.opacity !== undefined ?
                  importedConfig.config.opacity : config.opacity
    });

    // Update series if available
    if (importedConfig.series && importedConfig.series.length > 0) {
      setSeries(importedConfig.series);
    }

    // Update filters if available
    if (importedConfig.filters && importedConfig.filters.length > 0) {
      setFilters(importedConfig.filters.map((filter: any) => ({
        column: filter.column,
        operator: validateFilterOperator(filter.operator),
        value: filter.value,
        enabled: filter.enabled
      })));
    }

    toast.success("Chart configuration imported successfully");
  };

  // Render the component
  return (
    <div className={`chart-visualizer-container ${fullHeight ? 'h-full' : ''}`} data-chart-visualizer {...restProps}>
      <div className={`overflow-hidden ${!showConfig ? 'h-full flex flex-col' : ''}`}>
        {/* Compact Header with Chart Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
          <div>
            <h3 className="text-lg font-medium">{config.title}</h3>
            {config.description && (
              <p className="text-sm text-muted-foreground">{config.description}</p>
            )}
          </div>

          {/* Only show controls when showConfig is true */}
          {showConfig && (
            <div className="flex flex-wrap items-center gap-2">
              {/* Chart Type Selector with Multiple Selection */}
              <div className="flex gap-1">
                <Button
                  variant={config.type === 'line' || config.chartTypes.includes('line') ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    // If it's already the primary type, toggle it in the chartTypes array
                    if (config.type === 'line') {
                      const isInChartTypes = config.chartTypes.includes('line');

                      // Only allow removing if there's at least one other chart type selected
                      if (isInChartTypes && config.chartTypes.length > 1) {
                        // Remove from chartTypes and set a new primary type
                        const newChartTypes = config.chartTypes.filter(t => t !== 'line');
                        // Set the first remaining chart type as the primary type
                        handleConfigChange({
                          chartTypes: newChartTypes,
                          type: newChartTypes[0]
                        });
                      } else if (!isInChartTypes) {
                        // Add to chartTypes
                        handleConfigChange({
                          chartTypes: [...config.chartTypes, 'line' as ChartType]
                        });
                      }
                      // If it's the only chart type, don't allow removing it
                    } else {
                      // Make it the primary type and add to chartTypes if not already there
                      const newChartTypes = config.chartTypes.includes('line')
                        ? config.chartTypes
                        : [...config.chartTypes, 'line' as ChartType];

                      handleConfigChange({
                        type: 'line',
                        chartTypes: newChartTypes
                      });
                    }
                  }}
                  title="Line Chart"
                >
                  <LineChartIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={config.type === 'bar' || config.chartTypes.includes('bar') ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    // If it's already the primary type, toggle it in the chartTypes array
                    if (config.type === 'bar') {
                      const isInChartTypes = config.chartTypes.includes('bar');

                      // Only allow removing if there's at least one other chart type selected
                      if (isInChartTypes && config.chartTypes.length > 1) {
                        // Remove from chartTypes and set a new primary type
                        const newChartTypes = config.chartTypes.filter(t => t !== 'bar');
                        // Set the first remaining chart type as the primary type
                        handleConfigChange({
                          chartTypes: newChartTypes,
                          type: newChartTypes[0]
                        });
                      } else if (!isInChartTypes) {
                        // Add to chartTypes
                        handleConfigChange({
                          chartTypes: [...config.chartTypes, 'bar' as ChartType]
                        });
                      }
                      // If it's the only chart type, don't allow removing it
                    } else {
                      // Make it the primary type and add to chartTypes if not already there
                      const newChartTypes = config.chartTypes.includes('bar')
                        ? config.chartTypes
                        : [...config.chartTypes, 'bar' as ChartType];

                      handleConfigChange({
                        type: 'bar',
                        chartTypes: newChartTypes
                      });
                    }
                  }}
                  title="Bar Chart"
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={config.type === 'pie' || config.chartTypes.includes('pie') ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    // If it's already the primary type, toggle it off and switch to another type
                    if (config.type === 'pie') {
                      // If pie is the only chart type, set bar as default
                      handleConfigChange({
                        type: 'bar',
                        chartTypes: ['bar' as ChartType]
                      });
                    } else {
                      // Pie charts can't be combined with other types, so just set it as the primary type
                      handleConfigChange({
                        type: 'pie',
                        chartTypes: ['pie' as ChartType]
                      });
                    }
                  }}
                  title="Pie Chart"
                >
                  <PieChartIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={config.type === 'area' || config.chartTypes.includes('area') ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    // If it's already the primary type, toggle it in the chartTypes array
                    if (config.type === 'area') {
                      const isInChartTypes = config.chartTypes.includes('area');

                      // Only allow removing if there's at least one other chart type selected
                      if (isInChartTypes && config.chartTypes.length > 1) {
                        // Remove from chartTypes and set a new primary type
                        const newChartTypes = config.chartTypes.filter(t => t !== 'area');
                        // Set the first remaining chart type as the primary type
                        handleConfigChange({
                          chartTypes: newChartTypes,
                          type: newChartTypes[0]
                        });
                      } else if (!isInChartTypes) {
                        // Add to chartTypes
                        handleConfigChange({
                          chartTypes: [...config.chartTypes, 'area' as ChartType]
                        });
                      }
                      // If it's the only chart type, don't allow removing it
                    } else {
                      // Make it the primary type and add to chartTypes if not already there
                      const newChartTypes = config.chartTypes.includes('area')
                        ? config.chartTypes
                        : [...config.chartTypes, 'area' as ChartType];

                      handleConfigChange({
                        type: 'area',
                        chartTypes: newChartTypes
                      });
                    }
                  }}
                  title="Area Chart"
                >
                  <TrendingUp className="h-4 w-4" />
                </Button>

                {/* Table View Toggle */}
                <Button
                  variant={config.showAsTable ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 w-8 p-0 ml-1"
                  onClick={() => handleConfigChange({ showAsTable: !config.showAsTable })}
                  title="Toggle Table View"
                >
                  <Table className="h-4 w-4" />
                </Button>
              </div>

              {/* Visual Configuration Exporter */}
              <ChartConfigExporter
                config={config}
                series={series}
                filters={filters}
                data={processedData}
                onImport={handleImportConfig}
              />

              {/* Tabs Selection */}
              <Button
                variant={activeTab === 'chart' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
                onClick={() => setActiveTab('chart')}
              >
                <PanelTop className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Chart</span>
              </Button>
              <Button
                variant={activeTab === 'style' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
                onClick={() => setActiveTab('style')}
              >
                <Palette className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Style</span>
              </Button>
              <Button
                variant={activeTab === 'analytics' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
                onClick={() => setActiveTab('analytics')}
              >
                <ArrowUpDown className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Analytics</span>
              </Button>
              <Button
                variant={activeTab === 'filters' ? 'default' : 'outline'}
                size="sm"
                className={`h-8 ${filters.length > 0 ? 'bg-primary text-primary-foreground' : ''}`}
                onClick={() => setActiveTab('filters')}
              >
                <FilterIcon className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">
                  {filters.length > 0 ? `Filters (${filters.length})` : 'Filters'}
                </span>
              </Button>
            </div>
          )}
        </div>

        {/* Drag and Drop Interface - only when showConfig is true */}
        {showConfig && (
          <DndContext
            onDragEnd={(event: DragEndEvent) => {
              const { active, over } = event;
              document.body.classList.remove('dragging-active');

              if (!over) {
                // Item was dragged but not dropped on a valid target
                return;
              }

              const activeData = active.data.current as DraggableItem;
              const overId = String(over.id);

              if (!activeData) return;

              // Check if this is a move between zones
              if (activeData.sourceZone && activeData.sourceZone !== overId) {
                // First handle removal from source zone
                if (activeData.sourceZone) {
                  handleItemRemove(activeData.id, activeData.sourceZone);
                }

                // Then add to new zone
                handleItemDrop(activeData, overId);
              }
              // It's a drag from available columns to a zone
              else if (!activeData.sourceZone) {
                handleItemDrop(activeData, overId);
              }
              // It's a drag from a zone back to the available columns container
              else if (overId === 'available-columns' && activeData.sourceZone) {
                handleDragFromZone(activeData, activeData.sourceZone);
                handleItemRemove(activeData.id, activeData.sourceZone);
              }
            }}
            onDragStart={(event: DragStartEvent) => {
              const activeData = event.active.data.current as DraggableItem;
              if (activeData) {
                // Add visual feedback during drag
                document.body.classList.add('dragging-active');
              }
            }}
            onDragOver={() => {
              // Empty handler - prevent browser freezing by removing all processing
            }}
            onDragCancel={() => {
              document.body.classList.remove('dragging-active');
            }}
          >
            {/* Add high z-index styling for dragged items */}
            <style jsx global>{`
            .dragging-active [data-draggable="true"] {
              position: relative;
              z-index: 50;
            }

            /* Ensure dragged items stay on top of everything */
            [data-dnd-draggable-dragging="true"] {
              z-index: 9999 !important;
              transform: translate3d(0, 0, 0) !important;
              will-change: transform !important;
              pointer-events: none !important;
            }
          `}</style>

            <DragDropPanel
              activeTab={activeTab}
              allColumns={draggableColumns}
              xAxisColumns={xAxisColumns}
              yAxisColumns={yAxisColumns}
              groupByColumns={groupByColumns}
              filterColumns={filterColumns}
              labelColumns={labelColumns}
              usedColumnIds={usedColumnIds}
              config={config}
              filters={filters}
              handleItemDrop={handleItemDrop}
              handleItemRemove={handleItemRemove}
              handleConfigChange={handleConfigChange}
              updateFilter={updateFilter}
              handleDragFromZone={handleDragFromZone}
              chartData={processedData}
              chartSeries={series}
            />
          </DndContext>
        )}

        {/* Add chart preview portal for the chart-preview element */}
        {showConfig && (
          <ChartPreviewPortal>
            <ChartPreview
              config={config}
              series={series}
              data={processedData}
              isConfigMode={true}
              isDashboardChart={false}
            />
          </ChartPreviewPortal>
        )}

        {/* Chart View - always rendered with proper dimensions when showConfig is false */}
        {!showConfig && (
          <div className="w-full flex-1">
            <ChartPreview
              config={config}
              series={series}
              data={processedData}
              isConfigMode={false}
              fullHeight={fullHeight}
              isDashboardChart={false}
            />
          </div>
        )}
      </div>
    </div>
  );
}