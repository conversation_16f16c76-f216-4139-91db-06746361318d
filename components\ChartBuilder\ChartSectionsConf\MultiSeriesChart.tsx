// @ts-nocheck

'use client'

import { useState, useEffect, useRef, memo, useMemo } from 'react'
import * as echarts from 'echarts'
import { RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { Series, ChartTypeStyle } from '../types'

// Chart color palette
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
];

// Utility function to safely convert values to numbers
// Enhanced to handle string values better and preserve text values
const safeNumber = (value: any): number => {
  // Handle null, undefined, or empty string
  if (value === null || value === undefined || value === '') return 0;

  // If it's already a number, return it
  if (typeof value === 'number') return value;

  // If it's a string that can be converted to a number, convert it
  if (typeof value === 'string') {
    // First try direct conversion
    const directNum = Number(value);
    if (!isNaN(directNum)) {
      return directNum;
    }

    // Try to extract numeric part if it's a string with mixed content
    const numericMatch = value.match(/-?\d+(\.\d+)?/);
    if (numericMatch) {
      return Number(numericMatch[0]);
    }

    // For strings that don't contain numbers, use string length as a numeric representation
    // This allows string values to be displayed in charts without errors
    // We add a small value to ensure all text values don't map to the same position
    return value.length + (value.charCodeAt(0) % 10) / 10;
  }

  // For other types, try to convert to number or use 0 as fallback
  const num = Number(value);
  return isNaN(num) ? 0 : num;
}

interface MultiSeriesChartProps {
  data: Record<string, any>[]
  chartType: 'line' | 'bar' | 'area'
  chartTypes?: ('line' | 'bar' | 'area')[] // Support for multiple chart types
  xAxis: string
  xAxisColumns?: string[] // Support for multiple X-axis columns
  series: Series[]
  showGrid: boolean
  showLegend: boolean
  showLabels: boolean
  enableZoom?: boolean
  isDashboardChart?: boolean // Flag to indicate if this is used in a dashboard card

  // Chart type specific styling
  chartTypeStyles?: {
    line?: ChartTypeStyle;
    bar?: ChartTypeStyle;
    pie?: ChartTypeStyle;
    area?: ChartTypeStyle;
  }

  // Statistical indicators
  showMean?: boolean
  showMedian?: boolean
  showStdDev?: boolean
}

// Use memo to prevent unnecessary re-renders
export const MultiSeriesChart = memo(function MultiSeriesChart({
  data,
  chartType,
  chartTypes = [],
  xAxis,
  xAxisColumns = [],
  series,
  showGrid,
  showLegend,
  showLabels,
  enableZoom = true,
  isDashboardChart = false,
  chartTypeStyles = {},
  showMean = false,
  showMedian = false,
  showStdDev = false
}: MultiSeriesChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isZoomed, setIsZoomed] = useState(false);

  // Filter to only visible series
  const visibleSeries = series.filter(s => s.visible);

  // Determine if any series has text values
  const hasTextYValues = visibleSeries.length === 0 || data.some(item =>
    visibleSeries.some(s => {
      if (!item || item[s.field] === undefined || item[s.field] === null) {
        return false; // Skip undefined or null values
      }
      return typeof item[s.field] === 'string' &&
        isNaN(Number(item[s.field])) &&
        !String(item[s.field]).match(/-?\d+(\.\d+)?/);
    })
  );

  // Get all numeric values from all series using the safeNumber utility
  // Only used for numeric axis scaling
  const allYValues = hasTextYValues
    ? []
    : visibleSeries.flatMap(s =>
        data.map(item => safeNumber(item[s.field]))
      );

  // Calculate min and max with fallbacks
  const yMin = allYValues.length > 0 ? Math.min(...allYValues) * 0.9 : 0;
  const yMax = allYValues.length > 0 ? Math.max(...allYValues) * 1.1 : 100;

  // Initialize chart
  useEffect(() => {
    if (!chartRef.current) return;

    // Dispose of any existing chart
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // Get theme based on document class
    const isDarkMode = document.documentElement.classList.contains('dark');

    // Initialize chart
    chartInstance.current = echarts.init(chartRef.current, isDarkMode ? 'dark' : undefined);

    // Handle window resize
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    // Handle theme change
    const handleThemeChange = () => {
      if (!chartRef.current || !chartInstance.current) return;

      const newIsDarkMode = document.documentElement.classList.contains('dark');
      chartInstance.current.dispose();
      chartInstance.current = echarts.init(chartRef.current, newIsDarkMode ? 'dark' : undefined);
      renderChart();
    };

    window.addEventListener('resize', handleResize);
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);

    renderChart();

    return () => {
      window.removeEventListener('resize', handleResize);
      window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', handleThemeChange);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, chartType, chartTypes, xAxis, series, showGrid, showLegend, showLabels, enableZoom, chartTypeStyles, showMean, showMedian, showStdDev]);

  // Reset zoom
  const resetZoom = () => {
    if (chartInstance.current) {
      chartInstance.current.dispatchAction({
        type: 'dataZoom',
        start: 0,
        end: 100
      });
      setIsZoomed(false);
      toast.success("Zoom reset");
    }
  };

  // Render chart based on type
  const renderChart = () => {
    if (!chartInstance.current) return;

    // Get theme colors based on current mode
    const isDarkMode = document.documentElement.classList.contains('dark');
    const textColor = isDarkMode ? '#e5e7eb' : '#374151';
    const axisLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';

    // Prepare data - preserve original values for text data
    const processedData = data.map(item => {
      const newItem = { ...item };
      // Process each visible series field
      visibleSeries.forEach(s => {
        // If we have text values, preserve them exactly as they are
        if (hasTextYValues) {
          newItem[s.field] = item[s.field];
        } else {
          // Otherwise convert to numbers for numeric charts
          newItem[s.field] = safeNumber(item[s.field]);
        }
      });
      return newItem;
    });

    // Base chart options
    const baseOption: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: function(params: any) {
          if (!params || !Array.isArray(params) || params.length === 0) {
            return 'No data';
          }

          // Get the x-axis value (category)
          const xValue = params[0].name || '';

          // Build tooltip content with all series values
          let content = `<strong>${xValue}</strong><br/>`;

          // Add all X-axis columns if available
          if (xAxisColumns && xAxisColumns.length > 0 && params[0]?.dataIndex !== undefined) {
            const dataIndex = params[0].dataIndex;
            if (data[dataIndex]) {
              xAxisColumns.forEach(col => {
                if (col !== xAxis && data[dataIndex][col] !== undefined) {
                  content += `<span style="color:#888">${col}:</span> ${data[dataIndex][col]}<br/>`;
                }
              });
              // Add a separator if we added any X-axis columns
              if (xAxisColumns.length > 1) {
                content += `<div style="border-top:1px dashed rgba(255,255,255,0.2);margin:4px 0;"></div>`;
              }
            }
          }

          // Add each series value with proper handling for undefined values
          params.forEach((param: any) => {
            if (param) {
              const seriesName = param.seriesName || 'Series';

              // Get the raw value from the original data to ensure we display the exact original value
              let value = param.value;

              // Extract the field name from the series name
              // For multi-type charts, the series name might be in format "Field (type)"
              let fieldName = seriesName;
              const typeMatch = seriesName.match(/^(.*) \((line|bar|area)\)$/);
              if (typeMatch) {
                fieldName = typeMatch[1]; // Extract the field name part
              }

              if (param.dataIndex !== undefined && data[param.dataIndex]) {
                // First try to find the exact series by label or field
                const seriesItem = series.find(s =>
                  s.label === fieldName ||
                  s.field === fieldName ||
                  s.label === seriesName ||
                  s.field === seriesName
                );

                if (seriesItem && seriesItem.field && data[param.dataIndex][seriesItem.field] !== undefined) {
                  // Get the original value directly from the data array
                  value = data[param.dataIndex][seriesItem.field];
                }
                // If we couldn't find the series but have a value in params, use that
                else if (param.value !== undefined && param.value !== null) {
                  value = param.value;
                }
                // If we still don't have a value but have the dataIndex, try to find any matching field
                else if (visibleSeries.length === 1) {
                  // If there's only one series, use its field
                  const onlyField = visibleSeries[0].field;
                  if (data[param.dataIndex][onlyField] !== undefined) {
                    value = data[param.dataIndex][onlyField];
                  }
                }
              }

              // Add color marker for better visual association
              const colorSpan = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;

              content += `${colorSpan}${seriesName}: ${value}<br/>`;
            }
          });

          return content;
        },
        // Improve tooltip appearance
        backgroundColor: 'rgba(50, 50, 50, 0.8)',
        borderRadius: 4,
        padding: [8, 10],
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '10%',
        containLabel: true
      },
      legend: {
        show: showLegend,
        bottom: 0,
        textStyle: {
          color: textColor
        }
      },
      xAxis: {
        type: 'category',
        data: processedData.map(item => item[xAxis]),
        axisLabel: {
          rotate: 45,
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        }
      },
      yAxis: {
        type: hasTextYValues ? 'category' : 'value',
        // @ts-ignore
        data: hasTextYValues ? Array.from(new Set(processedData.flatMap(item =>
          // @ts-ignore
          series.map(s => item[s.field])
        ))) : undefined,
        min: hasTextYValues ? undefined : yMin,
        max: hasTextYValues ? undefined : yMax,
        axisLabel: {
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        },
        splitLine: {
          show: showGrid,
          lineStyle: {
            color: axisLineColor
          }
        }
      },
      dataZoom: enableZoom ? [
        {
          type: 'inside',
          start: 0,
          end: 100,
          xAxisIndex: 0,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true
        },
        {
          type: 'slider',
          show: isZoomed,
          start: 0,
          end: 100,
          height: 20,
          bottom: 0,
          borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
          textStyle: {
            color: textColor
          }
        }
      ] : [],
      // @ts-ignore
      series: visibleSeries.flatMap((s, seriesIndex) => {
        // Use chartTypes array if available, otherwise use single chartType
        const types = chartTypes.length > 0 ? chartTypes : [chartType];

        // Create a series for each chart type
        return types.map((type, typeIndex) => {
          // Get chart type specific styling or use defaults
          const typeStyle = chartTypeStyles && chartTypeStyles[type] ? {
            // @ts-ignore
            color: chartTypeStyles[type].color || s.color,
            opacity: chartTypeStyles[type].opacity || 0.8
          } : {
            color: s.color,
            opacity: 0.8
          };

          // Common series properties
          const seriesBase = {
            name: types.length > 1
              ? `${s.label || s.field} (${type})`
              : s.label || s.field,
            data: processedData.map(item => item[s.field]),
            itemStyle: {
              color: typeStyle.color || s.color,
              // Adjust opacity for multiple chart types to make them distinguishable
              opacity: types.length > 1 ? (typeStyle.opacity || 0.8) - (typeIndex * 0.2) : (typeStyle.opacity || 1)
            },
            label: {
              show: showLabels && typeIndex === 0, // Only show labels on the first chart type
              position: 'top',
              formatter: function(params: any) {
                // Get the original value from the data array if possible
                if (params.dataIndex !== undefined && data[params.dataIndex]) {
                  const seriesField = s.field;
                  return data[params.dataIndex][seriesField];
                }
                // Fallback to the value in params
                return params.value;
              },
              color: textColor
            },
            z: 10 - typeIndex // Stack order - first type on top
          };

          // Chart type specific properties
          switch (type) {
            case 'line':
              return {
                ...seriesBase,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                  color: typeStyle.color || s.color,
                  width: 2,
                  opacity: types.length > 1 ? (typeStyle.opacity || 0.8) - (typeIndex * 0.2) : (typeStyle.opacity || 1)
                }
              };
            case 'bar':
              return {
                ...seriesBase,
                type: 'bar',
                barMaxWidth: 50,
                barGap: '0%', // Make bars overlap for multiple chart types
                itemStyle: {
                  ...seriesBase.itemStyle,
                  borderRadius: [4, 4, 0, 0]
                }
              };
            case 'area':
              return {
                ...seriesBase,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                  color: typeStyle.color || s.color,
                  width: 2,
                  opacity: types.length > 1 ? (typeStyle.opacity || 0.8) - (typeIndex * 0.2) : (typeStyle.opacity || 1)
                },
                areaStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: typeStyle.color || s.color // Use chart type style color if available
                      },
                      {
                        offset: 1,
                        color: 'rgba(255, 255, 255, 0)' // Transparent
                      }
                    ],
                    opacity: types.length > 1 ? 0.5 - (typeIndex * 0.1) : 0.8
                  }
                }
              };
            default:
              return { ...seriesBase, type: 'line' };
          }
        });
      })
    };

    // Add statistical indicators if enabled
    if (showMean || showMedian || showStdDev) {
      // Only add statistical indicators for non-pie charts with numeric values
      if (!hasTextYValues) {
        // Calculate statistical indicators for all visible series
        const allSeriesStats = visibleSeries.map(s => {
          const values = data
            .map(item => typeof item[s.field] === 'number' ? item[s.field] : Number(item[s.field]))
            .filter(val => !isNaN(val));

          // Calculate mean
          const mean = values.length > 0
            ? values.reduce((sum, val) => sum + val, 0) / values.length
            : 0;

          // Calculate median
          const sortedValues = [...values].sort((a, b) => a - b);
          const median = sortedValues.length > 0
            ? sortedValues.length % 2 === 0
              ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
              : sortedValues[Math.floor(sortedValues.length / 2)]
            : 0;

          // Calculate standard deviation
          const stdDev = values.length > 0
            ? Math.sqrt(
                values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
              )
            : 0;

          return { mean, median, stdDev };
        });

        // Add statistical indicators to each series
        if (baseOption.series && Array.isArray(baseOption.series)) {
          baseOption.series.forEach((series: any, index: number) => {
            if (index < allSeriesStats.length) {
              const stats = allSeriesStats[index];
              const markLines: any[] = [];
              const markAreas: any[] = [];

            if (showMean) {
              markLines.push({
                name: 'Mean',
                label: {
                  formatter: `Mean: ${stats.mean.toFixed(2)}`,
                  position: 'end',
                  show: true,
                  color: textColor,
                  fontSize: 12,
                  backgroundColor: 'rgba(0, 0, 0, 0.65)',
                  padding: [2, 4],
                  borderRadius: 2
                },
                lineStyle: {
                  color: '#ff9800',
                  type: 'dashed',
                  width: 1.5
                },
                yAxis: stats.mean
              });
            }

            if (showMedian) {
              markLines.push({
                name: 'Median',
                label: {
                  formatter: `Median: ${stats.median.toFixed(2)}`,
                  position: 'end',
                  show: true,
                  color: textColor,
                  fontSize: 12,
                  backgroundColor: 'rgba(0, 0, 0, 0.65)',
                  padding: [2, 4],
                  borderRadius: 2
                },
                lineStyle: {
                  color: '#4caf50',
                  type: 'dashed',
                  width: 1.5
                },
                yAxis: stats.median
              });
            }

            if (showStdDev) {
              // Add standard deviation as a mark area (mean ± stdDev)
              markAreas.push([
                {
                  name: 'StdDev',
                  label: {
                    show: true,
                    position: 'insideTop',
                    formatter: 'σ',
                    fontSize: 12,
                    color: textColor
                  },
                  itemStyle: {
                    color: 'rgba(66, 133, 244, 0.2)'
                  },
                  yAxis: stats.mean + stats.stdDev
                },
                {
                  yAxis: stats.mean - stats.stdDev
                }
              ]);
            }

            // Add markLine and markArea to the series
            series.markLine = {
              silent: true,
              data: markLines
            };

            series.markArea = {
              silent: true,
              data: markAreas
            };
          }
        });
        }
      }
    }

    // Set chart options
    chartInstance.current.setOption(baseOption, true);

    // Listen for zoom events
    chartInstance.current.on('dataZoom', (params: any) => {
      setIsZoomed(params.start !== 0 || params.end !== 100);
    });
  };

  return (
    <div
      className={`relative w-full h-full ${isDashboardChart ? 'dashboard-chart-container' : 'notebook-chart-container'}`}
      style={{
        overflow: 'hidden',
        position: isDashboardChart ? 'absolute' : 'relative',
        ...(isDashboardChart ? { inset: 0 } : {})
      }}
    >
      <div
        ref={chartRef}
        className={`w-full h-full ${isDashboardChart ? 'absolute inset-0' : ''}`}
        style={{
          minHeight: '100px',
          position: isDashboardChart ? 'absolute' : 'relative',
          ...(isDashboardChart ? { top: 0, left: 0, right: 0, bottom: 0 } : {})
        }}
      />

      {enableZoom && isZoomed && (
        <div className="absolute top-2 right-2 flex space-x-1">
          <Button
            onClick={resetZoom}
            variant="outline"
            size="sm"
            className="bg-background/80 backdrop-blur-sm"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            <span className="text-xs">Reset Zoom</span>
          </Button>
        </div>
      )}
    </div>
  )
});