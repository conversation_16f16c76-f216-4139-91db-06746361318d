'use client'

import React, { useState, useMemo, useCallback } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Download, Search, ChevronLeft, ChevronRight } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Series } from '../types'

interface TableViewProps {
  data: Record<string, any>[]
  xAxis: string
  yAxis: string
  series?: Series[]
  title?: string
  description?: string
  isDashboardChart?: boolean
  isMultiSeries?: boolean
}

export function TableView({
  data,
  xAxis,
  yAxis,
  series = [],
  title,
  description,
  isDashboardChart = false,
  isMultiSeries = false
}: TableViewProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const rowsPerPage = 10

  // Get only the columns that are used in the chart
  const columns = useMemo(() => {
    if (data.length === 0) return [];

    // Start with the X-axis column
    const relevantColumns = [xAxis];

    // For multi-series charts, add all series fields
    if (isMultiSeries && series.length > 0) {
      series.forEach(s => {
        if (s.visible && !relevantColumns.includes(s.field)) {
          relevantColumns.push(s.field);
        }
      });
    }
    // For single series charts, add the Y-axis
    else if (yAxis && !relevantColumns.includes(yAxis)) {
      relevantColumns.push(yAxis);
    }

    return relevantColumns;
  }, [data, xAxis, yAxis, series, isMultiSeries]);

  // Filter data based on search term - memoized to prevent unnecessary recalculations
  const filteredData = useMemo(() => {
    return data.filter(row => {
      if (!searchTerm) return true;

      // Search only in relevant columns
      return columns.some(column => {
        const value = row[column];
        return value !== undefined &&
               String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });
  }, [data, columns, searchTerm]);

  // Paginate data - memoized to prevent unnecessary recalculations
  const totalPages = useMemo(() =>
    Math.ceil(filteredData.length / rowsPerPage),
    [filteredData.length, rowsPerPage]
  );

  const paginatedData = useMemo(() =>
    filteredData.slice(
      (currentPage - 1) * rowsPerPage,
      currentPage * rowsPerPage
    ),
    [filteredData, currentPage, rowsPerPage]
  )

  // Handle CSV export - memoized to prevent unnecessary function recreation
  const exportToCSV = useCallback(() => {
    // Create CSV content
    const csvContent = [
      // Header row
      columns.join(','),
      // Data rows
      ...filteredData.map(row =>
        columns.map(col => {
          // Handle values with commas by wrapping in quotes
          const value = row[col]
          if (value === undefined) return '';
          if (typeof value === 'string' && value.includes(',')) {
            return `"${value}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `${title || 'chart-data'}.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [columns, filteredData, title]);

  // Format cell value for display - memoized to prevent unnecessary function recreation
  const formatCellValue = useCallback((value: any) => {
    if (value === null || value === undefined) return ''
    if (typeof value === 'object') return JSON.stringify(value)
    return String(value)
  }, []);

  // Handle search input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on search
  }, []);

  // Handle pagination
  const goToPreviousPage = useCallback(() => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  }, []);

  const goToNextPage = useCallback(() => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  }, [totalPages]);

  return (
    <div
      className={`w-full h-full flex flex-col ${isDashboardChart ? 'dashboard-table' : 'notebook-table'}`}
      style={{
        height: '100%',
        position: isDashboardChart ? 'absolute' : 'relative',
        ...(isDashboardChart ? { inset: 0 } : {}),
        overflow: 'hidden'
      }}
    >
      {/* Table Header with Search and Export */}
      <div className="flex items-center justify-between p-2 border-b">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search data..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-8 h-9 text-xs w-full"
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={exportToCSV}
          className="h-9"
        >
          <Download className="h-4 w-4 mr-1" />
          <span className="text-xs">Export CSV</span>
        </Button>
      </div>

      {/* Table Content with both horizontal and vertical scrolling */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="min-w-full overflow-auto">
            {/* @ts-ignore */}
            <ScrollArea orientation="horizontal" className="w-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    {columns.map((column) => (
                      <TableHead
                        key={column}
                        className="text-xs py-2 px-4 whitespace-nowrap sticky top-0 bg-background z-10"
                      >
                        {column}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData.map((row, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {columns.map((column) => (
                        <TableCell
                          key={`${rowIndex}-${column}`}
                          className="text-xs py-2 px-4 whitespace-nowrap"
                        >
                          {formatCellValue(row[column])}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                  {paginatedData.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="text-center py-4 text-muted-foreground">
                        {searchTerm ? 'No results found' : 'No data available'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </ScrollArea>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between p-2 border-t">
          <div className="text-xs text-muted-foreground">
            Showing {((currentPage - 1) * rowsPerPage) + 1} to {Math.min(currentPage * rowsPerPage, filteredData.length)} of {filteredData.length} entries
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-xs mx-2">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
