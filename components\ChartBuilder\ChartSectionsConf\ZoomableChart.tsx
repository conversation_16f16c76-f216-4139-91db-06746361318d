// @ts-nocheck
'use client'

import { useState, useEffect, useRef, memo, useMemo } from 'react'
import * as echarts from 'echarts'
import { RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"

// Chart color palette
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
];

// Utility function to safely handle values for charts
// Returns the original value if it's a string that can't be converted to a number
const safeValue = (value: any): any => {
  // Handle null, undefined, or empty string
  if (value === null || value === undefined || value === '') return 0;

  // If it's already a number, return it
  if (typeof value === 'number') return value;

  // If it's a string, try to convert to number if possible
  if (typeof value === 'string') {
    // Try to extract numeric part if it's a string with mixed content
    const numericMatch = value.match(/-?\d+(\.\d+)?/);
    if (numericMatch) {
      return Number(numericMatch[0]);
    }

    // If it's not a numeric string, return the original string
    // This allows text values to be displayed properly in charts
    return value;
  }

  // For other types, try to convert to number or use original value
  const num = Number(value);
  return isNaN(num) ? value : num;
}

interface ChartTypeStyle {
  color: string;
  opacity: number;
}

interface ZoomableChartProps {
  data: Record<string, any>[]
  chartType: 'line' | 'bar' | 'pie' | 'area'
  chartTypes?: ('line' | 'bar' | 'pie' | 'area')[] // Support for multiple chart types
  xAxis: string
  xAxisColumns?: string[] // Support for multiple X-axis columns
  yAxis: string
  color: string
  chartTypeStyles?: {
    line?: ChartTypeStyle;
    bar?: ChartTypeStyle;
    pie?: ChartTypeStyle;
    area?: ChartTypeStyle;
  }
  showGrid: boolean
  showLegend: boolean
  showLabels: boolean
  customLabel?: string
  isZoomable?: boolean
  isDashboardChart?: boolean // Flag to indicate if this is used in a dashboard card

  // Statistical indicators
  showMean?: boolean
  showMedian?: boolean
  showStdDev?: boolean
}

// Use memo to prevent unnecessary re-renders
export const ZoomableChart = memo(function ZoomableChart({
  data,
  chartType,
  chartTypes = [],
  xAxis,
  xAxisColumns = [],
  yAxis,
  color,
  chartTypeStyles = {},
  showGrid,
  showLegend,
  showLabels,
  customLabel,
  isZoomable = true,
  isDashboardChart = false,
  showMean = false,
  showMedian = false,
  showStdDev = false
}: ZoomableChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isZoomed, setIsZoomed] = useState(false);

  // Determine if Y-axis has text values
  const hasTextYValues = !yAxis || data.some(item => {
    if (!item || item[yAxis] === undefined || item[yAxis] === null) {
      return false; // Skip undefined or null values
    }
    return typeof item[yAxis] === 'string' &&
      isNaN(Number(item[yAxis])) &&
      !item[yAxis].toString().match(/-?\d+(\.\d+)?/);
  });

  // Get min and max values for yAxis with better error handling (only for numeric values)
  const yValues = hasTextYValues
    ? []
    : data.map(item => typeof item[yAxis] === 'number' ? item[yAxis] : Number(item[yAxis])).filter(val => !isNaN(val));

  // Calculate statistical indicators
  const mean = yValues.length > 0
    ? yValues.reduce((sum, val) => sum + val, 0) / yValues.length
    : 0;

  // Calculate median
  const sortedValues = [...yValues].sort((a, b) => a - b);
  const median = sortedValues.length > 0
    ? sortedValues.length % 2 === 0
      ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
      : sortedValues[Math.floor(sortedValues.length / 2)]
    : 0;

  // Calculate standard deviation
  const stdDev = yValues.length > 0
    ? Math.sqrt(
        yValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / yValues.length
      )
    : 0;

  const yMin = yValues.length > 0 ? Math.min(...yValues) * 0.9 : 0;
  const yMax = yValues.length > 0 ? Math.max(...yValues) * 1.1 : 100;

  // Initialize chart
  useEffect(() => {
    if (!chartRef.current) return;

    // Dispose of any existing chart
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // Get theme based on document class
    const isDarkMode = document.documentElement.classList.contains('dark');

    // Initialize chart
    chartInstance.current = echarts.init(chartRef.current, isDarkMode ? 'dark' : undefined);

    // Enhanced window resize handler
    const handleResize = () => {
      if (chartInstance.current) {
        // Force a reflow before resizing to ensure accurate dimensions
        if (chartRef.current) {
          void chartRef.current.offsetWidth;
        }

        // Use a small delay to ensure the DOM has settled
        setTimeout(() => {
          if (chartInstance.current) {
            // Get the actual dimensions of the container
            const width = chartRef.current?.clientWidth || undefined;
            const height = chartRef.current?.clientHeight || undefined;

            // Resize with explicit dimensions when available
            if (width && height) {
              chartInstance.current.resize({
                width: width,
                height: height
              });
            } else {
              // Fall back to auto-sizing
              chartInstance.current.resize();
            }
          }
        }, 10);
      }
    };

    // Handle theme change
    const handleThemeChange = () => {
      if (!chartRef.current || !chartInstance.current) return;

      const newIsDarkMode = document.documentElement.classList.contains('dark');
      chartInstance.current.dispose();
      chartInstance.current = echarts.init(chartRef.current, newIsDarkMode ? 'dark' : undefined);
      renderChart();
    };

    window.addEventListener('resize', handleResize);
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);

    renderChart();

    return () => {
      window.removeEventListener('resize', handleResize);
      window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', handleThemeChange);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, chartType, chartTypes, xAxis, yAxis, color, chartTypeStyles, showGrid, showLegend, showLabels, customLabel, isZoomable, showMean, showMedian, showStdDev]);

  // Reset zoom
  const resetZoom = () => {
    if (chartInstance.current) {
      chartInstance.current.dispatchAction({
        type: 'dataZoom',
        start: 0,
        end: 100
      });
      setIsZoomed(false);
      toast.success("Zoom reset");
    }
  };

  // Render chart based on type
  const renderChart = () => {
    if (!chartInstance.current) return;

    // Get theme colors based on current mode
    const isDarkMode = document.documentElement.classList.contains('dark');
    const textColor = isDarkMode ? '#e5e7eb' : '#374151';
    const axisLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';

    // Prepare data - completely preserve original values without conversion
    const processedData = data.map(item => ({
      ...item
      // No conversion at all - keep original values exactly as they are
    }));

    // Base chart options
    const baseOption: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: chartType === 'pie' ? 'item' : 'axis',
        formatter: function(params: any) {
          // For pie charts
          if (chartType === 'pie') {
            const name = params.name || '';
            const value = params.value !== undefined ? params.value : '';
            const percent = params.percent !== undefined ? params.percent : 0;
            return `${name}: ${value} (${percent}%)`;
          }

          // For other chart types, preserve original text values
          const xValue = params.name || '';

          // Get the raw value from the original data to ensure we display the exact original value
          let yValue = params.value;

          // For array params (like in axis trigger), use the value directly
          if (Array.isArray(params)) {
            // Start with the X-axis value as the header
            let content = `<strong>${params[0]?.name || ''}</strong><br/>`;

            // Add all X-axis columns if available
            if (xAxisColumns && xAxisColumns.length > 0 && params[0]?.dataIndex !== undefined) {
              const dataIndex = params[0].dataIndex;
              if (data[dataIndex]) {
                xAxisColumns.forEach(col => {
                  if (col !== xAxis && data[dataIndex][col] !== undefined) {
                    content += `<span style="color:#888">${col}:</span> ${data[dataIndex][col]}<br/>`;
                  }
                });
                // Add a separator if we added any X-axis columns
                if (xAxisColumns.length > 1) {
                  content += `<div style="border-top:1px dashed rgba(255,255,255,0.2);margin:4px 0;"></div>`;
                }
              }
            }

            // Add each series value
            params.forEach(param => {
              if (param) {
                const name = param.seriesName || '';
                const value = param.value !== undefined ? param.value : '';
                const color = param.color || '';
                const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
                content += `${marker}${name}: ${value}<br/>`;
              }
            });

            return content;
          }

          // For single point, get data from the original dataset
          if (params.dataIndex !== undefined && data[params.dataIndex]) {
            if (yAxis && data[params.dataIndex][yAxis] !== undefined) {
              yValue = data[params.dataIndex][yAxis];
            }

            // Create tooltip content with all X-axis columns
            let content = `<strong>${xValue}</strong><br/>`;

            // Add all X-axis columns if available
            if (xAxisColumns && xAxisColumns.length > 0) {
              xAxisColumns.forEach(col => {
                if (col !== xAxis && data[params.dataIndex][col] !== undefined) {
                  content += `<span style="color:#888">${col}:</span> ${data[params.dataIndex][col]}<br/>`;
                }
              });
              // Add a separator if we added any X-axis columns
              if (xAxisColumns.length > 1) {
                content += `<div style="border-top:1px dashed rgba(255,255,255,0.2);margin:4px 0;"></div>`;
              }
            }

            // Get the series name for better context
            const seriesName = params.seriesName || customLabel || yAxis || '';

            // Create a color marker for the series
            const colorMarker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${params.color || color};"></span>`;

            // Add the Y-axis value
            content += `${colorMarker}${seriesName}: ${yValue}`;

            return content;
          }

          // Fallback to simple format if we can't get the data
          const seriesName = params.seriesName || customLabel || yAxis || '';
          const colorMarker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${params.color || color};"></span>`;
          return `<strong>${xValue}</strong><br/>${colorMarker}${seriesName}: ${yValue}`;
        },
        // Improve tooltip appearance
        backgroundColor: 'rgba(50, 50, 50, 0.8)',
        borderRadius: 4,
        padding: [8, 10],
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '10%',
        containLabel: true
      },
      legend: {
        show: showLegend,
        bottom: 0,
        textStyle: {
          color: textColor
        }
      },
      dataZoom: isZoomable && chartType !== 'pie' ? [
        {
          type: 'inside',
          start: 0,
          end: 100,
          xAxisIndex: 0,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true
        },
        {
          type: 'slider',
          show: isZoomed,
          start: 0,
          end: 100,
          height: 20,
          bottom: 0,
          borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
          textStyle: {
            color: textColor
          }
        }
      ] : []
    };

    // Chart specific options
    let specificOption: echarts.EChartsOption = {};

    // Use chartTypes array if available, otherwise use single chartType
    const types = chartTypes.length > 0 ? chartTypes : [chartType];

    // Common axis configuration for most chart types
    const commonAxisConfig = {
      xAxis: {
        type: 'category',
        data: processedData.map(item => item[xAxis]),
        axisLabel: {
          rotate: 45,
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        }
      },
      yAxis: {
        type: hasTextYValues ? 'category' : 'value',
        data: hasTextYValues ? Array.from(new Set(processedData.map(item => item[yAxis]))) : undefined,
        min: hasTextYValues ? undefined : yMin,
        max: hasTextYValues ? undefined : yMax,
        axisLabel: {
          color: textColor
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor
          }
        },
        splitLine: {
          show: showGrid,
          lineStyle: {
            color: axisLineColor
          }
        }
      }
    };

    // If pie chart is the primary type, use pie chart configuration
    if (types[0] === 'pie') {
      specificOption = {
        series: [
          {
            name: customLabel || yAxis,
            type: 'pie',
            radius: '70%',
            center: ['50%', '50%'],
            data: processedData.map(item => ({
              name: item[xAxis],
              value: item[yAxis]
            })),
            label: {
              show: showLabels,
              formatter: function(params: any) {
                // Find the original data item
                const originalItem = processedData.find(item => item[xAxis] === params.name);
                const value = originalItem ? originalItem[yAxis] : params.value;

                // Return the original value without conversion
                return `${params.name}: ${value} (${params.percent}%)`;
              },
              color: textColor
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    } else {
      // For non-pie charts, create series for each chart type
      specificOption = {
        ...commonAxisConfig,
        // @ts-ignore
        series: types.map((type, typeIndex) => {
          // Get chart type specific styling or use defaults
          const typeStyle = chartTypeStyles && chartTypeStyles[type] ? {
            color: chartTypeStyles[type].color || (
              type === 'line' ? COLORS[0] :
              type === 'bar' ? COLORS[1] :
              type === 'area' ? COLORS[3] :
              color
            ),
            opacity: chartTypeStyles[type].opacity || 0.8
          } : {
            color: type === 'line' ? COLORS[0] :
                   type === 'bar' ? COLORS[1] :
                   type === 'area' ? COLORS[3] :
                   color,
            opacity: 0.8
          };

          // Base series configuration
          const seriesBase = {
            name: types.length > 1
              ? `${customLabel || yAxis} (${type})`
              : customLabel || yAxis,
            data: processedData.map(item => item[yAxis]),
            itemStyle: {
              color: typeStyle.color || color, // Fallback to main color if type style color is not set
              // Adjust opacity based on chart type style
              opacity: types.length > 1 ? (typeStyle.opacity || 0.8) - (typeIndex * 0.2) : (typeStyle.opacity || 0.8)
            },
            label: {
              show: showLabels && typeIndex === 0, // Only show labels on the first chart type
              position: 'top',
              formatter: function(params: any) {
                // Get the original value from the data array if possible
                if (params.dataIndex !== undefined && data[params.dataIndex]) {
                  return data[params.dataIndex][yAxis];
                }
                // Fallback to the value in params
                return params.value;
              },
              color: textColor
            },
            z: 10 - typeIndex // Stack order - first type on top
          };

          // Chart type specific properties
          switch (type) {
            case 'line':
              return {
                ...seriesBase,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                  color: typeStyle.color || color, // Fallback to main color if type style color is not set
                  width: 2,
                  opacity: types.length > 1 ? (typeStyle.opacity || 0.8) - (typeIndex * 0.2) : (typeStyle.opacity || 0.8)
                }
              };
            case 'bar':
              return {
                ...seriesBase,
                type: 'bar',
                barMaxWidth: 50,
                barGap: '0%', // Make bars overlap for multiple chart types
                itemStyle: {
                  ...seriesBase.itemStyle,
                  borderRadius: [4, 4, 0, 0]
                }
              };
            case 'area':
              return {
                ...seriesBase,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                  color: typeStyle.color || color, // Fallback to main color if type style color is not set
                  width: 2,
                  opacity: types.length > 1 ? (typeStyle.opacity || 0.8) - (typeIndex * 0.2) : (typeStyle.opacity || 0.8)
                },
                areaStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: typeStyle.color || color // Fallback to main color if type style color is not set
                      },
                      {
                        offset: 1,
                        color: 'rgba(255, 255, 255, 0)' // Transparent
                      }
                    ],
                    opacity: types.length > 1 ? typeStyle.opacity * 0.6 - (typeIndex * 0.1) : typeStyle.opacity * 0.8
                  }
                }
              };
            default:
              return { ...seriesBase, type: 'line' };
          }
        })
      };
    }

    // Add statistical indicators if enabled
    if ((showMean || showMedian || showStdDev) && !hasTextYValues && types[0] !== 'pie') {
      // Only add statistical indicators for non-pie charts with numeric values
      const markLines: any[] = [];
      const markAreas: any[] = [];

      if (showMean) {
        markLines.push({
          name: 'Mean',
          label: {
            formatter: 'Mean: {c}',
            position: 'end',
            show: true,
            color: textColor,
            fontSize: 12,
            backgroundColor: 'rgba(0, 0, 0, 0.65)',
            padding: [2, 4],
            borderRadius: 2
          },
          lineStyle: {
            color: '#ff9800',
            type: 'dashed',
            width: 1.5
          },
          yAxis: mean
        });
      }

      if (showMedian) {
        markLines.push({
          name: 'Median',
          label: {
            formatter: 'Median: {c}',
            position: 'end',
            show: true,
            color: textColor,
            fontSize: 12,
            backgroundColor: 'rgba(0, 0, 0, 0.65)',
            padding: [2, 4],
            borderRadius: 2
          },
          lineStyle: {
            color: '#4caf50',
            type: 'dashed',
            width: 1.5
          },
          yAxis: median
        });
      }

      if (showStdDev) {
        // Add standard deviation as a mark area (mean ± stdDev)
        markAreas.push([
          {
            name: 'StdDev',
            label: {
              show: true,
              position: 'insideTop',
              formatter: 'σ',
              fontSize: 12,
              color: textColor
            },
            itemStyle: {
              color: 'rgba(66, 133, 244, 0.2)'
            },
            yAxis: mean + stdDev
          },
          {
            yAxis: mean - stdDev
          }
        ]);
      }

      // Add markLine and markArea to the first series
      // @ts-ignore
      if (specificOption.series && specificOption.series.length > 0) {
        // @ts-ignore
        specificOption.series[0].markLine = {
          silent: true,
          data: markLines
        };
        // @ts-ignore
        specificOption.series[0].markArea = {
          silent: true,
          data: markAreas
        };
      }
    }

    // Merge options and set
    const option = { ...baseOption, ...specificOption };
    chartInstance.current.setOption(option, true);

    // Listen for zoom events
    chartInstance.current.on('dataZoom', (params: any) => {
      setIsZoomed(params.start !== 0 || params.end !== 100);
    });
  };

  return (
    <div
      className={`relative w-full h-full ${isDashboardChart ? 'dashboard-chart-container' : 'notebook-chart-container'}`}
      style={{
        overflow: 'hidden',
        position: isDashboardChart ? 'absolute' : 'relative',
        ...(isDashboardChart ? { inset: 0 } : {})
      }}
    >
      <div
        ref={chartRef}
        className={`w-full h-full ${isDashboardChart ? 'absolute inset-0' : ''}`}
        style={{
          minHeight: '100px',
          position: isDashboardChart ? 'absolute' : 'relative',
          ...(isDashboardChart ? { top: 0, left: 0, right: 0, bottom: 0 } : {})
        }}
      />

      {isZoomable && chartType !== 'pie' && isZoomed && (
        <div className="absolute top-2 right-2 flex space-x-1 z-10">
          <Button
            onClick={resetZoom}
            variant="outline"
            size="sm"
            className="bg-background/80 backdrop-blur-sm"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            <span className="text-xs">Reset Zoom</span>
          </Button>
        </div>
      )}
    </div>
  )
});