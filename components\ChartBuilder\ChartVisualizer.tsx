'use client'

import { EnhancedChartVisualizer } from "./ChartSectionsConf/EnhancedChartVisualizer"
import { ChartVisualizerProps } from "./types"

// Original props interface can be removed since we import the type
export function ChartVisualizer(props: ChartVisualizerProps) {
  // Simply pass all props to the enhanced component
  return <EnhancedChartVisualizer {...props} data-chart-visualizer />;
}