'use client'

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { HeadingItem, HeadingLevel } from './types'
import { nanoid } from 'nanoid'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Type } from 'lucide-react'

interface AddHeadingDialogProps {
  onAddHeading: (heading: HeadingItem) => void
}

export function AddHeadingDialog({ onAddHeading }: AddHeadingDialogProps) {
  const [open, setOpen] = useState(false)
  const [content, setContent] = useState('')
  const [headingLevel, setHeadingLevel] = useState<HeadingLevel>('h2')
  const [textAlign, setTextAlign] = useState<'left' | 'center' | 'right'>('left')

  const handleAddHeading = () => {
    if (!content.trim()) return

    const newHeading: HeadingItem = {
      id: nanoid(),
      type: 'heading',
      headingLevel,
      content,
      textAlign,
      gridColumn: 0,
      gridRow: 0,
      width: headingLevel === 'h1' ? 12 : 6, // H1 spans full width, others half
      height: 1
    }

    onAddHeading(newHeading)
    setOpen(false)
    resetForm()
  }

  const resetForm = () => {
    setContent('')
    setHeadingLevel('h2')
    setTextAlign('left')
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Type className="h-4 w-4" />
          Add Heading
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Heading</DialogTitle>
          <DialogDescription>
            Add a heading element to your dashboard. Headings can be used to organize and structure your dashboard content.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="heading-level" className="text-right">
              Level
            </Label>
            <Select value={headingLevel} onValueChange={(value) => setHeadingLevel(value as HeadingLevel)}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select heading level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="h1">H1 - Main Title</SelectItem>
                <SelectItem value="h2">H2 - Section Title</SelectItem>
                <SelectItem value="h3">H3 - Subsection Title</SelectItem>
                <SelectItem value="h4">H4 - Group Title</SelectItem>
                <SelectItem value="h5">H5 - Small Title</SelectItem>
                <SelectItem value="h6">H6 - Tiny Title</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="content" className="text-right">
              Text
            </Label>
            <Input
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="col-span-3"
              placeholder="Enter heading text"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="text-align" className="text-right">
              Alignment
            </Label>
            <Select 
              value={textAlign} 
              onValueChange={(value) => setTextAlign(value as 'left' | 'center' | 'right')}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select text alignment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleAddHeading} disabled={!content.trim()}>
            Add Heading
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
