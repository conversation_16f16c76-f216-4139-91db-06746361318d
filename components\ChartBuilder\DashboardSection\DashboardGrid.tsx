'use client'
import { <PERSON>boardI<PERSON>, Saved<PERSON><PERSON>, HeadingItem, TextI<PERSON>, TableItem, PythonPlotItem } from './types';
import { DashboardChartCard } from './DashboardChartCard';
import { HeadingCard } from './HeadingCard';
import { NewTextCard } from './NewTextCard';
import { TableCard } from './TableCard';
import { PythonPlotCard } from './PythonPlotCard';

interface DashboardGridProps {
  items: DashboardItem[];
  isEditMode: boolean;
  onUpdateItem: (itemId: string, updates: Partial<DashboardItem>) => void;
  onRemoveItem: (itemId: string) => void;
  onToggleFullscreen: (chartId: string) => void;
}

export function DashboardGrid({
  items,
  isEditMode,
  onUpdateItem,
  onRemoveItem,
  onToggleFullscreen
}: DashboardGridProps) {
  return (
    <div className="relative min-h-[500px] p-4">
      {/* Grid Guidelines */}
      {isEditMode && (
        <div className="absolute inset-0 grid grid-cols-12 gap-4 pointer-events-none">
          {Array.from({ length: 12 }).map((_, i) => (
            <div
              key={i}
              className="border-r border-dashed border-primary/20"
              style={{ gridColumn: `${i + 1} / span 1` }}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {items.length === 0 && (
        <div className="flex flex-col items-center justify-center h-[400px] border-2 border-dashed rounded-lg p-6 text-center">
          <div className="text-muted-foreground mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
          </div>
          <h3 className="text-lg font-medium">No items in dashboard</h3>
          <p className="text-sm text-muted-foreground max-w-md mt-2">
            Create and save charts or add headings to your dashboard.
          </p>
        </div>
      )}

      {/* Dashboard Items Container */}
      {items.length > 0 && (
        <div className="relative grid grid-cols-12 gap-4 auto-rows-[minmax(80px,auto)]" style={{ maxWidth: '100%', overflowX: 'hidden' }}>
          {items.map((item) => (
            <div
              key={item.id}
              className="relative group"
              style={{
                gridColumn: `span ${item.width || item.gridSpan || 4} / auto`,
                gridRow: `span ${item.height || item.gridRowSpan || 1} / auto`,
                position: 'relative',
                transition: 'all 0.2s ease-in-out'
              }}
            >
              {item.type !== 'heading' || isEditMode ? (
                <div className="absolute inset-0 border-2 border-transparent group-hover:border-primary/20 rounded-lg pointer-events-none transition-colors duration-200" />
              ) : null}
              {(item.type !== 'heading' || isEditMode) && (
                <div className="absolute -top-2 -right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  {isEditMode && (
                    <div className="bg-white dark:bg-gray-800 rounded-full shadow-md text-xs px-2 py-0.5 border">
                      {item.width || item.gridSpan || 4}×{item.height || item.gridRowSpan || 1}
                    </div>
                  )}
                </div>
              )}

              {item.type === 'chart' ? (
                <DashboardChartCard
                  chart={item as SavedChart}
                  isEditMode={isEditMode}
                  onUpdateChart={(chartId, updates) => onUpdateItem(chartId, updates)}
                  onRemoveChart={onRemoveItem}
                  onToggleFullscreen={onToggleFullscreen}
                />
              ) : item.type === 'heading' ? (
                <HeadingCard
                  heading={item as HeadingItem}
                  isEditMode={isEditMode}
                  onUpdateHeading={(headingId, updates) => onUpdateItem(headingId, updates)}
                  onRemoveHeading={onRemoveItem}
                />
              ) : item.type === 'text' ? (
                <NewTextCard
                  textItem={item as TextItem}
                  isEditMode={isEditMode}
                  onUpdateText={(textId, updates) => onUpdateItem(textId, updates)}
                  onRemoveText={onRemoveItem}
                />
              ) : item.type === 'table' ? (
                <TableCard
                  table={item as TableItem}
                  isEditMode={isEditMode}
                  onUpdateTable={(tableId, updates) => onUpdateItem(tableId, updates)}
                  onRemoveTable={onRemoveItem}
                  onToggleFullscreen={onToggleFullscreen}
                />
              ) : item.type === 'pythonplot' ? (
                <PythonPlotCard
                  plot={item as PythonPlotItem}
                  isEditMode={isEditMode}
                  onUpdatePlot={(plotId, updates) => onUpdateItem(plotId, updates)}
                  onRemovePlot={onRemoveItem}
                  onToggleFullscreen={onToggleFullscreen}
                />
              ) : null}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

