'use client'

/**
 * DashboardToolbar Component
 *
 * This component provides the toolbar interface for the dashboard.
 * It includes controls for editing the dashboard, changing view modes,
 * adding text cards, and exporting the dashboard.
 *
 * Features:
 * - Toggle between edit and view modes
 * - Switch between grid and list views
 * - Add rich text editors to the dashboard
 * - Export dashboard as PNG or print
 * - Display dashboard statistics (number of items)
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Edit, Save, LayoutGrid, LayoutList,
  Download, Printer, RefreshCw, Trash2, Share2, FileText
} from 'lucide-react';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { HeadingItem, TextItem } from './types';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';

interface DashboardToolbarProps {
  isEditMode: boolean;
  viewMode: 'grid' | 'list';
  onToggleEditMode: () => void;
  onChangeView?: (view: 'grid' | 'list') => void;
  onSetViewMode?: (view: 'grid' | 'list') => void;
  onExport: () => void;
  onPrint: () => void;
  onAddChart?: () => void;
  onAddHeading?: (heading: HeadingItem) => void;
  onAddText?: (isTitleStyle?: boolean) => void;
  onAddTitle?: () => void;
  onAddRichText?: () => void;
  onAutoArrange?: () => void;
  onClearAll?: () => void;
  onShareDashboard?: () => void;
  chartsCount?: number;
  headingsCount?: number;
  textCount?: number;
  hasItems?: boolean;
  className?: string;
}

export function DashboardToolbar({
  isEditMode,
  viewMode,
  onToggleEditMode,
  onChangeView,
  onSetViewMode,
  onExport,
  onPrint,
  onAddChart,
  onAddHeading,
  onAddText,
  onAddTitle,
  onAddRichText,
  onAutoArrange,
  onClearAll,
  onShareDashboard,
  chartsCount = 0,
  headingsCount = 0,
  textCount = 0,
  hasItems = false,
  className
}: DashboardToolbarProps) {
  const [dashboardTitle, setDashboardTitle] = useState('My Dashboard');

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    if (onChangeView) {
      onChangeView(mode);
    } else if (onSetViewMode) {
      onSetViewMode(mode);
    }
  };

  return (
    <div className={`flex flex-col border-b ${className || ''}`}>
      {/* Main Toolbar */}
      <div className="flex items-center justify-between p-3 border-b bg-background">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">{dashboardTitle}</h2>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0"
            onClick={() => {
              const newTitle = prompt('Enter dashboard title:', dashboardTitle);
              if (newTitle) setDashboardTitle(newTitle);
            }}
          >
            <Edit className="h-3.5 w-3.5" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={isEditMode ? 'secondary' : 'outline'}
            size="sm"
            onClick={onToggleEditMode}
          >
            {isEditMode ? (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Exit Edit Mode
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Edit Layout
              </>
            )}
          </Button>

          {onShareDashboard && (
            <Button variant="outline" size="sm" onClick={onShareDashboard}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          )}
        </div>
      </div>

      {/* Secondary Toolbar */}
      <div className="flex items-center justify-between p-2 bg-muted/20">
        <div className="flex items-center gap-2">
          {/* View Mode Selector */}
          <div className="flex items-center border rounded-md bg-background">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleViewModeChange('grid')}
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Grid View</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleViewModeChange('list')}
                  >
                    <LayoutList className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>List View</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Text Editor Button - Only in edit mode */}
          {isEditMode && onAddRichText && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onAddRichText();
                toast.success('Adding text editor to dashboard...', {
                  duration: 2000
                });
              }}
            >
              <FileText className="h-4 w-4 mr-2" />
              Add Text Area
            </Button>
          )}

          {/* Auto Arrange - Only in edit mode */}
          {isEditMode && onAutoArrange && (
            <Button
              variant="secondary"
              size="sm"
              onClick={onAutoArrange}
              className="flex items-center gap-1 font-medium"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Auto Arrange
            </Button>
          )}

          {/* Dashboard Stats */}
          <div className="text-xs text-muted-foreground ml-2">
            {chartsCount} chart{chartsCount !== 1 ? 's' : ''}
            {headingsCount > 0 && `, ${headingsCount} heading${headingsCount !== 1 ? 's' : ''}`}
            {textCount > 0 && `, ${textCount} text card${textCount !== 1 ? 's' : ''}`}
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Action Buttons */}
          <div className="flex items-center gap-2 border-r pr-2 mr-2">
            {/* Clear All Button - Only in edit mode */}
            {isEditMode && onClearAll && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearAll}
                className="text-red-600 hover:bg-red-100 border-red-200"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Layout
              </Button>
            )}

            {/* Save Layout Button - Only in edit mode */}
            {isEditMode && (
              <Button
                variant="default"
                size="sm"
                onClick={onToggleEditMode}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Dashboard
              </Button>
            )}
          </div>

          {/* Export/Print Options */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onExport}>
                <Download className="h-4 w-4 mr-2" />
                Export as PNG
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onPrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print Dashboard
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}