'use client'

/**
 * EnhancedDashboardLayout Component
 *
 * This component is an optimized version of the dashboard grid layout.
 * It includes performance enhancements for smooth dragging and resizing,
 * utilizing memoization, refs, and GPU acceleration for better UX.
 */

import { useState, useEffect, useCallback, useMemo, memo, useRef } from 'react'
import GridLayout from 'react-grid-layout'
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'
import { DashboardItem, SavedChart, HeadingItem, TextItem } from './types'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { X, GripVertical } from 'lucide-react'
import { HeadingCard } from './HeadingCard'
import { NewTextCard } from './NewTextCard'
import { DashboardChartCard } from './DashboardChartCard'
import { toast } from 'sonner'
import './styles/dashboard-enhanced.css'

interface DashboardLayoutProps {
  items: DashboardItem[]
  onUpdateItem: (itemId: string, updatedItem: Partial<DashboardItem>) => void
  onRemoveItem: (itemId: string) => void
  onSaveLayout: (updatedItems: DashboardItem[]) => void
  isEditMode?: boolean
  viewMode?: 'grid' | 'list'
}

// Define window properties to track drag and resize operations
declare global {
  interface Window {
    isDraggingDashboardItem?: boolean;
    isResizingDashboardItem?: boolean;
    activeItemId?: string;
    lastLayoutUpdate?: number;
  }
}

// Memoized individual card component to reduce re-renders
const DashboardItemCard = memo(({
  item,
  isEditMode,
  onUpdateItem,
  onRemoveItem
}: {
  item: DashboardItem,
  isEditMode: boolean,
  onUpdateItem: (itemId: string, updates: Partial<DashboardItem>) => void,
  onRemoveItem: (itemId: string) => void
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);

  // Track drag and resize state with refs to avoid re-renders
  const dragStateRef = useRef(false);

  // Update global window state to optimize performance during drag/resize
  useEffect(() => {
    const handler = () => {
      if (dragStateRef.current) {
        window.isDraggingDashboardItem = true;
        window.activeItemId = item.id;
      }
      return () => {
        window.isDraggingDashboardItem = false;
        window.activeItemId = undefined;
      };
    };
    return handler();
  }, [item.id]);

  // Handle interaction state changes
  const handleInteractionStart = (type: 'drag' | 'resize') => {
    if (type === 'drag') {
      setIsDragging(true);
      dragStateRef.current = true;
    } else {
      setIsResizing(true);
      window.isResizingDashboardItem = true;
    }
    document.body.style.cursor = type === 'drag' ? 'grabbing' : 'se-resize';
  };

  const handleInteractionEnd = (type: 'drag' | 'resize') => {
    if (type === 'drag') {
      setIsDragging(false);
      dragStateRef.current = false;
    } else {
      setIsResizing(false);
      window.isResizingDashboardItem = false;
    }
    document.body.style.cursor = '';

    // Small delay to ensure we don't conflict with layout updates
    setTimeout(() => {
      window.isDraggingDashboardItem = false;
      window.isResizingDashboardItem = false;
    }, 50);
  };

  // For chart items, render DashboardChartCard directly without wrapping it in another Card
  if (item.type === 'chart') {
    return (
      <DashboardChartCard
        chart={item as SavedChart}
        isEditMode={isEditMode}
        onUpdateChart={(chartId, updates) => onUpdateItem(chartId, updates)}
        onRemoveChart={onRemoveItem}
        onToggleFullscreen={(chartId) => {
          // Handle fullscreen toggle if needed
          console.log('Toggle fullscreen for chart:', chartId);
        }}
      />
    );
  }

  // For non-chart items (headings, text), use the Card wrapper
  return (
    <Card
      className={`w-full h-full overflow-visible transition-all duration-200
        ${isEditMode ? 'dashboard-card-edit' : 'dashboard-card no-border'}
        ${isDragging ? 'ring-1 ring-primary' : ''}
        ${isResizing ? 'ring-1 ring-primary' : ''}
        ${item.type === 'text' && isEditMode ? 'bg-white dark:bg-gray-900 border border-primary/20 p-0' : item.type === 'text' ? 'bg-transparent dark:bg-transparent border-0 p-0' : ''}`}
      data-item-type={item.type}
      data-item-id={item.id}
      data-is-dragging={isDragging}
      data-is-resizing={isResizing}
      style={{
        position: 'relative',
        zIndex: (isDragging || isResizing) ? 100 : item.type === 'text' ? 10 : 1,
        minHeight: item.type === 'text' ? (item as TextItem).height <= 2 ? '60px' : '120px' : 'auto',
        transform: 'translate3d(0,0,0)',
        willChange: isEditMode ? 'transform, box-shadow' : 'auto'
      }}
      onMouseDown={() => {
        if (isEditMode) {
          window.activeItemId = item.id;
        }
      }}
      onMouseUp={() => {
        if (isEditMode) {
          window.activeItemId = undefined;
        }
      }}
    >
      {isEditMode && (
        <div className="draggable-handle absolute top-0 left-0 w-full flex items-center justify-center">
          <GripVertical className="h-3 w-3 text-muted-foreground" />
        </div>
      )}

      {isEditMode && (
        <div className="absolute top-2 right-2 flex gap-1 z-10">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 bg-white/80 hover:bg-red-100 hover:text-red-600 transition-colors rounded-full"
            onClick={() => onRemoveItem(item.id)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
      <div className={`h-full dashboard-item-content ${item.type === 'text' ? 'p-0' : 'p-2'}`}>
        {item.type === 'heading' ? (
          <div className="w-full h-full">
            <HeadingCard
              key={item.id}
              heading={item as HeadingItem}
              isEditMode={isEditMode}
              onUpdateHeading={(headingId, updates) => {
                onUpdateItem(headingId, updates);
              }}
              onRemoveHeading={(headingId) => {
                onRemoveItem(headingId);
              }}
            />
          </div>
        ) : item.type === 'text' ? (
          <NewTextCard
            key={item.id}
            textItem={item as TextItem}
            isEditMode={isEditMode}
            onUpdateText={(textId, updates) => {
              if (textId === item.id) {
                onUpdateItem(textId, updates);
              }
            }}
            onRemoveText={(textId) => {
              onRemoveItem(textId);
            }}
          />
        ) : null}
      </div>
    </Card>
  );
}, (prevProps, nextProps) => {
  // Skip re-renders during drag operations for items that aren't being dragged
  if (window.isDraggingDashboardItem && window.activeItemId !== nextProps.item.id) {
    return true; // prevent re-render
  }

  // Custom comparison for memo - only re-render if these specific props change
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.isEditMode === nextProps.isEditMode &&
    prevProps.item.type === nextProps.item.type &&
    // For text cards, check content
    (prevProps.item.type === 'text' ?
      (prevProps.item as TextItem).content === (nextProps.item as TextItem).content :
      true) &&
    // For charts, only re-render if title or type changes (not data)
    (prevProps.item.type === 'chart' ?
      (prevProps.item as SavedChart).title === (nextProps.item as SavedChart).title &&
      (prevProps.item as SavedChart).chartType === (nextProps.item as SavedChart).chartType :
      true)
  );
});

export function EnhancedDashboardLayout({
  items,
  onUpdateItem,
  onRemoveItem,
  onSaveLayout,
  isEditMode = false,
  viewMode = 'grid'
}: DashboardLayoutProps) {
  const [layout, setLayout] = useState<any[]>([])
  const [mounted, setMounted] = useState(false)
  const [containerWidth, setContainerWidth] = useState(1200)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [activeDragItemId, setActiveDragItemId] = useState<string | null>(null)

  // Use refs to store the latest state without triggering re-renders
  const isDraggingRef = useRef(false);
  const isResizingRef = useRef(false);
  const layoutBeforeDragRef = useRef<any[]>([]);

  // Update container width when window resizes - debounced for performance
  const updateWidth = useCallback(() => {
    try {
      // Calculate an appropriate width with some padding
      const width = Math.max(window.innerWidth - 80, 800)
      setContainerWidth(width)
    } catch (error) {
      // Fallback to a reasonable default if there's an error
      setContainerWidth(1000)
    }
  }, [])

  // Debounced version of onSaveLayout to prevent excessive updates
  const debouncedSaveLayout = useCallback((updatedItems: DashboardItem[]) => {
    // Skip if a drag or resize operation is in progress
    if (isDraggingRef.current || isResizingRef.current) {
      return;
    }

    // Only save if it's been at least 100ms since the last update
    const now = Date.now();
    if (window.lastLayoutUpdate && now - window.lastLayoutUpdate < 100) {
      return;
    }

    window.lastLayoutUpdate = now;
    onSaveLayout(updatedItems);
  }, [onSaveLayout]);

  // Generate layout once when items change - memoized for performance
  const processedLayout = useMemo(() => {
    if (!items || items.length === 0) {
      return [];
    }

    return items.map(item => {
      // Ensure we have valid numbers for all dimensions
      const x = typeof item.gridColumn === 'number' ? item.gridColumn : 0
      const y = typeof item.gridRow === 'number' ? item.gridRow : 0
      const w = typeof item.width === 'number' ? Math.min(Math.max(item.width, 2), 12) : 4
      const h = typeof item.height === 'number' ? Math.min(Math.max(item.height, 1), 8) : 3

      return {
        i: item.id,
        x,
        y,
        w,
        h,
        minW: item.type === 'heading' ? 1 : 2,
        minH: item.type === 'heading' ? 1 : 2,
        static: !isEditMode
      }
    });
  }, [items, isEditMode]);

  // Handle window resize and initial mounting
  useEffect(() => {
    updateWidth()
    setMounted(true)

    // Throttled resize listener for better performance
    let resizeTimer: any;
    const handleResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(updateWidth, 100);
    };

    window.addEventListener('resize', handleResize)
    return () => {
      clearTimeout(resizeTimer);
      window.removeEventListener('resize', handleResize);
    }
  }, [updateWidth]);

  // Update layout state when processedLayout changes
  useEffect(() => {
    setLayout(processedLayout);
  }, [processedLayout]);

  // Handle drag start and end
  const handleDragStart = useCallback((layout: any[], oldItem: any, newItem: any) => {
    setIsDragging(true);
    isDraggingRef.current = true;
    layoutBeforeDragRef.current = layout;
    window.isDraggingDashboardItem = true;
    setActiveDragItemId(newItem.i);

    // Set cursor for whole document during drag
    document.body.style.cursor = 'grabbing';
  }, []);

  const handleDragStop = useCallback((layout: any[], oldItem: any, newItem: any) => {
    setIsDragging(false);
    isDraggingRef.current = false;
    window.isDraggingDashboardItem = false;
    setActiveDragItemId(null);

    // Restore cursor
    document.body.style.cursor = '';

    // Apply layout changes with a small delay to ensure UI is responsive
    setTimeout(() => {
      handleLayoutChange(layout);
    }, 50);
  }, []);

  // Handle resize start and end
  const handleResizeStart = useCallback((layout: any[], oldItem: any, newItem: any) => {
    setIsResizing(true);
    isResizingRef.current = true;
    window.isResizingDashboardItem = true;
    setActiveDragItemId(newItem.i);

    // Set cursor for whole document during resize
    document.body.style.cursor = 'se-resize';
  }, []);

  // Define handleLayoutChange function first to avoid circular dependency
  const handleLayoutChange = useCallback((newLayout: any[]) => {
    try {
      // Skip if there are no items or layout
      if (!items || items.length === 0 || !newLayout || newLayout.length === 0) {
        return;
      }

      // Check if any layout item actually changed, to avoid unnecessary updates
      const hasChanges = newLayout.some(layoutItem => {
        const item = items.find(item => item.id === layoutItem.i);
        return (
          item &&
          (item.gridColumn !== layoutItem.x ||
           item.gridRow !== layoutItem.y ||
           item.width !== layoutItem.w ||
           item.height !== layoutItem.h)
        );
      });

      // Skip the update if nothing changed
      if (!hasChanges) {
        return;
      }

      const updatedItems = items.map(item => {
        const layoutItem = newLayout.find(layoutItem => layoutItem.i === item.id)
        if (layoutItem) {
          // Ensure values are valid numbers
          return {
            ...item,
            gridColumn: Math.max(0, layoutItem.x),
            gridRow: Math.max(0, layoutItem.y),
            width: Math.max(1, layoutItem.w),
            height: Math.max(1, layoutItem.h)
          };
        }
        return item
      });

      // Set layout state immediately for responsive UI
      setLayout(newLayout);

      // Notify charts that layout has changed to trigger resize
      window.dispatchEvent(new CustomEvent('dashboard-layout-changed', {
        detail: { layout: newLayout }
      }));

      // Only save the entire layout if we're not currently dragging or resizing
      if (!isDraggingRef.current && !isResizingRef.current) {
        // Use a debounced function to save layout changes
        setTimeout(() => {
          debouncedSaveLayout(updatedItems);
        }, 100);
      }
    } catch (error) {
      console.error('Error updating layout:', error)
    }
  }, [items, debouncedSaveLayout]);

  const handleResizeStop = useCallback((layout: any[], oldItem: any, newItem: any) => {
    setIsResizing(false);
    isResizingRef.current = false;
    window.isResizingDashboardItem = false;
    setActiveDragItemId(null);

    // Restore cursor
    document.body.style.cursor = '';

    // Apply layout changes
    handleLayoutChange(layout);

    // Force charts to redraw after resize with multiple attempts to ensure it works
    const redrawCharts = () => {
      // Trigger a window resize event to ensure charts redraw properly
      window.dispatchEvent(new Event('resize'));

      // Find the specific chart that was resized and ensure it's visible
      const chartContainer = document.querySelector(`[data-item-id="${newItem.i}"] .recharts-responsive-container`);
      if (chartContainer instanceof HTMLElement) {
        chartContainer.style.visibility = 'visible';
        chartContainer.style.opacity = '1';
      }
    };

    // Try multiple times with increasing delays to ensure the chart redraws
    setTimeout(redrawCharts, 50);
    setTimeout(redrawCharts, 200);
    setTimeout(redrawCharts, 500);
  }, [handleLayoutChange]);

  // Don't render until client-side
  if (!mounted) return <div className="p-8 text-center">Loading dashboard...</div>

  return (
    <div className="w-full h-full p-4 dashboard-container">
      {items.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-[400px] border-2 border-dashed rounded-lg p-6 text-center">
          <div className="text-muted-foreground mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
          </div>
          <h3 className="text-lg font-medium">No items in dashboard</h3>
          <p className="text-sm text-muted-foreground max-w-md mt-2">
            Create charts, add text cards, or add headings to your dashboard.
          </p>
        </div>
      ) : (
        <GridLayout
          className={`layout ${viewMode === 'list' ? 'list-view' : 'grid-view'}`}
          layout={layout}
          cols={12}
          rowHeight={80}
          width={containerWidth}
          onLayoutChange={handleLayoutChange}
          onDragStart={handleDragStart}
          onDragStop={handleDragStop}
          onResizeStart={handleResizeStart}
          onResizeStop={handleResizeStop}
          compactType="vertical"
          preventCollision={false}
          isResizable={isEditMode}
          isDraggable={isEditMode}
          margin={[16, 16]}
          isBounded={true}
          useCSSTransforms={true}
          transformScale={1}
          draggableHandle=".draggable-handle"
          draggableCancel=".non-draggable"
          style={{
            overflowX: "hidden",
            transition: "none"
          }}
          resizeHandles={['se']}
          onResize={(layout, oldItem, newItem) => {
            // Force redraw during resize to ensure chart visibility
            window.dispatchEvent(new Event('resize'));

            // Dispatch custom event for chart resizing
            window.dispatchEvent(new CustomEvent('dashboard-item-resized', {
              detail: { itemId: newItem.i, width: newItem.w, height: newItem.h }
            }));

            // Find all chart containers and ensure they're visible during resize
            const chartContainers = document.querySelectorAll(`[data-item-id="${newItem.i}"] .chart-container, [data-item-id="${newItem.i}"] canvas, [data-item-id="${newItem.i}"] .echarts-for-react`);
            chartContainers.forEach(container => {
              if (container instanceof HTMLElement) {
                container.style.visibility = 'visible';
                container.style.opacity = '1';

                // Force a reflow
                void container.offsetWidth;
              }
            });
          }}
          resizeHandle={
            <div className="custom-resize-handle">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 22L12 12M22 12L12 22" />
              </svg>
            </div>
          }
        >
          {items.map(item => (
            <div
              key={item.id}
              className={`relative dashboard-item-container ${
                activeDragItemId === item.id ? 'z-50' : ''
              } ${
                item.id === window.activeItemId ? 'active-item' : ''
              }`}
            >
              <DashboardItemCard
                item={item}
                isEditMode={isEditMode}
                onUpdateItem={onUpdateItem}
                onRemoveItem={onRemoveItem}
              />
            </div>
          ))}
        </GridLayout>
      )}
    </div>
  )
}