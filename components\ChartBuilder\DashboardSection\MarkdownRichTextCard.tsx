'use client'

/**
 * MarkdownRichTextCard Component
 *
 * A markdown-based rich text editor card for the dashboard.
 * Features:
 * - Markdown editing with preview
 * - Clean separation between edit and view modes
 * - Smart save functionality with explicit "Done" button
 * - Keyboard shortcuts
 */

import { useState, useEffect, useRef } from 'react';
import { TextItem } from './types';
import { Grip, Trash2, Edit, Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { useTheme } from 'next-themes';
import { cn } from "@/lib/utils";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover";

interface MarkdownRichTextCardProps {
  textItem: TextItem;
  isEditMode: boolean;
  onUpdateText: (textId: string, updates: Partial<TextItem>) => void;
  onRemoveText: (textId: string) => void;
}

// Markdown toolbar component
function MarkdownToolbar({ 
  onAction, 
  setIsToolbarAction 
}: { 
  onAction: (action: string) => void, 
  setIsToolbarAction: (value: boolean) => void 
}) {
  const handleButtonClick = (action: string) => {
    setIsToolbarAction(true);
    onAction(action);
    // Reset after a short delay
    setTimeout(() => setIsToolbarAction(false), 100);
  };

  return (
    <div className="flex items-center gap-1 p-1 bg-muted/20 border-b">
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('bold')}
        title="Bold"
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('italic')}
        title="Italic"
      >
        <Italic className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('bulletList')}
        title="Bullet List"
      >
        <List className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('numberedList')}
        title="Numbered List"
      >
        <ListOrdered className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('code')}
        title="Code"
      >
        <Code className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('link')}
        title="Link"
      >
        <Link className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('image')}
        title="Image"
      >
        <ImageIcon className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 w-7 p-0" 
        onClick={() => handleButtonClick('table')}
        title="Table"
      >
        <TableIcon className="h-4 w-4" />
      </Button>
    </div>
  );
}

export function MarkdownRichTextCard({ textItem, isEditMode, onUpdateText, onRemoveText }: MarkdownRichTextCardProps) {
  const { theme } = useTheme();
  
  // State for UI interactions
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [editValue, setEditValue] = useState<string>(textItem.content || '');
  const [isToolbarAction, setIsToolbarAction] = useState(false);
  
  // Refs for managing state and avoiding re-renders
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isNewRef = useRef<boolean>(textItem.isNew || false);
  const isMountedRef = useRef<boolean>(false);
  
  // Initialize once on mount
  useEffect(() => {
    // Cleanup function for any timeouts
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  // Setup initial state once mounted
  useEffect(() => {
    // If new item, enter edit mode after a brief delay
    if (textItem.isNew && !isMountedRef.current) {
      isNewRef.current = true;
      timeoutRef.current = setTimeout(() => {
        setIsEditing(true);
      }, 100);
    }
    
    // Update edit value when content changes from props
    setEditValue(textItem.content || '');
    
    isMountedRef.current = true;
  }, [textItem.id, textItem.isNew, textItem.content]);
  
  // Focus textarea and adjust height when entering edit mode
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      // Auto-adjust height based on content
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditing]);
  
  // Handle mouse enter/leave for hover effects
  const handleMouseEnter = () => {
    setIsHovered(true);
  };
  
  const handleMouseLeave = () => {
    setIsHovered(false);
  };
  
  // Handle double click to enter edit mode
  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode && !isEditing) {
      setIsEditing(true);
    }
  };
  
  // Handle edit button click
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (isEditMode && !isEditing) {
      setIsEditing(true);
    }
  };
  
  // Handle textarea change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditValue(e.target.value);
    
    // Auto-adjust height based on content
    e.target.style.height = "auto";
    e.target.style.height = `${e.target.scrollHeight}px`;
  };
  
  // Handle toolbar actions
  const handleAction = (action: string) => {
    if (!textareaRef.current) return;
    
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editValue.substring(start, end);
    
    let newText = editValue;
    let newCursorPos = end;
    
    switch (action) {
      case 'bold':
        newText = editValue.substring(0, start) + `**${selectedText}**` + editValue.substring(end);
        newCursorPos = end + 4;
        break;
      case 'italic':
        newText = editValue.substring(0, start) + `*${selectedText}*` + editValue.substring(end);
        newCursorPos = end + 2;
        break;
      case 'bulletList':
        if (selectedText) {
          const lines = selectedText.split('\n');
          const bulletList = lines.map(line => `- ${line}`).join('\n');
          newText = editValue.substring(0, start) + bulletList + editValue.substring(end);
          newCursorPos = start + bulletList.length;
        } else {
          newText = editValue.substring(0, start) + '- ' + editValue.substring(end);
          newCursorPos = start + 2;
        }
        break;
      case 'numberedList':
        if (selectedText) {
          const lines = selectedText.split('\n');
          const numberedList = lines.map((line, i) => `${i + 1}. ${line}`).join('\n');
          newText = editValue.substring(0, start) + numberedList + editValue.substring(end);
          newCursorPos = start + numberedList.length;
        } else {
          newText = editValue.substring(0, start) + '1. ' + editValue.substring(end);
          newCursorPos = start + 3;
        }
        break;
      case 'code':
        if (selectedText.includes('\n')) {
          newText = editValue.substring(0, start) + '```\n' + selectedText + '\n```' + editValue.substring(end);
          newCursorPos = end + 8;
        } else {
          newText = editValue.substring(0, start) + '`' + selectedText + '`' + editValue.substring(end);
          newCursorPos = end + 2;
        }
        break;
      case 'link':
        newText = editValue.substring(0, start) + `[${selectedText || 'Link text'}](url)` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 3 : 11);
        break;
      case 'image':
        newText = editValue.substring(0, start) + `![${selectedText || 'Alt text'}](url)` + editValue.substring(end);
        newCursorPos = start + (selectedText ? selectedText.length + 4 : 12);
        break;
      case 'table':
        newText = editValue.substring(0, start) + 
          '| Header 1 | Header 2 | Header 3 |\n' +
          '| -------- | -------- | -------- |\n' +
          '| Cell 1   | Cell 2   | Cell 3   |\n' +
          '| Cell 4   | Cell 5   | Cell 6   |' + 
          editValue.substring(end);
        newCursorPos = start + 129;
        break;
    }
    
    setEditValue(newText);
    
    // Set cursor position after state update
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        
        // Auto-adjust height based on content
        textareaRef.current.style.height = "auto";
        textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
      }
    }, 0);
  };
  
  // Handle save button click
  const handleSave = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    try {
      // Exit edit mode first
      setIsEditing(false);
      
      // Only save if content changed or it's a new item
      if (editValue !== textItem.content || isNewRef.current) {
        // Use timeout to avoid update cycles
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          onUpdateText(textItem.id, {
            content: editValue,
            isNew: false,
            isRichText: true
          });
          
          isNewRef.current = false;
          
          toast.success('Markdown saved', { duration: 2000 });
        }, 100);
      }
    } catch (error) {
      console.error('Error saving markdown:', error);
      toast.error('Failed to save markdown');
      setIsEditing(false);
    }
  };
  
  // Handle blur event
  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    // Only save on blur if it's not a toolbar action
    if (!isToolbarAction) {
      handleSave();
    }
  };
  
  // Cancel editing
  const handleCancel = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    setIsEditing(false);
    // Restore original content from props
    setEditValue(textItem.content || '');
  };
  
  // Handle rich text deletion
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onRemoveText(textItem.id);
  };
  
  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+Enter to save
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    }
    
    // Esc to cancel
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };
  
  // Custom components for ReactMarkdown
  const customComponents = {
    // Add custom components for markdown rendering if needed
  };
  
  // Render the card with conditional content based on edit mode
  return (
    <Card
      className={`w-full h-full relative rich-text-card ${isEditMode ? '' : 'no-border'}`}
      style={{
        zIndex: isEditing ? 100 : isHovered ? 10 : 'auto',
        boxShadow: isEditing ? '0 4px 12px rgba(0, 0, 0, 0.1)' : 'none',
        backgroundColor: 'transparent',
        border: isEditMode ? 
               (isEditing ? '1px solid hsl(var(--primary))' : 
               (isHovered ? '1px solid rgba(0, 0, 0, 0.1)' : '1px solid transparent')) : 
               'none',
        transition: 'border-color 0.2s ease, box-shadow 0.2s ease'
      }}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-item-id={textItem.id}
      data-is-editing={isEditing ? 'true' : 'false'}
    >
      {/* Card Header with Controls - Only shown when hovered or editing, and in edit mode */}
      {isEditMode && (
        <CardHeader 
          className="flex flex-row items-center justify-between p-1 space-y-0 bg-primary/5 border-b card-header non-draggable" 
          style={{ 
            opacity: isEditing || isHovered ? 1 : 0,
            transition: 'opacity 0.2s ease',
            height: '24px',
            overflow: 'hidden'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex items-center gap-1">
            <Grip className="h-3 w-3 text-primary/70 draggable-handle" />
            <span className="text-xs font-medium text-primary/70">Markdown</span>
          </div>
          
          {!isEditing && (
            <div className="flex items-center gap-1 non-draggable">
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 non-draggable"
                onClick={handleEditClick}
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600 non-draggable"
                onClick={handleDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          )}
        </CardHeader>
      )}
      
      {/* Card Content */}
      <CardContent 
        className="p-0" 
        style={{ 
          height: isEditMode && (isEditing || isHovered) ? 'calc(100% - 24px)' : '100%' 
        }}
      >
        {isEditing ? (
          // Edit mode with markdown editor
          <div className="w-full h-full flex flex-col non-draggable">
            {/* Toolbar */}
            <div className="flex items-center justify-between border-b bg-muted/20">
              <MarkdownToolbar 
                onAction={handleAction} 
                setIsToolbarAction={setIsToolbarAction}
              />
              
              <div className="flex items-center gap-1 p-1">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-6 text-xs non-draggable"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button 
                  variant="default" 
                  size="sm" 
                  className="h-6 text-xs non-draggable"
                  onClick={handleSave}
                >
                  Done
                </Button>
              </div>
            </div>
            
            {/* Markdown Editor */}
            <textarea
              ref={textareaRef}
              value={editValue}
              onChange={handleChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              className="w-full resize-none p-3 bg-background border-none focus:outline-none focus:ring-0 font-mono text-sm min-h-[100px] non-draggable"
              placeholder="Enter markdown content here..."
              style={{ height: 'auto' }}
            />
            
            <div className="absolute bottom-3 right-3 bg-primary/10 text-primary text-xs px-2 py-1 rounded-sm">
              Ctrl+Enter to save, Esc to cancel
            </div>
          </div>
        ) : (
          // View-only mode with markdown rendering
          <div 
            className={cn(
              "w-full h-full overflow-auto p-3 prose prose-sm max-w-none min-h-[60px]",
              "prose-headings:font-bold prose-headings:text-foreground",
              "prose-p:text-foreground prose-p:leading-relaxed",
              "prose-a:text-primary",
              "prose-blockquote:text-muted-foreground prose-blockquote:border-primary",
              "prose-ul:text-foreground prose-ol:text-foreground",
              "prose-code:text-muted-foreground prose-code:bg-muted prose-code:rounded-sm prose-code:px-1 prose-code:py-0.5",
              "prose-img:rounded-md",
              theme === 'dark' ? "dark:prose-invert" : ""
            )}
            style={{ 
              backgroundColor: 'transparent',
              textAlign: textItem.textAlign as any || 'left' 
            }}
          >
            <ReactMarkdown 
              remarkPlugins={[remarkGfm]}
              components={customComponents}
              rehypePlugins={[rehypeRaw]}
              className="break-words"
            >
              {editValue || '*Double-click to edit markdown*'}
            </ReactMarkdown>
          </div>
        )}
      </CardContent>
      
      {/* Custom resize handle - Only shown when hovered or in edit mode */}
      {isEditMode && !isEditing && (
        <div 
          className="absolute bottom-0 right-0 w-6 h-6 cursor-se-resize transition-opacity react-resizable-handle non-draggable"
          style={{
            zIndex: 5,
            opacity: isHovered ? 0.7 : 0,
            backgroundColor: 'rgba(var(--primary-rgb), 0.05)',
            borderTopLeftRadius: '4px',
            transition: 'opacity 0.2s ease, background-color 0.2s ease',
            boxShadow: '-1px -1px 2px rgba(0,0,0,0.05)'
          }}
        >
          <ChevronDown className="h-3 w-3 rotate-45 text-primary/70 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
      )}
    </Card>
  );
}
