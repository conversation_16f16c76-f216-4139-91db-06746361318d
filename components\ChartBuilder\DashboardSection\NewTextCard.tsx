'use client'

import { useState, useEffect } from 'react';
import { TextItem } from './types';
import { Grip, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';

interface NewTextCardProps {
  textItem: TextItem;
  isEditMode: boolean;
  onUpdateText: (textId: string, updates: Partial<TextItem>) => void;
  onRemoveText: (textId: string) => void;
}

export function NewTextCard({ textItem, isEditMode, onUpdateText, onRemoveText }: NewTextCardProps) {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [editValue, setEditValue] = useState<string>(textItem.content || '');
  const isTitleStyle = textItem.textStyle === 'title';

  // Initialize once on mount
  useEffect(() => {
    // If new item, enter edit mode
    if (textItem.isNew) {
      setIsEditing(true);
    }
  }, [textItem.isNew]);

  // Handle save
  const handleSave = () => {
    onUpdateText(textItem.id, {
      content: editValue,
      isNew: false
    });
    setIsEditing(false);
  };

  // Handle cancel
  const handleCancel = () => {
    setEditValue(textItem.content || '');
    setIsEditing(false);
  };

  // Handle double click to edit
  const handleDoubleClick = () => {
    if (isEditMode) {
      setIsEditing(true);
    }
  };

  return (
    <Card
      className={cn(
        "w-full h-full border transition-all duration-200",
        isTitleStyle ? "title-text-card" : "content-text-card",
        isEditMode ? "hover:border-primary" : "",
        isEditing ? "ring-2 ring-primary ring-offset-2" : ""
      )}
      style={{
        zIndex: isEditing ? 1000 : 10,
        border: isEditing ? '2px solid hsl(var(--primary))' : undefined,
        boxShadow: isEditing ? '0 4px 12px rgba(0, 0, 0, 0.1)' : undefined,
        transform: 'translate3d(0, 0, 0)', // Force GPU acceleration
        willChange: isEditing ? 'transform' : 'auto',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onDoubleClick={handleDoubleClick}
    >
      {/* Card Content */}
      <CardContent className={cn(
        "p-2 h-full",
        isTitleStyle ? "flex items-center justify-center" : ""
      )}>
        {isEditing ? (
          <div className="w-full h-full flex flex-col">
            <Textarea
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="flex-1 resize-none border-none focus-visible:ring-0 p-0"
              placeholder="Enter text here..."
              autoFocus
            />
            <div className="flex justify-end gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
              >
                Save
              </Button>
            </div>
          </div>
        ) : (
          <div className={cn(
            "w-full h-full",
            isTitleStyle ? "text-center font-bold text-lg" : "text-sm"
          )}>
            {textItem.content || 'Double-click to edit'}
          </div>
        )}
      </CardContent>

      {/* Edit/Delete Controls - Only visible in edit mode */}
      {isEditMode && isHovered && !isEditing && (
        <div className="absolute top-1 right-1 flex gap-1 z-10">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setIsEditing(true)}
          >
            <Edit className="h-3 w-3" />
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Text</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this text? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={() => onRemoveText(textItem.id)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      )}

      {/* Drag Handle - Only visible in edit mode */}
      {isEditMode && isHovered && !isEditing && (
        <div className="absolute top-1 left-1 z-10">
          <Grip className="h-3 w-3 text-muted-foreground" />
        </div>
      )}
    </Card>
  );
}
