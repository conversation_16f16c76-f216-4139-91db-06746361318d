'use client'

import { useState, useRef, useEffect } from 'react';
import { TableItem } from './types';
import { Maximize2, Trash2, GripVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import './styles/dashboard-chart-fix.css';

interface TableCardProps {
  table: TableItem;
  isEditMode: boolean;
  onUpdateTable: (tableId: string, updates: Partial<TableItem>) => void;
  onRemoveTable: (tableId: string) => void;
  onToggleFullscreen: (tableId: string) => void;
}

export function TableCard({
  table,
  isEditMode,
  onUpdateTable,
  onRemoveTable,
  onToggleFullscreen
}: TableCardProps) {
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Function to trigger window resize to ensure tables redraw properly
  const triggerResize = () => {
    window.dispatchEvent(new Event('resize'));
  };

  // Resize handle component
  const ResizeHandle = ({ direction }: { direction: 'e' | 's' | 'se' }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const startX = e.clientX;
      const startY = e.clientY;
      const startWidth = table.width || 6;
      const startHeight = table.height || 4;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        moveEvent.preventDefault();

        // Set a flag to indicate resizing is in progress
        if (typeof window !== 'undefined') {
          window.isResizingDashboardItem = true;
        }

        const deltaX = moveEvent.clientX - startX;
        const deltaY = moveEvent.clientY - startY;

        // Calculate new width and height based on direction
        let newWidth = startWidth;
        let newHeight = startHeight;

        if (direction === 'e' || direction === 'se') {
          // Limit width to a maximum of 12 grid columns to prevent breaking dashboard border
          newWidth = Math.min(12, Math.max(2, startWidth + Math.round(deltaX / 100)));
        }

        if (direction === 's' || direction === 'se') {
          // Ensure reasonable height limits
          newHeight = Math.max(2, startHeight + Math.round(deltaY / 100));
        }

        // Update table dimensions
        onUpdateTable(table.id, { width: newWidth, height: newHeight });
      };

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // Clear the resizing flag
        if (typeof window !== 'undefined') {
          window.isResizingDashboardItem = false;
        }

        // Trigger resize event after a short delay
        setTimeout(triggerResize, 100);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    return (
      <div
        className={`absolute ${direction === 'e' ? 'right-0 top-0 bottom-0 w-2 cursor-ew-resize' :
                              direction === 's' ? 'bottom-0 left-0 right-0 h-2 cursor-ns-resize' :
                              'bottom-0 right-0 w-4 h-4 cursor-nwse-resize'}`}
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.05)',
          zIndex: 10,
          borderRadius: direction === 'se' ? '0 0 4px 0' : '0'
        }}
        onMouseDown={handleMouseDown}
      />
    );
  };

  // Trigger resize when component mounts and when window resizes
  useEffect(() => {
    // Initial resize
    triggerResize();

    // Listen for window resize events
    window.addEventListener('resize', triggerResize);

    return () => {
      window.removeEventListener('resize', triggerResize);
    };
  }, []);

  return (
    <div
      ref={cardRef}
      className={cn(
        "relative rounded-lg dark:bg-black bg-white",
        isDragging ? "z-50 cursor-grabbing" : isEditMode ? "cursor-grab" : "",
        isEditMode ? "border shadow-sm hover:border-primary" : "border-0 shadow-none",
        "dashboard-item-container", // Add this class for grid layout integration
        !isEditMode && "no-border" // Add no-border class in view mode for cleaner appearance
      )}
      data-item-type="table"
      data-item-id={table.id}
      data-is-dragging={isDragging}
      data-is-resizing={false}
      style={{
        width: '100%',
        height: '100%',
        display: 'grid',
        gridTemplateRows: table.description
          ? '24px 1fr 20px'
          : '24px 1fr',
        overflow: 'hidden',
        transform: 'translate3d(0,0,0)', // Force GPU acceleration
        willChange: isEditMode ? 'transform, box-shadow' : 'auto'
      }}
    >
      {/* Draggable Handle - Positioned at the top */}
      {isEditMode && (
        <div className="draggable-handle absolute top-0 left-0 w-full h-6 flex items-center justify-center cursor-grab rounded-t-md z-10 dark:black bg-gray-100">
          <GripVertical className="h-4 w-4 text-primary" />
        </div>
      )}

      {/* Card Header */}
      <div className={`flex items-center justify-between px-1 py-0.5 ${isEditMode ? 'border-b dark:border-gray-700' : ''} dark:bg-black bg-white`}>
        <div className="flex items-center gap-1">
          <h3 className="text-xs font-medium truncate">{table.title || 'Data Table'}</h3>
          <span className="text-[10px] text-muted-foreground bg-muted/20 px-1 py-0.5 rounded ml-1">
            table
          </span>
        </div>
        <div className="flex items-center gap-1">
          {isEditMode && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 transition-colors non-draggable"
                onClick={() => onToggleFullscreen(table.id)}
              >
                <Maximize2 className="h-3 w-3" />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600 transition-colors non-draggable"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Table</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this table? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => onRemoveTable(table.id)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}
        </div>
      </div>

      {/* Table Area */}
      <div
        ref={tableContainerRef}
        className="dark:bg-black bg-white"
        style={{
          width: '100%',
          height: '100%',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column',
          marginTop: isEditMode ? '20px' : '0', // Add margin when in edit mode to account for the drag handle
          minHeight: '100px' // Ensure minimum height for small tables
        }}
      >
        <div className="w-full h-full overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {table.columns && table.columns.map((column, index) => (
                  <TableHead key={index} className="text-xs py-1 px-2">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.data && table.data.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {table.columns && table.columns.map((column, colIndex) => (
                    <TableCell key={colIndex} className="text-xs py-1 px-2">
                      {row[column] !== undefined ? String(row[column]) : ''}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Table Footer - Only shown if there's a description */}
      {table.description && (
        <div className={`${isEditMode ? 'border-t dark:border-gray-700' : ''} dark:bg-black bg-white text-[10px] text-muted-foreground overflow-hidden`}>
          <p className="truncate">{table.description}</p>
        </div>
      )}

      {/* Resize Handles */}
      {isEditMode && (
        <>
          <ResizeHandle direction="e" />
          <ResizeHandle direction="s" />
          <ResizeHandle direction="se" />
        </>
      )}
    </div>
  );
}
