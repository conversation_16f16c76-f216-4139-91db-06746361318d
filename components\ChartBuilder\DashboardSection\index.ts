/**
 * Dashboard Section Components
 *
 * This file exports all the components needed for the dashboard functionality.
 * Import this file to access all dashboard-related components.
 */

// Main dashboard components - export Dashboard as the primary component
export { Dashboard } from './Dashboard';
export { DashboardLayout } from './DashboardLayout';
export { DashboardToolbar } from './DashboardToolbar';
export { DashboardChartCard } from './DashboardChartCard';
export { DashboardGrid } from './DashboardGrid';
export { EnhancedDashboardLayout } from './EnhancedDashboardLayout';

// Card components
export { HeadingCard } from './HeadingCard';
export { AddHeadingDialog } from './AddHeadingDialog';
export { BlockNoteRichTextCard } from './BlockNoteRichTextCard';
export { TableCard } from './TableCard';
export { PythonPlotCard } from './PythonPlotCard';

// Types
export * from './types';

// Export types
export type {
  DashboardItemType,
  HeadingLevel,
  DashboardItem,
  HeadingItem,
  SavedChart,
  TextItem,
  TableItem,
  PythonPlotItem
} from './types'

// Import styles
import './styles/dashboard-enhanced.css';