/* Dashboard Chart Fix Styles - Enhanced for Responsiveness */

/* ===== Dashboard-specific Chart Styles ===== */
/* Make ECharts containers responsive in dashboard only */
.dashboard-item-container .echarts-for-react,
.dashboard-item-container div[ref="chartRef"],
.dashboard-item-container .chart-container,
.dashboard-item-container [_echarts_instance_],
.dashboard-item-container .echarts-for-react > div,
.dashboard-item-container .chart-preview,
.dashboard-chart,
.dashboard-chart-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 50px !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Ensure ECharts canvas is properly sized in dashboard */
.dashboard-item-container canvas,
.dashboard-chart canvas,
.dashboard-chart-container canvas {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* Fix for chart container to expand properly in dashboard */
.dashboard-item-container .chart-container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  justify-content: stretch !important;
}

/* Ensure EnhancedChartVisualizer takes full space in dashboard */
.dashboard-item-container .chart-container > div,
.dashboard-item-container .chart-visualizer-container,
.dashboard-item-container .chart-visualizer-container > div {
  flex: 1 1 auto !important;
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
}

/* ===== Notebook-specific Chart Styles ===== */
/* Ensure notebook charts have proper dimensions but don't use absolute positioning */
.notebook-chart,
.notebook-chart-container {
  position: relative !important;
  width: 100% !important;
  min-height: 250px !important;
  overflow: hidden !important;
}

.notebook-chart canvas,
.notebook-chart-container canvas {
  width: 100% !important;
  height: 100% !important;
}

/* ===== Legacy Recharts Styles (kept for backward compatibility) ===== */
/* Make charts fit properly in their containers */
.recharts-responsive-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 50px !important;
}

.recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
  min-height: 50px !important;
}

.recharts-wrapper svg {
  width: 100% !important;
  height: 100% !important;
  overflow: visible;
  min-height: 50px !important;
}

/* Reduce font sizes for better fit in small cards */
.recharts-text {
  font-size: 9px !important;
}

.recharts-cartesian-axis-tick-value {
  font-size: 7px !important;
}

.recharts-legend-item-text {
  font-size: 7px !important;
}

/* For very small charts, make text even smaller */
@media (max-height: 150px), (max-width: 150px) {
  .recharts-text {
    font-size: 6px !important;
  }

  .recharts-cartesian-axis-tick-value {
    font-size: 5px !important;
  }

  .recharts-legend-item-text {
    font-size: 5px !important;
  }
}

/* Ensure tooltip is visible */
.recharts-tooltip-wrapper {
  z-index: 1000;
}

/* Adjust legend position */
.recharts-legend-wrapper {
  bottom: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  height: auto !important;
  max-height: 30px !important;
}

/* For very small charts, adjust or hide legend */
@media (max-height: 120px) {
  .recharts-legend-wrapper {
    max-height: 15px !important;
    transform: scale(0.8) !important;
    transform-origin: center bottom !important;
  }
}

@media (max-height: 80px) {
  .recharts-legend-wrapper {
    display: none !important;
  }
}

/* Optimize for small containers */
.recharts-cartesian-axis {
  font-size: 8px !important;
}

/* Adjust axis tick count for small charts */
@media (max-width: 200px) {
  .recharts-cartesian-axis-tick:nth-child(even) {
    display: none !important;
  }
}

/* Ensure chart content is visible in small containers */
.recharts-surface {
  overflow: visible !important;
}

/* Fix for pie charts in small containers */
.recharts-pie {
  transform-origin: center !important;
}

@media (max-height: 150px), (max-width: 150px) {
  .recharts-pie {
    transform: scale(0.8) !important;
  }
}

/* Ensure chart is visible even in tiny containers */
.recharts-wrapper, .recharts-surface, .recharts-legend-wrapper {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}
