/* Dashboard chart styles */

/* Make the chart container take full height */
.dashboard-chart-container {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure the chart fills its container */
.dashboard-chart-container .recharts-responsive-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 100px !important;
}

/* Make sure SVG elements scale properly */
.dashboard-chart-container .recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
}

.dashboard-chart-container .recharts-wrapper svg {
  width: 100% !important;
  height: 100% !important;
  overflow: visible !important;
}

/* Target the EnhancedChartVisualizer component */
.recharts-wrapper svg > g {
  width: 100% !important;
  height: 100% !important;
  transform-origin: center center !important;
}

/* Fix chart alignment and sizing issues */
.recharts-surface {
  overflow: visible !important;
  display: block !important;
  width: 100% !important;
  height: 100% !important;
}

/* Enhance chart wrapper to prevent unexpected resizing */
.recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  position: relative !important;
}

/* Ensure chart components fill their containers */
.dashboard-chart-container > div {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
}

/* Fix for deeply nested chart containers */
.dashboard-chart-container > div > div {
  width: 100% !important;
  height: 100% !important;
}

/* Ensure EnhancedChartVisualizer fills container */
.dashboard-chart-container [data-chart-visualizer] {
  width: 100% !important;
  height: 100% !important;
}

/* Fix specific chart type sizing issues */
.dashboard-chart-container .recharts-bar-chart,
.dashboard-chart-container .recharts-line-chart,
.dashboard-chart-container .recharts-area-chart,
.dashboard-chart-container .recharts-pie-chart {
  width: 100% !important;
  height: 100% !important;
}

/* Prevent chart legend overlap on small containers */
@media (max-height: 300px) {
  .dashboard-chart-container .recharts-legend-wrapper {
    font-size: 10px !important;
    height: auto !important;
  }
}

/* Fix for chart labels in small containers */
@media (max-height: 250px) {
  .dashboard-chart-container .recharts-text {
    font-size: 9px !important;
  }
}

/* Adjust font sizes for smaller containers */
@media (max-width: 768px) {
  .dashboard-chart-container .recharts-text {
    font-size: 10px !important;
  }
  
  .dashboard-chart-container .recharts-cartesian-axis-tick-value {
    font-size: 8px !important;
  }
}

/* Ensure tooltip is visible */
.recharts-tooltip-wrapper {
  z-index: 1000;
}

/* Improve chart visibility in dark mode */
.dark .recharts-cartesian-grid-horizontal line,
.dark .recharts-cartesian-grid-vertical line {
  stroke: rgba(255, 255, 255, 0.1);
}

.dark .recharts-cartesian-axis-line {
  stroke: rgba(255, 255, 255, 0.2);
}

.dark .recharts-legend-item-text {
  color: rgba(255, 255, 255, 0.8) !important;
}
