/**
 * Enhanced Dashboard Styles
 *
 * This file contains improved styles for the dashboard components,
 * focusing on smooth drag and drop, modern card styling, and better
 * visual feedback during interactions.
 */

/* Dashboard Container */
.dashboard-container {
  --grid-color: transparent;
  background-size: 80px 80px;
  background-image: none;
  transition: all 0.3s ease;
  overflow: visible; /* Changed from hidden to visible to prevent content being cut off */
  padding: 16px;
  margin: 0;
  max-width: 100%;
  width: 100%;
  position: relative;
  overscroll-behavior: contain; /* Changed from none to contain for better scrolling behavior */
  min-height: calc(100vh - 120px) !important; /* Adjust for header/toolbar height */
  height: auto !important;
  padding-bottom: 100px; /* Add extra padding at the bottom */
  box-sizing: border-box; /* Ensure padding is included in width/height calculations */
}

/* Card Styles */
.dashboard-card {
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  will-change: transform, box-shadow;
  transform: translate3d(0,0,0); /* Force GPU acceleration */
  backface-visibility: hidden;
  perspective: 1000px;
}

.dashboard-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transform: translateY(-1px);
}

.dashboard-card-edit {
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px dashed rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  will-change: transform, box-shadow;
  transform: translate3d(0,0,0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.dashboard-card-edit:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  border-color: hsl(var(--primary) / 0.5);
}

/* Item Container */
.dashboard-item-container {
  transition: none !important; /* Remove transition during drags */
  will-change: transform, width, height;
  transform: translate3d(0,0,0);
  transform-style: preserve-3d;
  backface-visibility: hidden;
  touch-action: none !important;
}

.dashboard-item-container.dragging {
  transition: none !important;
  cursor: grabbing !important;
  z-index: 100 !important;
  pointer-events: all !important;
}

.dashboard-item-container.resizing {
  transition: none !important;
  z-index: 100 !important;
  pointer-events: all !important;
}

/* Drag Handle */
.draggable-handle {
  opacity: 1; /* Always visible in edit mode */
  background-color: rgba(var(--primary-rgb), 0.05);
  height: 24px !important;
  border-radius: 4px 4px 0 0;
  cursor: grab !important;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.dashboard-item-container:hover .draggable-handle {
  opacity: 1;
  background-color: rgba(var(--primary-rgb), 0.1);
}

.draggable-handle:hover {
  background-color: rgba(var(--primary-rgb), 0.2);
}

/* React Grid Layout Enhancements */
.react-grid-item {
  transition: all 0.25s ease-out; /* Smoother transition for card movement */
  will-change: transform, left, top, width, height !important;
  touch-action: none !important;
  transform: translate3d(0, 0, 0);
  z-index: 1; /* Default z-index */
}

.react-grid-item.react-grid-placeholder {
  background: rgba(var(--primary-rgb), 0.08);
  border: 2px dashed rgba(var(--primary-rgb), 0.15);
  border-radius: 8px;
  z-index: 0;
}

.react-grid-item.react-draggable-dragging {
  z-index: 100;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
  opacity: 0.95;
  cursor: grabbing !important;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
}

/* Custom resize handle */
.custom-resize-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0,0,0,0.2);
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease;
  background-color: rgba(var(--primary-rgb), 0.05);
  border-top-left-radius: 4px;
  z-index: 10;
  transform: translate3d(0, 0, 0);
}

.react-grid-item:hover .custom-resize-handle {
  opacity: 0.7;
}

/* Improved resize handle for rich text cards */
.rich-text-card .react-resizable-handle {
  opacity: 0;
  width: 24px !important;
  height: 24px !important;
  bottom: 0;
  right: 0;
  background-image: none !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--primary-rgb), 0.1);
  border-top-left-radius: 4px;
  transition: all 0.2s ease;
  z-index: 10;
}

.rich-text-card:hover .react-resizable-handle {
  opacity: 1;
}

.rich-text-card .react-resizable-handle::after {
  content: "";
  width: 8px;
  height: 8px;
  border-right: 2px solid rgba(var(--primary-rgb), 0.7);
  border-bottom: 2px solid rgba(var(--primary-rgb), 0.7);
  transform: rotate(45deg);
}

/* Text Card Styles */
.title-text-card {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.title-text-card:hover {
  background-color: rgba(0,0,0,0.01) !important;
}

.content-text-card {
  background-color: white;
  border: 1px solid rgba(0,0,0,0.05);
}

/* List View Styles */
.list-view .react-grid-item {
  width: 100% !important;
  margin-bottom: 16px !important;
}

/* Animation for new items */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.new-item {
  animation: fadeIn 0.3s ease-out;
}

/* Ghost position indicator */
.position-ghost {
  position: absolute;
  background-color: rgba(var(--primary-rgb), 0.1);
  border: 1px dashed rgba(var(--primary-rgb), 0.3);
  border-radius: 8px;
  pointer-events: none;
  z-index: -1;
  opacity: 0.7;
  transform: translate3d(0, 0, 0);
}

/* Snap guidelines */
.snap-guideline {
  position: absolute;
  background-color: rgba(var(--primary-rgb), 0.6);
  z-index: 1000;
  pointer-events: none;
  transform: translate3d(0, 0, 0);
}

.snap-guideline.horizontal {
  height: 1px;
  left: 0;
  right: 0;
}

.snap-guideline.vertical {
  width: 1px;
  top: 0;
  bottom: 0;
}

/* Dashboard item content */
.dashboard-item-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  transform: translate3d(0,0,0); /* Force GPU acceleration */
  will-change: transform;
  backface-visibility: hidden;
}

/* Dashboard toolbar enhancements */
.dashboard-toolbar {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  position: sticky;
  top: 0;
  z-index: 40;
  transform: translate3d(0,0,0);
}

/* Better performance for dragging operations */
.dragging-active * {
  pointer-events: none !important;
}

.dragging-active .react-draggable-dragging {
  pointer-events: auto !important;
}

/* Position indicators */
.position-indicator {
  position: absolute;
  background-color: rgba(var(--primary-rgb), 0.5);
  border-radius: 2px;
  z-index: 1000;
  pointer-events: none;
  opacity: 0.7;
  transition: all 0.1s ease;
}

.position-indicator-x {
  height: 1px;
  left: 0;
  right: 0;
  width: 100%;
}

.position-indicator-y {
  width: 1px;
  top: 0;
  bottom: 0;
  height: 100%;
}

/* Distance measurement */
.distance-label {
  position: absolute;
  background-color: rgba(var(--primary-rgb), 0.8);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  z-index: 1001;
  pointer-events: none;
  transform: translate3d(-50%, -50%, 0);
}

/* Auto-arrange helpers */
.auto-arrange-preview {
  position: absolute;
  border: 1px dashed rgba(var(--primary-rgb), 0.5);
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: 8px;
  z-index: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

/* Compact layout button */
.compact-layout-button {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 30;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.compact-layout-button:hover {
  opacity: 1;
}

/* Dark mode variants */
.dark .dashboard-container {
  --grid-color: rgba(255, 255, 255, 0.05);
}

.dark .dashboard-card {
  background-color: hsl(var(--card));
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.dark .dashboard-card-edit {
  background-color: hsl(var(--card));
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .dashboard-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.dark .title-text-card:hover {
  background-color: rgba(255,255,255,0.02) !important;
}

.dark .react-grid-item.react-grid-placeholder {
  background: rgba(var(--primary-rgb), 0.15);
  border-color: rgba(var(--primary-rgb), 0.3);
}

.dark .dashboard-toolbar {
  background-color: rgba(0, 0, 0, 0.7);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Root variables for colors */
:root {
  --primary-rgb: 37, 99, 235; /* Blue-600 */
}

.dark {
  --primary-rgb: 59, 130, 246; /* Blue-500 */
}

/* Fix for Safari */
@supports (-webkit-backdrop-filter: none) or (backdrop-filter: none) {
  .dashboard-toolbar {
    backdrop-filter: blur(8px);
  }
}

/* Performance optimizations for lower-end devices */
@media (prefers-reduced-motion: reduce) {
  .dashboard-card,
  .dashboard-card-edit,
  .react-grid-item {
    transition-duration: 0s !important;
  }

  .react-grid-item.react-draggable-dragging {
    animation: none !important;
  }
}

/* Better mobile experience */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 8px;
  }

  .react-resizable-handle {
    width: 20px !important;
    height: 20px !important;
  }

  .draggable-handle {
    height: 24px !important;
  }
}

/* Custom drag behavior improvements */
.dragging-active .react-grid-layout {
  cursor: grabbing !important;
}

/* Ensure drag overlay stays on top */
.react-draggable-dragging {
  z-index: 1000 !important;
  position: relative;
}

/* Fix for dragging performance by disabling pointer events on non-dragged elements */
.dragging-active .react-grid-item:not(.react-draggable-dragging) {
  pointer-events: none;
  transition: opacity 0.2s ease;
}

/* Speed up transition when dragging to make it follow cursor better */
.react-grid-layout.dragging {
  transition: none !important;
}

/* Ensure placeholder follows cursor movements immediately */
.react-grid-placeholder {
  transition: transform 50ms linear !important;
  transform: translate3d(0, 0, 0) !important;
}

/* Dashboard wrapper */
.dashboard-wrapper {
  min-height: 100vh !important;
  display: flex;
  flex-direction: column;
  height: auto !important;
  width: 100% !important;
  max-width: 100% !important;
  padding-bottom: 0;
  margin: 0;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  position: relative; /* Ensure proper stacking context */
  box-sizing: border-box; /* Ensure padding is included in width/height calculations */
}

/* Additional optimization for drag operations */
.react-draggable {
  transition: none !important;
}

/* Ensure the dashboard uses full screen width */
.dashboard-wrapper,
.dashboard-container,
.react-grid-layout {
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Improved chart cards - more bento-style */
[data-item-type="chart"] {
  overflow: hidden;
  border-radius: 8px;
  background-color: white;
  border: 1px solid transparent;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

[data-item-type="chart"]:hover {
  border-color: rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

/* Chart container styling is now in dashboard-chart-fix.css */

/* Rich text styling improvements */
.rich-text-card {
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Ensure no border in view mode - more aggressive selectors */
.no-border,
[data-edit-mode="false"] .card,
[data-edit-mode="false"] .dashboard-card,
[data-edit-mode="false"] [data-item-type],
[data-is-editing="false"] .card,
[data-is-editing="false"] .dashboard-card,
[data-is-editing="false"] [data-item-type] {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  outline: none !important;
}

/* Remove borders from dashboard cards in view mode */
.dashboard-card.no-border,
[data-edit-mode="false"] .dashboard-card,
[data-edit-mode="false"] .card {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  outline: none !important;
}

/* Remove borders from chart items in view mode */
[data-is-editing="false"] [data-item-type="chart"],
[data-edit-mode="false"] [data-item-type="chart"] {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Remove borders from text items in view mode */
[data-is-editing="false"] [data-item-type="text"],
[data-edit-mode="false"] [data-item-type="text"] {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  outline: none !important;
}

/* Rich text content should be clearly distinguishable but without borders in view mode */
.rich-text-content {
  outline: none !important;
  background-color: transparent !important;
}

/* Ensure editing content doesn't trigger dragging */
.editing-content {
  cursor: text !important;
  user-select: text !important;
  touch-action: auto !important;
}

/* Make chart cards more precise when dragging */
[data-item-type="chart"].react-draggable-dragging {
  cursor: grabbing !important;
  pointer-events: auto !important;
  touch-action: none !important;
  z-index: 1000 !important;
  box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
  transform: none !important;
  position: absolute !important;
}

/* Ensure cards follow cursor precisely */
.dragging-active .dashboard-item-container {
  transition: none !important;
  will-change: transform, left, top;
}

/* Improve resize handle visibility */
.react-resizable-handle {
  z-index: 10 !important;
  opacity: 0 !important;
  transition: opacity 0.2s ease;
}

.react-grid-item:hover .react-resizable-handle {
  opacity: 1 !important;
}

/* Additional Dashboard Layout Improvements for Bento Style */
.react-grid-layout {
  overflow: visible !important;
  position: relative;
}

/* Fix spacing for bento grid layout */
.bento-grid .dashboard-item-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* Non-draggable elements should be clickable without initiating drag */
.non-draggable {
  cursor: pointer !important;
  touch-action: auto !important;
  -webkit-user-drag: none !important;
  pointer-events: auto !important;
  z-index: 10 !important;
  position: relative;
}

/* Make sure the parent doesn't override events from non-draggable children */
.draggable-handle,
.react-grid-item {
  pointer-events: auto !important;
}

.non-draggable * {
  pointer-events: auto !important;
}
