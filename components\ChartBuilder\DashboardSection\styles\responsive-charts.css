/* Responsive chart styles */

/* Make sure the chart container takes full height */
.chart-container, .dashboard-chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Special styling for chart wrappers */
.chart-wrapper {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.chart-wrapper > div {
  width: 100% !important;
  height: 100% !important;
}

/* Ensure the ResponsiveContainer fills its parent */
.recharts-responsive-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 150px !important;
}

/* Adjust chart sizing for dashboard cards */
.dashboard-card .recharts-wrapper, .dashboard-chart-container .recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
  min-height: 150px !important;
}

/* Ensure EnhancedChartVisualizer takes full space */
.dashboard-chart-container > div {
  width: 100% !important;
  height: 100% !important;
}

/* Make sure all chart containers fill their parent */
.dashboard-chart-container .chart-preview {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Make sure SVG elements scale properly */
.recharts-wrapper svg {
  width: 100%;
  height: 100%;
  overflow: visible;
}

/* Enhanced dashboard card styling */
.dashboard-chart-container > div > div {
  height: 100% !important;
  width: 100% !important;
}

/* Fix for pie charts in dashboard */
.dashboard-chart-container .recharts-pie-chart {
  height: 100% !important;
}

.dashboard-chart-container .recharts-legend-wrapper {
  bottom: 0px !important;
}

/* Adjust font sizes for smaller containers */
@media (max-width: 768px) {
  .recharts-text {
    font-size: 10px !important;
  }

  .recharts-cartesian-axis-tick-value {
    font-size: 8px !important;
  }
}

/* Ensure tooltip is visible */
.recharts-tooltip-wrapper {
  z-index: 1000;
}

/* Fix for x-axis visibility */
.recharts-xAxis {
  transform: translateY(-10px);
}

/* Ensure chart container has enough space for x-axis */
.dashboard-chart-container {
  padding-bottom: 20px;
  max-width: 100%;
  overflow: hidden;
}

/* Prevent chart overflow */
.dashboard-chart-container .recharts-wrapper {
  max-width: 100%;
}

/* Ensure chart cards don't break dashboard layout */
.dashboard-wrapper {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Improve chart visibility in dark mode */
.dark .recharts-cartesian-grid-horizontal line,
.dark .recharts-cartesian-grid-vertical line {
  stroke: rgba(255, 255, 255, 0.1);
}

.dark .recharts-cartesian-axis-line {
  stroke: rgba(255, 255, 255, 0.2);
}

.dark .recharts-legend-item-text {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Ensure chart fits in card */
.dashboard-item-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-item-content > div {
  flex: 1;
  min-height: 0;
}
