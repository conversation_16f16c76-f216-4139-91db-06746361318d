/**
 * Dashboard Types
 *
 * This file defines the types used throughout the dashboard components.
 * It includes definitions for different types of dashboard items (charts, headings, text)
 * and their properties.
 */

export type DashboardItemType = 'chart' | 'heading' | 'text' | 'table' | 'pythonplot' | 'calculator';

export type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

export interface DashboardItemBase {
  id: string;
  type: DashboardItemType;
  gridColumn: number;
  gridRow: number;
  width: number;
  height: number;
  gridSpan?: number; // For backward compatibility
  gridRowSpan?: number; // For backward compatibility
  createdAt?: Date;
}

export interface HeadingItem extends DashboardItemBase {
  type: 'heading';
  headingLevel: HeadingLevel;
  content: string;
  placeholder?: string; // Placeholder text for empty headings
  textAlign?: 'left' | 'center' | 'right';
  textColor?: string;
  backgroundColor?: string;
  isNew?: boolean; // Flag to indicate a newly created heading
}

export interface SavedChart extends DashboardItemBase {
  type: 'chart';
  title?: string; // Added title property
  description?: string; // Added description
  chartType?: 'line' | 'bar' | 'pie' | 'area'; // Added chartType
  data: any[];
  config: {
    type: 'line' | 'bar' | 'pie' | 'area';
    xAxis: string;
    yAxis: string;
    title: string;
    description: string;
    showLegend: boolean;
    showLabels: boolean;
    showGrid: boolean;
    color?: string;
    customLabel?: string;
    // Add missing properties that are used in the ChartVisualizer
    aggregation?: 'sum' | 'average' | 'min' | 'max' | 'count' | 'none';
    groupBy?: string;
    timeScale?: 'day' | 'week' | 'month' | 'year' | 'none';
    enableZoom?: boolean;
    multiSeries?: boolean;
    fontSize?: string;
  };
  size?: 'small' | 'medium' | 'large' | 'full'; // Added size property
}

export interface TextItem extends DashboardItemBase {
  type: 'text';
  content?: string;
  placeholder?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  textStyle?: 'normal' | 'title' | 'subtitle' | 'richtext' | 'default' | 'blocknote' | 'h1' | 'h2' | 'h3' | 'h4';
  fontSize?: number;
  fontWeight?: string;
  color?: string;
  backgroundColor?: string;
  isRichText?: boolean;
  isNew?: boolean; // Flag to indicate a newly created text card
  isBold?: boolean;
  isItalic?: boolean;
  isUnderline?: boolean;
  isChecklist?: boolean;
  checklistItems?: Array<{text: string; checked: boolean}>;
}

export interface TableItem extends DashboardItemBase {
  type: 'table';
  title?: string;
  description?: string;
  data: any[];
  columns?: string[];
  cellId?: string;
  isNew?: boolean;
  config?: any;
}

export interface PythonPlotItem extends DashboardItemBase {
  type: 'pythonplot';
  title?: string;
  description?: string;
  plotUrl: string;
  cellId?: string;
  isNew?: boolean;
  data?: any[];
  config?: any;
}

export interface CalculatorResultItem extends DashboardItemBase {
  type: 'calculator';
  title?: string;
  description?: string;
  formula: string;
  result: any;
  timestamp: number;
  cellId?: string;
  isNew?: boolean;
  resultType?: 'number' | 'text' | 'error';
  formattedResult?: string;
  icon?: string; // Icon name for the calculator card
}

export type DashboardItem = SavedChart | HeadingItem | TextItem | TableItem | PythonPlotItem | CalculatorResultItem;

