"use client"

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { defaultProps } from "@blocknote/core";
import { createReactBlockSpec } from "@blocknote/react";

// Define type for Morocco info types
type MoroccoInfoType = "general" | "currency" | "holidays" | "business";

// Define type for HR info types
type HRInfoType = "salary" | "holidays" | "insurance" | "taxes";

// Custom Morocco Info Block
export const MoroccoInfoBlock = createReactBlockSpec(
  {
    type: "moroccoInfo",
    propSchema: {
      // Include default styling props if needed
      textAlignment: defaultProps.textAlignment,
      textColor: defaultProps.textColor,
      infoType: {
        default: "general",
        values: ["general", "currency", "holidays", "business"],
      },
    },
    content: "none",
  },
  {
    render: (props) => {
      const infoType = props.block.props.infoType;

      const moroccoData = {
        general: {
          title: "Morocco Overview",
          content: "Morocco is a country located in North Africa with a population of approximately 37 million people. The capital is Rabat, and the largest city is Casablanca.",
        },
        currency: {
          title: "Moroccan Currency",
          content: "The currency of Morocco is the Moroccan Dirham (MAD). The exchange rate is approximately 10 MAD to 1 USD.",
        },
        holidays: {
          title: "Public Holidays in Morocco",
          content: "Major holidays include Eid al-Fitr, Eid al-Adha, Throne Day (July 30), and Independence Day (November 18).",
        },
        business: {
          title: "Business in Morocco",
          content: "Morocco has a diverse economy with key sectors including agriculture, tourism, textiles, and increasingly, advanced manufacturing and services.",
        },
      };

      // Type safe access
      const safeInfoType = moroccoData[infoType as keyof typeof moroccoData] 
        ? infoType as keyof typeof moroccoData 
        : "general";
      const info = moroccoData[safeInfoType];

      return (
        <Card className="w-full my-2 bg-muted/50">
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium">{info.title}</CardTitle>
          </CardHeader>
          <CardContent className="py-2 text-sm">
            {info.content}
            <div className="flex justify-end mt-2">
              <select
                value={infoType}
                onChange={(e) => {
                  props.editor.updateBlock(props.block, {
                    props: { 
                      ...props.block.props,
                      infoType: e.target.value as MoroccoInfoType
                    },
                  });
                }}
                className="text-xs p-1 rounded border bg-background"
              >
                <option value="general">General Info</option>
                <option value="currency">Currency</option>
                <option value="holidays">Holidays</option>
                <option value="business">Business</option>
              </select>
            </div>
          </CardContent>
        </Card>
      );
    },
  }
);

// HR Info Block
export const HRInfoBlock = createReactBlockSpec(
  {
    type: "hrInfo",
    propSchema: {
      // Include default styling props if needed
      textAlignment: defaultProps.textAlignment,
      textColor: defaultProps.textColor,
      infoType: {
        default: "salary",
        values: ["salary", "holidays", "insurance", "taxes"],
      },
    },
    content: "none",
  },
  {
    render: (props) => {
      const infoType = props.block.props.infoType;

      const hrData = {
        salary: {
          title: "Salary Information",
          content: "The minimum wage in Morocco is approximately 2,700 MAD per month. Average salaries range from 5,000 to 20,000 MAD depending on sector and experience.",
        },
        holidays: {
          title: "Holiday Entitlement",
          content: "Employees in Morocco are typically entitled to 18-24 days of paid leave per year, in addition to public holidays.",
        },
        insurance: {
          title: "Health Insurance",
          content: "Employers in Morocco must contribute to social security (CNSS) which covers basic healthcare. Many companies offer additional private health insurance.",
        },
        taxes: {
          title: "Payroll Taxes",
          content: "Income tax in Morocco ranges from 0% to 38%, with social security contributions of approximately 4.48% from employees and 18.5% from employers.",
        },
      };

      // Type safe access
      const safeInfoType = hrData[infoType as keyof typeof hrData] 
        ? infoType as keyof typeof hrData 
        : "salary";
      const info = hrData[safeInfoType];

      return (
        <Card className="w-full my-2 bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-900">
          <CardHeader className="py-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">{info.title}</CardTitle>
          </CardHeader>
          <CardContent className="py-2 text-sm">
            {info.content}
            <div className="flex justify-end mt-2">
              <select
                value={infoType}
                onChange={(e) => {
                  props.editor.updateBlock(props.block, {
                    props: { 
                      ...props.block.props,
                      infoType: e.target.value as HRInfoType
                    },
                  });
                }}
                className="text-xs p-1 rounded border bg-background"
              >
                <option value="salary">Salary Info</option>
                <option value="holidays">Holidays</option>
                <option value="insurance">Health Insurance</option>
                <option value="taxes">Taxes</option>
              </select>
            </div>
          </CardContent>
        </Card>
      );
    },
  }
); 