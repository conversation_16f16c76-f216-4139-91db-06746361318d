
"use client"

import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import {
  BlockNoteSchema,
  defaultBlockSpecs,
  filterSuggestionItems,
  insertOrUpdateBlock
} from "@blocknote/core";
import {
  SuggestionMenuController,
  getDefaultReactSlashMenuItems,
  useCreateBlockNote
} from "@blocknote/react";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Save, Info, FileText, Database } from "lucide-react";
import { toast } from "sonner";
import { saveNote } from "@/actions/actions";
import { MoroccoInfoBlock, HRInfoBlock } from "./SimpleCustomBlock";
import { DataAnalysisBlock } from "./DataAnalysisBlock";

// Type for editor to fix any type warnings
interface EditorType {
  insertBlocks: (blocks: any, targetBlock: any, placement: "before" | "after" | "nested") => void;
  getTextCursorPosition: () => { block: any };
}

const DashNote = () => {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [title, setTitle] = useState("Dashboard Note");
  const [isLoading, setIsLoading] = useState(false);

  // Create the schema with our custom blocks
  const schema = BlockNoteSchema.create({
    blockSpecs: {
      // Include default blocks
      ...defaultBlockSpecs,
      // Add our custom blocks
      moroccoInfo: MoroccoInfoBlock,
      hrInfo: HRInfoBlock,
      dataAnalysis: DataAnalysisBlock,
    },
  });
  
  // Slash menu items for custom blocks
  const insertMoroccoInfo = (editor: EditorType) => ({
    title: "Morocco Info",
    onItemClick: () => {
      insertOrUpdateBlock(editor as any, {
        type: "moroccoInfo",
        props: { infoType: "general" } as any
      });
    },
    aliases: ["morocco", "info", "country"],
    group: "Custom Blocks",
    icon: <Info className="h-4 w-4 text-green-600" />,
  });

  const insertHRInfo = (editor: EditorType) => ({
    title: "HR Info",
    onItemClick: () => {
      insertOrUpdateBlock(editor as any, {
        type: "hrInfo",
        props: { infoType: "salary" } as any
      });
    },
    aliases: ["hr", "payroll", "human resources"],
    group: "Custom Blocks",
    icon: <FileText className="h-4 w-4 text-blue-600" />,
  });

  const insertDataAnalysis = (editor: EditorType) => ({
    title: "Data Analysis",
    onItemClick: () => {
      insertOrUpdateBlock(editor as any, {
        type: "dataAnalysis",
        props: { analysisType: "code" } as any
      });
    },
    aliases: ["data", "analysis", "code", "chart", "table", "cell"],
    group: "Data Tools",
    icon: <Database className="h-4 w-4 text-purple-600" />,
  });

  // Creates a new editor instance with our schema
  const editor = useCreateBlockNote({
    schema,
    initialContent: [
      {
        type: "paragraph",
        content: "Type / then select Morocco Info, HR Info, or Data Analysis to add interactive blocks.",
      },
    ],
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  const customDarkTheme = {
    colors: {
      editor: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 10%, 3.9%)',
      },
      menu: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      tooltip: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 10%, 3.9%)',
      },
      hovered: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      selected: {
        text: 'hsl(240, 5.9%, 10%)',
        background: 'hsl(0, 0%, 98%)',
      },
      disabled: {
        text: 'hsl(240, 5%, 64.9%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      shadow: 'hsl(240, 3.7%, 15.9%)',
      border: 'hsl(240, 3.7%, 15.9%)',
    },
  };

  const handleSave = async () => {
    setIsLoading(true);
    const content = JSON.stringify(editor.topLevelBlocks);
    
    toast.promise(
      saveNote('', title, content, null),
      {
        loading: 'Saving note...',
        success: (result) => {
          if (result.success) {
            return `Note "${title}" saved successfully!`;
          } else {
            throw new Error(result.error || 'Failed to save note');
          }
        },
        error: 'Failed to save note',
      }
    );
    
    setIsLoading(false);
  };

  if (!mounted) return null;

  return (
    <div className="h-full w-full flex flex-col border rounded-lg overflow-hidden">
      <div className="sticky top-0 z-10 bg-background/50 backdrop-blur-sm border-b">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between gap-2">
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-lg font-medium border-none focus:outline-none bg-transparent px-0"
              placeholder="Untitled"
              disabled={isLoading}
            />
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleSave} 
              disabled={isLoading}
            >
              <Save className="mr-2 h-4 w-4" /> Save
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="prose prose-sm dark:prose-invert max-w-none">
          {isLoading ? (
            <div className="flex justify-center items-center h-24">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <BlockNoteView editor={editor} theme={resolvedTheme === "dark" ? customDarkTheme : "light"} slashMenu={false}>
              {/* Custom Slash Menu Controller */}
              <SuggestionMenuController
                triggerCharacter={"/"}
                getItems={async (query) =>
                  // Gets all default slash menu items and our custom items
                  filterSuggestionItems(
                    [
                      ...getDefaultReactSlashMenuItems(editor), 
                      insertMoroccoInfo(editor as EditorType),
                      insertHRInfo(editor as EditorType),
                      insertDataAnalysis(editor as EditorType)
                    ],
                    query
                  )
                }
              />
            </BlockNoteView>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashNote;
