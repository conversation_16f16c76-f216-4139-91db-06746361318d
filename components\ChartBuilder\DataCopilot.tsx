'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Send,
  X,
  Copy,
  Plus,
  Brain,
  PanelRightClose,
  PanelRightOpen,
  Database,
  Code2,
  Zap,
  Eye,
  BarChart3,
  AlertCircle
} from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'
import { CellData } from './chartbuilderlogic'
import { cn } from "@/lib/utils"
import { WorkflowPipeline } from './WorkflowPipeline'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable"

interface DataCopilotProps {
  cells: CellData[]
  datasets: Dataset[]
  selectedDatasets: Dataset[]
  onInsertCode: (code: string, cellId?: string, createNew?: boolean) => void
  onCreateNewCell: (code: string, language: string) => void
  isCollapsed: boolean
  onToggleCollapse: () => void
  onShowReactFlow?: () => void
}

interface ConversationMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  code?: string
  language?: string
  cellContext?: string
}

interface CellSummary {
  id: string
  index: number
  language: string
  hasResult: boolean
  hasError: boolean
  summary: string
  datasetCount: number
}

export function DataCopilot({
  cells,
  datasets,
  selectedDatasets,
  onInsertCode,
  onCreateNewCell,
  isCollapsed,
  onToggleCollapse,
  onShowReactFlow
}: DataCopilotProps) {
  const [conversation, setConversation] = useState<ConversationMessage[]>([])
  const [prompt, setPrompt] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [streamedResponse, setStreamedResponse] = useState('')
  const [selectedCellForInsertion, setSelectedCellForInsertion] = useState<string>('')

  const inputRef = useRef<HTMLTextAreaElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const conversationEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom of conversation
  useEffect(() => {
    conversationEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [conversation, streamedResponse])

  // Focus input when not collapsed
  useEffect(() => {
    if (!isCollapsed && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isCollapsed])

  // Analyze cell patterns and relationships
  const analyzeCellPatterns = useCallback(() => {
    const patterns = {
      dataLoading: cells.filter(cell =>
        cell.content.includes('SELECT') ||
        cell.content.includes('pd.read') ||
        cell.content.includes('FROM')
      ).length,
      visualization: cells.filter(cell =>
        cell.content.includes('plt.') ||
        cell.content.includes('sns.') ||
        cell.content.includes('plotly') ||
        cell.content.includes('chart')
      ).length,
      dataProcessing: cells.filter(cell =>
        cell.content.includes('groupby') ||
        cell.content.includes('merge') ||
        cell.content.includes('join') ||
        cell.content.includes('GROUP BY')
      ).length,
      statisticalAnalysis: cells.filter(cell =>
        cell.content.includes('describe') ||
        cell.content.includes('corr') ||
        cell.content.includes('mean') ||
        cell.content.includes('std')
      ).length
    }

    const workflow = {
      hasDataLoading: patterns.dataLoading > 0,
      hasVisualization: patterns.visualization > 0,
      hasProcessing: patterns.dataProcessing > 0,
      hasAnalysis: patterns.statisticalAnalysis > 0,
      suggestedNextSteps: [] as string[]
    }

    // Suggest next steps based on current workflow
    if (!workflow.hasDataLoading && datasets.length > 0) {
      workflow.suggestedNextSteps.push('Load and explore your datasets')
    }
    if (workflow.hasDataLoading && !workflow.hasAnalysis) {
      workflow.suggestedNextSteps.push('Perform statistical analysis')
    }
    if (workflow.hasAnalysis && !workflow.hasVisualization) {
      workflow.suggestedNextSteps.push('Create visualizations')
    }

    return { patterns, workflow }
  }, [cells, datasets])

  // Helper function to categorize cell content
  const getCellCategory = (content: string, language: string) => {
    if (language === 'markdown') return 'documentation'

    const lowerContent = content.toLowerCase()
    if (lowerContent.includes('select') || lowerContent.includes('from')) return 'data_query'
    if (lowerContent.includes('plt.') || lowerContent.includes('sns.') || lowerContent.includes('plotly')) return 'visualization'
    if (lowerContent.includes('groupby') || lowerContent.includes('merge') || lowerContent.includes('join')) return 'data_processing'
    if (lowerContent.includes('describe') || lowerContent.includes('corr') || lowerContent.includes('mean')) return 'analysis'
    if (lowerContent.includes('pd.read') || lowerContent.includes('import')) return 'data_loading'

    return 'general'
  }

  // Helper function to extract operations from cell content
  const extractOperations = (content: string, language: string) => {
    const operations = []
    const lowerContent = content.toLowerCase()

    if (language === 'sql') {
      if (lowerContent.includes('select')) operations.push('SELECT')
      if (lowerContent.includes('where')) operations.push('WHERE')
      if (lowerContent.includes('group by')) operations.push('GROUP BY')
      if (lowerContent.includes('order by')) operations.push('ORDER BY')
      if (lowerContent.includes('join')) operations.push('JOIN')
    } else if (language === 'python') {
      if (lowerContent.includes('groupby')) operations.push('groupby')
      if (lowerContent.includes('merge')) operations.push('merge')
      if (lowerContent.includes('plot')) operations.push('plotting')
      if (lowerContent.includes('describe')) operations.push('describe')
      if (lowerContent.includes('corr')) operations.push('correlation')
    }

    return operations
  }

  // Helper function to determine current workflow stage
  const getCurrentWorkflowStage = (workflow: any) => {
    if (!workflow.hasDataLoading) return 'setup'
    if (!workflow.hasProcessing && !workflow.hasAnalysis) return 'exploration'
    if (!workflow.hasVisualization) return 'analysis'
    return 'presentation'
  }

  // Analyze variable dependencies across cells (Jupyter-like functionality)
  const analyzeVariableDependencies = useCallback(() => {
    const variables = new Map<string, {
      definedIn: number[],
      usedIn: number[],
      type: string,
      lastValue?: any
    }>()

    cells.forEach((cell, index) => {
      if (cell.language === 'python' && cell.cellType !== 'markdown') {
        const lines = cell.content.split('\n')

        lines.forEach(line => {
          const trimmed = line.trim()

          // Variable assignments
          const assignmentMatch = trimmed.match(/^(\w+)\s*=/)
          if (assignmentMatch) {
            const varName = assignmentMatch[1]
            if (!variables.has(varName)) {
              variables.set(varName, { definedIn: [], usedIn: [], type: 'unknown' })
            }
            variables.get(varName)!.definedIn.push(index)

            // Determine type
            if (trimmed.includes('pd.read_csv') || trimmed.includes('DataFrame')) {
              variables.get(varName)!.type = 'dataframe'
            } else if (trimmed.includes('plt.') || trimmed.includes('get_plot()')) {
              variables.get(varName)!.type = 'plot'
            } else if (varName === 'result') {
              variables.get(varName)!.type = 'result'
            } else {
              variables.get(varName)!.type = 'variable'
            }
          }

          // Variable usage
          if (!trimmed.match(/^(\w+)\s*=/) && !trimmed.startsWith('#') && !trimmed.startsWith('import')) {
            const varMatches = trimmed.match(/\b(\w+)\b/g)
            if (varMatches) {
              varMatches.forEach(varName => {
                if (variables.has(varName)) {
                  variables.get(varName)!.usedIn.push(index)
                }
              })
            }
          }
        })
      }
    })

    return Object.fromEntries(variables)
  }, [cells])

  // Generate comprehensive context for AI
  const generateContext = useCallback(() => {
    const { patterns, workflow } = analyzeCellPatterns()
    const variableDependencies = analyzeVariableDependencies()

    const cellsContext = cells.map((cell, index) => {
      // Analyze cell content for better context
      const cellAnalysis = {
        cellNumber: index + 1,
        language: cell.language,
        content: cell.content,
        contentLength: cell.content.length,
        hasResult: !!cell.result?.data?.length,
        hasError: !!cell.error,
        errorMessage: cell.error || undefined,
        resultRowCount: cell.result?.data?.length || 0,
        selectedDatasets: cell.selectedDatasetIds?.map(id =>
          datasets.find(ds => ds.id === id)?.name
        ).filter(Boolean) || [],
        // Categorize cell type
        cellCategory: getCellCategory(cell.content, cell.language),
        // Extract key operations
        operations: extractOperations(cell.content, cell.language),
        // Variable analysis
        variablesCreated: Object.entries(variableDependencies)
          .filter(([_, info]) => info.definedIn.includes(index))
          .map(([name, info]) => ({ name, type: info.type })),
        variablesUsed: Object.entries(variableDependencies)
          .filter(([_, info]) => info.usedIn.includes(index) && !info.definedIn.includes(index))
          .map(([name, info]) => ({ name, type: info.type }))
      }
      return cellAnalysis
    })

    const datasetsContext = datasets.map(ds => {
      // Enhanced dataset analysis
      const columnTypes = ds.headers?.map(header => {
        const sampleValues = ds.data?.slice(0, 10).map(row => row[header]).filter(val => val != null) || []
        const isNumeric = sampleValues.every(val => !isNaN(Number(val)) && val !== '')
        const isDate = sampleValues.some(val => !isNaN(Date.parse(val)))
        const uniqueValues = new Set(sampleValues).size

        return {
          name: header,
          type: isNumeric ? 'numeric' : isDate ? 'date' : 'text',
          uniqueValues: uniqueValues,
          sampleValues: sampleValues.slice(0, 3),
          hasNulls: sampleValues.length < 10
        }
      }) || []

      return {
        name: ds.name,
        columns: ds.headers,
        columnTypes: columnTypes,
        rowCount: ds.data?.length || 0,
        sampleData: ds.data?.slice(0, 3) || [],
        fileType: ds.fileType,
        // Dataset quality metrics
        qualityMetrics: {
          totalColumns: ds.headers?.length || 0,
          numericColumns: columnTypes.filter(col => col.type === 'numeric').length,
          textColumns: columnTypes.filter(col => col.type === 'text').length,
          dateColumns: columnTypes.filter(col => col.type === 'date').length
        }
      }
    })

    return {
      cells: cellsContext,
      datasets: datasetsContext,
      totalCells: cells.length,
      totalDatasets: datasets.length,
      selectedDatasets: selectedDatasets.map(ds => ds.name),
      variableDependencies,
      workflowAnalysis: {
        patterns,
        workflow,
        currentStage: getCurrentWorkflowStage(workflow),
        suggestedNextSteps: workflow.suggestedNextSteps
      },
      jupyterLikeFeatures: {
        variableSharing: Object.keys(variableDependencies).length > 0,
        crossCellDependencies: Object.values(variableDependencies).some(v =>
          v.definedIn.length > 0 && v.usedIn.length > 0
        ),
        dataflowComplexity: Object.values(variableDependencies).reduce((acc, v) =>
          acc + v.definedIn.length + v.usedIn.length, 0
        )
      }
    }
  }, [cells, datasets, selectedDatasets, analyzeCellPatterns, analyzeVariableDependencies])

  const handleGenerate = async (quickPrompt?: string) => {
    const currentPrompt = quickPrompt || prompt.trim()

    if (!currentPrompt) {
      toast.error('Please enter a prompt')
      return
    }

    // Add user message to conversation
    const userMessage: ConversationMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: currentPrompt,
      timestamp: new Date()
    }
    setConversation(prev => [...prev, userMessage])

    // Abort any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setIsStreaming(true)
    setStreamedResponse('')

    if (!quickPrompt) {
      setPrompt('') // Clear input only if not using quick action
    }

    try {
      const context = generateContext()

      const response = await fetch('/api/ai/copilot/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          context: context,
          conversationHistory: conversation.slice(-5) // Last 5 messages for context
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error('Failed to generate response')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let accumulatedResponse = ''
      let accumulatedCode = ''
      let detectedLanguage = 'sql'
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.content) {
                accumulatedResponse += data.content
                setStreamedResponse(accumulatedResponse)
              }
              if (data.code) {
                accumulatedCode += data.code
              }
              if (data.language) {
                detectedLanguage = data.language
              }
              if (data.done) {
                // Add assistant message to conversation
                const assistantMessage: ConversationMessage = {
                  id: Date.now().toString(),
                  type: 'assistant',
                  content: accumulatedResponse,
                  timestamp: new Date(),
                  code: accumulatedCode || undefined,
                  language: detectedLanguage
                }
                setConversation(prev => [...prev, assistantMessage])
                setStreamedResponse('')

                if (accumulatedCode) {
                  toast.success('Code generated! You can insert it into a cell.')
                }
                return
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info('Generation cancelled')
      } else {
        console.error('Error generating response:', error)
        toast.error('Failed to generate response. Please try again.')
      }
    } finally {
      setIsStreaming(false)
      abortControllerRef.current = null
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleGenerate()
    }
    if (e.key === 'Escape') {
      setPrompt('')
    }
  }

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setIsStreaming(false)
    setStreamedResponse('')
    toast.info('Generation stopped')
  }

  const handleInsertCode = (code: string, language: string) => {
    if (selectedCellForInsertion) {
      onInsertCode(code, selectedCellForInsertion)
      toast.success('Code inserted into selected cell!')
    } else {
      onCreateNewCell(code, language)
      toast.success('New cell created with generated code!')
    }
  }

  // Generate cell summaries for context
  const cellSummaries: CellSummary[] = cells.map((cell, index) => ({
    id: cell.id,
    index: index + 1,
    language: cell.language,
    hasResult: !!cell.result?.data?.length,
    hasError: !!cell.error,
    summary: cell.content.slice(0, 100) + (cell.content.length > 100 ? '...' : ''),
    datasetCount: cell.selectedDatasetIds?.length || 0
  }))



  if (isCollapsed) {
    return (
      <div className="w-10 h-full bg-background border-l border-border flex flex-col items-center py-3">
        <Button
          onClick={onToggleCollapse}
          variant="ghost"
          size="sm"
          className="w-8 h-8 p-0 mb-3 hover:bg-muted"
          title="Open Data Copilot"
        >
          <PanelRightOpen className="h-4 w-4" />
        </Button>
        <div className="flex flex-col gap-2 items-center">
          <div className="w-6 h-6 rounded bg-muted flex items-center justify-center">
            <Brain className="h-3 w-3" />
          </div>
          {conversation.length > 0 && (
            <Badge variant="secondary" className="w-5 h-5 rounded-full p-0 flex items-center justify-center text-xs">
              {conversation.length}
            </Badge>
          )}
          {isStreaming && (
            <div className="w-1.5 h-1.5 rounded-full bg-foreground animate-pulse"></div>
          )}
        </div>
      </div>
    )
  }

  // Get active cells and datasets info
  const activeCells = cells.filter(cell => cell.content.trim().length > 0)
  // @ts-ignore
  const cellsWithResults = cells.filter(cell => cell.result?.data?.length > 0)
  const cellsWithErrors = cells.filter(cell => cell.error)

  return (
    <div className="w-72 h-full bg-background border-l border-border flex flex-col">
      {/* Header */}
      <div className="px-2 py-1.5 border-b border-border flex-shrink-0">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-1.5">
            <Brain className="h-3.5 w-3.5" />
            <span className="font-medium text-xs">Data Copilot</span>
          </div>
          <Button
            onClick={onToggleCollapse}
            variant="ghost"
            size="sm"
            className="w-5 h-5 p-0"
            title="Close Data Copilot"
          >
            <PanelRightClose className="h-3 w-3" />
          </Button>
        </div>

        {/* Context Status */}
        <div className="flex items-center gap-2 text-[9px] text-muted-foreground">
          <div className="flex items-center gap-1">
            <Database className="h-2.5 w-2.5" />
            <span>{selectedDatasets.length}/{datasets.length}</span>
          </div>
          <div className="flex items-center gap-1">
            <Code2 className="h-2.5 w-2.5" />
            <span>{activeCells.length}</span>
          </div>
          {cellsWithResults.length > 0 && (
            <div className="flex items-center gap-1">
              <BarChart3 className="h-2.5 w-2.5 text-green-600" />
              <span>{cellsWithResults.length}</span>
            </div>
          )}
          {cellsWithErrors.length > 0 && (
            <div className="flex items-center gap-1">
              <AlertCircle className="h-2.5 w-2.5 text-red-500" />
              <span>{cellsWithErrors.length}</span>
            </div>
          )}
          {isStreaming && (
            <div className="w-1.5 h-1.5 rounded-full bg-foreground animate-pulse"></div>
          )}
        </div>
      </div>

      {/* Resizable Content */}
      <ResizablePanelGroup direction="vertical" className="flex-1">
        {/* Workflow Pipeline Panel */}
        {activeCells.length > 0 && (
          <>
            <ResizablePanel defaultSize={30} minSize={20} maxSize={50}>
              <div className="p-2">
                <WorkflowPipeline
                  cells={cells}
                  datasets={datasets}
                  selectedDatasets={selectedDatasets}
                />
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
          </>
        )}

        {/* Chat Panel */}
        <ResizablePanel defaultSize={activeCells.length > 0 ? 70 : 100} minSize={50}>
          <div className="flex flex-col h-full">

            {/* Smart Suggestions for Data Engineers */}
            {activeCells.length > 0 && (
              <div className="px-2 py-1 border-b border-border flex-shrink-0">
                <div className="flex gap-1 flex-wrap">
                  {cellsWithErrors.length > 0 && (
                    <Button
                      onClick={() => handleGenerate("Debug the errors in my cells and suggest fixes")}
                      disabled={isStreaming}
                      size="sm"
                      variant="outline"
                      className="h-4 px-1 text-[8px]"
                    >
                      <Zap className="h-2 w-2 mr-0.5" />
                      Debug
                    </Button>
                  )}
                  {cellsWithResults.length > 0 && (
                    <Button
                      onClick={() => handleGenerate("Optimize my queries for better performance")}
                      disabled={isStreaming}
                      size="sm"
                      variant="outline"
                      className="h-4 px-1 text-[8px]"
                    >
                      <Eye className="h-2 w-2 mr-0.5" />
                      Optimize
                    </Button>
                  )}
                  {selectedDatasets.length > 1 && (
                    <Button
                      onClick={() => handleGenerate("Show me how to join these datasets efficiently")}
                      disabled={isStreaming}
                      size="sm"
                      variant="outline"
                      className="h-4 px-1 text-[8px]"
                    >
                      <Database className="h-2 w-2 mr-0.5" />
                      Join
                    </Button>
                  )}
                  <Button
                    onClick={() => handleGenerate("Analyze variable dependencies and suggest optimizations")}
                    disabled={isStreaming}
                    size="sm"
                    variant="outline"
                    className="h-4 px-1 text-[8px]"
                  >
                    <BarChart3 className="h-2 w-2 mr-0.5" />
                    Variables
                  </Button>
                  {onShowReactFlow && (
                    <Button
                      onClick={onShowReactFlow}
                      disabled={isStreaming}
                      size="sm"
                      variant="outline"
                      className="h-4 px-1 text-[8px]"
                    >
                      <Eye className="h-2 w-2 mr-0.5" />
                      Flow
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Conversation Area */}
            <div className="flex-1 flex flex-col min-h-0">
              <ScrollArea className="flex-1 p-2">
                <div className="space-y-2">
                  {conversation.length === 0 && (
                    <div className="text-center py-6">
                      <Brain className="h-6 w-6 mx-auto text-muted-foreground mb-2" />
                      <p className="text-xs text-muted-foreground mb-1">
                        AI Data Assistant
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Ask about your data, get insights, debug queries
                      </p>
                    </div>
                  )}

                  {conversation.map((message) => (
                    <div key={message.id} className={cn(
                      "flex gap-1",
                      message.type === 'user' ? 'justify-end' : 'justify-start'
                    )}>
                      <div className={cn(
                        "max-w-[90%] rounded p-1.5 text-[10px]",
                        message.type === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      )}>
                        <p className="whitespace-pre-wrap text-[10px] leading-tight">{message.content}</p>
                        {message.code && (
                          <div className="mt-1.5 space-y-1">
                            <div className="p-1 bg-muted/50 rounded border text-[9px] font-mono">
                              <div className="flex items-center justify-between mb-1">
                                <Badge variant="outline" className="text-[8px] h-3 px-1">
                                  {message.language || 'code'}
                                </Badge>
                                <Button
                                  onClick={() => navigator.clipboard.writeText(message.code!)}
                                  size="sm"
                                  variant="ghost"
                                  className="h-3 w-3 p-0"
                                >
                                  <Copy className="h-2 w-2" />
                                </Button>
                              </div>
                              <ScrollArea className="max-h-20">
                                <pre className="text-[9px] whitespace-pre-wrap leading-tight">{message.code}</pre>
                              </ScrollArea>
                            </div>

                            <div className="flex gap-1">
                              <Button
                                onClick={() => handleInsertCode(message.code!, message.language || 'sql')}
                                size="sm"
                                variant="outline"
                                className="h-4 px-1 text-[8px]"
                              >
                                <Plus className="h-2 w-2 mr-0.5" />
                                New
                              </Button>

                              {cells.length > 0 && (
                                <Select onValueChange={(cellId) => onInsertCode(message.code!, cellId)}>
                                  <SelectTrigger className="h-4 w-14 text-[8px]">
                                    <SelectValue placeholder="Insert" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {cells.map((cell, index) => (
                                      <SelectItem key={cell.id} value={cell.id} className="text-[8px]">
                                        Cell {index + 1}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              )}
                            </div>
                          </div>
                        )}
                        <div className="mt-1 text-[8px] opacity-50">
                          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>
                    </div>
                  ))}

                  {isStreaming && streamedResponse && (
                    <div className="flex gap-1 justify-start">
                      <div className="max-w-[90%] rounded p-1.5 text-[10px] bg-muted">
                        <div className="flex items-center gap-1 mb-1">
                          <div className="w-2 h-2 border border-muted-foreground border-t-foreground rounded-full animate-spin"></div>
                          <span className="text-[9px]">Analyzing...</span>
                        </div>
                        <p className="whitespace-pre-wrap text-[10px] leading-tight">{streamedResponse}</p>
                      </div>
                    </div>
                  )}

                  <div ref={conversationEndRef} />
                </div>
              </ScrollArea>

              {/* Input Area - Moved to bottom */}
              <div className="p-1.5 border-t border-border flex-shrink-0">
                <div className="relative">
                  <Textarea
                    ref={inputRef}
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder="Ask about data, debug queries, optimize performance..."
                    className="min-h-[40px] resize-none pr-7 text-[10px] leading-tight"
                    disabled={isStreaming}
                  />
                  <div className="absolute bottom-1 right-1">
                    {isStreaming ? (
                      <Button
                        onClick={handleStop}
                        size="sm"
                        variant="ghost"
                        className="h-4 w-4 p-0"
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    ) : (
                      <Button
                        onClick={() => handleGenerate()}
                        disabled={!prompt.trim()}
                        size="sm"
                        variant="ghost"
                        className="h-4 w-4 p-0"
                      >
                        <Send className="h-2.5 w-2.5" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}
