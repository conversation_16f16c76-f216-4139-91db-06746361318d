"use client"

import React, { useEffect, useState, useRef } from 'react'
import { GraphicWalker } from '@kanaries/graphic-walker'
import '@kanaries/graphic-walker/dist/style.css'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Maximize2, Minimize2 } from "lucide-react"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"

interface GraphicWalkerProps {
  data: any[]
  onBack?: () => void
  title?: string
}

// Add this to check if we're in a browser environment
const isBrowser = typeof window !== 'undefined'

export function GraphicWalkerVisualization({ data, onBack, title = "Visual Data Explorer" }: GraphicWalkerProps) {
  const [fields, setFields] = useState<any[]>([])
  const [isFullscreen, setIsFullscreen] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Convert the data to the format expected by GraphicWalker
  useEffect(() => {
    if (data && data.length > 0) {
      // Extract field definitions from the data
      const fieldDefinitions = Object.keys(data[0]).map(key => {
        // Determine field type based on the first non-null value
        const sample = data.find(item => item[key] !== null && item[key] !== undefined)
        const value = sample ? sample[key] : null
        const type = typeof value === 'number' ? 'quantitative' : 
                    (typeof value === 'boolean' ? 'nominal' : 
                     (value instanceof Date ? 'temporal' : 'nominal'))
        
        return {
          fid: key,
          name: key,
          semanticType: type,
          analyticType: type === 'quantitative' ? 'measure' : 'dimension'
        }
      })
      
      setFields(fieldDefinitions)
    }
  }, [data])

  // Modify any useEffect that accesses DOM
  useEffect(() => {
    if (!isBrowser) return;
    
    // Trigger resize event when component mounts
    const timer = setTimeout(() => {
      if (containerRef.current) {
        window.dispatchEvent(new Event('resize'))
      }
    }, 100)
    
    return () => clearTimeout(timer)
  }, []);

  // If no data, show placeholder
  if (!data || data.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-8 h-[400px]">
          <p className="text-muted-foreground">No data available for visualization</p>
        </CardContent>
      </Card>
    )
  }

  // Determine if dark mode is active - safely
  const isDarkMode = isBrowser && typeof document !== 'undefined' 
    ? document.documentElement.classList.contains('dark')
    : false;

  // Render the component
  const visualization = (
    <Card className={`w-full ${isFullscreen ? 'h-screen fixed inset-0 z-50 overflow-auto' : ''}`}>
      <CardHeader className="py-2 border-b flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
          )}
          <CardTitle className="text-base">{title}</CardTitle>
        </div>
        
        <Button variant="ghost" size="sm" onClick={() => {
          setIsFullscreen(!isFullscreen)
          // Force resize after toggling fullscreen
          setTimeout(() => window.dispatchEvent(new Event('resize')), 100)
        }}>
          {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
        </Button>
      </CardHeader>
      <CardContent 
        className={isFullscreen ? 'h-[calc(100vh-60px)]' : 'h-[500px]'}
        ref={containerRef}
      >
        <GraphicWalker 
          data={data}
          fields={fields}
          i18nLang="en"
          dark={isDarkMode ? "dark" : "light"}
        />
      </CardContent>
    </Card>
  )

  return visualization
}

// Small button that can be used to launch GraphicWalker in a dialog
export function GraphicWalkerLauncher({ data }: { data: any[] }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1">
          <Maximize2 className="h-3.5 w-3.5" />
          Visual Explorer
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[90vw] w-[90vw] max-h-[90vh] h-[90vh]">
        <GraphicWalkerVisualization data={data} title="Visual Data Explorer" />
      </DialogContent>
    </Dialog>
  )
} 