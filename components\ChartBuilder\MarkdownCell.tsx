'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, Square, Palette } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { useTheme } from 'next-themes'
import { cn } from "@/lib/utils"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover"

interface MarkdownCellProps {
  content: string;
  onContentChange?: (value: string) => void;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
}

// Add a list of common colors
const colorOptions = [
  { name: 'Default', value: null },
  { name: 'Red', value: 'text-red-500' },
  { name: 'Blue', value: 'text-blue-500' },
  { name: 'Green', value: 'text-green-500' },
  { name: 'Yellow', value: 'text-yellow-500' },
  { name: 'Purple', value: 'text-purple-500' },
  { name: 'Pink', value: 'text-pink-500' },
  { name: 'Orange', value: 'text-orange-500' },
  { name: 'Teal', value: 'text-teal-500' },
  { name: 'Gray', value: 'text-gray-500' },
]

// Toolbar for markdown editor
function MarkdownToolbar({ 
  onAction,
  setIsToolbarAction
}: { 
  onAction: (action: string, value?: string) => void;
  setIsToolbarAction: (value: boolean) => void;
}) {
  return (
    <div className="flex flex-wrap gap-1 p-1 bg-muted rounded-md mb-2">
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('h1')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <span className="font-bold text-xs">H1</span>
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('h2')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <span className="font-bold text-xs">H2</span>
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('h3')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <span className="font-bold text-xs">H3</span>
      </Button>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('bold')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Bold className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('italic')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Italic className="h-3 w-3" />
      </Button>
      
      {/* Color Picker */}
      <Popover>
        <PopoverTrigger asChild onMouseDown={(e) => e.preventDefault()}>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 w-6 p-0"
            type="button"
          >
            <Palette className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-2" onMouseDown={(e) => {
          e.preventDefault();
          setIsToolbarAction(true); // Set flag to prevent blur
        }}>
          <div className="mb-2 text-xs font-medium">Text Color</div>
          <div className="grid grid-cols-5 gap-2">
            {colorOptions.map((color) => (
              <button
                key={color.name}
                className={cn(
                  "h-8 rounded border flex flex-col items-center justify-center",
                  color.value || "bg-background",
                  "hover:opacity-80 transition-opacity"
                )}
                title={color.name}
                onMouseDown={(e) => {
                  e.preventDefault(); // Prevent blur
                  setIsToolbarAction(true); // Set flag for preventing blur
                }}
                onClick={() => {
                  setIsToolbarAction(true); // Set flag again to be sure
                  onAction('color', color.value || 'default');
                }}
              >
                {color.value ? (
                  <span className={cn("text-[10px]", color.value)}>{color.name}</span>
                ) : (
                  <span className="text-[10px]">Default</span>
                )}
              </button>
            ))}
          </div>
        </PopoverContent>
      </Popover>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('bulletList')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <List className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('numberList')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <ListOrdered className="h-3 w-3" />
      </Button>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('code')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Code className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('codeBlock')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Square className="h-3 w-3" />
      </Button>
      
      <Separator orientation="vertical" className="h-4 mx-1" />
      
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('link')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <Link className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('image')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <ImageIcon className="h-3 w-3" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 w-6 p-0" 
        onClick={() => onAction('table')}
        type="button"
        onMouseDown={(e) => e.preventDefault()} // Prevent blur
      >
        <TableIcon className="h-3 w-3" />
      </Button>
      
      {/* Exit editor button */}
      <div className="ml-auto">
        <Button 
          variant="outline" 
          size="sm" 
          className="h-6 px-2 text-xs"
          onClick={() => onAction('exit')}
          type="button"
        >
          Done
        </Button>
      </div>
    </div>
  );
}

export function MarkdownCell({ 
  content, 
  onContentChange, 
  isEditing, 
  setIsEditing 
}: MarkdownCellProps) {
  const { theme } = useTheme()
  const [editValue, setEditValue] = useState(content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [isToolbarAction, setIsToolbarAction] = useState(false);

  // Update editValue when content changes from parent
  useEffect(() => {
    setEditValue(content);
  }, [content]);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      // Auto-adjust height based on content
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditing]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditValue(e.target.value);
    
    // Auto-adjust height
    e.target.style.height = "auto";
    e.target.style.height = `${e.target.scrollHeight}px`;

    if (onContentChange) {
      onContentChange(e.target.value);
    }
  };

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Use a small timeout to allow the toolbar action flag to be set
    setTimeout(() => {
      // Check if the related target is inside the toolbar
      if (toolbarRef.current && toolbarRef.current.contains(e.relatedTarget as Node)) {
        // Don't close the editor if clicking within the toolbar
        return;
      }
      
      // Check if clicking a popover
      const isPopoverClick = document.querySelector('[role="dialog"]')?.contains(e.relatedTarget as Node);
      if (isPopoverClick) {
        // Don't close the editor if clicking within a popover
        return;
      }
      
      // Only exit edit mode if we're not processing a toolbar action
      if (!isToolbarAction) {
        setIsEditing(false);
      }
      
      // Reset the toolbar action flag
      setIsToolbarAction(false);
    }, 10);
  };

  // Submit on Ctrl+Enter or Cmd+Enter
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      setIsEditing(false);
    }
  };

  // Handle markdown formatting actions
  const handleAction = (type: string, value?: string) => {
    // Special case for exit
    if (type === 'exit') {
      setIsEditing(false);
      return;
    }
    
    // Set toolbar action flag to prevent blur handling
    setIsToolbarAction(true);
    
    if (!textareaRef.current) return;
    
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editValue.substring(start, end);
    let newText = '';
    
    switch (type) {
      case 'bold':
        newText = `**${selectedText}**`;
        break;
      case 'italic':
        newText = `*${selectedText}*`;
        break;
      case 'h1':
        newText = `# ${selectedText}`;
        break;
      case 'h2':
        newText = `## ${selectedText}`;
        break;
      case 'h3':
        newText = `### ${selectedText}`;
        break;
      case 'color':
        // For color, we'll use HTML span with Tailwind classes
        if (value === 'default') {
          // If it's the default color, just use the text without wrapping
          newText = selectedText;
        } else {
          // Wrap with a span using tailwind color class
          newText = `<span class="${value}">${selectedText}</span>`;
        }
        break;
      case 'bulletList':
        newText = `- ${selectedText}`;
        break;
      case 'numberList':
        newText = `1. ${selectedText}`;
        break;
      case 'code':
        newText = `\`${selectedText}\``;
        break;
      case 'codeBlock':
        newText = `\`\`\`\n${selectedText}\n\`\`\``;
        break;
      case 'link':
        newText = `[${selectedText}](url)`;
        break;
      case 'image':
        newText = `![${selectedText}](image-url)`;
        break;
      case 'table':
        newText = `| Column 1 | Column 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |`;
        break;
      default:
        return;
    }
    
    const updatedValue = editValue.substring(0, start) + newText + editValue.substring(end);
    setEditValue(updatedValue);
    
    if (onContentChange) {
      onContentChange(updatedValue);
    }
    
    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      const newPosition = start + newText.length;
      textarea.setSelectionRange(newPosition, newPosition);
    }, 0);
  };

  // Custom components for ReactMarkdown to handle HTML
  const customComponents = {
    span: ({ node, className, children, ...props }: any) => {
      // Apply both markdown-generated classes and our own Tailwind classes
      return <span className={className} {...props}>{children}</span>;
    },
    a: ({ node, href, children, ...props }: any) => {
      return (
        <a 
          href={href} 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-primary hover:underline"
          {...props}
        >
          {children}
        </a>
      );
    },
    code: ({ node, inline, className, children, ...props }: any) => {
      if (inline) {
        return (
          <code 
            className="bg-muted text-muted-foreground rounded-sm px-1 py-0.5 text-sm"
            {...props}
          >
            {children}
          </code>
        );
      }
      return (
        <code 
          className={cn("block bg-muted p-2 rounded-md text-sm", className)}
          {...props}
        >
          {children}
        </code>
      );
    },
    pre: ({ children, ...props }: any) => {
      return (
        <pre className="bg-muted rounded-md p-0 overflow-x-auto" {...props}>
          {children}
        </pre>
      );
    }
  };

  if (isEditing) {
    return (
      <div className="w-full">
        <div ref={toolbarRef}>
          <MarkdownToolbar
            onAction={handleAction}
            setIsToolbarAction={setIsToolbarAction}
          />
        </div>
        <textarea
          ref={textareaRef}
          value={editValue}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className="w-full resize-none p-2 bg-transparent border-none outline-none font-sans text-sm min-h-[100px] focus:ring-0"
          placeholder="Type here... (supports both plain text and markdown)"
          style={{ fontFamily: 'inherit' }}
        />
      </div>
    );
  }

  // Function to detect if content is likely markdown
  const isLikelyMarkdown = (text: string) => {
    if (!text) return false;
    const markdownPatterns = [
      /^#{1,6}\s/, // Headers
      /\*\*.*\*\*/, // Bold
      /\*.*\*/, // Italic
      /\[.*\]\(.*\)/, // Links
      /^[-*+]\s/, // Lists
      /^>\s/, // Blockquotes
      /`.*`/, // Inline code
      /```/, // Code blocks
    ];
    return markdownPatterns.some(pattern => pattern.test(text));
  };

  const shouldRenderAsMarkdown = isLikelyMarkdown(content);

  return (
    <div
      onDoubleClick={handleDoubleClick}
      className={cn(
        "cursor-text p-2 min-h-[40px] border-none bg-transparent",
        shouldRenderAsMarkdown ? "prose prose-sm max-w-none" : "",
        shouldRenderAsMarkdown ? "prose-headings:font-bold prose-headings:text-foreground" : "",
        shouldRenderAsMarkdown ? "prose-p:text-foreground prose-p:leading-relaxed" : "",
        shouldRenderAsMarkdown ? "prose-a:text-primary" : "",
        shouldRenderAsMarkdown ? "prose-blockquote:text-muted-foreground prose-blockquote:border-primary" : "",
        shouldRenderAsMarkdown ? "prose-ul:text-foreground prose-ol:text-foreground" : "",
        shouldRenderAsMarkdown ? "prose-code:text-muted-foreground prose-code:bg-muted prose-code:rounded-sm prose-code:px-1 prose-code:py-0.5" : "",
        shouldRenderAsMarkdown ? "prose-img:rounded-md" : "",
        shouldRenderAsMarkdown && theme === 'dark' ? "dark:prose-invert" : ""
      )}
    >
      {shouldRenderAsMarkdown ? (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={customComponents}
          rehypePlugins={[rehypeRaw]}
          className="break-words"
        >
          {content || '*Double-click to edit*'}
        </ReactMarkdown>
      ) : (
        <div className="text-foreground leading-relaxed whitespace-pre-wrap font-sans">
          {content || <span className="text-muted-foreground italic">Double-click to edit</span>}
        </div>
      )}
    </div>
  );
} 