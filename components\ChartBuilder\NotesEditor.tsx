'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { MessageSquare, Save, Edit2, Reply, Trash2 } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { useUser } from "@clerk/nextjs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { formatDistanceToNow } from 'date-fns'
import { cn } from "@/lib/utils"

interface Comment {
  id: string;
  content: string;
  author: {
    name: string;
    image?: string;
  };
  createdAt: Date;
  replies?: Comment[];
  isEditing?: boolean;
}

interface NotesEditorProps {
  notes?: string;  // Make notes optional
  onSave: (notes: string) => void;
}

export function NotesEditor({ notes = '[]', onSave }: NotesEditorProps) {  // Provide default value
  const { user } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const [comments, setComments] = useState<Comment[]>(() => {
    try {
      // Parse notes string to get comments
      const parsedComments = JSON.parse(notes);
      
      // Ensure dates are properly instantiated
      return Array.isArray(parsedComments) ? parsedComments.map(comment => ({
        ...comment,
        createdAt: new Date(comment.createdAt),
        replies: Array.isArray(comment.replies) 
          ? comment.replies.map((reply: any) => ({
              ...reply,
              createdAt: new Date(reply.createdAt)
            }))
          : []
      })) : [];
    } catch (error) {
      console.error('Failed to parse notes:', error);
      return [];
    }
  });

  // Get comment count
  const commentCount = comments.length + comments.reduce((acc, comment) => 
    acc + (comment.replies?.length || 0), 0
  );

  const [value, setValue] = useState('')  // Remove initial value from notes
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')

  // Save comments whenever they change
  useEffect(() => {
    // Skip the initial render to avoid unnecessary saves
    const commentsJson = JSON.stringify(comments.map(comment => ({
      ...comment,
      createdAt: comment.createdAt.toISOString(),
      replies: Array.isArray(comment.replies) 
        ? comment.replies.map(reply => ({
            ...reply,
            createdAt: reply.createdAt.toISOString()
          }))
        : []
    })));
    
    // Only save if the comments have actually changed
    if (commentsJson !== notes) {
      try {
        // Update notes with new comments
        onSave(commentsJson);
        // Don't show toast here - we'll only show toast on explicit user actions
      } catch (error) {
        console.error('Failed to save notes:', error);
      }
    }
  }, [comments, onSave, notes]);

  // Close modal when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSave = () => {
    // Save already happens in the useEffect
    setIsOpen(false)
    toast.success('Notes saved successfully')
  }

  const handleAddComment = () => {
    if (!value.trim()) return;
    
    const newComment: Comment = {
      id: Date.now().toString(),
      content: value,
      author: {
        name: user?.fullName || 'Anonymous',
        image: user?.imageUrl
      },
      createdAt: new Date(),
      replies: []
    }
    
    setComments(prev => [...prev, newComment])
    setValue('')
  }

  const handleAddReply = (parentId: string) => {
    if (!replyContent.trim()) return;
    
    const reply: Comment = {
      id: Date.now().toString(),
      content: replyContent,
      author: {
        name: user?.fullName || 'Anonymous',
        image: user?.imageUrl
      },
      createdAt: new Date()
    }
    
    setComments(prev => prev.map(comment => 
      comment.id === parentId 
        ? { ...comment, replies: [...(comment.replies || []), reply] }
        : comment
    ))
    
    setReplyingTo(null)
    setReplyContent('')
  }

  const handleEdit = (commentId: string, newContent: string) => {
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, content: newContent, isEditing: false }
        : comment
    ))
  }

  const handleDelete = (commentId: string) => {
    setComments(prev => prev.filter(comment => comment.id !== commentId))
  }

  return (
    <div className="relative inline-block">
      {/* Icon with count - no modal trigger on hover */}
      <Button 
        variant="ghost" 
        size="sm"
        className="h-5 w-5 p-0"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="relative">
          {commentCount > 0 && (
            <span className="absolute -top-2 -right-2 text-[10px] font-medium bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
              {commentCount}
            </span>
          )}
          <MessageSquare className="h-3 w-3" />
        </div>
      </Button>

      {/* Modal - Only show when explicitly opened via click */}
      {isOpen && (
        <div
          ref={modalRef}
          className={cn(
            "fixed bg-background",
            "w-[250px] border rounded-md shadow-lg",
            "z-[9999] overflow-hidden"
          )}
          style={{ 
            boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            top: "50%", 
            left: "50%", 
            transform: "translate(-50%, -50%)"
          }}
        >
          <div className="p-2 space-y-2 bg-background z-10">
            <div className="sticky top-0 bg-background pb-1 border-b">
              <h3 className="font-medium text-xs">Comments</h3>
            </div>

            {/* Comments List */}
            <div className="space-y-2 max-h-[200px] overflow-y-auto">
              {comments.length === 0 ? (
                <div className="text-center text-xs text-muted-foreground py-4">
                  No comments yet. Add one below.
                </div>
              ) : (
                comments.map(comment => (
                  <div key={comment.id} className="space-y-1">
                    <div className="flex items-start gap-1 bg-muted/50 p-1 rounded-md">
                      <Avatar className="h-5 w-5">
                      <AvatarImage src={comment.author.image} />
                      <AvatarFallback>{comment.author.name[0]}</AvatarFallback>
                    </Avatar>
                      <div className="flex-1 space-y-0.5">
                      <div className="flex items-center justify-between">
                          <span className="font-medium text-[10px]">{comment.author.name}</span>
                          <span className="text-[8px] text-muted-foreground">
                          {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                        </span>
                      </div>
                      {comment.isEditing ? (
                        <Textarea
                          defaultValue={comment.content}
                          onBlur={e => handleEdit(comment.id, e.target.value)}
                          autoFocus
                            className="text-[10px] min-h-[40px]"
                        />
                      ) : (
                          <p className="text-[10px]">{comment.content}</p>
                      )}
                        <div className="flex items-center gap-0.5 mt-0.5">
                        <Button 
                          variant="ghost" 
                          size="sm"
                            className="h-5 text-[10px] px-0.5"
                          onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                        >
                            <Reply className="h-2 w-2 mr-0.5" />
                          Reply
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                            className="h-5 text-[10px] px-0.5"
                          onClick={() => setComments(prev => prev.map(c => 
                            c.id === comment.id ? { ...c, isEditing: true } : c
                          ))}
                        >
                            <Edit2 className="h-2 w-2 mr-0.5" />
                          Edit
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                            className="h-5 text-[10px] px-0.5"
                          onClick={() => handleDelete(comment.id)}
                        >
                            <Trash2 className="h-2 w-2 mr-0.5" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Replies */}
                  {comment.replies?.map(reply => (
                      <div key={reply.id} className="ml-4 flex items-start gap-1 bg-muted/30 p-1 rounded-md">
                        <Avatar className="h-4 w-4">
                        <AvatarImage src={reply.author.image} />
                          <AvatarFallback className="text-[8px]">{reply.author.name[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                            <span className="font-medium text-[9px]">{reply.author.name}</span>
                            <span className="text-[7px] text-muted-foreground">
                            {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
                          </span>
                          </div>
                          <p className="text-[9px] mt-0.5">{reply.content}</p>
                      </div>
                    </div>
                  ))}
                  
                  {/* Reply Input */}
                  {replyingTo === comment.id && (
                      <div className="ml-4 space-y-1">
                      <Textarea
                        placeholder="Write a reply..."
                        value={replyContent}
                        onChange={e => setReplyContent(e.target.value)}
                          className="text-[9px] min-h-[30px]"
                      />
                        <div className="flex justify-end gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm"
                            className="h-5 text-[8px] px-1"
                          onClick={() => setReplyingTo(null)}
                        >
                          Cancel
                        </Button>
                        <Button 
                          size="sm"
                            className="h-5 text-[8px] px-1"
                          onClick={() => handleAddReply(comment.id)}
                        >
                          Reply
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                ))
              )}
            </div>

            {/* New Comment Input */}
            <div className="sticky bottom-0 bg-background pt-1 border-t">
              <Textarea
                placeholder="Add a comment..."
                value={value}
                onChange={e => setValue(e.target.value)}
                className="min-h-[40px] resize-none text-[10px]"
              />
              <div className="flex justify-end gap-1 mt-1">
                <Button 
                  onClick={() => handleAddComment()}
                  size="sm" 
                  className="h-6 text-[10px] px-2"
                >
                  Add Comment
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}