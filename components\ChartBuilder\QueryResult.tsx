'use client'

import { useState, useMemo, useRef, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { BarChart3, AlertCircle, TableIcon, ImageIcon, LineChart, PieChart, AreaChart, Maximize2, PlusCircle } from "lucide-react"
import { NotesEditor } from './NotesEditor'
import { ChartVisualizer } from './ChartVisualizer'
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { GraphicWalkerVisualization } from './GraphicWalker'
import { toast } from "sonner"
import { RichDataTable } from './RichDataTable'

// Default chart colors array
const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

interface QueryResultProps {
  data?: any[]
  error?: string
  output?: string
  plots?: string[]
  isSuccess?: boolean
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotIndex: number, plotId?: string) => void
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number; // in milliseconds
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  viewMode?: 'table' | 'chart' | 'output' | 'plots' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'plots' | 'graphicwalker') => void
  cellId?: string // Add cellId prop to identify this specific result
  language?: string; // Add language prop to identify the cell language

  // Cell control props
  onMoveUp?: (id: string) => void
  onMoveDown?: (id: string) => void
  notes?: string
  onUpdateNotes?: (id: string, notes: string) => void
}

// Keep track of chart configurations in memory using this simple cache
const chartConfigCache: Record<string, any> = {};

export function QueryResult({
  data,
  error,
  output,
  plots,
  isSuccess,
  showGraphicWalker = false,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  errorDetails,
  executionTime,
  chartType: propChartType = 'bar',
  onChartTypeChange,
  viewMode: propViewMode = 'table',
  onViewModeChange,
  cellId = 'default',
  language = 'sql',
  // Cell control props
  onMoveUp,
  onMoveDown,
  notes = '[]',
  onUpdateNotes
}: QueryResultProps) {
  // Track rendered state to prevent unnecessary updates
  const hasRenderedChart = useRef(false);

  // Use the props as initial values, but check cache first
  const [viewMode, setViewMode] = useState<'table' | 'chart' | 'output' | 'plots' | 'graphicwalker'>(propViewMode);
  const [chartType, setChartType] = useState<'line' | 'bar' | 'pie' | 'area'>(() => {
    return chartConfigCache[cellId]?.type || propChartType;
  });

  const [chartConfig, setChartConfig] = useState<any>(() => {
    return chartConfigCache[cellId] || null;
  });

  // Simple cache-based handler for chart config updates
  const handleChartConfigUpdate = (newConfig: any) => {
    setChartConfig(newConfig);
    // Store in memory cache instead of URL
    chartConfigCache[cellId] = newConfig;
  };

  // Handle chart type change and update cache
  const handleChartTypeChange = (type: 'line' | 'bar' | 'pie' | 'area') => {
    setChartType(type);

    // Also update the config in cache
    const updatedConfig = { ...chartConfig, type };
    chartConfigCache[cellId] = updatedConfig;
    setChartConfig(updatedConfig);

    if (onChartTypeChange) {
      onChartTypeChange(type);
    }
  };

  // Process data for charting
  const processedData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    try {
      return data.map((row, index) => {
        if (!row || typeof row !== 'object') {
          return { _index: index };
        }

        const newRow = { ...row, _index: index };

        // Only convert boolean values to numbers for charting
        Object.keys(row).forEach(key => {
          if (typeof row[key] === 'boolean') {
            newRow[`${key}_value`] = row[key] ? 1 : 0;
          }
        });

        return newRow;
      });
    } catch (error) {
      console.error('Error processing data for chart:', error);
      return [];
    }
  }, [data]);

  // Find suitable numeric columns
  const numericColumns = useMemo(() => {
    if (!processedData || processedData.length === 0) return ['_index'];

    const firstRow = processedData[0];
    const numericCols = Object.keys(firstRow).filter(key => {
      const value = firstRow[key];
      // Check if value is already a number or can be converted to one
      return typeof value === 'number' ||
        (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '');
    });

    if (!numericCols.includes('_index')) {
      numericCols.push('_index');
    }

    return numericCols;
  }, [processedData]);

  // Find suitable category columns
  const categoryColumns = useMemo(() => {
    if (!processedData || processedData.length === 0) return [];

    const firstRow = processedData[0];
    return Object.keys(firstRow).filter(key =>
      typeof firstRow[key] === 'string' &&
      processedData.length < 100 // Limit categories for performance
    );
  }, [processedData]);

  // Get columns for chart configuration
  const columns = useMemo(() =>
    processedData.length > 0 ? Object.keys(processedData[0]) : [],
    [processedData]
  );

  // Simple chart config with default values
  const defaultChartConfig = useMemo(() => ({
    xAxis: categoryColumns[0] || columns[0] || '_index',
    yAxis: numericColumns[0] || '_index',
    title: 'Data Visualization',
    description: 'Chart visualization',
    showLegend: true,
    showLabels: false,
    showGrid: true,
  }), [categoryColumns, numericColumns, columns]);

  // Update the parent when viewMode changes
  const handleViewModeChange = (mode: 'table' | 'chart' | 'output' | 'plots' | 'graphicwalker') => {
    if (viewMode !== mode) {
      setViewMode(mode);
      if (onViewModeChange) {
        onViewModeChange(mode);
      }

      // Reset chart rendering flag when switching away from chart
      if (mode !== 'chart') {
        hasRenderedChart.current = false;
      }
    }
  };

  // Check if data is available
  const hasData = useMemo(() =>
    data && data.length > 0,
    [data]
  );

  // Format cell value for display
  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) return '-'
    if (typeof value === 'object') return JSON.stringify(value)
    if (typeof value === 'number') return value.toLocaleString()

    // Check if it's a base64 image and show a placeholder instead
    if (typeof value === 'string') {
      if (value.startsWith('data:image') ||
          (value.includes('base64,')) ||
          (/^[A-Za-z0-9+/=]+$/.test(value.substring(0, 20)) && value.length > 100)) {
        return '[Image Data]'
      }
    }

    return String(value)
  }

  // We no longer automatically store results in the dashboard store
  // This prevents duplicate items when saving to dashboard
  // Users must explicitly click "Save to Dashboard" button

  // Handle saving chart to dashboard
  const handleSaveChart = () => {
    if (onSaveChart && processedData.length > 0) {
      // Get the current complete chart configuration
      const currentConfig = chartConfig || defaultChartConfig;

      // Create a complete config object with all properties
      const completeConfig = {
        // Base properties
        xAxis: currentConfig.xAxis || defaultChartConfig.xAxis,
        yAxis: currentConfig.yAxis || defaultChartConfig.yAxis,
        title: currentConfig.title || defaultChartConfig.title,
        description: currentConfig.description || defaultChartConfig.description,

        // Visual properties
        showLegend: currentConfig.showLegend !== undefined ? currentConfig.showLegend : defaultChartConfig.showLegend,
        showLabels: currentConfig.showLabels !== undefined ? currentConfig.showLabels : defaultChartConfig.showLabels,
        showGrid: currentConfig.showGrid !== undefined ? currentConfig.showGrid : defaultChartConfig.showGrid,
        type: chartType,
        color: currentConfig.color || COLORS[0],

        // Advanced properties - preserve all configuration
        aggregation: currentConfig.aggregation,
        groupBy: currentConfig.groupBy,
        timeScale: currentConfig.timeScale,
        customLabel: currentConfig.customLabel,
        enableZoom: currentConfig.enableZoom !== undefined ? currentConfig.enableZoom : true,
        multiSeries: currentConfig.multiSeries,
        fontSize: currentConfig.fontSize,

        // Preserve any other properties that might be in the config
        ...currentConfig
      };

      // Create a chart object with a unique ID
      const chartId = `chart-${cellId}-${Date.now()}`;

      // Create the saved chart with complete configuration
      const savedChart = {
        id: chartId,
        type: 'chart' as const,
        title: completeConfig.title || 'Untitled Chart',
        description: completeConfig.description || 'Chart visualization',
        chartType: chartType,
        data: processedData,
        config: completeConfig,
        gridColumn: 0,
        gridRow: 0,
        width: 6,
        height: 4,
        createdAt: new Date()
      };

      // Call the parent callback which will add the chart to the Zustand store
      // This prevents duplicate charts from being created
      onSaveChart(processedData, completeConfig, chartType, chartId);

      toast.success("Chart saved to dashboard");
    } else {
      if (!processedData.length) {
        toast.error("No data available to save");
      } else if (!onSaveChart) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle saving table to dashboard
  const handleSaveTable = () => {
    if (onSaveTable && data && data.length > 0) {
      // Create a table object with a unique ID
      const tableId = `table-${cellId}-${Date.now()}`;

      // Get columns from the first row of data
      const columns = data.length > 0 ? Object.keys(data[0]) : [];

      // Call the parent callback to save the table
      onSaveTable(data, columns, tableId);

      toast.success("Table saved to dashboard");
    } else {
      if (!data || data.length === 0) {
        toast.error("No table data available to save");
      } else if (!onSaveTable) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle saving plot to dashboard
  const handleSavePlot = (plotUrl: string, plotIndex: number) => {
    if (onSavePlot && plotUrl) {
      // Create a plot object with a unique ID
      const plotId = `plot-${cellId}-${plotIndex}-${Date.now()}`;

      // Call the parent callback to save the plot
      onSavePlot(plotUrl, plotIndex, plotId);

      toast.success(`Plot ${plotIndex + 1} saved to dashboard`);
    } else {
      if (!plotUrl) {
        toast.error("Invalid plot data");
      } else if (!onSavePlot) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Render empty state if no data
  if (!hasData && !output && !plots?.length && !error) {
    return null;
  }

  return (
    <Card className={cn(
      "w-full overflow-hidden group relative shadow-none border-l-2 border-l-muted border-t-0 border-r-0 border-b-0 rounded-none",
      error ? "border-l-red-500" : isSuccess ? "border-l-green-500" : "border-l-muted"
    )}>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between px-2 py-1 border-b bg-muted/30 gap-1">
        <span className="text-xs text-muted-foreground">
          {data && data.length > 0 ? `${data.length} rows` : ''}
          {output ? ' | Has output' : ''}
          {plots && plots.length > 0 ? ` | ${plots.length} plot${plots.length > 1 ? 's' : ''}` : ''}
          {executionTime ? ` | ${(executionTime / 1000).toFixed(2)}s` : ''}
        </span>

        <div className="flex flex-wrap gap-1">
          {error && (
            <span className="text-sm text-red-500 font-medium">
              Execution Failed
            </span>
          )}
          {isSuccess && (
            <span className="text-sm text-green-500 font-medium">
              Execution Successful
            </span>
          )}



          {/* Table button with Notes icon next to it */}
          <div className="flex items-center gap-1">
            {hasData && (
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                className="h-6 px-1.5 text-[10px]"
                onClick={() => handleViewModeChange('table')}
              >
                <TableIcon className="h-3 w-3 mr-1" />
                Table
              </Button>
            )}

            {/* Notes Button */}
            {onUpdateNotes && (
              <div className="h-6 flex items-center">
                <NotesEditor
                  notes={notes}
                  onSave={(updatedNotes) => onUpdateNotes(cellId, updatedNotes)}
                />
              </div>
            )}
          </div>

          {/* Chart button */}
          {hasData && (
            <Button
              variant={viewMode === 'chart' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('chart')}
              data-chart-button
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Chart
            </Button>
          )}

          {/* Visual Explorer button */}
          {hasData && (
            <Button
              variant={viewMode === 'graphicwalker' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('graphicwalker')}
            >
              <Maximize2 className="h-3 w-3 mr-1" />
              Visual Explorer
            </Button>
          )}

          {/* Output View button */}
          {output && (
            <Button
              variant={viewMode === 'output' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('output')}
            >
              <ImageIcon className="h-3 w-3 mr-1" />
              Output
            </Button>
          )}

          {/* Plots View button - Always show for Python cells */}
          {(plots && plots.length > 0 || (language === 'python')) && (
            <Button
              variant={viewMode === 'plots' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => {
                // Log plots data for debugging
                console.log("Plots data:", plots);
                handleViewModeChange('plots');
              }}
            >
              <ImageIcon className="h-3 w-3 mr-1" />
              Plots {plots?.length ? `(${plots.length})` : ''}
            </Button>
          )}
        </div>
      </div>

      {/* Chart Type Selector - Only visible when chart view is active */}
      {viewMode === 'chart' && (
        <div className="flex flex-wrap items-center gap-1 p-0.5 border-b bg-muted/20">
          {/* <div className="flex items-center gap-1">
            <Button
              variant={chartType === 'bar' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('bar')}
            >
              <BarChart3 className="h-3 w-3" />
            </Button>

            <Button
              variant={chartType === 'line' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('line')}
            >
              <LineChart className="h-3 w-3" />
            </Button>

            <Button
              variant={chartType === 'area' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('area')}
            >
              <AreaChart className="h-3 w-3" />
            </Button>

            <Button
              variant={chartType === 'pie' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('pie')}
            >
              <PieChart className="h-3 w-3" />
            </Button>
          </div> */}

          {/* Add Save to Dashboard button */}
          {onSaveChart && (
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-1.5 ml-auto text-[10px]"
              onClick={handleSaveChart}
            >
              Save to Dashboard
            </Button>
          )}
        </div>
      )}

      {/* Content Area */}
      <div className="relative">
        {/* Error View */}
        {error && (
          <div className="p-1">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="ml-2">
                <div className="font-medium">{error}</div>
                {errorDetails && (
                  <div className="mt-2 space-y-2">
                    {errorDetails.code && (
                      <div className="text-xs">
                        <span className="font-semibold">Error code:</span> {errorDetails.code}
                      </div>
                    )}
                    {errorDetails.serverTrace && (
                      <div className="overflow-auto max-h-[150px] text-xs font-mono whitespace-pre-wrap p-1 bg-background/40 rounded-md border">
                        {errorDetails.serverTrace}
                      </div>
                    )}
                    {executionTime && (
                      <div className="text-xs mt-1">
                        Execution time: {(executionTime / 1000).toFixed(2)}s
                      </div>
                    )}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Compact Split View Layout using grid */}
        <div className="grid gap-1 px-2 py-1 grid-cols-1">
          {/* Charts Section - make more compact */}
          {viewMode === 'chart' && hasData && (
            <div className="col-span-full border rounded-md overflow-hidden">
              <div className="h-full">
                <ChartVisualizer
                  key={`chart-${cellId}`}
                  data={processedData}
                  initialChartType={chartType}
                  chartConfig={chartConfig || defaultChartConfig}
                  showConfig={true}
                  onConfigChange={handleChartConfigUpdate}
                  cellId={cellId}
                />
              </div>
            </div>
          )}

          {/* Table View */}
          {viewMode === 'table' && hasData && (
            <RichDataTable
              data={data || []}
              onSaveTable={onSaveTable}
              onSaveToTable={handleSaveTable}
              maxHeight="350px"
            />
          )}

          {/* GraphicWalker View - full width */}
          {viewMode === 'graphicwalker' && hasData && (
            <div className="col-span-full p-0 border rounded-md">
              <GraphicWalkerVisualization
                data={data || []}
                title="Interactive Data Explorer"
                onBack={() => handleViewModeChange('table')}
              />
            </div>
          )}

          {/* Output View */}
          {viewMode === 'output' && (output || error) && (
            <div className="max-h-[350px] overflow-y-auto border rounded-md">
              <div className="p-2">
                <pre className={cn(
                  "whitespace-pre-wrap p-2 rounded-md text-sm font-mono",
                  error ? "bg-red-50 dark:bg-red-950/20 text-red-600 dark:text-red-400" : "bg-muted"
                )}>
                  {output || error}
                </pre>
              </div>
            </div>
          )}

          {/* Plots View */}
          {viewMode === 'plots' && (
            <div className="max-h-[350px] overflow-y-auto border rounded-md">
              <div className="p-2 space-y-2">
                {/* Debug and Save buttons */}
                <div className="flex justify-between mb-2">
                  <div>
                    {onSavePlot && plots && plots.length > 0 && (
                      <div className="text-xs text-muted-foreground">
                        Click "Save to Dashboard" button on each plot to add it to the dashboard
                      </div>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => {
                      console.log('Raw plots data:', plots);
                      if (plots && plots.length > 0) {
                        const plotPreview = plots.map((p, i) => ({
                          index: i,
                          preview: p.substring(0, 50) + '...',
                          isDataUrl: p.startsWith('data:'),
                          length: p.length
                        }));
                        console.table(plotPreview);
                        toast.success(`Debug info logged to console. Found ${plots.length} plots.`);
                      } else {
                        toast.error('No plots data available');
                      }
                    }}
                  >
                    Debug Plots
                  </Button>
                </div>

                {plots && plots.length > 0 ? (
                  plots.map((plot, index) => {
                    // Ensure the plot data is properly formatted with correct MIME type
                    let imageData = '';

                    try {
                      // Check if it's already a complete data URL
                      if (plot.startsWith('data:image')) {
                        imageData = plot;
                      }
                      // Check if it's a base64 string that needs a MIME type prefix
                      else if (/^[A-Za-z0-9+/=]+$/.test(plot.substring(0, 20))) {
                        // Detect PNG vs JPG based on the base64 header
                        // PNG files typically start with iVBOR in base64
                        const isPng = plot.startsWith('iVBOR');
                        const mimeType = isPng ? 'image/png' : 'image/jpeg';
                        imageData = `data:${mimeType};base64,${plot}`;
                      }
                      // Special case for when the result is a data URL but doesn't have the prefix
                      else if (plot.includes('base64,')) {
                        const base64Part = plot.split('base64,')[1];
                        if (base64Part) {
                          const isPng = base64Part.startsWith('iVBOR');
                          const mimeType = isPng ? 'image/png' : 'image/jpeg';
                          imageData = `data:${mimeType};base64,${base64Part}`;
                        } else {
                          imageData = plot; // Use as-is if we can't extract the base64 part
                        }
                      }
                      // If it's neither, try to use it as-is (might be a URL)
                      else {
                        imageData = plot;
                      }

                      console.log(`Plot ${index + 1} formatted as: ${imageData.substring(0, 30)}...`);
                    } catch (error) {
                      console.error(`Error formatting plot ${index + 1}:`, error);
                      imageData = '';
                    }

                    return (
                      <div key={index} className="flex flex-col items-center">
                        {imageData ? (
                          <div className="relative">
                            <img
                              src={imageData}
                              alt={`Plot ${index + 1}`}
                              className="w-full h-auto max-h-[300px] rounded-lg shadow-lg"
                              onError={(e) => {
                                console.error('Failed to load plot:', {
                                  index,
                                  dataPreview: imageData.substring(0, 50) + '...'
                                });
                                // Instead of hiding, show an error message
                                e.currentTarget.style.display = 'none';
                                // Add error message to parent div
                                const parent = e.currentTarget.parentElement;
                                if (parent) {
                                  const errorMsg = document.createElement('div');
                                  errorMsg.className = 'text-red-500 text-sm mt-2';
                                  errorMsg.textContent = 'Failed to load plot. Invalid image data.';
                                  parent.appendChild(errorMsg);
                                }
                              }}
                              onLoad={() => {
                                console.log('Plot loaded successfully:', index + 1);
                              }}
                            />
                            {/* Buttons for plot actions */}
                            <div className="absolute top-2 right-2 flex gap-1">
                              {/* Save to Dashboard button */}
                              {onSavePlot && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="bg-background/80 text-xs"
                                  onClick={() => handleSavePlot(imageData, index)}
                                >
                                  Save to Dashboard
                                </Button>
                              )}
                              {/* Open in new tab button */}
                              <Button
                                variant="outline"
                                size="sm"
                                className="bg-background/80 text-xs"
                                onClick={() => {
                                  window.open(imageData, '_blank');
                                }}
                              >
                                Open in New Tab
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-red-500 text-sm">
                            Invalid plot data
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground mt-1">
                          Plot {index + 1}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <div className="text-muted-foreground mb-4">
                      {language === 'python' ?
                        "No plots available yet. Make sure your Python code includes 'result = get_plot()' to display plots." :
                        "No plots available. Switch to Python language to create visualizations."}
                    </div>
                    <div className="bg-muted p-2 rounded-md text-xs font-mono max-w-xl text-left">
                      <p className="mb-2">Example code to generate a plot:</p>
                      <pre className="bg-background p-1 rounded text-xs">
{`import matplotlib.pyplot as plt
import numpy as np

# Create some data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create a plot
plt.figure(figsize=(10, 6))
plt.plot(x, y)
plt.title('Sine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.grid(True)

# IMPORTANT: This line is required to display the plot
result = get_plot()`}
                      </pre>

                      <p className="mt-4 mb-2">Or use a helper function:</p>
                      <pre className="bg-background p-1 rounded text-xs">
{`# Use a helper function to create a plot with one line
result = plot_histogram('age', bins=15)

# Other helper functions:
# result = plot_scatter('age', 'salary')
# result = plot_barplot('department', 'salary')
# result = plot_countplot('department')`}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
