'use client'

import React, { use<PERSON>emo, useCallback } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
  Panel,
  BackgroundVariant,
  NodeProps,
  Handle,
  Position
} from 'reactflow'
import 'reactflow/dist/style.css'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Database, Code2, BarChart3, AlertCircle, X, Maximize2, Minimize2 } from "lucide-react"
import { CellData } from './chartbuilderlogic'
import { Dataset } from '@/types/index'
import { cn } from "@/lib/utils"

interface ReactFlowVisualizationProps {
  cells: CellData[]
  datasets: Dataset[]
  selectedDatasets: Dataset[]
  isVisible: boolean
  onClose: () => void
  onToggleMaximize: () => void
  isMaximized: boolean
  onNodeClick?: (cellId: string) => void
}

interface CellNodeData {
  label: string
  cellId: string
  index: number
  language: string
  hasResult: boolean
  hasError: boolean
  datasets: string[]
  variables: string[]
  outputs: string[]
}

// Custom Cell Node Component
function CellNode({ data, selected }: NodeProps<CellNodeData>) {
  return (
    <div className={cn(
      "px-3 py-2 rounded-lg border-2 bg-background shadow-lg min-w-[150px] cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-105",
      data.hasError ? "border-red-500 bg-red-50 dark:bg-red-950/20" :
      data.hasResult ? "border-green-500 bg-green-50 dark:bg-green-950/20" :
      "border-border",
      selected && "ring-2 ring-blue-500 ring-offset-2"
    )}>
      <Handle type="target" position={Position.Left} className="w-3 h-3 border-2 bg-background" />

      <div className="space-y-1">
        {/* Cell Header */}
        <div className="flex items-center gap-2">
          <span className="font-mono text-xs font-bold">[{data.index + 1}]</span>
          <Badge variant="outline" className="text-xs h-4">
            {data.language}
          </Badge>
          {data.hasError && <AlertCircle className="h-3 w-3 text-red-500" />}
          {data.hasResult && <BarChart3 className="h-3 w-3 text-green-500" />}
        </div>

        {/* Cell Label */}
        <div className="text-xs font-medium truncate">{data.label}</div>

        {/* Datasets */}
        {data.datasets.length > 0 && (
          <div className="flex items-center gap-1">
            <Database className="h-2.5 w-2.5 text-blue-500" />
            <span className="text-xs text-muted-foreground">
              {data.datasets.slice(0, 2).join(', ')}
              {data.datasets.length > 2 && ` +${data.datasets.length - 2}`}
            </span>
          </div>
        )}

        {/* Variables */}
        {data.variables.length > 0 && (
          <div className="text-xs text-purple-600 dark:text-purple-400">
            Uses: {data.variables.slice(0, 2).join(', ')}
            {data.variables.length > 2 && ` +${data.variables.length - 2}`}
          </div>
        )}

        {data.outputs.length > 0 && (
          <div className="text-xs text-orange-600 dark:text-orange-400">
            Creates: {data.outputs.slice(0, 2).join(', ')}
            {data.outputs.length > 2 && ` +${data.outputs.length - 2}`}
          </div>
        )}
      </div>

      <Handle type="source" position={Position.Right} className="w-3 h-3 border-2 bg-background" />
    </div>
  )
}

const nodeTypes = {
  cellNode: CellNode
}

function FlowVisualization({ cells, datasets, selectedDatasets, isVisible, onClose, onToggleMaximize, isMaximized, onNodeClick }: ReactFlowVisualizationProps) {
  // Generate nodes and edges from cells with enhanced connections
  const { nodes: initialNodes, edges: initialEdges } = useMemo(() => {
    const nodes: Node<CellNodeData>[] = []
    const edges: Edge[] = []
    const variableMap = new Map<string, { cellId: string, index: number, type: string }>()
    const datasetUsage = new Map<string, string[]>() // Track which cells use which datasets

    cells.forEach((cell, index) => {
      if (cell.cellType === 'markdown') return

      // Extract variables from cell content
      const variables: string[] = []
      const outputs: string[] = []

      if (cell.language === 'python') {
        const lines = cell.content.split('\n')

        lines.forEach(line => {
          const trimmed = line.trim()

          // Variable assignments
          const assignmentMatch = trimmed.match(/^(\w+)\s*=/)
          if (assignmentMatch) {
            const varName = assignmentMatch[1]
            outputs.push(varName)

            // Determine variable type
            let varType = 'variable'
            if (trimmed.includes('pd.read_csv') || trimmed.includes('DataFrame')) {
              varType = 'dataframe'
            } else if (trimmed.includes('plt.') || trimmed.includes('get_plot()')) {
              varType = 'plot'
            } else if (varName === 'result') {
              varType = 'result'
            }

            variableMap.set(varName, { cellId: cell.id, index, type: varType })
          }

          // Variable usage
          if (!trimmed.match(/^(\w+)\s*=/) && !trimmed.startsWith('#') && !trimmed.startsWith('import')) {
            const varMatches = trimmed.match(/\b(\w+)\b/g)
            if (varMatches) {
              varMatches.forEach(varName => {
                if (variableMap.has(varName) && variableMap.get(varName)!.index < index) {
                  variables.push(varName)
                }
              })
            }
          }
        })
      }

      // Get datasets for this cell
      const cellDatasets = (cell.selectedDatasetIds || [])
        .map(id => datasets.find(ds => ds.id === id)?.name)
        .filter(Boolean) as string[]

      // Track dataset usage
      cellDatasets.forEach(datasetName => {
        if (!datasetUsage.has(datasetName)) {
          datasetUsage.set(datasetName, [])
        }
        datasetUsage.get(datasetName)!.push(cell.id)
      })

      // Create node with horizontal positioning
      const xOffset = index * 300 // Horizontal spacing between nodes
      const yOffset = 50 // Keep all nodes at same vertical level

      nodes.push({
        id: cell.id,
        type: 'cellNode',
        position: { x: xOffset, y: yOffset },
        data: {
          label: `Cell ${index + 1}`,
          cellId: cell.id,
          index,
          language: cell.language,
          hasResult: !!cell.result?.data?.length,
          hasError: !!cell.error,
          datasets: cellDatasets,
          variables: Array.from(new Set(variables)),
          outputs
        }
      })

      // Create edges for variable dependencies with different styles
      variables.forEach(varName => {
        const sourceVar = variableMap.get(varName)
        if (sourceVar) {
          const edgeStyle = {
            dataframe: { stroke: '#3b82f6', strokeWidth: 3, strokeDasharray: '5,5' },
            plot: { stroke: '#f59e0b', strokeWidth: 2, strokeDasharray: '3,3' },
            result: { stroke: '#10b981', strokeWidth: 3 },
            variable: { stroke: '#8b5cf6', strokeWidth: 2 }
          }[sourceVar.type] || { stroke: '#8b5cf6', strokeWidth: 2 }

          edges.push({
            id: `${sourceVar.cellId}-${cell.id}-${varName}`,
            source: sourceVar.cellId,
            target: cell.id,
            sourceHandle: 'right',
            targetHandle: 'left',
            label: varName,
            type: 'smoothstep',
            animated: true,
            style: edgeStyle,
           
            labelStyle: {
              fontSize: 10,
              fontWeight: 'bold',
              fill: edgeStyle.stroke,
              backgroundColor: 'rgba(255,255,255,0.8)',
              padding: '2px 4px',
              borderRadius: '4px'
            }
          })
        }
      })
    })

    // Add sequential flow edges (cell execution order)
    for (let i = 0; i < nodes.length - 1; i++) {
      const currentCell = nodes[i]
      const nextCell = nodes[i + 1]

      // Only add sequential edge if there's no variable dependency
      const hasVariableDependency = edges.some(edge =>
        edge.source === currentCell.id && edge.target === nextCell.id
      )

      if (!hasVariableDependency) {
        edges.push({
          id: `seq-${currentCell.id}-${nextCell.id}`,
          source: currentCell.id,
          target: nextCell.id,
          sourceHandle: 'right',
          targetHandle: 'left',
          type: 'straight',
          animated: false,
          style: {
            stroke: '#d1d5db',
            strokeWidth: 1,
            strokeDasharray: '2,2'
          },
         
        })
      }
    }

    return { nodes, edges }
  }, [cells, datasets])

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  // Handle node click to navigate to cell
  const onNodeClickHandler = useCallback((event: React.MouseEvent, node: Node) => {
    if (onNodeClick && node.data?.cellId) {
      onNodeClick(node.data.cellId)
    }
  }, [onNodeClick])

  // Update nodes when cells change
  React.useEffect(() => {
    setNodes(initialNodes)
    setEdges(initialEdges)
  }, [initialNodes, initialEdges, setNodes, setEdges])

  if (!isVisible) return null

  return (
    <Card className={cn(
      "fixed bottom-0 left-0 right-0 z-50 border-t border-border bg-background",
      isMaximized ? "h-[80vh]" : "h-[300px]"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-border">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          <span className="text-sm font-medium">Workflow Visualization</span>
          <Badge variant="outline" className="text-xs">
            {cells.filter(c => c.cellType !== 'markdown').length} cells
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            onClick={onToggleMaximize}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            {isMaximized ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* ReactFlow */}
      <div className="h-full">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onNodeClick={onNodeClickHandler}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{ padding: 0.1 }}
          minZoom={0.3}
          maxZoom={2}
          defaultEdgeOptions={{
            type: 'smoothstep',
            animated: true,
            style: { strokeWidth: 2 }
          }}
          nodesDraggable={true}
          nodesConnectable={false}
          elementsSelectable={true}
        >
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          <Controls className="bg-background border-border" />
          
          <Panel position="top-right" className="bg-background/95 p-2 rounded border">
            <div className="text-xs space-y-1">
              <div className="font-semibold mb-2">Legend</div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span>Executed</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
                <span>Error</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-0.5 bg-blue-500"></div>
                <span>DataFrame</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-0.5 bg-amber-500" style={{ borderTop: '2px dashed' }}></div>
                <span>Plot</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-0.5 bg-purple-500"></div>
                <span>Variable</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-0.5 bg-gray-400" style={{ borderTop: '1px dashed' }}></div>
                <span>Sequence</span>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                Click nodes to navigate
              </div>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    </Card>
  )
}

export function ReactFlowVisualization(props: ReactFlowVisualizationProps) {
  return (
    <ReactFlowProvider>
      <FlowVisualization {...props} />
    </ReactFlowProvider>
  )
}
