'use client'

import React, { useState, useEffect,useMemo, useCallback, useRef } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Calculator, AlertCircle, Copy, Save } from "lucide-react"
import { toast } from "sonner"
import * as math from 'mathjs'
import { FormulaResult, FormulaHistory } from './types'
import { useChartSaving } from '../chartbuilderlogic/useChartSaving'
import { FormulaInput } from './components/FormulaInput'
import { FormulaResult as FormulaResultComponent } from './components/FormulaResult'
import { FormulaHistory as FormulaHistoryComponent } from './components/FormulaHistory'
import { SaveCalculationDialog } from './components/SaveCalculationDialog'



interface FormulaCalculatorProps {
  data: any[]
  columns: string[]
  onClose?: () => void
  onCellClick?: (cellRef: string) => void
  onColumnClick?: (columnRef: string) => void
}

export function FormulaCalculator({ data, columns, onClose, onCellClick, onColumnClick }: FormulaCalculatorProps) {
  const [formula, setFormula] = useState('')
  const [result, setResult] = useState<FormulaResult | null>(null)
  const [history, setHistory] = useState<FormulaHistory[]>([])
  const [isCalculating, setIsCalculating] = useState(false)
  const formulaInputRef = useRef<HTMLInputElement>(null)

  // Save dialog state
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveTitle, setSaveTitle] = useState('')
  const [saveDescription, setSaveDescription] = useState('')
  const [selectedIcon, setSelectedIcon] = useState('Calculator')

  // Use the chart saving hook for dashboard functionality
  const { handleSaveCalculatorResult } = useChartSaving()

  // Insert text at cursor position
  const insertAtCursor = useCallback((text: string) => {
    const input = formulaInputRef.current
    if (input) {
      const start = input.selectionStart || 0
      const end = input.selectionEnd || 0
      const currentFormula = formula
      const newFormula = currentFormula.slice(0, start) + text + currentFormula.slice(end)
      setFormula(newFormula)

      // Set cursor position after inserted text
      setTimeout(() => {
        if (input) {
          input.focus()
          input.setSelectionRange(start + text.length, start + text.length)
        }
      }, 0)
    } else {
      // Fallback if no input ref
      setFormula(prev => prev + text)
    }
  }, [formula])

  // Add cell reference to formula (called from table clicks)
  const addCellReference = useCallback((cellRef: string) => {
    insertAtCursor(cellRef)
  }, [insertAtCursor])

  // Add column reference to formula (called from table clicks)
  const addColumnReference = useCallback((columnRef: string) => {
    insertAtCursor(columnRef)
  }, [insertAtCursor])

  // Expose functions to window for table click integration
  useEffect(() => {
    (window as any).addCellToFormula = addCellReference;
    (window as any).addColumnToFormula = addColumnReference;

    return () => {
      delete (window as any).addCellToFormula;
      delete (window as any).addColumnToFormula;
    };
  }, [addCellReference, addColumnReference])

  // Helper function to convert number to Excel column name (0->A, 1->B, 25->Z, 26->AA)
  const getExcelColumnName = useCallback((index: number): string => {
    let result = ''
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result
      index = Math.floor(index / 26) - 1
    }
    return result
  }, [])

  // Helper function to convert Excel column name to index (A->0, B->1, AA->26)
  const getColumnIndex = useCallback((columnName: string): number => {
    let result = 0
    for (let i = 0; i < columnName.length; i++) {
      result = result * 26 + (columnName.charCodeAt(i) - 64)
    }
    return result - 1
  }, [])

  // Create Excel-like column mapping (A, B, C, etc.)
  const columnMapping = useMemo(() => {
    const mapping: Record<string, string> = {}
    const reverseMapping: Record<string, string> = {}

    columns.forEach((column, index) => {
      const excelColumn = getExcelColumnName(index)
      mapping[excelColumn] = column
      reverseMapping[column] = excelColumn
    })

    return { mapping, reverseMapping }
  }, [columns, getExcelColumnName])

  // Get cell value by Excel reference (A1, B2, etc.)
  const getCellValue = useCallback((cellRef: string): number => {
    const match = cellRef.match(/^([A-Z]+)(\d+)$/)
    if (!match) return 0

    const columnName = match[1]
    const rowNumber = parseInt(match[2]) - 1 // Convert to 0-based index
    
    const actualColumn = columnMapping.mapping[columnName]
    if (!actualColumn || rowNumber < 0 || rowNumber >= data.length) return 0

    const value = data[rowNumber][actualColumn]
    
    // Handle different value types
    if (value === null || value === undefined) return 0
    if (typeof value === 'number') return value
    if (typeof value === 'string' && !isNaN(Number(value))) return Number(value)
    if (typeof value === 'boolean') return value ? 1 : 0
    return 0
  }, [columnMapping, data])

  // Get range values (A1:A4, B2:D5, etc.)
  const getRangeValues = useCallback((rangeRef: string): number[] => {
    const match = rangeRef.match(/^([A-Z]+)(\d+):([A-Z]+)(\d+)$/)
    if (!match) return []

    const [_, startCol, startRow, endCol, endRow] = match
    const startColIndex = getColumnIndex(startCol)
    const endColIndex = getColumnIndex(endCol)
    const startRowIndex = parseInt(startRow) - 1
    const endRowIndex = parseInt(endRow) - 1

    // Handle reversed ranges
    const minColIndex = Math.min(startColIndex, endColIndex)
    const maxColIndex = Math.max(startColIndex, endColIndex)
    const minRowIndex = Math.min(startRowIndex, endRowIndex)
    const maxRowIndex = Math.min(Math.max(startRowIndex, endRowIndex), data.length - 1)

    const values: number[] = []
    
    for (let row = minRowIndex; row <= maxRowIndex; row++) {
      if (row < 0) continue
      
      for (let col = minColIndex; col <= maxColIndex; col++) {
        if (col < 0 || col >= columns.length) continue
        
        const columnName = getExcelColumnName(col)
        const cellRef = `${columnName}${row + 1}`
        const value = getCellValue(cellRef)
        values.push(value)
      }
    }
    
    return values
  }, [getColumnIndex, getExcelColumnName, getCellValue, data, columns])

  // Excel-like formula evaluation
  const evaluateFormula = useCallback((formulaText: string): FormulaResult => {
    setIsCalculating(true)

    try {
      let processedFormula = formulaText.trim()

      // Remove leading = if present (Excel style)
      if (processedFormula.startsWith('=')) {
        processedFormula = processedFormula.substring(1)
      }

      console.log(`Processing formula: ${processedFormula}`)

      // Process Excel functions with proper range handling
      processedFormula = processedFormula
        // SUM function
        .replace(/SUM\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing SUM: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const sum = values.reduce((acc, val) => acc + val, 0)
            console.log(`SUM(${args}) = ${sum}`)
            return sum.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`SUM(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values (A1,B1,C1)
          if (args.includes(',')) {
            const items = args.split(',').map((item: string) => item.trim())
            let sum = 0
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                sum += getCellValue(item)
              } else if (!isNaN(Number(item))) {
                sum += Number(item)
              }
            }
            console.log(`SUM(${args}) = ${sum}`)
            return sum.toString()
          }
          
          // Handle direct number
          if (!isNaN(Number(args))) {
            return args
          }
          
          return '0'
        })

        // AVERAGE function
        .replace(/AVERAGE\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing AVERAGE: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const avg = values.length > 0 ? values.reduce((acc, val) => acc + val, 0) / values.length : 0
            console.log(`AVERAGE(${args}) = ${avg}`)
            return avg.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`AVERAGE(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map((item: string) => item.trim())
            let sum = 0
            let count = 0
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                sum += getCellValue(item)
                count++
              } else if (!isNaN(Number(item))) {
                sum += Number(item)
                count++
              }
            }
            const avg = count > 0 ? sum / count : 0
            console.log(`AVERAGE(${args}) = ${avg}`)
            return avg.toString()
          }
          
          return args
        })

        // COUNT function
        .replace(/COUNT\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing COUNT: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            console.log(`COUNT(${args}) = ${values.length}`)
            return values.length.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`COUNT(${args}) = 1`)
            return '1'
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map((item: string) => item.trim())
            let count = 0
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                count++
              } else if (!isNaN(Number(item))) {
                count++
              }
            }
            console.log(`COUNT(${args}) = ${count}`)
            return count.toString()
          }
          
          return '1'
        })

        // MIN function
        .replace(/MIN\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing MIN: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const min = values.length > 0 ? Math.min(...values) : 0
            console.log(`MIN(${args}) = ${min}`)
            return min.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`MIN(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map((item: string) => item.trim())
            const values: number[] = []
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                values.push(getCellValue(item))
              } else if (!isNaN(Number(item))) {
                values.push(Number(item))
              }
            }
            const min = values.length > 0 ? Math.min(...values) : 0
            console.log(`MIN(${args}) = ${min}`)
            return min.toString()
          }
          
          return args
        })

        // MAX function
        .replace(/MAX\(([^)]+)\)/gi, (match, args) => {
          console.log(`Processing MAX: ${args}`)
          
          // Handle range (A1:A4)
          if (args.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
            const values = getRangeValues(args)
            const max = values.length > 0 ? Math.max(...values) : 0
            console.log(`MAX(${args}) = ${max}`)
            return max.toString()
          }
          
          // Handle single cell (A1)
          if (args.match(/^[A-Z]+\d+$/)) {
            const value = getCellValue(args)
            console.log(`MAX(${args}) = ${value}`)
            return value.toString()
          }
          
          // Handle comma-separated values
          if (args.includes(',')) {
            const items = args.split(',').map((item: string) => item.trim())
            const values: number[] = []
            for (const item of items) {
              if (item.match(/^[A-Z]+\d+$/)) {
                values.push(getCellValue(item))
              } else if (!isNaN(Number(item))) {
                values.push(Number(item))
              }
            }
            const max = values.length > 0 ? Math.max(...values) : 0
            console.log(`MAX(${args}) = ${max}`)
            return max.toString()
          }
          
          return args
        })

      // Replace any remaining cell references with values
      processedFormula = processedFormula.replace(/([A-Z]+)(\d+)/g, (match) => {
        const cellValue = getCellValue(match)
        console.log(`Replacing cell ${match} with value: ${cellValue}`)
        return cellValue.toString()
      })

      console.log(`Final formula to evaluate: ${processedFormula}`)

      // If the formula is just a number after processing, return it directly
      if (!isNaN(Number(processedFormula))) {
        const numResult = Number(processedFormula)
        return {
          formula: formulaText,
          result: numResult,
          timestamp: Date.now()
        }
      }

      // Otherwise evaluate with math.js
      const result = math.evaluate(processedFormula)
      
      // Post-process result for Excel compatibility
      let finalResult = result
      if (typeof finalResult === 'number') {
        if (isNaN(finalResult)) finalResult = '#NUM!'
        if (!isFinite(finalResult)) finalResult = '#DIV/0!'
        if (finalResult > 1e15) finalResult = '#NUM!'
        if (finalResult < -1e15) finalResult = '#NUM!'
      }

      return {
        formula: formulaText,
        result: finalResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      console.error('Formula evaluation error:', error)
      return {
        formula: formulaText,
        result: null,
        error: error.message || 'Calculation failed',
        timestamp: Date.now()
      }
    } finally {
      setIsCalculating(false)
    }
  }, [getCellValue, getRangeValues])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    if (calculationResult.result !== null && !calculationResult.error) {
      const historyItem: FormulaHistory = {
        id: `calc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }



  // Clear formula and result
  const clearFormula = () => {
    setFormula('')
    setResult(null)
  }

  // Copy result to clipboard
  const copyResult = () => {
    if (result && result.result !== null) {
      navigator.clipboard.writeText(String(result.result))
      toast.success('Result copied to clipboard')
    }
  }

  // Open save dialog with pre-filled values
  const openSaveDialog = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save. Please calculate a formula first.')
      return
    }

    // Pre-fill the title and description
    const defaultTitle = formula.length > 30
      ? `${formula.substring(0, 30)}...`
      : formula

    setSaveTitle(defaultTitle)
    setSaveDescription(`Result: ${result.result}`)
    setShowSaveDialog(true)
  }

  // Save calculator result to dashboard
  const saveToDashboard = () => {
    if (!result || result.result === null || result.error) {
      toast.error('No valid result to save.')
      return
    }

    if (!saveTitle.trim()) {
      toast.error('Please enter a title for the calculation.')
      return
    }

    // Call the save function from useChartSaving hook with icon
    handleSaveCalculatorResult(
      formula,
      result.result,
      saveTitle,
      saveDescription,
      selectedIcon
    )

    setShowSaveDialog(false)
    setSaveTitle('')
    setSaveDescription('')
    setSelectedIcon('Calculator') // Reset to default
  }

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleCalculate()
    }
  }

  return (
    <div className="w-full max-w-full overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border rounded-lg p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Calculator className="h-4 w-4 text-blue-600 flex-shrink-0" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Excel Calculator</h3>
        </div>
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
          >
            ×
          </Button>
        )}
      </div>

      {/* Formula Input */}
      <FormulaInput
        formula={formula}
        setFormula={setFormula}
        onCalculate={handleCalculate}
        onKeyDown={handleKeyDown}
        isCalculating={isCalculating}
        formulaInputRef={formulaInputRef}
        insertAtCursor={insertAtCursor}
        clearFormula={clearFormula}
        columns={columns}
        getExcelColumnName={getExcelColumnName}
      />

      {/* Result Display */}
      {result && (
        <FormulaResultComponent
          result={result}
          onCopyResult={copyResult}
          onOpenSaveDialog={openSaveDialog}
        />
      )}

      {/* History */}
      <FormulaHistoryComponent
        history={history}
        onSelectFormula={setFormula}
      />

      {/* Save Dialog */}
      <SaveCalculationDialog
        isOpen={showSaveDialog}
        onClose={() => setShowSaveDialog(false)}
        saveTitle={saveTitle}
        setSaveTitle={setSaveTitle}
        saveDescription={saveDescription}
        setSaveDescription={setSaveDescription}
        selectedIcon={selectedIcon}
        setSelectedIcon={setSelectedIcon}
        onSave={saveToDashboard}
      />
    </div>
  )
}

export default FormulaCalculator
