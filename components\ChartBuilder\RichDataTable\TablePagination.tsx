'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  MoreHorizontal
} from "lucide-react"
import { PaginationState } from './types'

interface TablePaginationProps {
  pagination: PaginationState
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
  onGoToPage?: (page: number) => void
  showPageSizeSelector?: boolean
  showGoToPage?: boolean
  pageSizeOptions?: number[]
}

export function TablePagination({
  pagination,
  onPageChange,
  onPageSizeChange,
  onGoToPage,
  showPageSizeSelector = true,
  showGoToPage = true,
  pageSizeOptions = [10, 25, 50, 100, 500, 1000]
}: TablePaginationProps) {
  const { pageIndex, pageSize, totalRows, totalPages } = pagination
  
  const startRow = pageIndex * pageSize + 1
  const endRow = Math.min((pageIndex + 1) * pageSize, totalRows)
  
  const canPreviousPage = pageIndex > 0
  const canNextPage = pageIndex < totalPages - 1

  const handleGoToPage = (page: string) => {
    const pageNum = parseInt(page, 10)
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages && onGoToPage) {
      onGoToPage(pageNum - 1) // Convert to 0-based index
    }
  }

  // Generate page numbers for pagination display
  const getPageNumbers = () => {
    const pages: (number | string)[] = []
    const maxVisiblePages = 7
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 0; i < totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Show first page
      pages.push(0)
      
      if (pageIndex > 3) {
        pages.push('...')
      }
      
      // Show pages around current page
      const start = Math.max(1, pageIndex - 2)
      const end = Math.min(totalPages - 2, pageIndex + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      if (pageIndex < totalPages - 4) {
        pages.push('...')
      }
      
      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages - 1)
      }
    }
    
    return pages
  }

  return (
    <div className="flex items-center justify-between gap-4 p-3 bg-background border-t">
      {/* Row count and page size selector */}
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <div>
          Showing {startRow.toLocaleString()} to {endRow.toLocaleString()} of{" "}
          {totalRows.toLocaleString()} rows
        </div>
        
        {showPageSizeSelector && (
          <div className="flex items-center gap-2">
            <span>Rows per page:</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => onPageSizeChange(Number(value))}
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size.toLocaleString()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-2">
        {/* Go to page input */}
        {showGoToPage && totalPages > 10 && (
          <div className="flex items-center gap-2 mr-4">
            <span className="text-sm text-muted-foreground">Go to page:</span>
            <Input
              type="number"
              min={1}
              max={totalPages}
              className="w-16 h-8 text-center"
              placeholder={(pageIndex + 1).toString()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleGoToPage((e.target as HTMLInputElement).value)
                }
              }}
              onBlur={(e) => {
                handleGoToPage(e.target.value)
              }}
            />
          </div>
        )}

        {/* Navigation buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(0)}
            disabled={!canPreviousPage}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pageIndex - 1)}
            disabled={!canPreviousPage}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Page numbers */}
          <div className="flex items-center gap-1">
            {getPageNumbers().map((page, index) => (
              <div key={index}>
                {page === '...' ? (
                  <div className="flex items-center justify-center w-8 h-8">
                    <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
                  </div>
                ) : (
                  <Button
                    variant={page === pageIndex ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(page as number)}
                    className="h-8 w-8 p-0"
                  >
                    {(page as number) + 1}
                  </Button>
                )}
              </div>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pageIndex + 1)}
            disabled={!canNextPage}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages - 1)}
            disabled={!canNextPage}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Page info */}
        <div className="text-sm text-muted-foreground ml-4">
          Page {pageIndex + 1} of {totalPages}
        </div>
      </div>
    </div>
  )
}
