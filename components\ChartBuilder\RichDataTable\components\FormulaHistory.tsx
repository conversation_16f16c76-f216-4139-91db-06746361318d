'use client'

import React from 'react'
import { ScrollArea } from "@/components/ui/scroll-area"
import { FormulaHistory as FormulaHistoryType } from '../types'

interface FormulaHistoryProps {
  history: FormulaHistoryType[]
  onSelectFormula: (formula: string) => void
}

export function FormulaHistory({ history, onSelectFormula }: FormulaHistoryProps) {
  if (history.length === 0) {
    return null
  }

  return (
    <div className="mt-4">
      <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Calculations</h4>
      <ScrollArea className="h-24">
        <div className="space-y-1">
          {history.map((item: FormulaHistoryType) => (
            <div
              key={item.id}
              className="text-xs p-2 bg-white dark:bg-gray-800 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
              onClick={() => onSelectFormula(item.formula)}
            >
              <div className="font-mono text-gray-600 dark:text-gray-400">{item.formula}</div>
              <div className="font-semibold text-green-600">
                {typeof item.result === 'number' ? item.result.toLocaleString() : item.result}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}
