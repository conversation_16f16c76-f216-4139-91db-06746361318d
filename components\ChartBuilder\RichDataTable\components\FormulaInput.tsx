'use client'

import React from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface FormulaInputProps {
  formula: string
  setFormula: (formula: string) => void
  onCalculate: () => void
  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void
  isCalculating: boolean
  formulaInputRef: React.RefObject<HTMLInputElement>
  insertAtCursor: (text: string) => void
  clearFormula: () => void
  columns: string[]
  getExcelColumnName: (index: number) => string
}

export function FormulaInput({
  formula,
  setFormula,
  onCalculate,
  onKeyDown,
  isCalculating,
  formulaInputRef,
  insertAtCursor,
  clearFormula,
  columns,
  getExcelColumnName
}: FormulaInputProps) {
  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            ref={formulaInputRef}
            value={formula}
            onChange={(e) => setFormula(e.target.value)}
            onKeyDown={onKeyDown}
            placeholder="Enter Excel formula (e.g., =SUM(A1:A4), =AVERAGE(B1:B10))"
            className="font-mono text-sm"
          />
        </div>
        <Button
          onClick={onCalculate}
          disabled={isCalculating || !formula.trim()}
          className="px-4"
        >
          {isCalculating ? 'Calculating...' : 'Calculate'}
        </Button>
      </div>

      {/* Quick Function Buttons */}
      <div className="flex flex-wrap gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={() => insertAtCursor('SUM(')}
          className="text-xs"
        >
          SUM
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => insertAtCursor('AVERAGE(')}
          className="text-xs"
        >
          AVERAGE
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => insertAtCursor('COUNT(')}
          className="text-xs"
        >
          COUNT
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => insertAtCursor('MIN(')}
          className="text-xs"
        >
          MIN
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => insertAtCursor('MAX(')}
          className="text-xs"
        >
          MAX
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={clearFormula}
          className="text-xs"
        >
          Clear
        </Button>
      </div>

      {/* Column References */}
      <div className="flex flex-wrap gap-1">
        <span className="text-xs text-gray-600 mr-2">Columns:</span>
        {columns.slice(0, 10).map((column, index) => {
          const excelColumn = getExcelColumnName(index)
          return (
            <Button
              key={column}
              variant="ghost"
              size="sm"
              onClick={() => insertAtCursor(excelColumn)}
              className="text-xs h-6 px-2"
              title={`${excelColumn} = ${column}`}
            >
              {excelColumn}
            </Button>
          )
        })}
        {columns.length > 10 && (
          <span className="text-xs text-gray-500">+{columns.length - 10} more</span>
        )}
      </div>
    </div>
  )
}
