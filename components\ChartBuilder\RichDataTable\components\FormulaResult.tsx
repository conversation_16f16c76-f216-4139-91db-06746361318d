'use client'

import React from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, Copy, Save } from "lucide-react"
import { FormulaResult as FormulaResultType } from '../types'

interface FormulaResultProps {
  result: FormulaResultType
  onCopyResult: () => void
  onOpenSaveDialog: () => void
}

export function FormulaResult({ result, onCopyResult, onOpenSaveDialog }: FormulaResultProps) {
  return (
    <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="text-xs text-gray-500 mb-1">Formula: {result.formula}</div>
          {result.error ? (
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Error: {result.error}</span>
            </div>
          ) : (
            <div className="text-lg font-bold text-green-600">
              {typeof result.result === 'number' ? result.result.toLocaleString() : result.result}
            </div>
          )}
        </div>
        {!result.error && (
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onCopyResult}
              className="h-8 w-8 p-0"
              title="Copy result"
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onOpenSaveDialog}
              className="h-8 w-8 p-0"
              title="Save to dashboard"
            >
              <Save className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
