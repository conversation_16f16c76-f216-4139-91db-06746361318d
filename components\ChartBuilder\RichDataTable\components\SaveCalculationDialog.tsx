'use client'

import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Calculator, TrendingUp, DollarSign, Users, Target, BarChart3, PieChart, Activity, Zap, Star, Award, Briefcase, Calendar, FileText, Settings, Heart, Shield, Globe, Home, Hash } from "lucide-react"

// Available icons for calculator cards
const AVAILABLE_ICONS = [
  { name: 'Calculator', icon: Calculator, color: 'text-blue-600' },
  { name: 'TrendingUp', icon: TrendingUp, color: 'text-green-600' },
  { name: 'DollarSign', icon: DollarSign, color: 'text-emerald-600' },
  { name: 'Users', icon: Users, color: 'text-purple-600' },
  { name: 'Target', icon: Target, color: 'text-red-600' },
  { name: '<PERSON><PERSON>hart3', icon: <PERSON><PERSON>hart3, color: 'text-indigo-600' },
  { name: '<PERSON><PERSON><PERSON>', icon: <PERSON><PERSON><PERSON>, color: 'text-pink-600' },
  { name: 'Activity', icon: Activity, color: 'text-orange-600' },
  { name: 'Zap', icon: Zap, color: 'text-yellow-600' },
  { name: 'Star', icon: Star, color: 'text-amber-600' },
  { name: 'Award', icon: Award, color: 'text-cyan-600' },
  { name: 'Briefcase', icon: Briefcase, color: 'text-slate-600' },
  { name: 'Calendar', icon: Calendar, color: 'text-teal-600' },
  { name: 'FileText', icon: FileText, color: 'text-gray-600' },
  { name: 'Settings', icon: Settings, color: 'text-stone-600' },
  { name: 'Heart', icon: Heart, color: 'text-rose-600' },
  { name: 'Shield', icon: Shield, color: 'text-blue-700' },
  { name: 'Globe', icon: Globe, color: 'text-green-700' },
  { name: 'Home', icon: Home, color: 'text-blue-500' },
  { name: 'Hash', icon: Hash, color: 'text-gray-500' }
]

interface SaveCalculationDialogProps {
  isOpen: boolean
  onClose: () => void
  saveTitle: string
  setSaveTitle: (title: string) => void
  saveDescription: string
  setSaveDescription: (description: string) => void
  selectedIcon: string
  setSelectedIcon: (icon: string) => void
  onSave: () => void
}

export function SaveCalculationDialog({
  isOpen,
  onClose,
  saveTitle,
  setSaveTitle,
  saveDescription,
  setSaveDescription,
  selectedIcon,
  setSelectedIcon,
  onSave
}: SaveCalculationDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Save Calculation Result</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={saveTitle}
              onChange={(e) => setSaveTitle(e.target.value)}
              placeholder="Enter a title for this calculation"
            />
          </div>
          <div>
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={saveDescription}
              onChange={(e) => setSaveDescription(e.target.value)}
              placeholder="Add a description..."
              rows={2}
            />
          </div>
          <div>
            <Label>Icon</Label>
            <div className="grid grid-cols-5 gap-2 mt-2">
              {AVAILABLE_ICONS.map((iconItem) => {
                const IconComponent = iconItem.icon
                return (
                  <Button
                    key={iconItem.name}
                    variant={selectedIcon === iconItem.name ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedIcon(iconItem.name)}
                    className="h-10 w-10 p-0"
                    title={iconItem.name}
                  >
                    <IconComponent className={`h-4 w-4 ${iconItem.color}`} />
                  </Button>
                )
              })}
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={onSave}>
              Save to Dashboard
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
