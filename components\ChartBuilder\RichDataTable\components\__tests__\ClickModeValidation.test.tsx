import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import FormulaCalculator from '../FormulaCalculator'

// Mock the chart saving hook
jest.mock('../../chartbuilderlogic/useChartSaving', () => ({
  useChartSaving: () => ({
    handleSaveCalculatorResult: jest.fn()
  })
}))

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

// Mock mathjs
jest.mock('mathjs', () => ({
  evaluate: jest.fn((expr) => {
    // Simple mock evaluation for basic arithmetic
    if (expr === '10 + 5') return 15
    if (expr === '20 * 2') return 40
    if (expr === '100 / 4') return 25
    return 0
  })
}))

const mockData = [
  { name: '<PERSON>', age: 30, salary: 50000 },
  { name: '<PERSON>', age: 25, salary: 60000 },
  { name: '<PERSON>', age: 35, salary: 70000 },
  { name: '<PERSON>', age: 28, salary: 55000 }
]

const mockColumns = ['name', 'age', 'salary']

describe('FormulaCalculator Click Mode Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear any existing window functions
    delete (window as any).addCellToFormula
    delete (window as any).addColumnToFormula
  })

  afterEach(() => {
    // Clean up window functions
    delete (window as any).addCellToFormula
    delete (window as any).addColumnToFormula
  })

  test('exposes window functions for click mode integration', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    // Check that window functions are exposed
    expect(typeof (window as any).addCellToFormula).toBe('function')
    expect(typeof (window as any).addColumnToFormula).toBe('function')
  })

  test('addCellToFormula function adds cell reference to input', async () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    
    // Simulate clicking a cell (A1)
    if ((window as any).addCellToFormula) {
      (window as any).addCellToFormula('A1')
    }

    await waitFor(() => {
      expect(input).toHaveValue('A1')
    })
  })

  test('addColumnToFormula function adds column reference to input', async () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    
    // Simulate clicking a column (A:A)
    if ((window as any).addColumnToFormula) {
      (window as any).addColumnToFormula('A:A')
    }

    await waitFor(() => {
      expect(input).toHaveValue('A:A')
    })
  })

  test('multiple cell clicks append to formula', async () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    
    // First add SUM function
    fireEvent.click(screen.getByText('SUM'))
    
    // Then add cell references via window functions
    if ((window as any).addCellToFormula) {
      (window as any).addCellToFormula('A1')
      (window as any).addCellToFormula(':A4')
    }

    await waitFor(() => {
      expect(input).toHaveValue('SUM(A1:A4')
    })
  })

  test('window functions are cleaned up on unmount', () => {
    const { unmount } = render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    // Functions should exist
    expect((window as any).addCellToFormula).toBeDefined()
    expect((window as any).addColumnToFormula).toBeDefined()

    // Unmount component
    unmount()

    // Functions should be cleaned up
    expect((window as any).addCellToFormula).toBeUndefined()
    expect((window as any).addColumnToFormula).toBeUndefined()
  })

  test('insertAtCursor maintains cursor position', async () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/) as HTMLInputElement
    
    // Set initial value and cursor position
    fireEvent.change(input, { target: { value: 'SUM()' } })
    
    // Set cursor position inside parentheses
    input.setSelectionRange(4, 4) // Position after 'SUM('
    
    // Add cell reference
    if ((window as any).addCellToFormula) {
      (window as any).addCellToFormula('A1:A4')
    }

    await waitFor(() => {
      expect(input.value).toBe('SUM(A1:A4)')
    })
  })

  test('handles type safety for item parameters', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    
    // Test SUM with comma-separated values
    fireEvent.change(input, { target: { value: '=SUM(A1,B1,C1)' } })
    fireEvent.click(screen.getByText('Calculate'))

    // Should not throw type errors and should calculate
    expect(input.value).toBe('=SUM(A1,B1,C1)')
  })

  test('validates formula input properly', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    const calculateButton = screen.getByText('Calculate')
    
    // Test empty formula
    fireEvent.click(calculateButton)
    
    // Should show error for empty formula
    expect(calculateButton).toBeInTheDocument()
    
    // Test valid formula
    fireEvent.change(input, { target: { value: '=10+5' } })
    expect(calculateButton).not.toBeDisabled()
  })
})
