import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import FormulaCalculator from '../FormulaCalculator'

// Mock the chart saving hook
jest.mock('../../chartbuilderlogic/useChartSaving', () => ({
  useChartSaving: () => ({
    handleSaveCalculatorResult: jest.fn()
  })
}))

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

// Mock mathjs
jest.mock('mathjs', () => ({
  evaluate: jest.fn((expr) => {
    // Simple mock evaluation for basic arithmetic
    if (expr === '10 + 5') return 15
    if (expr === '20 * 2') return 40
    if (expr === '100 / 4') return 25
    return 0
  })
}))

const mockData = [
  { name: '<PERSON>', age: 30, salary: 50000 },
  { name: '<PERSON>', age: 25, salary: 60000 },
  { name: '<PERSON>', age: 35, salary: 70000 },
  { name: '<PERSON>', age: 28, salary: 55000 }
]

const mockColumns = ['name', 'age', 'salary']

describe('FormulaCalculator', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders formula calculator with input and buttons', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    expect(screen.getByPlaceholderText(/Enter Excel formula/)).toBeInTheDocument()
    expect(screen.getByText('Calculate')).toBeInTheDocument()
    expect(screen.getByText('SUM')).toBeInTheDocument()
    expect(screen.getByText('AVERAGE')).toBeInTheDocument()
    expect(screen.getByText('COUNT')).toBeInTheDocument()
  })

  test('displays column references correctly', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    expect(screen.getByText('A')).toBeInTheDocument() // name column
    expect(screen.getByText('B')).toBeInTheDocument() // age column
    expect(screen.getByText('C')).toBeInTheDocument() // salary column
  })

  test('can insert function at cursor position', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    const sumButton = screen.getByText('SUM')

    fireEvent.click(sumButton)
    expect(input).toHaveValue('SUM(')
  })

  test('can clear formula', () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    const clearButton = screen.getByText('Clear')

    // Add some text first
    fireEvent.change(input, { target: { value: 'SUM(A1:A4)' } })
    expect(input).toHaveValue('SUM(A1:A4)')

    // Clear it
    fireEvent.click(clearButton)
    expect(input).toHaveValue('')
  })

  test('validates formula calculation with proper types', async () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    const calculateButton = screen.getByText('Calculate')

    // Enter a simple formula
    fireEvent.change(input, { target: { value: '=10+5' } })
    fireEvent.click(calculateButton)

    // Should show result
    await waitFor(() => {
      expect(screen.getByText('15')).toBeInTheDocument()
    })
  })

  test('handles formula errors gracefully', async () => {
    // Mock mathjs to throw an error
    const mathjs = require('mathjs')
    mathjs.evaluate.mockImplementation(() => {
      throw new Error('Invalid formula')
    })

    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    const calculateButton = screen.getByText('Calculate')

    fireEvent.change(input, { target: { value: '=INVALID()' } })
    fireEvent.click(calculateButton)

    await waitFor(() => {
      expect(screen.getByText(/Error:/)).toBeInTheDocument()
    })
  })

  test('creates history items with proper FormulaHistory type', async () => {
    render(
      <FormulaCalculator
        data={mockData}
        columns={mockColumns}
      />
    )

    const input = screen.getByPlaceholderText(/Enter Excel formula/)
    const calculateButton = screen.getByText('Calculate')

    // Calculate a formula
    fireEvent.change(input, { target: { value: '=10+5' } })
    fireEvent.click(calculateButton)

    // Wait for calculation and history to appear
    await waitFor(() => {
      expect(screen.getByText('Recent Calculations')).toBeInTheDocument()
    })

    // History should show the formula
    expect(screen.getByText('=10+5')).toBeInTheDocument()
  })
})
