/**
 * Validation utilities for FormulaCalculator components
 */

import { FormulaResult, FormulaHistory } from '../types'

/**
 * Validates that a FormulaResult object has all required properties
 */
export function validateFormulaResult(result: any): result is FormulaResult {
  return (
    typeof result === 'object' &&
    result !== null &&
    typeof result.formula === 'string' &&
    (result.result === null || typeof result.result === 'number' || typeof result.result === 'string') &&
    typeof result.timestamp === 'number' &&
    (result.error === undefined || typeof result.error === 'string')
  )
}

/**
 * Validates that a FormulaHistory object has all required properties including the id
 */
export function validateFormulaHistory(history: any): history is FormulaHistory {
  return (
    typeof history === 'object' &&
    history !== null &&
    typeof history.id === 'string' &&
    typeof history.formula === 'string' &&
    (history.result === null || typeof history.result === 'number' || typeof history.result === 'string') &&
    typeof history.timestamp === 'number' &&
    (history.description === undefined || typeof history.description === 'string')
  )
}

/**
 * Creates a properly typed FormulaHistory object
 */
export function createFormulaHistory(
  formula: string,
  result: number | string | null,
  timestamp: number,
  description?: string
): FormulaHistory {
  return {
    id: `calc_${timestamp}_${Math.random().toString(36).substring(2, 11)}`,
    formula,
    result,
    timestamp,
    description
  }
}

/**
 * Creates a properly typed FormulaResult object
 */
export function createFormulaResult(
  formula: string,
  result: number | string | null,
  timestamp: number,
  error?: string
): FormulaResult {
  return {
    formula,
    result,
    timestamp,
    error
  }
}

/**
 * Validates an array of FormulaHistory objects
 */
export function validateFormulaHistoryArray(historyArray: any[]): historyArray is FormulaHistory[] {
  return Array.isArray(historyArray) && historyArray.every(validateFormulaHistory)
}

/**
 * Type guard to check if a value is a valid Excel cell reference (e.g., A1, B2, AA10)
 */
export function isValidCellReference(value: string): boolean {
  return /^[A-Z]+\d+$/.test(value)
}

/**
 * Type guard to check if a value is a valid Excel range reference (e.g., A1:B4)
 */
export function isValidRangeReference(value: string): boolean {
  return /^[A-Z]+\d+:[A-Z]+\d+$/.test(value)
}

/**
 * Validates formula input for basic safety
 */
export function validateFormulaInput(formula: string): { isValid: boolean; error?: string } {
  if (!formula || typeof formula !== 'string') {
    return { isValid: false, error: 'Formula must be a non-empty string' }
  }

  if (formula.length > 1000) {
    return { isValid: false, error: 'Formula is too long (max 1000 characters)' }
  }

  // Check for potentially dangerous patterns
  const dangerousPatterns = [
    /eval\s*\(/i,
    /function\s*\(/i,
    /new\s+Function/i,
    /setTimeout/i,
    /setInterval/i,
    /document\./i,
    /window\./i,
    /global\./i
  ]

  for (const pattern of dangerousPatterns) {
    if (pattern.test(formula)) {
      return { isValid: false, error: 'Formula contains potentially unsafe content' }
    }
  }

  return { isValid: true }
}

/**
 * Test function to validate all components work correctly
 */
export function runValidationTests(): { passed: number; failed: number; errors: string[] } {
  const errors: string[] = []
  let passed = 0
  let failed = 0

  try {
    // Test FormulaResult validation
    const validResult = createFormulaResult('=SUM(A1:A4)', 100, Date.now())
    if (validateFormulaResult(validResult)) {
      passed++
    } else {
      failed++
      errors.push('FormulaResult validation failed for valid object')
    }

    // Test FormulaHistory validation
    const validHistory = createFormulaHistory('=AVERAGE(B1:B10)', 50.5, Date.now(), 'Test calculation')
    if (validateFormulaHistory(validHistory)) {
      passed++
    } else {
      failed++
      errors.push('FormulaHistory validation failed for valid object')
    }

    // Test invalid objects
    const invalidResult = { formula: 'test' } // missing required fields
    if (!validateFormulaResult(invalidResult)) {
      passed++
    } else {
      failed++
      errors.push('FormulaResult validation should have failed for invalid object')
    }

    // Test cell reference validation
    if (isValidCellReference('A1') && isValidCellReference('Z99') && !isValidCellReference('invalid')) {
      passed++
    } else {
      failed++
      errors.push('Cell reference validation failed')
    }

    // Test range reference validation
    if (isValidRangeReference('A1:B4') && !isValidRangeReference('invalid:range')) {
      passed++
    } else {
      failed++
      errors.push('Range reference validation failed')
    }

    // Test formula input validation
    const validFormula = validateFormulaInput('=SUM(A1:A10)')
    const invalidFormula = validateFormulaInput('eval(malicious_code)')
    
    if (validFormula.isValid && !invalidFormula.isValid) {
      passed++
    } else {
      failed++
      errors.push('Formula input validation failed')
    }

  } catch (error) {
    failed++
    errors.push(`Validation test threw error: ${error}`)
  }

  return { passed, failed, errors }
}
