export interface RichDataTableProps {
  data: any[];
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void;
  onSaveToTable?: () => void;
  maxHeight?: string;
  enableVirtualization?: boolean;
  enableFormulas?: boolean;
  pageSize?: number;
  showPagination?: boolean;
  showSearch?: boolean;
  showColumnFilters?: boolean;
  showFormulaCalculator?: boolean;
}

export interface TableColumn {
  id: string;
  header: string;
  accessorKey: string;
  type: 'string' | 'number' | 'boolean' | 'date';
  sortable?: boolean;
  filterable?: boolean;
}

export interface FormulaResult {
  formula: string;
  result: number | string | null;
  error?: string;
  timestamp: number;
}

export interface FormulaHistory {
  id: string;
  formula: string;
  result: number | string | null;
  timestamp: number;
  description?: string;
}

export interface CellReference {
  column: string;
  row: number;
  value: any;
}

export interface FormulaFunction {
  name: string;
  description: string;
  syntax: string;
  example: string;
  category: 'math' | 'statistical' | 'logical' | 'text' | 'date';
}

export interface PaginationState {
  pageIndex: number;
  pageSize: number;
  totalRows: number;
  totalPages: number;
}

export interface SortingState {
  column: string;
  direction: 'asc' | 'desc';
}

export interface FilterState {
  column: string;
  value: string;
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte';
}

export interface TableState {
  pagination: PaginationState;
  sorting: SortingState[];
  filters: FilterState[];
  globalFilter: string;
  selectedRows: number[];
}
