'use client'

import React, { useMemo } from 'react'
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Database, Code2, Bar<PERSON>hart3, AlertCircle, ArrowRight, Play } from "lucide-react"
import { CellData } from './chartbuilderlogic'
import { Dataset } from '@/types/index'
import { cn } from "@/lib/utils"

interface WorkflowPipelineProps {
  cells: CellData[]
  datasets: Dataset[]
  selectedDatasets: Dataset[]
}

interface VariableInfo {
  name: string
  type: 'dataframe' | 'variable' | 'plot' | 'result'
  cellId: string
  cellIndex: number
}

interface CellNode {
  id: string
  index: number
  language: string
  hasResult: boolean
  hasError: boolean
  datasets: string[]
  variables: VariableInfo[]
  outputs: VariableInfo[]
}

export function WorkflowPipeline({ cells, datasets, selectedDatasets }: WorkflowPipelineProps) {
  // Analyze cells to extract variable usage and data flow
  const cellNodes = useMemo(() => {
    const nodes: CellNode[] = []
    const globalVariables = new Map<string, VariableInfo>()

    cells.forEach((cell, index) => {
      if (cell.cellType === 'markdown') return

      const variables: VariableInfo[] = []
      const outputs: VariableInfo[] = []

      // Extract variables from Python code
      if (cell.language === 'python') {
        const lines = cell.content.split('\n')
        
        lines.forEach(line => {
          const trimmed = line.trim()
          
          // Look for variable assignments
          const assignmentMatch = trimmed.match(/^(\w+)\s*=/)
          if (assignmentMatch) {
            const varName = assignmentMatch[1]
            let varType: VariableInfo['type'] = 'variable'
            
            if (trimmed.includes('pd.read_csv') || trimmed.includes('DataFrame')) {
              varType = 'dataframe'
            } else if (trimmed.includes('plt.') || trimmed.includes('get_plot()')) {
              varType = 'plot'
            } else if (varName === 'result') {
              varType = 'result'
            }
            
            const varInfo: VariableInfo = {
              name: varName,
              type: varType,
              cellId: cell.id,
              cellIndex: index
            }
            
            outputs.push(varInfo)
            globalVariables.set(varName, varInfo)
          }
          
          // Look for variable usage (excluding assignments)
          if (!trimmed.match(/^(\w+)\s*=/) && !trimmed.startsWith('#') && !trimmed.startsWith('import')) {
            const varMatches = trimmed.match(/\b(\w+)\b/g)
            if (varMatches) {
              varMatches.forEach(varName => {
                if (globalVariables.has(varName) && globalVariables.get(varName)!.cellIndex < index) {
                  variables.push(globalVariables.get(varName)!)
                }
              })
            }
          }
        })
      }

      // Extract datasets used by this cell
      const cellDatasets = (cell.selectedDatasetIds || [])
        .map(id => datasets.find(ds => ds.id === id)?.name)
        .filter(Boolean) as string[]

      nodes.push({
        id: cell.id,
        index,
        language: cell.language,
        hasResult: !!cell.result?.data?.length,
        hasError: !!cell.error,
        datasets: cellDatasets,
        variables: Array.from(new Set(variables.map(v => v.name))).map(name => 
          variables.find(v => v.name === name)!
        ),
        outputs: outputs
      })
    })

    return nodes
  }, [cells, datasets])

  if (cellNodes.length === 0) {
    return (
      <Card className="p-3">
        <div className="text-center text-xs text-muted-foreground">
          <Code2 className="h-4 w-4 mx-auto mb-1" />
          <p>No cells to visualize</p>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-2">
      <div className="space-y-2">
        <div className="flex items-center gap-1 mb-2">
          <BarChart3 className="h-3 w-3" />
          <span className="text-[10px] font-medium">Workflow Pipeline</span>
        </div>
        
        <div className="space-y-1.5">
          {cellNodes.map((node, index) => (
            <div key={node.id} className="relative">
              {/* Cell Node */}
              <div className={cn(
                "flex items-center gap-1.5 p-1.5 rounded border text-[9px]",
                node.hasError ? "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20" :
                node.hasResult ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20" :
                "border-border bg-muted/30"
              )}>
                {/* Cell Number */}
                <div className="flex items-center gap-1">
                  <span className="font-mono font-semibold text-[8px]">[{index + 1}]</span>
                  <Badge variant="outline" className="h-3 px-1 text-[7px]">
                    {node.language}
                  </Badge>
                </div>

                {/* Status */}
                <div className="flex items-center gap-0.5">
                  {node.hasError && <AlertCircle className="h-2.5 w-2.5 text-red-500" />}
                  {node.hasResult && <Play className="h-2.5 w-2.5 text-green-500" />}
                </div>

                {/* Datasets */}
                {node.datasets.length > 0 && (
                  <div className="flex items-center gap-0.5">
                    <Database className="h-2.5 w-2.5 text-blue-500" />
                    <span className="text-[8px] text-muted-foreground">
                      {node.datasets.slice(0, 2).join(', ')}
                      {node.datasets.length > 2 && ` +${node.datasets.length - 2}`}
                    </span>
                  </div>
                )}

                {/* Variables Used */}
                {node.variables.length > 0 && (
                  <div className="flex items-center gap-0.5">
                    <span className="text-[8px] text-purple-600 dark:text-purple-400">
                      uses: {node.variables.slice(0, 2).map(v => v.name).join(', ')}
                      {node.variables.length > 2 && ` +${node.variables.length - 2}`}
                    </span>
                  </div>
                )}

                {/* Variables Created */}
                {node.outputs.length > 0 && (
                  <div className="flex items-center gap-0.5">
                    <span className="text-[8px] text-orange-600 dark:text-orange-400">
                      creates: {node.outputs.slice(0, 2).map(v => v.name).join(', ')}
                      {node.outputs.length > 2 && ` +${node.outputs.length - 2}`}
                    </span>
                  </div>
                )}
              </div>

              {/* Arrow to next cell */}
              {index < cellNodes.length - 1 && (
                <div className="flex justify-center py-0.5">
                  <ArrowRight className="h-2.5 w-2.5 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-2 pt-1.5 border-t border-border">
          <div className="grid grid-cols-2 gap-2 text-[8px] text-muted-foreground">
            <div className="flex items-center gap-1">
              <Database className="h-2.5 w-2.5" />
              <span>{selectedDatasets.length} datasets</span>
            </div>
            <div className="flex items-center gap-1">
              <Code2 className="h-2.5 w-2.5" />
              <span>{cellNodes.length} cells</span>
            </div>
            <div className="flex items-center gap-1">
              <Play className="h-2.5 w-2.5 text-green-500" />
              <span>{cellNodes.filter(n => n.hasResult).length} executed</span>
            </div>
            <div className="flex items-center gap-1">
              <AlertCircle className="h-2.5 w-2.5 text-red-500" />
              <span>{cellNodes.filter(n => n.hasError).length} errors</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
