'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { Loader2, FolderPlus, Globe, Lock } from "lucide-react"
import { WorkspaceModalProps } from './types/workspace'

export function WorkspaceModal({ isOpen, onClose, onWorkspaceCreated }: WorkspaceModalProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [isPublic, setIsPublic] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      toast.error('Workspace name is required')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/workspaces', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim(),
          description: description.trim() || null,
          isPublic
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create workspace')
      }

      const data = await response.json()
      if (data.success) {
        toast.success('Workspace created successfully!')
        onWorkspaceCreated(data.workspace)
        handleClose()
      } else {
        throw new Error(data.error || 'Failed to create workspace')
      }
    } catch (error) {
      console.error('Error creating workspace:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create workspace')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setName('')
    setDescription('')
    setIsPublic(false)
    setIsLoading(false)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderPlus className="h-5 w-5" />
            Create New Data Workspace
          </DialogTitle>
          <DialogDescription>
            Create a new data workspace to organize your analysis notebooks and visualization dashboards. You can analyze data with SQL/Python, save results, and share your work.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="workspace-name">Data Workspace Name *</Label>
            <Input
              id="workspace-name"
              placeholder="e.g., Sales Analytics, HR Data Analysis, Q4 Financial Report"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
              maxLength={100}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="workspace-description">Description</Label>
            <Textarea
              id="workspace-description"
              placeholder="Describe what this data workspace is for... (e.g., Customer behavior analysis, Financial reporting, etc.)"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isLoading}
              rows={3}
              maxLength={500}
            />
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                {isPublic ? (
                  <Globe className="h-4 w-4 text-blue-500" />
                ) : (
                  <Lock className="h-4 w-4 text-gray-500" />
                )}
                <Label htmlFor="workspace-public" className="font-medium">
                  {isPublic ? 'Public Data Workspace' : 'Private Data Workspace'}
                </Label>
              </div>
              <p className="text-sm text-muted-foreground">
                {isPublic
                  ? 'Anyone with the link can view this data workspace (read-only)'
                  : 'Only you can access this data workspace'
                }
              </p>
            </div>
            <Switch
              id="workspace-public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
              disabled={isLoading}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !name.trim()}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <FolderPlus className="h-4 w-4 mr-2" />
                  Create Data Workspace
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
