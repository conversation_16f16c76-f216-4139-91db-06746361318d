'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, FolderPlus, Folder, Globe, Lock, Save, ExternalLink, FolderOpen } from "lucide-react"
import { toast } from "sonner"
import { useRouter } from 'next/navigation'
import { WorkspaceModal } from './WorkspaceModal'
import { Workspace, WorkspaceSelectorProps } from './types/workspace'

export function WorkspaceSelector({ 
  currentWorkspace, 
  onWorkspaceChange, 
  onSaveToWorkspace,
  isSaving = false 
}: WorkspaceSelectorProps) {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const router = useRouter()

  useEffect(() => {
    fetchWorkspaces()
  }, [])

  const fetchWorkspaces = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/workspaces')
      
      if (!response.ok) {
        throw new Error('Failed to fetch workspaces')
      }

      const data = await response.json()
      if (data.success) {
        setWorkspaces(data.workspaces)
        
        // If no current workspace is selected and we have workspaces, select the most recent one
        if (!currentWorkspace && data.workspaces.length > 0) {
          onWorkspaceChange(data.workspaces[0])
        }
      } else {
        throw new Error(data.error || 'Failed to fetch workspaces')
      }
    } catch (error) {
      console.error('Error fetching workspaces:', error)
      toast.error('Failed to load workspaces')
    } finally {
      setIsLoading(false)
    }
  }

  const handleWorkspaceCreated = (newWorkspace: Workspace) => {
    setWorkspaces(prev => [newWorkspace, ...prev])
    onWorkspaceChange(newWorkspace)
    toast.success('Workspace created and selected!')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const openWorkspace = (workspace: Workspace) => {
    window.open(`/hr/chartbuilder/workspace/${workspace.id}`, '_blank')
  }

  return (
    <>
      <div className="flex items-center gap-2">
        {/* Workspace Selector */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-6 text-xs px-2">
              <Folder className="h-3 w-3" />
              <span className="hidden sm:inline ml-1 max-w-[120px] truncate">
                {currentWorkspace ? currentWorkspace.name : 'Select Workspace'}
              </span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-80">
            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2">
                Current Data Workspace
              </div>
              {currentWorkspace ? (
                <div className="p-2 bg-muted rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{currentWorkspace.name}</span>
                      {currentWorkspace.isPublic ? (
                        <Globe className="h-3 w-3 text-blue-500" />
                      ) : (
                        <Lock className="h-3 w-3 text-gray-500" />
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0"
                      onClick={() => openWorkspace(currentWorkspace)}
                      title="Open workspace in new tab"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                  {currentWorkspace.description && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {currentWorkspace.description}
                    </p>
                  )}
                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                    <span>{currentWorkspace.notebooks.length} notebooks</span>
                    <span>{currentWorkspace.dashboards.length} dashboards</span>
                    <span>Updated {formatDate(currentWorkspace.updatedAt)}</span>
                  </div>
                </div>
              ) : (
                <div className="p-2 text-sm text-muted-foreground">
                  No data workspace selected
                </div>
              )}
            </div>

            <DropdownMenuSeparator />

            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2">
                Switch Data Workspace
              </div>
              {isLoading ? (
                <div className="text-sm text-muted-foreground p-2">Loading...</div>
              ) : workspaces.length > 0 ? (
                <div className="max-h-48 overflow-y-auto space-y-1">
                  {workspaces.map((workspace) => (
                    <DropdownMenuItem
                      key={workspace.id}
                      onClick={() => onWorkspaceChange(workspace)}
                      className="flex-col items-start p-2 cursor-pointer"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{workspace.name}</span>
                          {workspace.isPublic ? (
                            <Globe className="h-3 w-3 text-blue-500" />
                          ) : (
                            <Lock className="h-3 w-3 text-gray-500" />
                          )}
                        </div>
                        {currentWorkspace?.id === workspace.id && (
                          <Badge variant="secondary" className="text-xs">Current</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                        <span>{workspace.notebooks.length} notebooks</span>
                        <span>{workspace.dashboards.length} dashboards</span>
                        <span>{formatDate(workspace.updatedAt)}</span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground p-2">
                  No data workspaces found
                </div>
              )}
            </div>

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={() => router.push('/hr/chartbuilder/workspaces')}>
              <FolderOpen className="h-4 w-4 mr-2" />
              View All Data Workspaces
            </DropdownMenuItem>

            <DropdownMenuItem onClick={() => setShowCreateModal(true)}>
              <FolderPlus className="h-4 w-4 mr-2" />
              Create New Data Workspace
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Save Button */}
        {currentWorkspace && (
          <Button
            onClick={onSaveToWorkspace}
            size="sm"
            variant="default"
            className="h-6 text-xs px-2"
            disabled={isSaving}
          >
            <Save className="h-3 w-3" />
            <span className="hidden sm:inline ml-1">
              {isSaving ? 'Saving...' : 'Save'}
            </span>
          </Button>
        )}
      </div>

      {/* Create Workspace Modal */}
      <WorkspaceModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onWorkspaceCreated={handleWorkspaceCreated}
      />
    </>
  )
}
