import { DefaultContentLanguage } from './types';

// Safely check for browser environment
export const isBrowser = typeof window !== 'undefined';

/**
 * Check for internet connectivity
 * @returns Promise<boolean> - True if connected, false otherwise
 */
export const checkInternetConnection = async (): Promise<boolean> => {
  if (!isBrowser) return true; // Assume we have connectivity on server

  try {
    // Try to fetch a small resource to check connectivity
    const controller = new AbortController();
    const signal = controller.signal;
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch('/api/ping', {
      method: 'HEAD', // Just get headers, not the body
      cache: 'no-store', // Don't use cached responses
      signal, // Use the abort signal for timeout
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.log('Internet connection check failed:', error);
    return false;
  }
};

/**
 * Check if SQL query has GraphicWalker comment
 * @param code SQL query string
 * @returns boolean
 */
export const hasGraphicWalkerComment = (code: string): boolean => {
  return code.includes("--#graphicwalker") || code.includes("-- #graphicwalker");
};

/**
 * Check if SQL query has loopchart command
 * @param code SQL query string
 * @returns boolean
 */
export const hasLoopchartCommand = (code: string): boolean => {
  return code.includes("--loopchart") || code.includes("-- loopchart");
};

/**
 * Get default content for a cell based on language
 * @param language The programming language
 * @returns Default content string
 */
export const getDefaultContent = (language: DefaultContentLanguage): string => {
  switch (language) {
    case 'python':
      return `# Selected datasets are available using their actual names
# Available libraries: pandas as pd, numpy as np, matplotlib.pyplot as plt, seaborn as sns

# Check what datasets are available
show_datasets()  # This will show you the exact variable names and filenames

# Method 1: Use direct variables (recommended)
# Your datasets are available as variables with their actual names
# Example: if you selected "employees.csv", use: employees
print(f"First dataset shape: {df.shape}")  # df is always your first dataset
print(f"Columns: {df.columns.tolist()}")

# Display first dataset
result = df.head()

# Method 2: Load using pd.read_csv() with actual names
# Example: employees = pd.read_csv('employees.csv')
# The show_datasets() function above will show you the exact names to use

# Example 2: Compare multiple datasets
"""
# First check available datasets
show_datasets()

# Load multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

print(f"df1 has {len(df1)} rows and {len(df1.columns)} columns")
print(f"df2 has {len(df2)} rows and {len(df2.columns)} columns")

# Find common columns
common_cols = set(df1.columns) & set(df2.columns)
print(f"Common columns: {list(common_cols)}")

result = pd.concat([df1.head(), df2.head()], keys=['df1', 'df2'])
"""

# Example 3: Merge datasets
"""
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Merge on common column (adjust column name as needed)
if 'id' in df1.columns and 'id' in df2.columns:
    merged = pd.merge(df1, df2, on='id', how='inner')
    print(f"Merged dataset shape: {merged.shape}")
    result = merged.head()
"""

# Example 2: Analyze multiple datasets (uncomment to use)
"""
# Compare datasets
if 'df1' in locals() and 'df2' in locals():
    print("\\n=== Dataset Comparison ===")
    print(f"df1 has {len(df1)} rows and {len(df1.columns)} columns")
    print(f"df2 has {len(df2)} rows and {len(df2.columns)} columns")
    
    # Find common columns
    common_cols = set(df1.columns) & set(df2.columns)
    print(f"Common columns: {list(common_cols)}")
    
    # Show summary of both datasets
    result = {
        'df1_summary': df1.describe(),
        'df2_summary': df2.describe()
    }
"""

# Example 3: Join/merge multiple datasets (uncomment to use)
"""
# Merge datasets if they have a common key
if 'df1' in locals() and 'df2' in locals():
    # Assuming both datasets have an 'id' column
    if 'id' in df1.columns and 'id' in df2.columns:
        merged_df = pd.merge(df1, df2, on='id', how='inner')
        print(f"Merged dataset shape: {merged_df.shape}")
        result = merged_df.head()
    else:
        print("No common 'id' column found for merging")
        result = "Cannot merge - no common key column"
"""

# Example 4: Concatenate datasets (uncomment to use)
"""
# Combine datasets vertically (same columns)
if 'df1' in locals() and 'df2' in locals():
    # Check if datasets have similar structure
    if set(df1.columns) == set(df2.columns):
        combined_df = pd.concat([df1, df2], ignore_index=True)
        print(f"Combined dataset shape: {combined_df.shape}")
        result = combined_df.head()
    else:
        print("Datasets have different columns, cannot concatenate directly")
        result = "Cannot concatenate - different column structures"
"""

# Example 5: Create plots comparing datasets (uncomment to use)
"""
# Compare numeric columns across datasets
if 'df1' in locals() and 'df2' in locals():
    # Get numeric columns from both datasets
    numeric_cols1 = df1.select_dtypes(include=['number']).columns
    numeric_cols2 = df2.select_dtypes(include=['number']).columns
    
    # Find common numeric columns
    common_numeric = set(numeric_cols1) & set(numeric_cols2)
    
    if common_numeric:
        col_to_plot = list(common_numeric)[0]  # Use first common numeric column
        
        plt.figure(figsize=(12, 6))
        
        # Create side-by-side histograms
        plt.subplot(1, 2, 1)
        plt.hist(df1[col_to_plot].dropna(), bins=20, alpha=0.7, label='Dataset 1')
        plt.title(f'{col_to_plot} - Dataset 1')
        plt.xlabel(col_to_plot)
        plt.ylabel('Frequency')
        
        plt.subplot(1, 2, 2)
        plt.hist(df2[col_to_plot].dropna(), bins=20, alpha=0.7, label='Dataset 2', color='orange')
        plt.title(f'{col_to_plot} - Dataset 2')
        plt.xlabel(col_to_plot)
        plt.ylabel('Frequency')
        
        plt.tight_layout()
        result = get_plot()
    else:
        print("No common numeric columns found for plotting")
        result = "No common numeric columns for comparison"
"""`;
    case 'javascript':
      return `// Dataset is available as "df" with helper methods
// Examples:

// Get first 5 rows
result = df.head()`;
    case 'markdown':
      return `# Markdown Cell

This is a markdown cell where you can write formatted text.

## Features:
- **Bold text** and _italic text_
- Bulleted lists like this one
- Numbered lists
1. First item
2. Second item

## Tables
| Column 1 | Column 2 |
|----------|----------|
| Cell 1   | Cell 2   |

---
Double-click to edit this cell`;
    default:
      return `-- Write your SQL query here
-- Selected datasets are available using their actual names (e.g., employees, sales, etc.)
-- Also available as: dataset1, dataset2, dataset3, etc. for compatibility

-- Basic query - replace 'your_dataset_name' with actual dataset name
SELECT * FROM your_dataset_name LIMIT 5;

-- Example with actual dataset names:
-- SELECT * FROM employees LIMIT 5;
-- SELECT * FROM sales WHERE amount > 1000;

-- Join multiple datasets using actual names
-- SELECT
--   e.id,
--   e.name,
--   s.amount,
--   s.date
-- FROM employees e
-- JOIN sales s ON e.id = s.employee_id;

-- Union data from different datasets
-- SELECT name, salary FROM employees
-- UNION ALL
-- SELECT name, amount FROM contractors;

-- Complex analysis across datasets
-- SELECT
--   e.department,
--   COUNT(*) AS employee_count,
--   AVG(e.salary) AS avg_salary,
--   SUM(s.amount) AS total_sales
-- FROM employees e
-- JOIN sales s ON e.id = s.employee_id
-- GROUP BY e.department
-- ORDER BY total_sales DESC;

-- Add --#graphicwalker comment to open the interactive visual explorer
-- --#graphicwalker
-- SELECT * FROM your_dataset_name;`;
  }
};

/**
 * Initialize alasql-utils module
 * @returns Promise<boolean> - True if initialized successfully
 */
export const initializeAlasqlUtils = async (): Promise<boolean> => {
  if (isBrowser) {
    try {
      const module = await import('@/lib/alasql-utils');
      return true;
    } catch (error) {
      console.error('Failed to load alasql-utils:', error);
      return false;
    }
  }
  return false;
};
