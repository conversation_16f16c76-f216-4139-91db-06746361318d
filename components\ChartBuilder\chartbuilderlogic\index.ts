/**
 * ChartBuilder Logic
 * 
 * This module contains all the logic components for the ChartBuilder.
 * It separates the UI from the business logic for better maintainability.
 */

// Export all hooks
export { useDatasetHandling } from './useDatasetHandling';
export { useCellExecution } from './useCellExecution';
export { useChartSaving } from './useChartSaving';
export { useDashboardInteraction } from './useDashboardInteraction';

// Export utility functions
export {
  checkInternetConnection,
  hasGraphicWalkerComment,
  hasLoopchartCommand,
  getDefaultContent,
  initializeAlasqlUtils,
  isBrowser
} from './chartBuilderUtils';

// Export types
export * from './types';
