import { toast } from 'sonner';
import { Dataset } from '@/types/index';
import { CellData, UseDashboardInteractionReturn } from './types';
import { useDashboardStore } from '@/lib/dashboardStore';

/**
 * Hook for handling dashboard interaction in the ChartBuilder
 * @param setCells Function to update cells state
 * @param cells Current cells state
 * @param datasets Available datasets
 * @param handleSelectDatasets Function to select datasets for a cell
 * @returns Dashboard interaction functions
 */
export const useDashboardInteraction = (
  setCells: React.Dispatch<React.SetStateAction<CellData[]>>,
  cells: CellData[],
  datasets: Dataset[],
  handleSelectDatasets: (cellId: string, datasetIds: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] }>
): UseDashboardInteractionReturn => {
  // Get Zustand store functions
  const { addChart } = useDashboardStore();

  // Handle importing a notebook
  const handleNotebookImport = (importedNotebook: any) => {
    try {
      // Import cells with all their properties
      if (importedNotebook.cells && Array.isArray(importedNotebook.cells)) {
        // Make sure all the necessary cell properties are preserved
        const importedCells = importedNotebook.cells.map((cell: any) => ({
          id: cell.id,
          content: cell.content || '',
          language: cell.language || 'sql',
          result: cell.result,
          error: cell.result?.error || cell.error,
          errorDetails: cell.result?.errorDetails || cell.errorDetails,
          executionTime: cell.result?.executionTime || cell.executionTime,
          isSuccess: cell.isSuccess !== undefined ? cell.isSuccess : (!!cell.result && !cell.result.error),
          showGraphicWalker: cell.showGraphicWalker || false,
          viewMode: cell.viewMode || 'table'
        }));

        setCells(importedCells);
      }

      // Import saved charts
      if (importedNotebook.savedCharts && Array.isArray(importedNotebook.savedCharts)) {
        // Ensure charts have the right format and structure with all required properties
        const processedCharts = importedNotebook.savedCharts.map((chart: any, index: number) => ({
          ...chart,
          chartType: chart.chartType || 'bar',
          createdAt: chart.createdAt instanceof Date ? chart.createdAt : new Date(chart.createdAt),
          size: chart.size || 'medium',
          // Ensure required properties from DashboardSavedChart are present
          type: 'chart',
          gridColumn: chart.gridColumn || 0,
          gridRow: chart.gridRow || (index * 3), // Position charts vertically if no position is specified
          width: chart.width || 4,
          height: chart.height || 3
        }));

        // Add each chart to the Zustand store
        // @ts-ignore
        processedCharts.forEach(chart => {
          addChart(chart);
        });
      }

      // Try to load the datasets if they exist
      if (importedNotebook.datasets && Array.isArray(importedNotebook.datasets)) {
        const datasetIds = importedNotebook.datasets
          .map((ds: any) => ds.id)
          .filter((id: string) => datasets.some(d => d.id === id));

        if (datasetIds.length > 0) {
          // Load the datasets for the first cell
          if (cells.length > 0) {
            const firstCellId = cells[0].id;
            handleSelectDatasets(firstCellId, datasetIds).then(result => {
              if (result && result.datasetIds) {
                // Update the cell's selectedDatasetIds in the parent component
                setCells(prev => prev.map(c =>
                  c.id === firstCellId ? { ...c, selectedDatasetIds: result.datasetIds } : c
                ));
              }
            }).catch(err => {
              console.error('Failed to load datasets:', err);
              toast.error(`Could not load some datasets`);
            });
          }
        } else if (importedNotebook.datasets.length > 0) {
          toast.warning(`None of the imported datasets were found in your available datasets`);
        }
      }
      // Support for legacy notebook format with single dataset
      else if (importedNotebook.dataset && importedNotebook.dataset.id) {
        const datasetId = importedNotebook.dataset.id;
        // Check if dataset exists in available datasets
        const datasetExists = datasets.some(d => d.id === datasetId);

        if (datasetExists) {
          // Load the dataset for the first cell
          if (cells.length > 0) {
            const firstCellId = cells[0].id;
            handleSelectDatasets(firstCellId, [datasetId]).then(result => {
              if (result && result.datasetIds) {
                // Update the cell's selectedDatasetIds in the parent component
                setCells(prev => prev.map(c =>
                  c.id === firstCellId ? { ...c, selectedDatasetIds: result.datasetIds } : c
                ));
              }
            }).catch(err => {
              console.error('Failed to load dataset:', err);
              toast.error(`Could not load dataset: ${importedNotebook.dataset.name}`);
            });
          }
        } else {
          toast.warning(`Dataset "${importedNotebook.dataset.name}" not found in your available datasets`);
        }
      }

      toast.success("Notebook imported successfully");
    } catch (error) {
      console.error('Notebook import error:', error);
      toast.error('Failed to import notebook');
    }
  };

  // Handle importing chart configuration
  const handleImportChartConfig = (cellId: string, config: any) => {
    try {
      // Find the cell
      const cell = cells.find(c => c.id === cellId);
      if (!cell) return;

      // If the config contains data, update the cell with it
      if (config.data && Array.isArray(config.data)) {
        setCells(prev => prev.map(c =>
          c.id === cellId
            ? {
                ...c,
                result: {
                  ...c.result || {},
                  data: config.data,
                },
                isSuccess: true
              }
            : c
        ));

        toast.success("Chart configuration imported successfully");
      }
    } catch (error) {
      console.error('Failed to import chart config:', error);
      toast.error('Failed to import chart configuration');
    }
  };

  return {
    handleNotebookImport,
    handleImportChartConfig
  };
};
