import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Dataset } from '@/types/index';
import { UseDatasetHandlingReturn } from './types';

/**
 * Hook for handling datasets in the ChartBuilder
 * @returns Dataset handling functions and state
 */
export const useDatasetHandling = (): UseDatasetHandlingReturn => {
  // Store all loaded datasets in a cache for quick access
  const [datasetCache, setDatasetCache] = useState<Record<string, Dataset>>({});
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(true);

  // Function to fetch datasets (can be called manually)
  const fetchDatasets = async () => {
      try {
        setIsLoadingDatasets(true);
        console.log('useDatasetHandling: Fetching datasets from API...');

        // Fetch datasets from API (primary endpoint that includes all datasets)
        const datasetsResponse = await fetch('/api/datasets');

        console.log('useDatasetHandling: API response received:', {
          datasetsStatus: datasetsResponse.status
        });

        // Check authentication
        if (datasetsResponse.status === 401) {
          console.warn('useDatasetHandling: Authentication required - user not signed in');
          toast.error('Please sign in to access your datasets');
          setDatasets([]);
          setDatasetCache({});
          return;
        }

        if (!datasetsResponse.ok) {
          console.error(`useDatasetHandling: API Error - Datasets ${datasetsResponse.status}`);

          // Try to get error details
          try {
            const errorText = await datasetsResponse.text();
            console.error('useDatasetHandling: Error details:', errorText);
          } catch (e) {
            console.error('useDatasetHandling: Could not parse error details');
          }

          throw new Error(`Failed to fetch datasets: ${datasetsResponse.status}`);
        }

        const datasetsData = await datasetsResponse.json();

        console.log('useDatasetHandling: API Response received:', {
          datasetsSuccess: datasetsData.success,
          datasetsCount: datasetsData.datasets?.length || 0
        });

        // Get all datasets from the response
        const allDatasets: any[] = [];

        if (datasetsData.success && Array.isArray(datasetsData.datasets)) {
          console.log(`useDatasetHandling: Found ${datasetsData.datasets.length} datasets from /api/datasets`);
          allDatasets.push(...datasetsData.datasets);
        }

        console.log(`useDatasetHandling: Total datasets found: ${allDatasets.length}`);

        if (allDatasets.length > 0) {
          // Transform API data to match our Dataset interface
          const transformedDatasets = allDatasets.map((ds: any) => ({
            id: ds.id,
            name: ds.name,
            data: ds.data || [],
            columns: ds.headers?.map((header: string) => ({ name: header, type: 'string' })) || [],
            headers: ds.headers || [],
            fileType: ds.fileType || 'json',
            createdAt: new Date(ds.createdAt),
            description: ds.description
          }));

          setDatasets(transformedDatasets);
          console.log(`useDatasetHandling: Successfully loaded ${transformedDatasets.length} datasets from database`);
          console.log('useDatasetHandling: Dataset names:', transformedDatasets.map(ds => ds.name));

          // Also populate the cache with real data
          const realCache: Record<string, Dataset> = {};
          transformedDatasets.forEach(ds => {
            realCache[ds.id] = ds;
          });
          setDatasetCache(realCache);

          toast.success(`Loaded ${transformedDatasets.length} datasets from database`);
        } else {
          // When no datasets found, show helpful message
          console.log('useDatasetHandling: No datasets found in API response - showing empty state');
          setDatasets([]);
          setDatasetCache({});
          toast.info('No datasets found in your database. Upload some datasets to get started.');
        }
      } catch (error) {
        console.error('useDatasetHandling: Error fetching datasets:', error);
        setDatasets([]);
        setDatasetCache({});
        toast.error('Failed to load datasets from database. Please check your connection and try again.');
      } finally {
        setIsLoadingDatasets(false);
      }
    };

  // Fetch datasets on component mount
  useEffect(() => {
    fetchDatasets();
  }, []);

  // Handle dataset selection for a specific cell
  const handleSelectDatasets = async (cellId: string, datasetIds: string[]) => {
    try {
      const selectedList: Dataset[] = [];
      const newCache = { ...datasetCache };

      // Fetch each selected dataset
      for (const datasetId of datasetIds) {
        // Check if we already have this dataset in the cache
        if (newCache[datasetId]) {
          selectedList.push(newCache[datasetId]);
          continue;
        }

        // Fetch from API
        const response = await fetch(`/api/datasets?datasetId=${datasetId}`);

        if (response.status === 401) {
          console.warn(`Authentication required for dataset ${datasetId}`);
          toast.error(`Authentication required for dataset ${datasetId}`);
          continue; // Skip this dataset when auth failed
        } else if (!response.ok) {
          console.warn(`Failed to load dataset ${datasetId} from API (${response.status})`);
          toast.error(`Failed to load dataset ${datasetId} from database`);
          continue; // Skip this dataset when API failed
        } else {
          const data = await response.json();
          if (data.success && data.datasetInfo) {
            const dataset: Dataset = {
              id: datasetId,
              name: data.datasetInfo.name,
              data: data.datasetInfo.data,
              columns: data.datasetInfo.headers?.map((header: string) => ({ name: header, type: 'string' })) || [],
              headers: data.datasetInfo.headers,
              fileType: data.datasetInfo.fileType,
              createdAt: new Date(data.datasetInfo.createdAt)
            };
            selectedList.push(dataset);
            newCache[datasetId] = dataset;
          } else {
            console.warn(`Invalid dataset response for ${datasetId}`);
            toast.error(`Invalid response for dataset ${datasetId}`);
            continue; // Skip this dataset when invalid response
          }
        }
      }

      // Update the dataset cache with any new datasets
      setDatasetCache(newCache);

      if (selectedList.length > 0) {
        toast.success(`${selectedList.length} dataset(s) selected for cell`);
      }

      return { selectedList, datasetIds };
    } catch (error) {
      console.error('Failed to load datasets:', error);
      toast.error('Failed to load one or more datasets');
      throw error;
    }
  };

  return {
    datasets,
    datasetCache,
    isLoadingDatasets,
    handleSelectDatasets,
    refreshDatasets: fetchDatasets
  };
};
