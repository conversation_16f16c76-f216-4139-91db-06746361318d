import { useState, useCallback, useRef } from 'react'
import { CellData } from './types'

interface VariableInfo {
  name: string
  type: 'dataframe' | 'variable' | 'plot' | 'result' | 'function'
  value?: any
  definedInCell: string
  lastUpdated: Date
}

interface VariableContext {
  variables: Map<string, VariableInfo>
  cellExecutionOrder: string[]
}

/**
 * Hook for managing global variable context across cells (Jupyter-like functionality)
 */
export const useVariableContext = () => {
  const [context, setContext] = useState<VariableContext>({
    variables: new Map(),
    cellExecutionOrder: []
  })
  
  const executionCountRef = useRef(0)

  // Extract variables from Python code
  const extractVariablesFromCode = useCallback((code: string, cellId: string): VariableInfo[] => {
    const variables: VariableInfo[] = []
    const lines = code.split('\n')
    
    lines.forEach(line => {
      const trimmed = line.trim()
      
      // Skip comments and imports
      if (trimmed.startsWith('#') || trimmed.startsWith('import') || trimmed.startsWith('from')) {
        return
      }
      
      // Look for variable assignments
      const assignmentMatch = trimmed.match(/^(\w+)\s*=/)
      if (assignmentMatch) {
        const varName = assignmentMatch[1]
        let varType: VariableInfo['type'] = 'variable'
        
        // Determine variable type based on assignment
        if (trimmed.includes('pd.read_csv') || trimmed.includes('DataFrame') || trimmed.includes('pd.DataFrame')) {
          varType = 'dataframe'
        } else if (trimmed.includes('plt.') || trimmed.includes('get_plot()') || trimmed.includes('plt.figure')) {
          varType = 'plot'
        } else if (varName === 'result') {
          varType = 'result'
        } else if (trimmed.includes('def ') || trimmed.includes('lambda')) {
          varType = 'function'
        }
        
        variables.push({
          name: varName,
          type: varType,
          definedInCell: cellId,
          lastUpdated: new Date()
        })
      }
    })
    
    return variables
  }, [])

  // Update variable context when a cell is executed
  const updateVariableContext = useCallback((cellId: string, code: string, result?: any) => {
    setContext(prev => {
      const newContext = { ...prev }
      
      // Update execution order
      const existingIndex = newContext.cellExecutionOrder.indexOf(cellId)
      if (existingIndex !== -1) {
        newContext.cellExecutionOrder.splice(existingIndex, 1)
      }
      newContext.cellExecutionOrder.push(cellId)
      
      // Extract and update variables
      const extractedVars = extractVariablesFromCode(code, cellId)
      
      extractedVars.forEach(varInfo => {
        newContext.variables.set(varInfo.name, {
          ...varInfo,
          value: result && varInfo.name === 'result' ? result : undefined
        })
      })
      
      return newContext
    })
    
    executionCountRef.current += 1
  }, [extractVariablesFromCode])

  // Get variables available to a specific cell (based on execution order)
  const getAvailableVariables = useCallback((cellId: string, cells: CellData[]): VariableInfo[] => {
    const cellIndex = cells.findIndex(cell => cell.id === cellId)
    if (cellIndex === -1) return []
    
    const availableVars: VariableInfo[] = []
    const seenVars = new Set<string>()
    
    // Get variables from cells executed before this one (in order)
    for (let i = 0; i < cellIndex; i++) {
      const cell = cells[i]
      if (cell.language === 'python' && cell.result && !cell.error) {
        const cellVars = extractVariablesFromCode(cell.content, cell.id)
        cellVars.forEach(varInfo => {
          if (!seenVars.has(varInfo.name)) {
            availableVars.push(varInfo)
            seenVars.add(varInfo.name)
          }
        })
      }
    }
    
    return availableVars
  }, [extractVariablesFromCode])

  // Get variable dependencies for a cell
  const getVariableDependencies = useCallback((cellId: string, cells: CellData[]) => {
    const cell = cells.find(c => c.id === cellId)
    if (!cell || cell.language !== 'python') return { uses: [], creates: [] }
    
    const creates = extractVariablesFromCode(cell.content, cellId)
    const uses: VariableInfo[] = []
    
    // Find variables used but not defined in this cell
    const lines = cell.content.split('\n')
    const definedInThisCell = new Set(creates.map(v => v.name))
    
    lines.forEach(line => {
      const trimmed = line.trim()
      if (!trimmed.startsWith('#') && !trimmed.startsWith('import') && !trimmed.match(/^(\w+)\s*=/)) {
        const varMatches = trimmed.match(/\b(\w+)\b/g)
        if (varMatches) {
          varMatches.forEach(varName => {
            if (!definedInThisCell.has(varName) && context.variables.has(varName)) {
              const varInfo = context.variables.get(varName)!
              if (!uses.find(v => v.name === varName)) {
                uses.push(varInfo)
              }
            }
          })
        }
      }
    })
    
    return { uses, creates }
  }, [context.variables, extractVariablesFromCode])

  // Generate context for AI assistant
  const generateVariableContextForAI = useCallback((cells: CellData[]) => {
    const variableMap = new Map<string, {
      type: string
      definedIn: number[]
      usedIn: number[]
      lastValue?: any
    }>()
    
    cells.forEach((cell, index) => {
      if (cell.language === 'python' && cell.cellType !== 'markdown') {
        const dependencies = getVariableDependencies(cell.id, cells)
        
        // Track variable creation
        dependencies.creates.forEach(varInfo => {
          if (!variableMap.has(varInfo.name)) {
            variableMap.set(varInfo.name, {
              type: varInfo.type,
              definedIn: [],
              usedIn: [],
              lastValue: varInfo.value
            })
          }
          variableMap.get(varInfo.name)!.definedIn.push(index)
        })
        
        // Track variable usage
        dependencies.uses.forEach(varInfo => {
          if (variableMap.has(varInfo.name)) {
            variableMap.get(varInfo.name)!.usedIn.push(index)
          }
        })
      }
    })
    
    return {
      totalVariables: variableMap.size,
      variableFlow: Object.fromEntries(variableMap),
      executionOrder: context.cellExecutionOrder,
      hasVariableSharing: Array.from(variableMap.values()).some(v => 
        v.definedIn.length > 0 && v.usedIn.length > 0
      )
    }
  }, [context, getVariableDependencies])

  // Clear variable context
  const clearVariableContext = useCallback(() => {
    setContext({
      variables: new Map(),
      cellExecutionOrder: []
    })
    executionCountRef.current = 0
  }, [])

  return {
    context,
    updateVariableContext,
    getAvailableVariables,
    getVariableDependencies,
    generateVariableContextForAI,
    clearVariableContext,
    executionCount: executionCountRef.current
  }
}
