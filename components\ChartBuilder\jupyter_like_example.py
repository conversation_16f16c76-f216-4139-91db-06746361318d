# %% Cell 1: Data Exploration
# This is a Jupyter-like notebook example
print("Dataset shape:", df.shape)
print("Columns:", df.columns.tolist())
print("\nFirst 5 rows:")
result = df.head()

# %% Cell 2: Missing Values Analysis
print("Missing values:")
missing = df.isnull().sum()
print(missing[missing > 0])  # Only show columns with missing values

# %% Cell 3: Data Types and Basic Statistics
print("Data types:")
print(df.dtypes)
print("\nBasic statistics:")
result = df.describe()

# %% Cell 4: Department Distribution
print("Department distribution:")
dept_counts = df['department'].value_counts()
print(dept_counts)

# Create a pie chart of departments
plt.figure(figsize=(10, 6))
plt.pie(dept_counts, labels=dept_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('Employee Distribution by Department')
plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
result = get_plot()

# %% Cell 5: Salary Analysis
# Calculate average salary by department
dept_salary = df.groupby('department')['salary'].mean().sort_values(ascending=False).reset_index()
print("Average salary by department:")
print(dept_salary)

# Create a bar chart
plt.figure(figsize=(12, 6))
sns.barplot(x='department', y='salary', data=dept_salary)
plt.title('Average Salary by Department')
plt.xlabel('Department')
plt.ylabel('Average Salary ($)')
plt.xticks(rotation=45)
plt.tight_layout()
result = get_plot()

# %% Cell 6: Gender Analysis
# Calculate gender distribution by department
gender_dept = pd.crosstab(df['department'], df['gender'])
print("Gender distribution by department:")
print(gender_dept)

# Create a grouped bar chart
plt.figure(figsize=(12, 6))
gender_dept.plot(kind='bar', figsize=(12, 6))
plt.title('Gender Distribution by Department')
plt.xlabel('Department')
plt.ylabel('Count')
plt.legend(title='Gender')
plt.tight_layout()
result = get_plot()

# %% Cell 7: Performance Score Analysis
# Create a histogram of performance scores
plt.figure(figsize=(12, 6))
sns.histplot(df['performance_score'].dropna(), bins=10, kde=True)
plt.title('Distribution of Performance Scores')
plt.xlabel('Performance Score')
plt.ylabel('Frequency')
plt.tight_layout()
result = get_plot()

# %% Cell 8: Correlation Analysis
# Calculate correlation matrix
corr_matrix = df.select_dtypes(include=['number']).corr()
print("Correlation matrix:")
print(corr_matrix)

# Create a heatmap
plt.figure(figsize=(12, 10))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f')
plt.title('Correlation Matrix')
plt.tight_layout()
result = get_plot()

# %% Cell 9: Salary vs. Years of Service
# Create a scatter plot
plt.figure(figsize=(12, 6))
sns.scatterplot(x='years_of_service', y='salary', hue='department', data=df)
plt.title('Salary vs. Years of Service')
plt.xlabel('Years of Service')
plt.ylabel('Salary ($)')
plt.tight_layout()
result = get_plot()

# %% Cell 10: Advanced Analysis - Total Compensation
# Calculate total compensation (salary + bonus)
if 'total_compensation' not in df.columns:
    df['total_compensation'] = df['salary'] + df['bonus']

# Show top 5 employees by total compensation
print("Top 5 employees by total compensation:")
result = df.sort_values('total_compensation', ascending=False)[['name', 'department', 'salary', 'bonus', 'total_compensation']].head()

# %% Cell 11: Location Analysis
# Count employees by location
location_counts = df['location'].value_counts()
print("Employee count by location:")
print(location_counts)

# Create a bar chart
plt.figure(figsize=(12, 6))
sns.barplot(x=location_counts.index, y=location_counts.values)
plt.title('Number of Employees by Location')
plt.xlabel('Location')
plt.ylabel('Count')
plt.xticks(rotation=45)
plt.tight_layout()
result = get_plot()

# %% Cell 12: Education Level Analysis
# Count employees by education level
education_counts = df['education'].value_counts()
print("Employee count by education level:")
print(education_counts)

# Create a pie chart
plt.figure(figsize=(10, 6))
plt.pie(education_counts, labels=education_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('Employee Distribution by Education Level')
plt.axis('equal')
result = get_plot()

# %% Cell 13: Summary Statistics by Department
# Group by department and calculate summary statistics
dept_summary = df.groupby('department').agg({
    'salary': ['mean', 'median', 'min', 'max', 'std'],
    'age': ['mean', 'min', 'max'],
    'performance_score': ['mean'],
    'years_of_service': ['mean']
}).round(2)

print("Summary statistics by department:")
result = dept_summary

# %% Cell 14: Final Insights
print("Key Insights:")
print("1. The", df['department'].value_counts().idxmax(), "department has the most employees.")
print("2. The average salary across all departments is $", df['salary'].mean().round(2))
print("3. The highest average salary is in the", dept_salary.iloc[0]['department'], "department.")
print("4. The average performance score is", df['performance_score'].mean().round(2))
print("5. There are", df['gender'].value_counts()['M'], "male and", df['gender'].value_counts()['F'], "female employees.")

# Create a summary visualization
plt.figure(figsize=(14, 8))

# Create 2x2 subplots
fig, axes = plt.subplots(2, 2, figsize=(14, 10))

# Plot 1: Department distribution
dept_counts.plot(kind='bar', ax=axes[0, 0])
axes[0, 0].set_title('Employees by Department')
axes[0, 0].set_ylabel('Count')
axes[0, 0].tick_params(axis='x', rotation=45)

# Plot 2: Salary by department
sns.barplot(x='department', y='salary', data=dept_salary, ax=axes[0, 1])
axes[0, 1].set_title('Average Salary by Department')
axes[0, 1].set_ylabel('Salary ($)')
axes[0, 1].tick_params(axis='x', rotation=45)

# Plot 3: Gender distribution
df['gender'].value_counts().plot(kind='pie', autopct='%1.1f%%', ax=axes[1, 0])
axes[1, 0].set_title('Gender Distribution')
axes[1, 0].set_ylabel('')

# Plot 4: Education distribution
education_counts.plot(kind='pie', autopct='%1.1f%%', ax=axes[1, 1])
axes[1, 1].set_title('Education Distribution')
axes[1, 1].set_ylabel('')

plt.tight_layout()
result = get_plot()
