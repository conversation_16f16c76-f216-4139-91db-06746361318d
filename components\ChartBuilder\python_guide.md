# Python in ChartBuilder: User Guide

This guide explains how to use Python in the ChartBuilder component to analyze data and create visualizations, similar to Jupyter Notebook.

## Getting Started

When you select Python as the language in a cell, you'll have access to a powerful data analysis environment with pre-loaded libraries and helper functions.

### Available Libraries

The following libraries are automatically imported:

- `pandas` (as `pd`): For data manipulation and analysis
- `numpy` (as `np`): For numerical operations
- `matplotlib.pyplot` (as `plt`): For creating visualizations
- `seaborn` (as `sns`): For advanced statistical visualizations
- Other standard libraries: `os`, `sys`, `io`, `json`, `datetime`, `re`

### Importing Additional Libraries

You can import additional libraries using the standard Python import syntax:

```python
import sklearn
from sklearn.cluster import KMeans

# Or use the custom import function that will automatically install missing packages
statsmodels = import_module('statsmodels')
```

### Accessing Your Data

Your dataset is automatically loaded as a pandas DataFrame named `df`. You can start working with it immediately:

```python
# View the first 5 rows of your data
result = df.head()

# Get basic information about your dataset
print(df.info())

# Get statistical summary of numeric columns
result = df.describe()

# Check available columns
print(f"Available columns: {df.columns.tolist()}")
```

### Reading External Data

You can read external data files using the `read_file` function:

```python
# Read a CSV file
my_data = read_file('path/to/your/file.csv')

# Read other file formats
excel_data = read_file('path/to/your/file.xlsx')
json_data = read_file('path/to/your/file.json')
text_data = read_file('path/to/your/file.txt')
```

## Creating Visualizations

You can create visualizations using matplotlib and seaborn:

```python
# Create a simple bar chart
plt.figure(figsize=(10, 6))
plt.bar(df['department'], df['salary'])
plt.title('Salary by Department')
plt.xlabel('Department')
plt.ylabel('Salary')
plt.xticks(rotation=45)
plt.tight_layout()
result = get_plot()  # Important: Use get_plot() to display the visualization
```

### Simple Plot Example

Here's a simple example that creates a sine wave plot:

```python
import matplotlib.pyplot as plt
import numpy as np

# Create some data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create a plot
plt.figure(figsize=(10, 6))
plt.plot(x, y)
plt.title('Sine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.grid(True)
plt.tight_layout()

# Display the plot - this is required to show the plot
result = get_plot()
```

### Helper Functions for Quick Visualizations

We've included several helper functions to create common visualizations with a single line of code:

```python
# Create a histogram
result = plot_histogram('age', bins=15)

# Create a boxplot
result = plot_boxplot('salary')

# Create a scatter plot
result = plot_scatter('age', 'salary')

# Create a correlation heatmap
result = plot_correlation()

# Create a count plot
result = plot_countplot('department')

# Create a bar plot
result = plot_barplot('department', 'salary')

# Create a line plot
result = plot_lineplot('id', 'salary')
```

## Advanced Data Analysis

The `analyzer` object provides methods for detailed data analysis:

```python
# Get comprehensive dataset analysis
result = analyzer.analyze_dataset(df)

# Analyze a numeric column
result = analyzer.analyze_numeric(df, 'salary')

# Analyze a categorical column
result = analyzer.analyze_categorical(df, 'department')

# Detect outliers in a numeric column
result = analyzer.detect_outliers(df, 'salary', method='iqr')
```

## Package Management

You can check installed packages and install new ones if needed:

```python
# List installed packages
result = pip_list()

# Show details about a specific package
result = pip_show('pandas')

# Install a package (use with caution)
# pip_install('scikit-learn')
```

## Multiple Plots in One Figure

You can create multiple plots in a single figure:

```python
# Create a figure with multiple subplots
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Plot 1: Bar chart
axes[0, 0].bar(df['department'], df['salary'])
axes[0, 0].set_title('Salary by Department')

# Plot 2: Histogram
axes[0, 1].hist(df['age'], bins=10)
axes[0, 1].set_title('Age Distribution')

# Plot 3: Scatter plot
axes[1, 0].scatter(df['age'], df['salary'])
axes[1, 0].set_title('Age vs Salary')

# Plot 4: Pie chart
df['department'].value_counts().plot.pie(autopct='%1.1f%%', ax=axes[1, 1])
axes[1, 1].set_title('Department Distribution')

plt.tight_layout()
result = get_plot()
```

## Theming

You can switch between light and dark themes for your visualizations:

```python
# Set up dark theme for plots
PlotManager.setup_default_style(theme='dark')

# Create a plot with dark theme
plt.figure(figsize=(10, 6))
plt.bar(df['department'], df['salary'], color='skyblue')
plt.title('Salary by Department (Dark Theme)')
plt.tight_layout()
result = get_plot()

# Reset to light theme
PlotManager.setup_default_style(theme='light')
```

## Important Tips

1. Always use `result = ...` to store the final output you want to display
2. For visualizations, always call `result = get_plot()` after creating your plot
3. Use `print()` to display text output in the console
4. If you're creating multiple plots, make sure to call `plt.figure()` before each new plot or use subplots
5. Use `plt.tight_layout()` to ensure your plots are properly displayed

## Using Jupyter-like Cells

You can split your code into multiple cells using the `# %%` marker, just like in Jupyter Notebook:

```python
# %% Cell 1: Data Exploration
print("Dataset shape:", df.shape)
result = df.head()

# %% Cell 2: Missing Values Analysis
print("Missing values:")
print(df.isnull().sum())

# %% Cell 3: Statistical Summary
print("Statistical summary:")
print(df.describe())

# %% Cell 4: Visualization
plt.figure(figsize=(12, 6))
sns.histplot(df['age'], kde=True)
plt.title('Age Distribution')
plt.xlabel('Age')
plt.ylabel('Frequency')
result = get_plot()
```

Each cell will be executed separately, and the output from each cell will be displayed. The `result` from the last cell will be used as the final result.

## Package Management

You can check installed packages and install new ones if needed:

```python
# List installed packages
result = pip_list()

# Show details about a specific package
result = pip_show('pandas')

# Install a package using the import_module function
sklearn = import_module('sklearn')
```

## Example Workflow

Here's a complete example of a data analysis workflow:

```python
# 1. Explore the data
print("Dataset shape:", df.shape)
print("Columns:", df.columns.tolist())
result = df.head()

# 2. Check for missing values
print("Missing values:")
print(df.isnull().sum())

# 3. Get statistical summary
print("Statistical summary:")
print(df.describe())

# 4. Create a visualization
plt.figure(figsize=(12, 6))
sns.histplot(df['age'], kde=True)
plt.title('Age Distribution')
plt.xlabel('Age')
plt.ylabel('Frequency')
result = get_plot()

# 5. Advanced analysis
# Group by department and calculate average salary
dept_salary = df.groupby('department')['salary'].mean().reset_index()
print("Average salary by department:")
print(dept_salary)

# 6. Create another visualization
plt.figure(figsize=(12, 6))
sns.barplot(x='department', y='salary', data=dept_salary)
plt.title('Average Salary by Department')
plt.xlabel('Department')
plt.ylabel('Average Salary')
plt.xticks(rotation=45)
result = get_plot()
```

For more examples, check the `python_examples.txt` file in the ChartBuilder component.

but make sure it run python backend and show plots and other functionality that online has
enhance the Python offline mode to better handle plots and data analysis