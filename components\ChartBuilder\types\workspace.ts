// Shared workspace types for consistency across components

export interface WorkspaceNotebook {
  id: string
  name: string
  description?: string
  cells?: NotebookCell[]
  cellOrder?: string[]
  settings?: any
  createdAt: string
  updatedAt: string
  _count?: { cells: number }
}

export interface WorkspaceDashboard {
  id: string
  name: string
  description?: string
  items?: DashboardItem[]
  layout?: any
  settings?: any
  createdAt: string
  updatedAt: string
  _count?: { items: number }
}

export interface NotebookCell {
  id: string
  cellType: string
  language?: string
  content: string
  result?: any
  error?: string
  errorDetails?: any
  executionTime?: number
  isSuccess: boolean
  selectedDatasetIds: string[]
  notes?: string
  order: number
  createdAt: string
  updatedAt: string
}

export interface DashboardItem {
  id: string
  type: string
  title?: string
  description?: string
  data?: any
  config?: any
  content?: string
  gridColumn: number
  gridRow: number
  width: number
  height: number
  sourceNotebookId?: string
  sourceCellId?: string
  createdAt: string
  updatedAt: string
}

export interface Workspace {
  id: string
  name: string
  description?: string
  isPublic: boolean
  publicId?: string
  settings?: any
  notebooks: WorkspaceNotebook[]
  dashboards: WorkspaceDashboard[]
  createdAt: string
  updatedAt: string
}

export interface WorkspaceSelectorProps {
  currentWorkspace?: Workspace | null
  onWorkspaceChange: (workspace: Workspace) => void
  onSaveToWorkspace: () => void
  isSaving?: boolean
}

export interface WorkspaceModalProps {
  isOpen: boolean
  onClose: () => void
  onWorkspaceCreated: (workspace: Workspace) => void
}

// Cell types for consistency
export type CellType = 'code' | 'markdown'
export type CellLanguage = 'sql' | 'python' | 'javascript' | 'markdown'

// Function types for consistency
export type AddCellFunction = (afterCellId?: string, cellType?: CellType) => void
export type ImportFunction = (data: any) => void
