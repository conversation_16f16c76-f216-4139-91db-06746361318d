'use client'

import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie<PERSON>hart,
  AreaChart,
  Radar,
  Gauge,
  BarChart3,
  BarChartHorizontal,
  Grid3X3,
  Network
} from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { RiBubbleChartFill } from 'react-icons/ri';

// Chart types for correlation analysis
export type ChartType = 'bar' | 'line' | 'scatter' | 'pie' | 'area' | 'radar' | 'gauge' | 'bubble' | 'stacked-bar' | 'horizontal-bar' | 'heatmap' | 'network';

interface ChartTypeSelectorProps {
  selectedTypes: ChartType[];
  onSelectType: (type: ChartType) => void;
  onClearAll: () => void;
  mode?: 'compare' | 'matrix';
}

export const ChartTypeSelector: React.FC<ChartTypeSelectorProps> = ({
  selectedTypes,
  onSelectType,
  onClearAll,
  mode = 'compare'
}) => {
  // Chart type options
  const chartTypeOptions: Array<{
    type: ChartType;
    icon: React.ReactNode;
    label: string;
    description: string;
  }> = [
    {
      type: 'bar',
      icon: <BarChart className="h-4 w-4" />,
      label: 'Bar',
      description: 'Compare values with bars showing differences between datasets'
    },
    {
      type: 'line',
      icon: <LineChart className="h-4 w-4" />,
      label: 'Line',
      description: 'View trends over time with connected data points'
    },
    {
      type: 'area',
      icon: <AreaChart className="h-4 w-4" />,
      label: 'Area',
      description: 'Visualize cumulative totals across categories'
    },
    {
      type: 'scatter',
      icon: <ScatterChart className="h-4 w-4" />,
      label: 'Scatter',
      description: 'See individual data points to identify patterns and outliers'
    },
    {
      type: 'pie',
      icon: <PieChart className="h-4 w-4" />,
      label: 'Pie',
      description: 'Show proportional distribution of values'
    },
    {
      type: 'radar',
      icon: <Radar className="h-4 w-4" />,
      label: 'Radar',
      description: 'Compare multiple variables in a radial format'
    },
    {
      type: 'bubble',
      icon: <RiBubbleChartFill className="h-4 w-4" />,
      label: 'Bubble',
      description: 'Display three dimensions of data using size, position, and color'
    },
    {
      type: 'stacked-bar',
      icon: <BarChart3 className="h-4 w-4" />,
      label: 'Stacked',
      description: 'Show part-to-whole relationships with stacked bars'
    },
    {
      type: 'horizontal-bar',
      icon: <BarChartHorizontal className="h-4 w-4" />,
      label: 'Horizontal',
      description: 'Compare values with horizontal bars for better readability with long labels'
    },
    {
      type: 'heatmap',
      icon: <Grid3X3 className="h-4 w-4" />,
      label: 'Heatmap',
      description: 'View correlation strength with color intensity in a matrix format'
    },
    {
      type: 'network',
      icon: <Network className="h-4 w-4" />,
      label: 'Network',
      description: 'See relationships between variables as a connected network graph'
    },

  ];

  // Filter chart types based on mode
  const filteredChartTypes = chartTypeOptions.filter(chartType => {
    if (mode === 'compare') {
      // Exclude matrix-specific chart types in compare mode
      return !['heatmap', 'network'].includes(chartType.type);
    } else {
      // Exclude compare-specific chart types in matrix mode
      return ['heatmap', 'network'].includes(chartType.type);
    }
  });

  return (
    <div className="flex flex-col w-full gap-2 mb-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium">
          {mode === 'compare' ? 'Dataset Comparison Charts' : 'Column Correlation Charts'}
        </h3>
        {selectedTypes.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAll}
            className="h-7 px-2 text-xs"
          >
            Clear All
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        <TooltipProvider>
          {filteredChartTypes.map((chartType) => (
            <Tooltip key={chartType.type}>
              <TooltipTrigger asChild>
                <Button
                  variant={selectedTypes.includes(chartType.type) ? "default" : "outline"}
                  size="sm"
                  className={cn(
                    "flex items-center gap-1 h-8 px-2",
                    selectedTypes.includes(chartType.type) ? "bg-primary text-primary-foreground" : ""
                  )}
                  onClick={() => onSelectType(chartType.type)}
                >
                  {chartType.icon}
                  <span className="text-xs">{chartType.label}</span>
                  {selectedTypes.includes(chartType.type) && (
                    <Badge variant="outline" className="ml-1 h-4 px-1 text-[10px] bg-primary/20 border-primary/30">
                      {selectedTypes.indexOf(chartType.type) + 1}
                    </Badge>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">{chartType.description}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </TooltipProvider>
      </div>

      {selectedTypes.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          <p className="text-xs text-muted-foreground">Selected charts:</p>
          {selectedTypes.map((type, index) => {
            const chartType = chartTypeOptions.find(ct => ct.type === type);
            if (!chartType) return null;
            return (
              <Badge key={type} variant="secondary" className="text-[10px]">
                {index + 1}. {chartType.label}
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
};
