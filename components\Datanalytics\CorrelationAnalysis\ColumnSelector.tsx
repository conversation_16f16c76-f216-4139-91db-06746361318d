'use client'

import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface ColumnSelectorProps {
  headers: string[];
  selectedColumn: string | null;
  onSelectColumn: (column: string) => void;
}

export const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  headers,
  selectedColumn,
  onSelectColumn
}) => {
  const [open, setOpen] = useState(false);
  const [numericHeaders, setNumericHeaders] = useState<string[]>([]);

  // Filter headers to only include numeric columns
  useEffect(() => {
    // Make sure headers is defined and is an array
    if (headers && Array.isArray(headers)) {
      setNumericHeaders(headers);
    } else {
      // If headers is undefined or not an array, set to empty array
      setNumericHeaders([]);
    }
  }, [headers]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="justify-between"
        >
          {selectedColumn || "Select column..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search columns..." />
          <CommandEmpty>No columns found.</CommandEmpty>
          <CommandGroup>
            <ScrollArea className="h-[200px]">
              {numericHeaders && numericHeaders.length > 0 ? (
                numericHeaders.map((header) => (
                  <CommandItem
                    key={header}
                    value={header}
                    onSelect={() => {
                      onSelectColumn(header);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedColumn === header ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {header}
                  </CommandItem>
                ))
              ) : (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  No columns available. Please select datasets first.
                </div>
              )}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
