'use client'

import React, { useState, useEffect, useRef } from 'react';
import { SimpleDatasetSelector } from './SimpleDatasetSelector';
import { SimpleColumnSelector } from './SimpleColumnSelector';
import { MultiColumnSelector } from './MultiColumnSelector';
import { CorrelationChartNew } from './CorrelationChartNew';
import { CorrelationTable } from './CorrelationTable';
import { ChartTypeSelector, ChartType } from './ChartTypeSelector';
import {
  Loader2,
  TrendingUp,
  BarChart,
  ArrowLeftRight,
  Grid2X2,
  Table as TableIcon,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface SavedDataset {
  id: string;
  name: string;
  description?: string;
  fileType: string;
  createdAt: string;
  data: any[];
  headers: string[];
  folderId?: string | null;
}

interface CorrelationAnalysisProps {
  savedDatasets: SavedDataset[];
  isLoading?: boolean;
}

export const CorrelationAnalysis: React.FC<CorrelationAnalysisProps> = ({
  savedDatasets,
  isLoading = false
}) => {
  // Mode selection: 'compare' for comparing datasets, 'matrix' for correlation matrix
  const [analysisMode, setAnalysisMode] = useState<'compare' | 'matrix'>('compare');

  // Chart type selection for compare mode
  const [selectedChartTypes, setSelectedChartTypes] = useState<ChartType[]>(['bar']);

  // Chart type selection for matrix mode
  const [selectedMatrixChartTypes, setSelectedMatrixChartTypes] = useState<ChartType[]>(['heatmap']);

  // Toggle for showing/hiding the table
  const [showTable, setShowTable] = useState(true);

  // For dataset comparison mode
  const [selectedDatasetIds, setSelectedDatasetIds] = useState<string[]>([]);
  const [selectedColumn, setSelectedColumn] = useState<string | null>(null);
  const [selectedAllColumns, setSelectedAllColumns] = useState<boolean>(false);
  const [selectedDatasets, setSelectedDatasets] = useState<SavedDataset[]>([]);
  const [commonHeaders, setCommonHeaders] = useState<string[]>([]);

  // For correlation matrix mode
  const [selectedSingleDatasetId, setSelectedSingleDatasetId] = useState<string | null>(null);
  const [selectedSingleDataset, setSelectedSingleDataset] = useState<SavedDataset | null>(null);
  const [selectedMatrixColumns, setSelectedMatrixColumns] = useState<string[]>([]);
  const [selectedMatrixColumn, setSelectedMatrixColumn] = useState<string | null>(null);
  const [numericHeaders, setNumericHeaders] = useState<string[]>([]);

  // Handle chart type selection for compare mode
  const handleChartTypeSelect = (type: ChartType) => {
    setSelectedChartTypes(prev => {
      // If already selected, remove it (unless it's the only one)
      if (prev.includes(type) && prev.length > 1) {
        return prev.filter(t => t !== type);
      }

      // If not selected, add it (up to a maximum of 3)
      if (!prev.includes(type) && prev.length < 3) {
        return [...prev, type];
      }

      return prev;
    });
  };

  // Clear all selected chart types and reset to default for compare mode
  const handleClearChartTypes = () => {
    setSelectedChartTypes(['bar']);
  };

  // Handle chart type selection for matrix mode
  const handleMatrixChartTypeSelect = (type: ChartType) => {
    setSelectedMatrixChartTypes(prev => {
      // If already selected, remove it (unless it's the only one)
      if (prev.includes(type) && prev.length > 1) {
        return prev.filter(t => t !== type);
      }

      // If not selected, add it (up to a maximum of 3)
      if (!prev.includes(type) && prev.length < 3) {
        return [...prev, type];
      }

      return prev;
    });
  };

  // Clear all selected chart types and reset to default for matrix mode
  const handleClearMatrixChartTypes = () => {
    setSelectedMatrixChartTypes(['heatmap']);
  };

  // Common state
  const [tableHeight, setTableHeight] = useState(300);
  const resizeRef = useRef<HTMLDivElement>(null);
  const startYRef = useRef(0);
  const startHeightRef = useRef(0);

  // Handle resize functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    startYRef.current = e.clientY;
    startHeightRef.current = tableHeight;

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e: MouseEvent) => {
    const deltaY = startYRef.current - e.clientY;
    const newHeight = Math.max(100, Math.min(600, startHeightRef.current + deltaY));
    setTableHeight(newHeight);
  };

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // Update selected datasets when IDs change (for compare mode)
  useEffect(() => {
    const datasets = savedDatasets.filter(dataset =>
      selectedDatasetIds.includes(dataset.id)
    );
    setSelectedDatasets(datasets);

    // Reset selected column and selectedAllColumns when datasets change
    setSelectedColumn(null);
    setSelectedAllColumns(false);
  }, [savedDatasets, selectedDatasetIds]);

  // Update selected single dataset when ID changes (for matrix mode)
  useEffect(() => {
    if (selectedSingleDatasetId) {
      const dataset = savedDatasets.find(dataset => dataset.id === selectedSingleDatasetId);
      setSelectedSingleDataset(dataset || null);

      // Reset selected matrix columns
      setSelectedMatrixColumns([]);
      setSelectedMatrixColumn(null);

      // Find numeric columns in the dataset
      if (dataset && dataset.data && dataset.data.length > 0) {
        const headers = dataset.headers || [];

        // Filter to only include numeric columns
        const numericCols = headers.filter(header => {
          // Check if this column contains numeric data
          if (!dataset.data || !Array.isArray(dataset.data) || dataset.data.length === 0) {
            return false;
          }

          // Get a sample of values from this column
          const sampleValues = dataset.data
            .slice(0, Math.min(10, dataset.data.length))
            .map(row => row && typeof row === 'object' ? row[header] : null)
            .filter(val => val !== undefined);

          // Check if at least one value is numeric
          return sampleValues.some(value =>
            value !== null && value !== '' && !isNaN(Number(value))
          );
        });

        setNumericHeaders(numericCols);
      } else {
        setNumericHeaders([]);
      }
    } else {
      setSelectedSingleDataset(null);
      setNumericHeaders([]);
      setSelectedMatrixColumn(null);
    }
  }, [savedDatasets, selectedSingleDatasetId]);

  // Update selectedMatrixColumn when selectedMatrixColumns changes
  useEffect(() => {
    if (selectedMatrixColumns.length > 0) {
      // If current selection is not in the list, select the first column
      if (!selectedMatrixColumn || !selectedMatrixColumns.includes(selectedMatrixColumn)) {
        setSelectedMatrixColumn(selectedMatrixColumns[0]);
      }
    } else {
      setSelectedMatrixColumn(null);
    }
  }, [selectedMatrixColumns, selectedMatrixColumn]);

  // Find common headers among selected datasets
  useEffect(() => {
    if (selectedDatasets.length === 0) {
      setCommonHeaders([]);
      return;
    }

    try {
      // Get headers from first dataset
      const firstDataset = selectedDatasets[0];

      // Check if headers exist and are an array
      if (!firstDataset.headers || !Array.isArray(firstDataset.headers)) {
        console.error('Headers are missing or not an array in the first dataset');
        setCommonHeaders([]);
        return;
      }

      const firstDatasetHeaders = firstDataset.headers;

      // Find headers that exist in all selected datasets
      const common = firstDatasetHeaders.filter(header =>
        selectedDatasets.every(dataset =>
          dataset.headers && Array.isArray(dataset.headers) && dataset.headers.includes(header)
        )
      );

      // Filter to only include numeric columns
      const numericHeaders = common.filter(header => {
        // Check if this column contains numeric data in all datasets
        return selectedDatasets.every(dataset => {
          if (!dataset.data || !Array.isArray(dataset.data) || dataset.data.length === 0) {
            return false;
          }

          // Get a sample of values from this column
          const sampleValues = dataset.data
            .slice(0, Math.min(10, dataset.data.length))
            .map(row => row && typeof row === 'object' ? row[header] : null)
            .filter(val => val !== undefined);

          // Check if at least one value is numeric
          return sampleValues.some(value =>
            value !== null && value !== '' && !isNaN(Number(value))
          );
        });
      });

      setCommonHeaders(numericHeaders);
    } catch (error) {
      console.error('Error finding common headers:', error);
      setCommonHeaders([]);
    }
  }, [selectedDatasets]);

  // Combine data from all selected datasets for table view
  const combinedData = selectedDatasets.flatMap(dataset => {
    // Check if dataset has data and it's an array
    if (!dataset.data || !Array.isArray(dataset.data)) {
      return [];
    }

    // Add dataset name to each row
    return dataset.data.map(row => {
      if (!row || typeof row !== 'object') {
        return {
          __dataset_name: dataset.name,
          __dataset_date: new Date(dataset.createdAt).toLocaleDateString()
        };
      }

      return {
        ...row,
        __dataset_name: dataset.name,
        __dataset_date: new Date(dataset.createdAt).toLocaleDateString()
      };
    });
  });

  // Add dataset name and date to headers for table view
  const combinedHeaders = Array.isArray(commonHeaders) && commonHeaders.length > 0
    ? ['__dataset_name', '__dataset_date', ...commonHeaders]
    : ['__dataset_name', '__dataset_date'];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold text-foreground">Correlation Analysis</h2>
          </div>
          <p className="text-sm text-muted-foreground">
            Analyze correlations between datasets or within a single dataset
          </p>
        </div>

        <div className="p-4">
          <Tabs defaultValue="compare" className="mb-6" onValueChange={(value) => setAnalysisMode(value as 'compare' | 'matrix')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="compare" className="flex items-center gap-2">
                <ArrowLeftRight className="h-4 w-4" />
                <span>Compare Datasets</span>
              </TabsTrigger>
              <TabsTrigger value="matrix" className="flex items-center gap-2">
                <Grid2X2 className="h-4 w-4" />
                <span>Column Correlation</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="compare" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block text-foreground">Select Datasets</label>
                  <SimpleDatasetSelector
                    datasets={savedDatasets}
                    selectedDatasetIds={selectedDatasetIds}
                    onSelectDatasets={setSelectedDatasetIds}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block text-foreground">Select Column to Analyze</label>
                  <SimpleColumnSelector
                    headers={commonHeaders}
                    selectedColumn={selectedColumn}
                    onSelectColumn={setSelectedColumn}
                    onSelectAllColumns={(headers) => {
                      if (headers.length > 0) {
                        // Select the first column for the chart
                        setSelectedColumn(headers[0]);
                        setSelectedAllColumns(true);
                        console.log('Selected all columns:', headers);
                      }
                    }}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="matrix" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block text-foreground">Select Dataset</label>
                  <SimpleDatasetSelector
                    datasets={savedDatasets}
                    selectedDatasetIds={selectedSingleDatasetId ? [selectedSingleDatasetId] : []}
                    onSelectDatasets={(ids) => setSelectedSingleDatasetId(ids.length > 0 ? ids[0] : null)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block text-foreground">Select Columns to Analyze</label>
                  <MultiColumnSelector
                    headers={numericHeaders}
                    selectedColumns={selectedMatrixColumns}
                    onSelectColumns={setSelectedMatrixColumns}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Chart Type Selector */}
          {(analysisMode === 'compare' && selectedDatasets.length > 0 && selectedColumn) ||
           (analysisMode === 'matrix' && selectedSingleDataset && selectedMatrixColumns.length >= 2) ? (
            <div className="mb-6">
              <ChartTypeSelector
                selectedTypes={analysisMode === 'compare' ? selectedChartTypes : selectedMatrixChartTypes}
                onSelectType={analysisMode === 'compare' ? handleChartTypeSelect : handleMatrixChartTypeSelect}
                onClearAll={analysisMode === 'compare' ? handleClearChartTypes : handleClearMatrixChartTypes}
                mode={analysisMode}
              />
            </div>
          ) : null}

          {/* Chart Section */}
          <div className="mb-6">
            {analysisMode === 'compare' ? (
              <>
                <CorrelationChartNew
                  datasets={selectedDatasets}
                  selectedColumn={selectedColumn}
                  isLoading={isLoading}
                  chartTypes={selectedChartTypes}
                  mode="compare"
                />

                {/* Display message if all columns are selected in compare mode */}
                {selectedAllColumns && commonHeaders.length > 1 && (
                  <div className="mt-4 p-3 bg-primary/10 text-primary rounded-md border border-primary/20 dark:bg-primary/5">
                    <p className="text-sm">
                      <strong>All columns selected.</strong> The chart is showing data for "{selectedColumn}".
                      All columns will be included in the table below.
                    </p>
                  </div>
                )}
              </>
            ) : (
              <>
                {selectedSingleDataset && selectedMatrixColumns.length >= 2 ? (
                  <>
                    {/* Column selector for matrix mode */}
                    <div className="mb-4">
                      <label className="text-sm font-medium mb-2 block text-foreground">Select Column to Visualize</label>
                      <div className="flex flex-wrap gap-2">
                        {selectedMatrixColumns.map(column => (
                          <Button
                            key={column}
                            variant={selectedMatrixColumn === column ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSelectedMatrixColumn(column)}
                            className="text-xs"
                          >
                            {column}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <CorrelationChartNew
                      datasets={[selectedSingleDataset]}
                      selectedColumn={selectedMatrixColumn}
                      isLoading={isLoading}
                      chartTypes={selectedMatrixChartTypes}
                      mode="matrix"
                    />
                    <div className="mt-4 p-3 bg-primary/10 text-primary rounded-md border border-primary/20 dark:bg-primary/5">
                      <p className="text-sm">
                        <strong>Showing data for "{selectedMatrixColumn}".</strong> You've selected {selectedMatrixColumns.length} columns.
                        Use the chart type selector above to change visualization.
                      </p>
                    </div>
                  </>
                ) : (
                  <div className="bg-card rounded-lg border shadow-sm p-4">
                    <div className="flex items-center justify-center h-[400px]">
                      <div className="text-center p-4">
                        <h3 className="text-lg font-medium mb-2 text-foreground">Column Correlation</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Select a dataset and columns to analyze correlations between different columns.
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Please select at least 2 columns to analyze correlations
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Table Toggle Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTable(!showTable)}
            className="mb-4 w-full flex items-center justify-center gap-2 h-10 border-dashed"
          >
            <TableIcon className="h-4 w-4" />
            <span>{showTable ? 'Hide Data Table' : 'Show Data Table'}</span>
            {showTable ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>

          {/* Table Section */}
          {showTable && (
            <>
              {/* Resizable Divider */}
              <div
                ref={resizeRef}
                className="h-2 bg-muted cursor-ns-resize mb-4 flex items-center justify-center hover:bg-muted/80 transition-colors rounded-full"
                onMouseDown={handleMouseDown}
              >
                <div className="w-16 h-1 bg-muted-foreground/40 rounded-full"></div>
              </div>

              <div className="mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <TableIcon className="h-5 w-5 text-primary" />
                    <span>Data Table</span>
                  </h3>
                </div>

                <div style={{ height: `${tableHeight}px` }} className="overflow-hidden flex flex-col">
                  {analysisMode === 'compare' ? (
                    // Compare mode table
                    selectedDatasets.length > 0 ? (
                      <div className="flex-1 flex flex-col">
                        {/* Use our custom table for better display of selected columns */}
                        <CorrelationTable
                          data={combinedData}
                          headers={combinedHeaders}
                          selectedColumn={selectedColumn}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-card rounded-lg border">
                        <div className="text-center p-4">
                          <h3 className="text-lg font-medium mb-2 text-foreground">No Datasets Selected</h3>
                          <p className="text-sm text-muted-foreground">
                            Please select at least one dataset to view the data
                          </p>
                        </div>
                      </div>
                    )
                  ) : (
                    // Matrix mode table
                    selectedSingleDataset ? (
                      <div className="flex-1 flex flex-col">
                        <CorrelationTable
                          data={selectedSingleDataset.data || []}
                          headers={selectedMatrixColumns.length > 0 ? selectedMatrixColumns : selectedSingleDataset.headers || []}
                          selectedColumn={null}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-card rounded-lg border">
                        <div className="text-center p-4">
                          <h3 className="text-lg font-medium mb-2 text-foreground">No Dataset Selected</h3>
                          <p className="text-sm text-muted-foreground">
                            Please select a dataset to view the data
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
