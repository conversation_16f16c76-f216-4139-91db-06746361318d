'use client'

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Loader2 } from 'lucide-react';

interface CorrelationChartProps {
  datasets: Array<{
    id: string;
    name: string;
    data: any[];
    headers: string[];
    createdAt: string;
  }>;
  selectedColumn: string | null;
  isLoading?: boolean;
}

export const CorrelationChart: React.FC<CorrelationChartProps> = ({
  datasets,
  selectedColumn,
  isLoading = false
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    // Initialize chart
    if (chartRef.current) {
      console.log('Initializing chart, chartRef exists');

      // Check if chart already exists and dispose it
      if (chartInstance.current) {
        console.log('Disposing existing chart instance');
        chartInstance.current.dispose();
      }

      try {
        // Initialize with auto-detection of theme based on CSS variables
        const isDarkMode = document.documentElement.classList.contains('dark');

        // Make sure the chart container has dimensions
        if (chartRef.current.clientWidth === 0 || chartRef.current.clientHeight === 0) {
          console.warn('Chart container has zero dimensions:', {
            width: chartRef.current.clientWidth,
            height: chartRef.current.clientHeight
          });

          // Force dimensions if needed
          chartRef.current.style.width = '100%';
          chartRef.current.style.height = '400px';
        }

        chartInstance.current = echarts.init(chartRef.current, isDarkMode ? 'dark' : undefined);
        console.log('Chart initialized with theme:', isDarkMode ? 'dark' : 'light');
      } catch (error) {
        console.error('Error initializing chart:', error);
      }

      // Handle resize
      const handleResize = () => {
        if (chartInstance.current) {
          console.log('Resizing chart');
          chartInstance.current.resize();
        }
      };

      // Handle theme change
      const handleThemeChange = () => {
        const newIsDarkMode = document.documentElement.classList.contains('dark');
        if (chartInstance.current) {
          chartInstance.current.dispose();
          chartInstance.current = echarts.init(chartRef.current, newIsDarkMode ? 'dark' : undefined);
          // Re-render chart with current data
          if (selectedColumn && datasets.length >= 2) {
            renderChart();
          }
        }
      };

      window.addEventListener('resize', handleResize);
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', handleThemeChange);
        if (chartInstance.current) {
          chartInstance.current.dispose();
          chartInstance.current = null;
        }
      };
    }
  }, []);

  // Function to render the chart
  const renderChart = () => {
    if (!chartInstance.current || !selectedColumn || !datasets || datasets.length < 2) {
      console.log('Cannot render chart:', {
        hasChartInstance: !!chartInstance.current,
        selectedColumn,
        datasetsLength: datasets?.length
      });
      return;
    }

    try {
      console.log('Rendering chart with datasets:', datasets.length, 'and column:', selectedColumn);

      // Sort datasets by createdAt date
      const sortedDatasets = [...datasets].sort((a, b) => {
        const dateA = new Date(a.createdAt || 0).getTime();
        const dateB = new Date(b.createdAt || 0).getTime();
        return dateA - dateB;
      });

      console.log('Sorted datasets:', sortedDatasets.map(d => d.name));

      // Extract dates and values for the selected column
      const dates = sortedDatasets.map(dataset => {
        const date = new Date(dataset.createdAt || new Date());
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      });

      console.log('Extracted dates:', dates);

      const values = sortedDatasets.map(dataset => {
        // Calculate average value for the selected column
        if (!dataset.data || !Array.isArray(dataset.data) || dataset.data.length === 0) {
          console.warn('Dataset has no data:', dataset.name);
          return 0;
        }

        // Log the first few rows to debug
        console.log('Sample data rows for', dataset.name, ':',
          dataset.data.slice(0, 3).map(row => row && typeof row === 'object' ? row[selectedColumn] : null)
        );

        const columnValues = dataset.data
          .filter(row => row && typeof row === 'object')
          .map(row => {
            const value = row[selectedColumn];
            return !isNaN(Number(value)) ? Number(value) : null;
          })
          .filter((val): val is number => val !== null && val !== undefined && !isNaN(val));

        if (columnValues.length === 0) {
          console.warn('No numeric values found for column:', selectedColumn);
          return 0;
        }

        return columnValues.reduce((sum, val) => sum + val, 0) / columnValues.length;
      });

      console.log('Calculated values:', values);

      // Calculate month-over-month differences
      const differences = [];
      for (let i = 1; i < values.length; i++) {
        differences.push({
          value: values[i] - values[i-1],
          itemStyle: {
            color: values[i] > values[i-1] ? '#10b981' : '#ef4444'
          }
        });
      }

      console.log('Calculated differences:', differences);

      // Get theme colors based on current mode
      const isDarkMode = document.documentElement.classList.contains('dark');
      const textColor = isDarkMode ? '#e5e7eb' : '#374151';
      const axisLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';

      // Configure chart options
      const option = {
        backgroundColor: 'transparent',
        title: {
          text: `Monthly Trend Analysis: ${selectedColumn}`,
          left: 'center',
          textStyle: {
            color: textColor
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params: any) {
            if (!params || !params[0]) return '';

            const dataIndex = params[0].dataIndex;
            if (dataIndex >= dates.length - 1 || dataIndex < 0) return '';

            const date = dates[dataIndex + 1]; // +1 because differences start from the second dataset
            const value = params[0].value;
            const prevValue = values[dataIndex];
            const currValue = values[dataIndex + 1];

            // Avoid division by zero
            const percentChange = prevValue !== 0
              ? ((currValue - prevValue) / Math.abs(prevValue)) * 100
              : currValue > 0 ? 100 : currValue < 0 ? -100 : 0;

            return `
              <div style="font-weight: bold; margin-bottom: 5px;">${date}</div>
              <div>Change: ${Number(value).toFixed(2)}</div>
              <div>From: ${Number(prevValue).toFixed(2)} To: ${Number(currValue).toFixed(2)}</div>
              <div>Percent Change: ${Number(percentChange).toFixed(2)}%</div>
            `;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates.slice(1).map((date, i) => `${dates[i]} → ${date}`),
          axisLabel: {
            interval: 0,
            rotate: 45,
            color: textColor
          },
          axisLine: {
            lineStyle: {
              color: axisLineColor
            }
          }
        },
        yAxis: {
          type: 'value',
          name: 'Change',
          nameTextStyle: {
            color: textColor
          },
          axisLabel: {
            formatter: '{value}',
            color: textColor
          },
          axisLine: {
            lineStyle: {
              color: axisLineColor
            }
          },
          splitLine: {
            lineStyle: {
              color: axisLineColor
            }
          }
        },
        series: [
          {
            name: 'Month-over-Month Change',
            type: 'bar',
            data: differences,
            label: {
              show: true,
              position: 'top',
              formatter: function(params: any) {
                return Number(params.value).toFixed(2);
              },
              color: textColor
            }
          }
        ]
      };

      // Set chart options
      chartInstance.current.setOption(option, true);
      console.log('Chart options set successfully');
    } catch (error) {
      console.error('Error rendering chart:', error);
    }
  };

  // Call renderChart when datasets or selectedColumn changes
  useEffect(() => {
    console.log('Datasets or selectedColumn changed:', {
      datasetsLength: datasets.length,
      selectedColumn,
      hasChartInstance: !!chartInstance.current,
      chartRef: !!chartRef.current
    });

    // Add a small delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      renderChart();
    }, 100);

    return () => clearTimeout(timer);
  }, [datasets, selectedColumn]);

  if (isLoading) {
    return (
      <div className="w-full h-[400px] flex items-center justify-center bg-background rounded-lg shadow dark:shadow-gray-800">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!selectedColumn || datasets.length < 2) {
    return (
      <div className="w-full h-[400px] flex items-center justify-center bg-background rounded-lg shadow dark:shadow-gray-800">
        <div className="text-center p-4">
          <h3 className="text-lg font-medium mb-2 text-foreground">No Data to Display</h3>
          <p className="text-sm text-muted-foreground">
            {!selectedColumn
              ? "Please select a column to analyze"
              : "You need at least 2 datasets with the same structure to perform correlation analysis"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-card rounded-lg border shadow-sm dark:shadow-gray-800">
      <div className="p-4">
        {/* Chart title */}
        {selectedColumn && (
          <h3 className="text-lg font-medium mb-4 text-center text-foreground">
            Correlation Analysis: {selectedColumn}
          </h3>
        )}

        {/* Chart container with explicit dimensions */}
        <div
          ref={chartRef}
          style={{ width: '100%', height: '400px', minHeight: '400px' }}
          className="chart-container relative"
        />

        {/* Overlay a loading indicator to ensure visibility */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
      </div>
    </div>
  );
};
