'use client'

import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';
import { Loader2, Arrow<PERSON><PERSON><PERSON>, ArrowRight, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { ChartType } from './ChartTypeSelector';
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface CorrelationChartProps {
  datasets: Array<{
    id: string;
    name: string;
    data: any[];
    headers: string[];
    createdAt: string;
  }>;
  selectedColumn: string | null;
  isLoading?: boolean;
  chartTypes: ChartType[];
  mode?: 'compare' | 'matrix';
}

export const CorrelationChartNew: React.FC<CorrelationChartProps> = ({
  datasets,
  selectedColumn,
  isLoading = false,
  chartTypes = ['bar'],
  mode = 'compare'
}) => {
  const [activeChartIndex, setActiveChartIndex] = useState(0);
  const activeChartType = chartTypes[activeChartIndex] || 'bar';
  const chartRef = useRef<HTMLDivElement>(null);
  const fullscreenChartRef = useRef<HTMLDivElement>(null);
  const [chartError, setChartError] = useState<string | null>(null);
  const [isChartReady, setIsChartReady] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Handle chart navigation
  const nextChart = () => {
    if (chartTypes.length <= 1) return;
    setActiveChartIndex((prevIndex) => (prevIndex + 1) % chartTypes.length);
  };

  const prevChart = () => {
    if (chartTypes.length <= 1) return;
    setActiveChartIndex((prevIndex) => (prevIndex - 1 + chartTypes.length) % chartTypes.length);
  };

  useEffect(() => {
    let chart: echarts.ECharts | null = null;
    let fullscreenChart: echarts.ECharts | null = null;

    const initChart = () => {
      const targetRef = isFullscreen ? fullscreenChartRef.current : chartRef.current;
      if (!targetRef) return;

      try {
        // Dispose of any existing chart
        if (isFullscreen) {
          echarts.dispose(fullscreenChartRef.current!);
        } else {
          echarts.dispose(chartRef.current!);
        }

        // Get theme based on document class
        const isDarkMode = document.documentElement.classList.contains('dark');

        // Initialize chart
        const newChart = echarts.init(targetRef, isDarkMode ? 'dark' : undefined);

        if (isFullscreen) {
          fullscreenChart = newChart;
        } else {
          chart = newChart;
        }

        setIsChartReady(true);

        // Handle window resize
        const handleResize = () => {
          if (isFullscreen) {
            fullscreenChart?.resize();
          } else {
            chart?.resize();
          }
        };

        window.addEventListener('resize', handleResize);

        return () => {
          window.removeEventListener('resize', handleResize);
          if (isFullscreen) {
            fullscreenChart?.dispose();
          } else {
            chart?.dispose();
          }
        };
      } catch (error) {
        console.error('Error initializing chart:', error);
        setChartError('Failed to initialize chart');
      }
    };

    const renderChart = () => {
      const targetChart = isFullscreen ? fullscreenChart : chart;

      if (!targetChart || !selectedColumn ||
          (mode === 'compare' && datasets.length < 1) ||
          (mode === 'matrix' && datasets.length < 1)) {
        return;
      }

      try {
        setChartError(null);

        // Set chart options with modern styling
        const isDarkMode = document.documentElement.classList.contains('dark');
        const textColor = isDarkMode ? '#e5e7eb' : '#374151';
        const axisLineColor = isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)';
        const primaryColor = isDarkMode ? '#0ea5e9' : '#0284c7';
        // const secondaryColor = isDarkMode ? '#8b5cf6' : '#7c3aed'; // Unused variable
        const positiveColor = isDarkMode ? '#10b981' : '#059669';
        const negativeColor = isDarkMode ? '#ef4444' : '#dc2626';

        // Different data processing based on mode
        let differences: Array<{value: number, itemStyle?: any}> = [];
        let dates: string[] = [];
        let values: number[] = [];
        let xAxisData: string[] = [];

        // Helper function to sample large datasets
        const sampleData = (data: any[], maxSamples: number = 1000) => {
          if (!data || !Array.isArray(data) || data.length <= maxSamples) {
            return data;
          }

          // For very large datasets, use sampling to improve performance
          const samplingRate = Math.ceil(data.length / maxSamples);
          return data.filter((_, index) => index % samplingRate === 0);
        };

        if (mode === 'compare') {
          // Sort datasets by date
          const sortedDatasets = [...datasets].sort((a, b) => {
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          });

          // Extract dates for x-axis
          dates = sortedDatasets.map(dataset => {
            const date = new Date(dataset.createdAt);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          });

          // Calculate average values for the selected column
          values = sortedDatasets.map(dataset => {
            if (!dataset.data || !Array.isArray(dataset.data) || dataset.data.length === 0) {
              return 0;
            }

            // Sample data if it's too large
            const sampledData = sampleData(dataset.data);

            const numericValues = sampledData
              .filter(row => row && typeof row === 'object')
              .map(row => {
                const value = row[selectedColumn];
                return !isNaN(Number(value)) ? Number(value) : null;
              })
              .filter((val): val is number => val !== null);

            if (numericValues.length === 0) return 0;

            return numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;
          });

          // Calculate month-over-month differences
          for (let i = 1; i < values.length; i++) {
            differences.push({
              value: values[i] - values[i-1],
              itemStyle: {
                color: values[i] > values[i-1] ? positiveColor : negativeColor
              }
            });
          }

          // Prepare x-axis data
          xAxisData = dates.slice(1).map((date, i) => `${dates[i]} → ${date}`);
        }
        else if (mode === 'matrix') {
          // For matrix mode, we're working with a single dataset
          const dataset = datasets[0];

          if (dataset && dataset.data && Array.isArray(dataset.data) && dataset.data.length > 0) {
            // Sample data if it's too large
            const sampledData = sampleData(dataset.data);

            // Get all numeric values for the selected column
            const columnValues = sampledData
              .filter(row => row && typeof row === 'object')
              .map(row => {
                const value = row[selectedColumn];
                return !isNaN(Number(value)) ? Number(value) : null;
              })
              .filter((val): val is number => val !== null);

            // Use the values directly for visualization
            values = columnValues;

            // Create labels for x-axis (row numbers or indices)
            xAxisData = columnValues.map((_, i) => `Row ${i + 1}`);

            // For differences, we'll use the actual values
            differences = columnValues.map(value => ({
              value,
              itemStyle: {
                color: value > 0 ? positiveColor : value < 0 ? negativeColor : '#9ca3af'
              }
            }));
          }
        }



        // xAxisData is now prepared in the mode-specific code above

        // Create series data based on active chart type
        let seriesConfig = [];

        // Common tooltip formatter
        const tooltipFormatter = function(params: any) {
          if (!params || !params[0]) return '';

          const dataIndex = params[0].dataIndex;

          if (mode === 'compare') {
            if (dataIndex >= dates.length - 1 || dataIndex < 0) return '';

            const date = dates[dataIndex + 1];
            const value = params[0].value;
            const prevValue = values[dataIndex];
            const currValue = values[dataIndex + 1];

            const percentChange = prevValue !== 0
              ? ((currValue - prevValue) / Math.abs(prevValue)) * 100
              : currValue > 0 ? 100 : currValue < 0 ? -100 : 0;

            return `
              <div style="font-weight: bold; margin-bottom: 5px;">${date}</div>
              <div>Change: ${Number(value).toFixed(2)}</div>
              <div>From: ${Number(prevValue).toFixed(2)} To: ${Number(currValue).toFixed(2)}</div>
              <div>Percent Change: ${Number(percentChange).toFixed(2)}%</div>
            `;
          } else {
            // Matrix mode
            if (dataIndex >= values.length || dataIndex < 0) return '';

            const value = values[dataIndex];
            const rowLabel = xAxisData[dataIndex];

            return `
              <div style="font-weight: bold; margin-bottom: 5px;">${rowLabel}</div>
              <div>${selectedColumn}: ${Number(value).toFixed(2)}</div>
            `;
          }
        };

        // Configure chart based on active chart type
        switch (activeChartType) {
          case 'bar':
            seriesConfig = [{
              name: 'Month-over-Month Change',
              type: 'bar',
              data: differences,
              label: {
                show: true,
                position: 'top',
                formatter: (params: any) => Number(params.value).toFixed(2),
                color: textColor
              },
              itemStyle: {
                borderRadius: [4, 4, 0, 0]
              }
            }];
            break;

          case 'line':
            seriesConfig = [{
              name: 'Month-over-Month Change',
              type: 'line',
              data: differences.map(d => d.value),
              label: {
                show: true,
                position: 'top',
                formatter: (params: any) => Number(params.value).toFixed(2),
                color: textColor
              },
              smooth: true,
              symbol: 'circle',
              symbolSize: 8,
              lineStyle: {
                width: 3,
                color: primaryColor
              },
              itemStyle: {
                color: primaryColor
              },
              areaStyle: {
                opacity: 0.2,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: primaryColor },
                    { offset: 1, color: 'transparent' }
                  ]
                }
              }
            }];
            break;

          case 'area':
            seriesConfig = [{
              name: 'Month-over-Month Change',
              type: 'line',
              data: differences.map(d => d.value),
              label: {
                show: true,
                position: 'top',
                formatter: (params: any) => Number(params.value).toFixed(2),
                color: textColor
              },
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 2,
                color: primaryColor
              },
              itemStyle: {
                color: primaryColor
              },
              areaStyle: {
                opacity: 0.6,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: primaryColor },
                    { offset: 1, color: 'transparent' }
                  ]
                }
              }
            }];
            break;

          case 'scatter':
            seriesConfig = [{
              name: 'Month-over-Month Change',
              type: 'scatter',
              data: differences.map((d, i) => [i, d.value]),
              label: {
                show: true,
                position: 'right',
                formatter: (params: any) => Number(params.value[1]).toFixed(2),
                color: textColor
              },
              symbolSize: 12,
              itemStyle: {
                opacity: 0.8,
                color: (params: any) => {
                  return params.data[1] > 0 ? positiveColor : negativeColor;
                }
              }
            }];
            break;

          case 'pie':
            // For pie chart, we need to restructure the data
            seriesConfig = [{
              name: 'Changes',
              type: 'pie',
              radius: '70%',
              center: ['50%', '50%'],
              data: differences.map((d, i) => ({
                name: xAxisData[i],
                value: Math.abs(d.value),
                itemStyle: {
                  color: d.value > 0 ? positiveColor : negativeColor
                }
              })),
              label: {
                formatter: '{b}: {c} ({d}%)',
                color: textColor
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }];
            break;

          case 'radar':
            // For radar chart
            seriesConfig = [{
              name: 'Changes',
              type: 'radar',
              data: [{
                value: differences.map(d => d.value),
                name: 'Month-over-Month Change',
                areaStyle: {
                  opacity: 0.6,
                  color: primaryColor
                },
                lineStyle: {
                  color: primaryColor
                },
                itemStyle: {
                  color: primaryColor
                }
              }]
            }];
            break;

          case 'bubble':
            // For bubble chart
            seriesConfig = [{
              name: 'Changes',
              type: 'scatter',
              data: differences.map((d, i) => [
                i,
                d.value,
                Math.abs(d.value) * 10 // Size based on absolute value
              ]),
              symbolSize: function (data: any) {
                return data[2];
              },
              itemStyle: {
                opacity: 0.7,
                color: (params: any) => {
                  return params.data[1] > 0 ? positiveColor : negativeColor;
                }
              },
              label: {
                show: true,
                formatter: (params: any) => Number(params.data[1]).toFixed(2),
                position: 'right',
                color: textColor
              }
            }];
            break;

          case 'stacked-bar':
            // For stacked bar chart
            seriesConfig = [
              {
                name: 'Positive Changes',
                type: 'bar',
                stack: 'total',
                data: differences.map(d => d.value > 0 ? d.value : 0),
                itemStyle: {
                  color: positiveColor,
                  borderRadius: [4, 4, 0, 0]
                },
                label: {
                  show: true,
                  position: 'top',
                  formatter: (params: any) => params.value > 0 ? Number(params.value).toFixed(2) : '',
                  color: textColor
                }
              },
              {
                name: 'Negative Changes',
                type: 'bar',
                stack: 'total',
                data: differences.map(d => d.value < 0 ? d.value : 0),
                itemStyle: {
                  color: negativeColor,
                  borderRadius: [0, 0, 4, 4]
                },
                label: {
                  show: true,
                  position: 'bottom',
                  formatter: (params: any) => params.value < 0 ? Number(params.value).toFixed(2) : '',
                  color: textColor
                }
              }
            ];
            break;

          case 'horizontal-bar':
            // For horizontal bar chart
            seriesConfig = [{
              name: 'Month-over-Month Change',
              type: 'bar',
              data: differences,
              label: {
                show: true,
                position: 'right',
                formatter: (params: any) => Number(params.value).toFixed(2),
                color: textColor
              },
              itemStyle: {
                borderRadius: [0, 4, 4, 0],
                color: (params: any) => {
                  return params.value > 0 ? positiveColor : negativeColor;
                }
              }
            }];
            break;

          default:
            // Default to bar chart
            seriesConfig = [{
              name: 'Month-over-Month Change',
              type: 'bar',
              data: differences,
              label: {
                show: true,
                position: 'top',
                formatter: (params: any) => Number(params.value).toFixed(2),
                color: textColor
              }
            }];
        }

        // Base chart options with zoom functionality
        const option = {
          backgroundColor: 'transparent',
          title: {
            text: `${selectedColumn} ${mode === 'compare' ? 'Comparison' : 'Analysis'}`,
            subtext: `Chart Type: ${activeChartType.charAt(0).toUpperCase() + activeChartType.slice(1)}`,
            left: 'center',
            textStyle: {
              color: textColor
            },
            subtextStyle: {
              color: isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
            }
          },
          tooltip: {
            trigger: activeChartType === 'pie' ? 'item' : 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: activeChartType === 'pie' ? undefined : tooltipFormatter,
            confine: true // Keep tooltip inside chart area
          },
          legend: {
            show: ['stacked-bar'].includes(activeChartType),
            top: 'bottom',
            textStyle: {
              color: textColor
            }
          },
          // Add data zoom for large datasets
          dataZoom: !['pie', 'radar'].includes(activeChartType) ? [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
              height: 20,
              bottom: 0,
              borderColor: axisLineColor,
              textStyle: {
                color: textColor
              },
              handleStyle: {
                color: primaryColor,
                borderColor: primaryColor
              }
            },
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100,
              zoomOnMouseWheel: true,
              moveOnMouseMove: true
            }
          ] : [],
          // Add toolbox with zoom features
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: 'none',
                icon: {
                  zoom: 'path://M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1',
                  back: 'path://M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26'
                }
              },
              restore: {},
              saveAsImage: {}
            },
            right: 15,
            top: 15,
            iconStyle: {
              borderColor: textColor,
              color: textColor
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '10%', // Increased to make room for zoom slider
            containLabel: true
          },
          radar: activeChartType === 'radar' ? {
            indicator: xAxisData.map((name) => ({
              name,
              max: Math.max(...differences.map(d => Math.abs(d.value))) * 1.2
            }))
          } : undefined,
          xAxis: !['pie', 'radar'].includes(activeChartType) ? {
            type: activeChartType === 'horizontal-bar' ? 'value' : 'category',
            data: activeChartType === 'horizontal-bar' ? undefined : xAxisData,
            axisLabel: {
              interval: 0,
              rotate: 45,
              color: textColor
            },
            axisLine: {
              lineStyle: {
                color: axisLineColor
              }
            }
          } : undefined,
          yAxis: !['pie', 'radar'].includes(activeChartType) ? {
            type: activeChartType === 'horizontal-bar' ? 'category' : 'value',
            data: activeChartType === 'horizontal-bar' ? xAxisData : undefined,
            name: 'Change',
            nameTextStyle: {
              color: textColor
            },
            axisLabel: {
              formatter: '{value}',
              color: textColor
            },
            axisLine: {
              lineStyle: {
                color: axisLineColor
              }
            },
            splitLine: {
              lineStyle: {
                color: axisLineColor
              }
            }
          } : undefined,
          series: seriesConfig
        };

        targetChart.setOption(option);
      } catch (error) {
        console.error('Error rendering chart:', error);
        setChartError('Failed to render chart data');
      }
    };

    const timer = setTimeout(() => {
      initChart();
      if (selectedColumn &&
          ((mode === 'compare' && datasets.length >= 1) ||
           (mode === 'matrix' && datasets.length >= 1))) {
        renderChart();
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      if (chart) {
        chart.dispose();
      }
      if (fullscreenChart) {
        fullscreenChart.dispose();
      }
    };
  }, [datasets, selectedColumn, activeChartType, activeChartIndex, isFullscreen]);

  if (isLoading) {
    return (
      <div className="w-full h-[400px] flex items-center justify-center bg-card rounded-lg border shadow-sm">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (chartError) {
    return (
      <div className="w-full h-[400px] flex items-center justify-center bg-card rounded-lg border shadow-sm">
        <div className="text-center p-4">
          <h3 className="text-lg font-medium mb-2 text-destructive">Chart Error</h3>
          <p className="text-sm text-muted-foreground">{chartError}</p>
        </div>
      </div>
    );
  }

  // Different validation based on mode
  if (!selectedColumn || (mode === 'compare' && datasets.length < 1) || (mode === 'matrix' && datasets.length < 1)) {
    return (
      <div className="w-full h-[400px] flex items-center justify-center bg-card rounded-lg border shadow-sm">
        <div className="text-center p-4">
          <h3 className="text-lg font-medium mb-2 text-foreground">No Data to Display</h3>
          <p className="text-sm text-muted-foreground">
            {!selectedColumn
              ? "Please select a column to analyze"
              : mode === 'compare'
                ? "Please select at least one dataset and a column to analyze"
                : "Please select a dataset and at least 2 columns to analyze correlations"}
          </p>
        </div>
      </div>
    );
  }

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <>
      <div className="w-full bg-card rounded-lg border shadow-sm">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-foreground">
              Correlation Analysis: {selectedColumn}
            </h3>

            <div className="flex items-center gap-2">
              {chartTypes.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={prevChart}
                    disabled={chartTypes.length <= 1}
                    className="h-8 w-8 p-0"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>

                  <div className="text-sm font-medium">
                    Chart {activeChartIndex + 1} of {chartTypes.length}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={nextChart}
                    disabled={chartTypes.length <= 1}
                    className="h-8 w-8 p-0"
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
                className="h-8 w-8 p-0 ml-2"
                title="View in fullscreen"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="mb-2 text-sm text-muted-foreground text-center">
            {chartTypes.length > 0 && (
              <span>
                Showing {activeChartType.charAt(0).toUpperCase() + activeChartType.slice(1)} Chart
              </span>
            )}
          </div>

          <div
            ref={chartRef}
            className="w-full h-[400px]"
          />

          {!isChartReady && !isFullscreen && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          )}
        </div>
      </div>

      {/* Fullscreen Dialog */}
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogContent className="max-w-[90vw] w-[90vw] max-h-[90vh] h-[90vh]">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-medium text-foreground">
              {selectedColumn} {mode === 'compare' ? 'Comparison' : 'Analysis'}
            </h3>

            <div className="flex items-center gap-2">
              {chartTypes.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={prevChart}
                    disabled={chartTypes.length <= 1}
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>

                  <div className="text-sm font-medium mx-2">
                    Chart {activeChartIndex + 1} of {chartTypes.length}: {activeChartType.charAt(0).toUpperCase() + activeChartType.slice(1)}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={nextChart}
                    disabled={chartTypes.length <= 1}
                  >
                    Next
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
                className="ml-2"
                title="Exit fullscreen"
              >
                <Minimize2 className="h-4 w-4 mr-1" />
                Exit Fullscreen
              </Button>
            </div>
          </div>

          <div
            ref={fullscreenChartRef}
            className="w-full h-[calc(90vh-120px)]"
          />

          {!isChartReady && isFullscreen && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};
