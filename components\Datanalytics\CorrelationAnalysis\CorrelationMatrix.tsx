'use client'

import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';
import { Loader2, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface CorrelationMatrixProps {
  dataset: {
    id: string;
    name: string;
    data: any[];
    headers: string[];
    createdAt: string;
  } | null;
  selectedColumns: string[];
  isLoading?: boolean;
  chartType?: 'heatmap' | 'network';
}

export const CorrelationMatrix: React.FC<CorrelationMatrixProps> = ({
  dataset,
  selectedColumns,
  isLoading = false,
  chartType = 'heatmap'
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const fullscreenChartRef = useRef<HTMLDivElement>(null);
  const [chartError, setChartError] = useState<string | null>(null);
  const [isChartReady, setIsChartReady] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    let chart: echarts.ECharts | null = null;
    let fullscreenChart: echarts.ECharts | null = null;

    const initChart = () => {
      const targetRef = isFullscreen ? fullscreenChartRef.current : chartRef.current;
      if (!targetRef) return;

      try {
        // Dispose of any existing chart
        if (isFullscreen) {
          echarts.dispose(fullscreenChartRef.current!);
        } else {
          echarts.dispose(chartRef.current!);
        }

        // Get theme based on document class
        const isDarkMode = document.documentElement.classList.contains('dark');

        // Initialize chart
        const newChart = echarts.init(targetRef, isDarkMode ? 'dark' : undefined);

        if (isFullscreen) {
          fullscreenChart = newChart;
        } else {
          chart = newChart;
        }

        setIsChartReady(true);

        // Handle window resize
        const handleResize = () => {
          if (isFullscreen) {
            fullscreenChart?.resize();
          } else {
            chart?.resize();
          }
        };

        window.addEventListener('resize', handleResize);

        return () => {
          window.removeEventListener('resize', handleResize);
          if (isFullscreen) {
            fullscreenChart?.dispose();
          } else {
            chart?.dispose();
          }
        };
      } catch (error) {
        console.error('Error initializing chart:', error);
        setChartError('Failed to initialize chart');
      }
    };

    // Calculate Pearson correlation coefficient between two arrays
    const calculateCorrelation = (x: number[], y: number[]): number => {
      if (x.length !== y.length) {
        throw new Error('Arrays must have the same length');
      }

      const n = x.length;

      // Calculate means
      const xMean = x.reduce((sum, val) => sum + val, 0) / n;
      const yMean = y.reduce((sum, val) => sum + val, 0) / n;

      // Calculate covariance and standard deviations
      let covariance = 0;
      let xVariance = 0;
      let yVariance = 0;

      for (let i = 0; i < n; i++) {
        const xDiff = x[i] - xMean;
        const yDiff = y[i] - yMean;
        covariance += xDiff * yDiff;
        xVariance += xDiff * xDiff;
        yVariance += yDiff * yDiff;
      }

      // Avoid division by zero
      if (xVariance === 0 || yVariance === 0) {
        return 0;
      }

      return covariance / (Math.sqrt(xVariance) * Math.sqrt(yVariance));
    };

    const renderCorrelationMatrix = () => {
      const targetChart = isFullscreen ? fullscreenChart : chart;

      if (!targetChart || !dataset || !dataset.data || dataset.data.length === 0 || selectedColumns.length < 2) {
        return;
      }

      try {
        setChartError(null);

        // Helper function to sample large datasets
        const sampleData = (data: any[], maxSamples: number = 1000) => {
          if (!data || !Array.isArray(data) || data.length <= maxSamples) {
            return data;
          }

          // For very large datasets, use sampling to improve performance
          const samplingRate = Math.ceil(data.length / maxSamples);
          return data.filter((_, index) => index % samplingRate === 0);
        };

        // Sample data if it's too large
        const sampledData = sampleData(dataset.data);

        // Extract numeric data for selected columns
        const numericData: Record<string, number[]> = {};

        selectedColumns.forEach(column => {
          numericData[column] = sampledData
            .map(row => {
              const value = row[column];
              return !isNaN(Number(value)) ? Number(value) : null;
            })
            .filter((val): val is number => val !== null);
        });

        // Calculate correlation matrix
        const correlationMatrix: number[][] = [];

        for (let i = 0; i < selectedColumns.length; i++) {
          correlationMatrix[i] = [];
          for (let j = 0; j < selectedColumns.length; j++) {
            if (i === j) {
              // Diagonal is always 1 (perfect correlation with itself)
              correlationMatrix[i][j] = 1;
            } else {
              try {
                correlationMatrix[i][j] = calculateCorrelation(
                  numericData[selectedColumns[i]],
                  numericData[selectedColumns[j]]
                );
              } catch (error) {
                console.error('Error calculating correlation:', error);
                correlationMatrix[i][j] = 0;
              }
            }
          }
        }

        // Create data for heatmap
        const data: [number, number, number][] = [];
        for (let i = 0; i < selectedColumns.length; i++) {
          for (let j = 0; j < selectedColumns.length; j++) {
            data.push([i, j, correlationMatrix[i][j]]);
          }
        }

        // Set chart options
        const isDarkMode = document.documentElement.classList.contains('dark');
        const textColor = isDarkMode ? '#e5e7eb' : '#374151';

        const option = {
          backgroundColor: 'transparent',
          title: {
            text: 'Column Correlation Matrix',
            left: 'center',
            textStyle: {
              color: textColor
            }
          },
          tooltip: {
            position: 'top',
            formatter: function (params: any) {
              const i = params.data[0];
              const j = params.data[1];
              const value = params.data[2].toFixed(2);
              return `${selectedColumns[i]} & ${selectedColumns[j]}: ${value}`;
            },
            confine: true // Keep tooltip inside chart area
          },
          // Add toolbox with zoom features
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: 'none',
                icon: {
                  zoom: 'path://M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1',
                  back: 'path://M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26'
                }
              },
              restore: {},
              saveAsImage: {}
            },
            right: 15,
            top: 15,
            iconStyle: {
              borderColor: textColor,
              color: textColor
            }
          },
          // Add data zoom for large datasets
          dataZoom: chartType === 'heatmap' ? [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
              height: 20,
              bottom: 10,
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
              textStyle: {
                color: textColor
              }
            },
            {
              type: 'slider',
              show: true,
              yAxisIndex: [0],
              start: 0,
              end: 100,
              width: 20,
              right: 10,
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
              textStyle: {
                color: textColor
              }
            },
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100
            },
            {
              type: 'inside',
              yAxisIndex: [0],
              start: 0,
              end: 100
            }
          ] : [],
          grid: {
            top: 60,
            bottom: 90, // Increased to make room for zoom slider
            left: '10%',
            right: '10%'
          },
          xAxis: {
            type: 'category',
            data: selectedColumns,
            splitArea: {
              show: true
            },
            axisLabel: {
              color: textColor,
              rotate: 45,
              interval: 0
            }
          },
          yAxis: {
            type: 'category',
            data: selectedColumns,
            splitArea: {
              show: true
            },
            axisLabel: {
              color: textColor
            }
          },
          visualMap: {
            min: -1,
            max: 1,
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            bottom: 10,
            color: ['#d73027', '#f46d43', '#fdae61', '#fee090', '#ffffbf', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4'],
            text: ['Strong Positive', 'Strong Negative'],
            textStyle: {
              color: textColor
            }
          },
          series: chartType === 'heatmap' ? [
            {
              name: 'Correlation',
              type: 'heatmap',
              data: data,
              label: {
                show: true,
                formatter: function (params: any) {
                  return params.data[2].toFixed(2);
                },
                color: '#000'
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ] :
          // Network graph
          selectedColumns.map((col, idx) => {
            return {
              name: col,
              type: 'graph',
              layout: 'force',
              circular: {
                rotateLabel: true
              },
              force: {
                repulsion: 500,
                edgeLength: 120
              },
              roam: true,
              label: {
                show: true,
                position: 'right',
                formatter: '{b}'
              },
              data: selectedColumns.map((name, index) => ({
                name: name,
                value: index,
                symbolSize: 30,
                itemStyle: {
                  color: index === idx ? '#5470c6' : '#91cc75'
                }
              })),
              edges: selectedColumns.map((_, j) => {
                if (j === idx) return null;
                const value = correlationMatrix[idx][j];
                const absValue = Math.abs(value);
                return {
                  source: idx,
                  target: j,
                  value: value.toFixed(2),
                  lineStyle: {
                    width: absValue * 5,
                    color: value > 0 ? '#4575b4' : '#d73027',
                    curveness: 0.3
                  },
                  label: {
                    show: true,
                    formatter: '{c}'
                  }
                };
              }).filter(Boolean)
            };
          })
        };

        targetChart.setOption(option);
      } catch (error) {
        console.error('Error rendering correlation matrix:', error);
        setChartError('Failed to render correlation matrix');
      }
    };

    const timer = setTimeout(() => {
      initChart();
      if (dataset && selectedColumns.length >= 2) {
        renderCorrelationMatrix();
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      if (chart) {
        chart.dispose();
      }
      if (fullscreenChart) {
        fullscreenChart.dispose();
      }
    };
  }, [dataset, selectedColumns, chartType, isFullscreen]);

  if (isLoading) {
    return (
      <div className="w-full h-[500px] flex items-center justify-center bg-card rounded-lg border shadow-sm">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (chartError) {
    return (
      <div className="w-full h-[500px] flex items-center justify-center bg-card rounded-lg border shadow-sm">
        <div className="text-center p-4">
          <h3 className="text-lg font-medium mb-2 text-destructive">Chart Error</h3>
          <p className="text-sm text-muted-foreground">{chartError}</p>
        </div>
      </div>
    );
  }

  if (!dataset || selectedColumns.length < 2) {
    return (
      <div className="w-full h-[500px] flex items-center justify-center bg-card rounded-lg border shadow-sm">
        <div className="text-center p-4">
          <h3 className="text-lg font-medium mb-2 text-foreground">No Data to Display</h3>
          <p className="text-sm text-muted-foreground">
            {!dataset
              ? "Please select a dataset to analyze"
              : selectedColumns.length < 2
                ? "Please select at least 2 columns to analyze"
                : "No data available for correlation analysis"}
          </p>
        </div>
      </div>
    );
  }

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <>
      <div className="w-full bg-card rounded-lg border shadow-sm">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-foreground">
              Correlation Matrix: {dataset.name}
            </h3>

            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
              className="h-8 w-8 p-0"
              title="View in fullscreen"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>

          <div
            ref={chartRef}
            className="w-full h-[500px]"
          />

          {!isChartReady && !isFullscreen && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          )}
        </div>
      </div>

      {/* Fullscreen Dialog */}
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogContent className="max-w-[90vw] w-[90vw] max-h-[90vh] h-[90vh]">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-medium text-foreground">
              Correlation Matrix: {dataset.name}
            </h3>

            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
              title="Exit fullscreen"
            >
              <Minimize2 className="h-4 w-4 mr-1" />
              Exit Fullscreen
            </Button>
          </div>

          <div
            ref={fullscreenChartRef}
            className="w-full h-[calc(90vh-120px)]"
          />

          {!isChartReady && isFullscreen && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};
