'use client'

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  SortAsc,
  SortDesc,
  Filter,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ArrowUpDown,
  Download,
  RefreshCw
} from 'lucide-react';
import { cn } from "@/lib/utils";

interface CorrelationTableProps {
  data: any[];
  headers: string[];
  selectedColumn: string | null;
}

export const CorrelationTable: React.FC<CorrelationTableProps> = ({
  data,
  headers,
  selectedColumn
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [sortConfig, setSortConfig] = useState<{
    key: string | null;
    direction: 'asc' | 'desc';
  }>({
    key: selectedColumn || null,
    direction: 'asc'
  });

  // Track previous values for highlighting changes
  const [previousValues, setPreviousValues] = useState<Record<string, any>>({});

  // Process data to identify differences between datasets
  React.useEffect(() => {
    if (data.length > 0 && selectedColumn) {
      // Group data by dataset
      const datasetGroups: Record<string, any[]> = {};
      data.forEach(row => {
        const datasetName = row.__dataset_name || 'Unknown';
        if (!datasetGroups[datasetName]) {
          datasetGroups[datasetName] = [];
        }
        datasetGroups[datasetName].push(row);
      });

      // Calculate average values for each dataset
      const datasetAverages: Record<string, Record<string, number>> = {};
      Object.entries(datasetGroups).forEach(([datasetName, rows]) => {
        datasetAverages[datasetName] = {};

        headers.forEach(header => {
          if (header !== '__dataset_name' && header !== '__dataset_date') {
            const values = rows
              .map(row => row[header])
              .filter(val => val !== undefined && val !== null && !isNaN(Number(val)))
              .map(val => Number(val));

            if (values.length > 0) {
              datasetAverages[datasetName][header] = values.reduce((sum, val) => sum + val, 0) / values.length;
            }
          }
        });
      });

      // Store the previous values for comparison
      setPreviousValues(datasetAverages);
    }
  }, [data, headers, selectedColumn]);

  // Filter data based on search term
  const filteredData = React.useMemo(() => {
    return data.filter(row => {
      if (!searchTerm) return true;

      // Search in all columns
      return Object.entries(row).some(([key, value]) => {
        if (key === '__dataset_name' || key === '__dataset_date') {
          return String(value).toLowerCase().includes(searchTerm.toLowerCase());
        }

        if (headers.includes(key)) {
          return String(value).toLowerCase().includes(searchTerm.toLowerCase());
        }

        return false;
      });
    });
  }, [data, searchTerm, headers]);

  // Sort data
  const sortedData = React.useMemo(() => {
    if (!sortConfig.key) return filteredData;

    return [...filteredData].sort((a, b) => {
      if (a[sortConfig.key!] === null || a[sortConfig.key!] === undefined) return 1;
      if (b[sortConfig.key!] === null || b[sortConfig.key!] === undefined) return -1;

      const aValue = a[sortConfig.key!];
      const bValue = b[sortConfig.key!];

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return sortConfig.direction === 'asc'
        ? String(aValue).localeCompare(String(bValue))
        : String(bValue).localeCompare(String(aValue));
    });
  }, [filteredData, sortConfig]);

  // Calculate value differences for highlighting
  const getValueDifference = (row: any, header: string): number | null => {
    if (!row || !header || header === '__dataset_name' || header === '__dataset_date') {
      return null;
    }

    const datasetName = row.__dataset_name;
    const currentValue = Number(row[header]);

    if (!datasetName || isNaN(currentValue)) {
      return null;
    }

    // Get previous dataset's average value for this column
    const datasets = Object.keys(previousValues);
    const currentIndex = datasets.indexOf(datasetName);

    if (currentIndex <= 0 || !previousValues[datasets[currentIndex - 1]]) {
      return null;
    }

    const previousDataset = datasets[currentIndex - 1];
    const previousValue = previousValues[previousDataset][header];

    if (previousValue === undefined || isNaN(previousValue)) {
      return null;
    }

    return currentValue - previousValue;
  };

  // Get cell color based on difference
  const getCellColor = (difference: number | null): string => {
    if (difference === null) return '';

    if (difference > 0) {
      return 'text-emerald-600 dark:text-emerald-400 font-medium';
    } else if (difference < 0) {
      return 'text-red-600 dark:text-red-400 font-medium';
    }

    return '';
  };

  // Paginate data
  const paginatedData = sortedData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const totalPages = Math.ceil(sortedData.length / pageSize);

  // Handle sorting
  const handleSort = (key: string) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate range around current page
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if at the beginning or end
      if (currentPage <= 2) {
        endPage = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        startPage = Math.max(2, totalPages - 3);
      }

      // Add ellipsis if needed
      if (startPage > 2) {
        pages.push(-1); // Use -1 to represent ellipsis
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis if needed
      if (endPage < totalPages - 1) {
        pages.push(-2); // Use -2 to represent ellipsis
      }

      // Always show last page
      pages.push(totalPages);
    }

    return pages;
  };

  return (
    <div className="w-full space-y-4">
      {/* Table Controls */}
      <div className="flex flex-col sm:flex-row gap-2 justify-between">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search data..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>

        <div className="flex items-center gap-2">
          {/* Page Size Selector */}
          <div className="flex items-center gap-1 text-sm">
            <span className="text-muted-foreground hidden sm:inline">Show</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm"
            >
              {[5, 10, 20, 50, 100].map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className="text-muted-foreground hidden sm:inline">rows</span>
          </div>

          {/* Data Stats */}
          <div className="text-sm text-muted-foreground">
            {filteredData.length} items
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50 hover:bg-muted">
                {headers.map((header) => (
                  <TableHead
                    key={header}
                    className={cn(
                      "cursor-pointer h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
                      header === selectedColumn ? 'bg-primary/10 text-primary' : ''
                    )}
                    onClick={() => handleSort(header)}
                  >
                    <div className="flex items-center gap-1">
                      <span className="truncate max-w-[150px]">{header}</span>
                      {sortConfig.key === header ? (
                        sortConfig.direction === 'asc'
                          ? <SortAsc className="h-3 w-3 text-primary" />
                          : <SortDesc className="h-3 w-3 text-primary" />
                      ) : (
                        <ArrowUpDown className="h-3 w-3 opacity-30" />
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.length > 0 ? (
                paginatedData.map((row, rowIndex) => {
                  const isNewDataset = rowIndex === 0 ||
                    row.__dataset_name !== paginatedData[rowIndex - 1].__dataset_name;

                  return (
                    <TableRow
                      key={rowIndex}
                      className={cn(
                        isNewDataset ? 'border-t-2 border-t-border' : '',
                        rowIndex % 2 === 0 ? 'bg-muted/20' : 'bg-background'
                      )}
                    >
                      {headers.map((header) => {
                        const difference = getValueDifference(row, header);
                        const cellColorClass = getCellColor(difference);
                        const isNumeric = !isNaN(Number(row[header]));

                        return (
                          <TableCell
                            key={`${rowIndex}-${header}`}
                            className={cn(
                              "p-2 align-middle",
                              header === selectedColumn ? 'bg-primary/5 font-medium' : '',
                              cellColorClass,
                              isNumeric ? 'text-right tabular-nums' : ''
                            )}
                          >
                            {row[header] !== undefined && row[header] !== null ? (
                              <>
                                {String(row[header])}
                                {difference !== null && (
                                  <span className="ml-1 text-xs">
                                    {difference > 0 ? '↑' : difference < 0 ? '↓' : ''}
                                    {Math.abs(difference) > 0 && (
                                      <Badge
                                        variant="outline"
                                        className={cn(
                                          "ml-1 px-1 py-0 h-4 text-[10px]",
                                          difference > 0 ? 'bg-emerald-100 dark:bg-emerald-950 border-emerald-300 dark:border-emerald-800' :
                                          'bg-red-100 dark:bg-red-950 border-red-300 dark:border-red-800'
                                        )}
                                      >
                                        {difference > 0 ? '+' : ''}{difference.toFixed(2)}
                                      </Badge>
                                    )}
                                  </span>
                                )}
                              </>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={headers.length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <p className="mb-2">No results found</p>
                      {searchTerm && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSearchTerm('')}
                          className="flex items-center gap-1"
                        >
                          <RefreshCw className="h-3 w-3" />
                          <span>Clear search</span>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries
          </div>

          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(1)}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
              </PaginationItem>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </PaginationItem>

              {getPageNumbers().map((pageNum, i) => (
                <PaginationItem key={`page-${pageNum}-${i}`}>
                  {pageNum < 0 ? (
                    <PaginationEllipsis />
                  ) : (
                    <Button
                      variant={pageNum === currentPage ? "default" : "outline"}
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setCurrentPage(pageNum)}
                    >
                      {pageNum}
                    </Button>
                  )}
                </PaginationItem>
              ))}

              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </PaginationItem>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(totalPages)}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};
