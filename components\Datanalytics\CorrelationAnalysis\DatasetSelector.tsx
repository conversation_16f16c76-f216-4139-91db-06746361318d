'use client'

import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface DatasetSelectorProps {
  datasets: Array<{
    id: string;
    name: string;
    headers: string[];
  }>;
  selectedDatasetIds: string[];
  onSelectDatasets: (datasetIds: string[]) => void;
}

export const DatasetSelector: React.FC<DatasetSelectorProps> = ({
  datasets,
  selectedDatasetIds,
  onSelectDatasets
}) => {
  const [open, setOpen] = useState(false);
  const [compatibleDatasets, setCompatibleDatasets] = useState<typeof datasets>([]);

  // Group datasets by their headers (structure)
  useEffect(() => {
    if (selectedDatasetIds.length === 0) {
      // If no datasets are selected, all datasets are available
      setCompatibleDatasets(datasets);
      return;
    }

    // Get the headers of the first selected dataset
    const firstSelectedDataset = datasets.find(d => d.id === selectedDatasetIds[0]);
    if (!firstSelectedDataset) {
      setCompatibleDatasets([]);
      return;
    }

    // Find all datasets with the same headers
    const compatible = datasets.filter(dataset => {
      // Check if headers match (same columns in the same order)
      if (dataset.headers.length !== firstSelectedDataset.headers.length) return false;
      
      // Check if all headers match
      return dataset.headers.every(header => 
        firstSelectedDataset.headers.includes(header)
      );
    });

    setCompatibleDatasets(compatible);
  }, [datasets, selectedDatasetIds]);

  const toggleDataset = (datasetId: string) => {
    if (selectedDatasetIds.includes(datasetId)) {
      // Remove dataset
      onSelectDatasets(selectedDatasetIds.filter(id => id !== datasetId));
    } else {
      // Add dataset
      onSelectDatasets([...selectedDatasetIds, datasetId]);
    }
  };

  const removeAllDatasets = () => {
    onSelectDatasets([]);
  };

  return (
    <div className="flex flex-col space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="justify-between"
          >
            {selectedDatasetIds.length > 0
              ? `${selectedDatasetIds.length} datasets selected`
              : "Select datasets..."}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <Command>
            <CommandInput placeholder="Search datasets..." />
            <CommandEmpty>No compatible datasets found.</CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-[200px]">
                {compatibleDatasets.map((dataset) => (
                  <CommandItem
                    key={dataset.id}
                    value={dataset.id}
                    onSelect={() => toggleDataset(dataset.id)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedDatasetIds.includes(dataset.id) ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {dataset.name}
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedDatasetIds.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedDatasetIds.map(id => {
            const dataset = datasets.find(d => d.id === id);
            if (!dataset) return null;
            
            return (
              <Badge key={id} variant="secondary" className="flex items-center gap-1">
                {dataset.name}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1"
                  onClick={() => toggleDataset(id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 text-xs"
            onClick={removeAllDatasets}
          >
            Clear all
          </Button>
        </div>
      )}
    </div>
  );
};
