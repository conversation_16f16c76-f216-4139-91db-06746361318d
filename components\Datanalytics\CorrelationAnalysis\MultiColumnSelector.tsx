'use client'

import React, { useState, useEffect } from 'react';
import { ChevronDown, X, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface MultiColumnSelectorProps {
  headers: string[];
  selectedColumns: string[];
  onSelectColumns: (columns: string[]) => void;
}

export const MultiColumnSelector: React.FC<MultiColumnSelectorProps> = ({
  headers,
  selectedColumns,
  onSelectColumns
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [safeHeaders, setSafeHeaders] = useState<string[]>([]);

  // Ensure headers is an array
  useEffect(() => {
    if (headers && Array.isArray(headers)) {
      setSafeHeaders(headers);
    } else {
      setSafeHeaders([]);
    }
  }, [headers]);

  const toggleColumn = (column: string) => {
    if (selectedColumns.includes(column)) {
      onSelectColumns(selectedColumns.filter(c => c !== column));
    } else {
      onSelectColumns([...selectedColumns, column]);
    }
  };

  const selectAllColumns = () => {
    onSelectColumns([...safeHeaders]);
  };

  const clearAllColumns = () => {
    onSelectColumns([]);
  };

  return (
    <div className="space-y-2">
      <div className="relative">
        <button
          type="button"
          className="w-full flex items-center justify-between px-3 py-2 border border-input rounded-md shadow-sm bg-background text-sm font-medium text-foreground hover:bg-accent/40 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-0 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span>
            {selectedColumns.length > 0
              ? `${selectedColumns.length} columns selected`
              : "Select columns..."}
          </span>
          <ChevronDown className="h-4 w-4 ml-2 text-muted-foreground" />
        </button>

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-background shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-border overflow-auto focus:outline-none sm:text-sm dark:shadow-gray-800">
            {safeHeaders.length > 0 ? (
              <div className="py-1">
                <div className="flex justify-between px-3 py-2 border-b border-border">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-7"
                    onClick={selectAllColumns}
                  >
                    Select All
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-7"
                    onClick={clearAllColumns}
                  >
                    Clear All
                  </Button>
                </div>
                <ul className="py-1">
                  {safeHeaders.map((header) => (
                    <li
                      key={header}
                      className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-accent/40 ${
                        selectedColumns.includes(header) ? 'bg-accent/40 text-foreground' : 'text-foreground'
                      }`}
                      onClick={() => toggleColumn(header)}
                    >
                      <div className="flex items-center">
                        <div className="flex-shrink-0 mr-2">
                          {selectedColumns.includes(header) ? (
                            <Check className="h-4 w-4 text-primary" />
                          ) : (
                            <div className="h-4 w-4 border border-input rounded" />
                          )}
                        </div>
                        <span>{header}</span>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <div className="py-3 px-4 text-sm text-muted-foreground">
                No columns available. Please select a dataset first.
              </div>
            )}
          </div>
        )}
      </div>

      {selectedColumns.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedColumns.map(column => (
            <Badge key={column} variant="secondary" className="flex items-center gap-1">
              {column}
              <button
                type="button"
                className="ml-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-muted-foreground hover:text-foreground focus:outline-none transition-colors"
                onClick={() => toggleColumn(column)}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          
          {selectedColumns.length > 1 && (
            <button
              type="button"
              className="text-xs text-muted-foreground hover:text-foreground transition-colors px-2 py-1 rounded hover:bg-muted"
              onClick={clearAllColumns}
            >
              Clear all
            </button>
          )}
        </div>
      )}
    </div>
  );
};
