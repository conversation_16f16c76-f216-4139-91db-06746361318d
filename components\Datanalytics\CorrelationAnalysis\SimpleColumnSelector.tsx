'use client'

import React, { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface SimpleColumnSelectorProps {
  headers: string[];
  selectedColumn: string | null;
  onSelectColumn: (column: string) => void;
  onSelectAllColumns?: (headers: string[]) => void;
}

export const SimpleColumnSelector: React.FC<SimpleColumnSelectorProps> = ({
  headers,
  selectedColumn,
  onSelectColumn,
  onSelectAllColumns
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [safeHeaders, setSafeHeaders] = useState<string[]>([]);

  // Ensure headers is an array
  useEffect(() => {
    if (headers && Array.isArray(headers)) {
      setSafeHeaders(headers);
    } else {
      setSafeHeaders([]);
    }
  }, [headers]);

  return (
    <div className="relative">
      <button
        type="button"
        className="w-full flex items-center justify-between px-3 py-2 border border-input rounded-md shadow-sm bg-background text-sm font-medium text-foreground hover:bg-accent/40 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-0 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{selectedColumn || "Select column..."}</span>
        <ChevronDown className="h-4 w-4 ml-2 text-muted-foreground" />
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-background shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-border overflow-auto focus:outline-none sm:text-sm dark:shadow-gray-800">
          {safeHeaders.length > 0 ? (
            <ul className="py-1">
              {/* Select All option */}
              {onSelectAllColumns && (
                <li
                  className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-accent/40 text-foreground font-medium border-b border-border`}
                  onClick={() => {
                    onSelectAllColumns(safeHeaders);
                    setIsOpen(false);
                  }}
                >
                  Select All Columns
                </li>
              )}

              {safeHeaders.map((header) => (
                <li
                  key={header}
                  className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-accent/40 ${
                    selectedColumn === header ? 'bg-accent/40 text-foreground' : 'text-foreground'
                  }`}
                  onClick={() => {
                    onSelectColumn(header);
                    setIsOpen(false);
                  }}
                >
                  {header}
                  {selectedColumn === header && (
                    <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-primary">
                      <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </span>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <div className="py-3 px-4 text-sm text-muted-foreground">
              No columns available. Please select datasets first.
            </div>
          )}
        </div>
      )}
    </div>
  );
};
