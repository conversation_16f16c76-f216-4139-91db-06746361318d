'use client'

import React, { useState, useEffect } from 'react';
import { ChevronDown, X } from 'lucide-react';

interface SimpleDatasetSelectorProps {
  datasets: Array<{
    id: string;
    name: string;
    headers: string[];
  }>;
  selectedDatasetIds: string[];
  onSelectDatasets: (datasetIds: string[]) => void;
}

export const SimpleDatasetSelector: React.FC<SimpleDatasetSelectorProps> = ({
  datasets,
  selectedDatasetIds,
  onSelectDatasets
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [compatibleDatasets, setCompatibleDatasets] = useState<typeof datasets>([]);

  // Group datasets by their headers (structure)
  useEffect(() => {
    try {
      if (!datasets || !Array.isArray(datasets)) {
        setCompatibleDatasets([]);
        return;
      }

      if (selectedDatasetIds.length === 0) {
        // If no datasets are selected, all datasets are available
        setCompatibleDatasets(datasets);
        return;
      }

      // Get the headers of the first selected dataset
      const firstSelectedDataset = datasets.find(d => d.id === selectedDatasetIds[0]);
      if (!firstSelectedDataset || !firstSelectedDataset.headers || !Array.isArray(firstSelectedDataset.headers)) {
        setCompatibleDatasets([]);
        return;
      }

      // Find all datasets with the same headers
      const compatible = datasets.filter(dataset => {
        if (!dataset.headers || !Array.isArray(dataset.headers)) return false;

        // Check if headers match (same columns in the same order)
        if (dataset.headers.length !== firstSelectedDataset.headers.length) return false;

        // Check if all headers match
        return dataset.headers.every(header =>
          firstSelectedDataset.headers.includes(header)
        );
      });

      setCompatibleDatasets(compatible);
    } catch (error) {
      console.error('Error finding compatible datasets:', error);
      setCompatibleDatasets([]);
    }
  }, [datasets, selectedDatasetIds]);

  const toggleDataset = (datasetId: string) => {
    if (selectedDatasetIds.includes(datasetId)) {
      // Remove dataset
      onSelectDatasets(selectedDatasetIds.filter(id => id !== datasetId));
    } else {
      // Add dataset
      onSelectDatasets([...selectedDatasetIds, datasetId]);
    }
  };

  const removeAllDatasets = () => {
    onSelectDatasets([]);
  };

  return (
    <div className="space-y-2">
      <div className="relative">
        <button
          type="button"
          className="w-full flex items-center justify-between px-3 py-2 border border-input rounded-md shadow-sm bg-background text-sm font-medium text-foreground hover:bg-accent/40 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-0 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span>
            {selectedDatasetIds.length > 0
              ? `${selectedDatasetIds.length} datasets selected`
              : "Select datasets..."}
          </span>
          <ChevronDown className="h-4 w-4 ml-2 text-muted-foreground" />
        </button>

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-background shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-border overflow-auto focus:outline-none sm:text-sm dark:shadow-gray-800">
            {compatibleDatasets.length > 0 ? (
              <ul className="py-1">
                {compatibleDatasets.map((dataset) => (
                  <li
                    key={dataset.id}
                    className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-accent/40 ${
                      selectedDatasetIds.includes(dataset.id) ? 'bg-accent/40 text-foreground' : 'text-foreground'
                    }`}
                    onClick={() => toggleDataset(dataset.id)}
                  >
                    {dataset.name}
                    {selectedDatasetIds.includes(dataset.id) && (
                      <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-primary">
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="py-3 px-4 text-sm text-muted-foreground">
                No compatible datasets found.
              </div>
            )}
          </div>
        )}
      </div>

      {selectedDatasetIds.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedDatasetIds.map(id => {
            const dataset = datasets.find(d => d.id === id);
            if (!dataset) return null;

            return (
              <div key={id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary dark:bg-primary/20">
                {dataset.name}
                <button
                  type="button"
                  className="ml-1.5 h-4 w-4 rounded-full inline-flex items-center justify-center text-primary/70 hover:text-primary focus:outline-none transition-colors"
                  onClick={() => toggleDataset(id)}
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            );
          })}

          <button
            type="button"
            className="text-xs text-muted-foreground hover:text-foreground transition-colors px-2 py-1 rounded hover:bg-muted"
            onClick={removeAllDatasets}
          >
            Clear all
          </button>
        </div>
      )}
    </div>
  );
};
