'use client'

import DataEditorMain from './DataEditorComponents/DataEditorMain';

// Keep the interfaces as they might be needed for type checking
interface ChartConfig {
  id: string;
  chartType: 'line' | 'bar' | 'area' | 'scatter' | 'pie' | 'radar';
  title: string;
  description?: string;
  xAxis: string;
  yAxis: string;
}

interface DataRow {
  [key: string]: unknown;
}

interface ParseResult {
  data: DataRow[];
  errors: Array<{message: string}>;
  meta: {
    fields?: string[];
  };
}

interface DataProcessingOptions {
  removeNulls: boolean;
  standardize: boolean;
  removeOutliers: boolean;
  fillMissingValues: 'mean' | 'median' | 'mode' | 'none';
}

interface SavedDataset {
  id: string;
  name: string;
  description?: string;
  fileType: string;
  createdAt: string;
  data: any[];
  headers: string[];
}

const DataEditor: React.FC = () => {
  return <DataEditorMain />;
};

export default DataEditor;
