'use client'

import React, { useState, useCallback, useEffect, Suspense, lazy } from 'react';
import { Upload, FileSpreadsheet, Save, RefreshCw, Table, <PERSON><PERSON><PERSON>, Loader2, TrendingUp } from "lucide-react";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from 'sonner';
import { useAuth } from "@clerk/nextjs";
import { TableTab } from '../TableTab';
import { DataOverview } from "../DataOverview";
import { UploadSection } from './UploadSection';
import { VersionHistory } from './VersionHistory';
import { SaveDatasetDialog } from './SaveDatasetDialog';
import { DatasetInfo } from './DatasetInfo';
import { MiniSidebar } from './MiniSidebar';
import { sanitizeExcelData, validateDataset } from './utils';

// Lazy load the CorrelationAnalysis component
const CorrelationAnalysis = lazy(() => import('../CorrelationAnalysis/CorrelationAnalysis').then(
  module => ({ default: module.CorrelationAnalysis })
));

interface DataRow {
  [key: string]: unknown;
}

interface SavedDataset {
  id: string;
  name: string;
  description?: string;
  fileType: string;
  createdAt: string;
  data: any[];
  headers: string[];
  folderId?: string | null;
}

interface StorageInfo {
  used: number;
  total: number;
  percentage: number;
}

interface SaveFormState {
  name: string;
  description: string;
}

interface VersionHistory {
  id: string;
  versionNumber: number;
  changes: {
    type: 'edit' | 'deleteRow' | 'deleteColumn';
    row?: number;
    column?: string;
    oldValue?: any;
    newValue?: any;
  }[];
  createdAt: Date;
  user: {
    name: string;
  };
}

interface DataProcessingOptions {
  removeNulls: boolean;
  standardize: boolean;
  removeOutliers: boolean;
  fillMissingValues: 'mean' | 'median' | 'mode' | 'none';
}

const DataEditorMain: React.FC = () => {
  // State Management
  const [data, setData] = useState<DataRow[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [fileName, setFileName] = useState<string>('');
  const [dataProcessingOptions, setDataProcessingOptions] = useState<DataProcessingOptions>({
    removeNulls: true,
    standardize: false,
    removeOutliers: false,
    fillMissingValues: 'mean'
  });
  const [showUpload, setShowUpload] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveForm, setSaveForm] = useState<SaveFormState>({
    name: '',
    description: ''
  });
  const [savedDatasets, setSavedDatasets] = useState<SavedDataset[]>([]);
  const [folders, setFolders] = useState<any[]>([]);
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(false);
  const [storageInfo, setStorageInfo] = useState<StorageInfo>({
    used: 0,
    total: 5 * 1024 * 1024, // 5MB in bytes
    percentage: 0
  });
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [currentDatasetId, setCurrentDatasetId] = useState<string | null>(null);
  const [versionHistory, setVersionHistory] = useState<VersionHistory[]>([]);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [isLoadingVersion, setIsLoadingVersion] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("visualize");
  const [allVersions, setAllVersions] = useState<Record<string, VersionHistory[]>>({});

  // Add auth hook
  const { isLoaded, isSignedIn } = useAuth();

  // Data Processing Functions
  const processData = useCallback((rawData: DataRow[]) => {
    let processedData = [...rawData];

    if (dataProcessingOptions.removeNulls) {
      processedData = processedData.filter(row =>
        Object.values(row as Record<string, unknown>).every(value => value != null)
      );
    }

    if (dataProcessingOptions.standardize) {
      headers.forEach(header => {
        const values = processedData.map(row => Number(row[header])).filter(v => !isNaN(v));
        if (values.length) {
          const mean = values.reduce((a, b) => a + b, 0) / values.length;
          const std = Math.sqrt(values.reduce((a, b) => a + (b - mean) ** 2, 0) / values.length);
          processedData = processedData.map(row => ({
            ...row,
            [header]: !isNaN(Number(row[header])) ? (Number(row[header]) - mean) / std : row[header]
          }));
        }
      });
    }

    return processedData;
  }, [headers, dataProcessingOptions]);

  // This one is for the toolbar button - just shows the dialog
  const handleSaveClick = async () => {
    if (!isLoaded || !isSignedIn) {
      toast.error('Please sign in to save datasets');
      return;
    }

    setShowSaveDialog(true);
  };

  // This one is for the actual saving process in the dialog
  const handleSaveToDb = async () => {
    try {
      setIsSaving(true);
      setProcessingStage('Validating data...');

      // Sanitize the data first
      const sanitizedData = sanitizeExcelData(data);

      // Validate the sanitized data
      const validation = validateDataset(sanitizedData);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      setProcessingStage('Preparing data for save...');

      const savePromise = fetch('/api/datasets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: saveForm.name,
          description: saveForm.description,
          data: sanitizedData,
          headers: headers,
          fileType: fileName.endsWith('.csv') ? 'csv' : 'excel'
        })
      });

      // Use toast.promise for better UX
      await toast.promise(savePromise, {
        loading: 'Saving dataset...',
        success: async (response) => {
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to save dataset');
          }
          setShowSaveDialog(false);
          setSaveForm({ name: '', description: '' });
          await fetchSavedDatasets();
          return 'Dataset saved successfully';
        },
        error: (error) => {
          console.error('Save error:', error);
          return `Failed to save dataset: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      });

    } catch (error) {
      console.error('Save error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save dataset');
    } finally {
      setIsSaving(false);
      setProcessingStage('');
    }
  };

  // Add function to fetch saved datasets and folders
  const fetchSavedDatasets = async () => {
    if (!isLoaded || !isSignedIn) {
      return;
    }

    setIsLoadingDatasets(true);
    try {
      // Fetch datasets
      const response = await fetch('/api/datasets', {
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to fetch datasets');
      const { datasets } = await response.json();

      console.log('Datasets fetched:', datasets);

      // Calculate total storage used
      const totalUsed = datasets.reduce((acc: number, dataset: any) => {
        return acc + new Blob([JSON.stringify(dataset.data)]).size;
      }, 0);

      setStorageInfo({
        used: totalUsed,
        total: 5 * 1024 * 1024, // 5MB
        percentage: (totalUsed / (5 * 1024 * 1024)) * 100
      });

      // Make sure each dataset has a folderId property (even if null)
      const datasetsWithFolderInfo = datasets.map((dataset: any) => ({
        ...dataset,
        folderId: dataset.folderId || null
      }));

      setSavedDatasets(datasetsWithFolderInfo);

      // Fetch folders
      try {
        const foldersResponse = await fetch('/api/datasets/folders');
        if (foldersResponse.ok) {
          const foldersData = await foldersResponse.json();
          if (foldersData.success) {
            console.log('Folders fetched:', foldersData.folders);
            console.log('Root datasets fetched:', foldersData.datasets);
            setFolders(foldersData.folders || []);

            // If we have root datasets from the folders API, we can use those too
            if (foldersData.datasets && foldersData.datasets.length > 0) {
              // Merge with existing datasets to ensure we have everything
              const allDatasets = [...datasetsWithFolderInfo];

              // Add any root datasets that might not be in the main dataset list
              foldersData.datasets.forEach((rootDataset: any) => {
                if (!allDatasets.some(d => d.id === rootDataset.id)) {
                  allDatasets.push(rootDataset);
                }
              });

              setSavedDatasets(allDatasets);
            }
          }
        }
      } catch (folderError) {
        console.error('Error fetching folders:', folderError);
        // Don't show error toast for folders as it's not critical
      }

      // Fetch version histories for all datasets
      await fetchAllVersionHistories(datasets);
    } catch (error) {
      console.error('Error fetching datasets:', error);
      toast.error('Failed to load saved datasets');
    } finally {
      setIsLoadingDatasets(false);
    }
  };

  // Add function to fetch all version histories
  const fetchAllVersionHistories = async (datasets: SavedDataset[]) => {
    if (datasets.length === 0) return;

    console.log(`Fetching version histories for ${datasets.length} datasets`);

    try {
      // Create a map to store versions by dataset ID
      const versionsByDataset: Record<string, VersionHistory[]> = {};

      // Fetch versions for each dataset sequentially to avoid race conditions
      for (const dataset of datasets) {
        try {
          const response = await fetch(`/api/datasets?datasetId=${dataset.id}`);
          const result = await response.json();

          if (result.success && result.versions) {
            versionsByDataset[dataset.id] = result.versions.sort(
              (a: VersionHistory, b: VersionHistory) => b.versionNumber - a.versionNumber
            );
            console.log(`Found ${result.versions.length} versions for dataset ${dataset.id}`);
          } else {
            console.log(`No versions found for dataset ${dataset.id}`);
            versionsByDataset[dataset.id] = [];
          }
        } catch (err) {
          console.error(`Error fetching versions for dataset ${dataset.id}:`, err);
          versionsByDataset[dataset.id] = [];
        }
      }

      // Store the complete version map in state for reference
      setAllVersions(versionsByDataset);

      // Create a flattened array of all versions (for backward compatibility)
      const allVersionsFlat = Object.values(versionsByDataset).flat();
      setVersionHistory(allVersionsFlat);

      console.log('All versions fetched:', versionsByDataset);
    } catch (error) {
      console.error('Error fetching all version histories:', error);
    }
  };

  // Add useEffect to fetch datasets on mount
  useEffect(() => {
    console.log('DataEditorMain mounted, fetching datasets...');
    fetchSavedDatasets();
  }, []);

  // Add useEffect to log when savedDatasets changes
  useEffect(() => {
    console.log('DataEditorMain: savedDatasets updated:', savedDatasets.length, 'datasets');
    if (savedDatasets.length > 0) {
      console.log('DataEditorMain: First dataset:', {
        id: savedDatasets[0].id,
        name: savedDatasets[0].name,
        folderId: savedDatasets[0].folderId || 'none'
      });
    }

    // If we have datasets but no folders, fetch folders again
    if (savedDatasets.length > 0 && folders.length === 0) {
      console.log('DataEditorMain: We have datasets but no folders, fetching folders again...');
      try {
        fetch('/api/datasets/folders')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              console.log('DataEditorMain: Folders fetched:', data.folders?.length || 0);
              setFolders(data.folders || []);
            }
          })
          .catch(error => {
            console.error('DataEditorMain: Error fetching folders:', error);
          });
      } catch (error) {
        console.error('DataEditorMain: Error fetching folders:', error);
      }
    }
  }, [savedDatasets, folders]);

  // Update the handleLoadDataset function
  const handleLoadDataset = (dataset: SavedDataset) => {
    try {
      setData(dataset.data);
      setHeaders(dataset.headers);
      setFileName(dataset.name);
      setCurrentDatasetId(dataset.id);
      setShowUpload(false);

      toast.success('Dataset loaded successfully');
    } catch (error) {
      console.error('Error loading dataset:', error);
      toast.error('Failed to load dataset');
    }
  };

  // Add delete function
  const handleDeleteDataset = async (datasetId: string) => {
    try {
      const response = await fetch(`/api/datasets/${datasetId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete dataset');
      }

      toast.success('Dataset deleted successfully');
      await fetchSavedDatasets();
    } catch (error) {
      console.error('Error deleting dataset:', error);
      toast.error('Failed to delete dataset');
    }
  };

  // Add function to fetch version history
  const fetchVersionHistory = async (datasetId: string) => {
    try {
      console.log(`Fetching version history for dataset ID: ${datasetId}`);
      const response = await fetch(`/api/datasets?datasetId=${datasetId}`);
      const result = await response.json();

      if (result.success) {
        // Log the actual versions returned from the API
        console.log(`Versions retrieved for ${datasetId}:`, result.versions);

        // Set only the versions for this specific dataset
        setVersionHistory(result.versions || []);

        // Log whether any versions were found
        if (!result.versions || result.versions.length === 0) {
          console.log(`No versions found for dataset ${datasetId}. API returned empty array.`);
        }
      } else {
        console.error(`Failed to fetch versions for dataset ${datasetId}:`, result.error);
      }
    } catch (error) {
      console.error('Error fetching version history:', error);
    }
  };

  // Update the loadVersionData function
  const loadVersionData = async (datasetId: string, versionNumber: number) => {
    if (isLoadingVersion) return; // Prevent multiple clicks

    setIsLoadingVersion(true);
    try {
      // First update UI state to show loading
      toast.loading(`Loading version ${versionNumber}...`, {
        id: `load-version-${datasetId}-${versionNumber}`
      });

      const response = await fetch(`/api/datasets/${datasetId}/versions/${versionNumber}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to load version');
      }

      // Update the data and headers
      setData(data.data);
      setHeaders(data.headers);

      // Update current dataset ID
      setCurrentDatasetId(datasetId);

      // Close the version history dialog
      setShowVersionHistory(false);

      // Switch to table tab after loading
      setActiveTab('table');

      toast.success(`Version ${versionNumber} loaded successfully`, {
        id: `load-version-${datasetId}-${versionNumber}`
      });
    } catch (error) {
      console.error('Error loading version:', error);
      toast.error('Failed to load version', {
        id: `load-version-${datasetId}-${versionNumber}`
      });
    } finally {
      setIsLoadingVersion(false);
    }
  };

  // Add this to handle dataset selection
  const handleDatasetSelect = (dataset: SavedDataset) => {
    try {
      setCurrentDatasetId(dataset.id);
      setData(dataset.data);
      setHeaders(dataset.headers);
      setFileName(dataset.name);
      setShowUpload(false);
      setActiveTab('table'); // Switch to table view

      // Fetch version history
      fetchVersionHistory(dataset.id);

      toast.success('Dataset loaded successfully');
    } catch (error) {
      console.error('Error loading dataset:', error);
      toast.error('Failed to load dataset');
    }
  };

  // Helper function for processing stage
  const [processingStage, setProcessingStage] = useState<string>('');

  // Handler for data loaded from upload
  const handleDataLoaded = (newData: any[], newHeaders: string[], newFileName: string) => {
    setData(newData);
    setHeaders(newHeaders);
    setFileName(newFileName);
    setShowUpload(false);
  };

  // Render dataset info for the upload view
  const renderDatasetInfo = () => {
    return (
      <DatasetInfo
        savedDatasets={savedDatasets}
        allVersions={allVersions}
        onDatasetSelect={handleDatasetSelect}
        onDeleteDataset={handleDeleteDataset}
        onShowVersionHistory={(dataset) => {
          handleDatasetSelect(dataset);
          setShowVersionHistory(true);
        }}
        loadVersionData={loadVersionData}
      />
    );
  };

  return (
    <div className="min-h-screen bg-background overflow-hidden">
      <div className="flex h-screen">
        {/* Mini Sidebar - Always visible */}
        <MiniSidebar
          savedDatasets={savedDatasets}
          storageInfo={storageInfo}
          isLoadingDatasets={isLoadingDatasets}
          onDatasetSelect={handleDatasetSelect}
          onDeleteDataset={handleDeleteDataset}
          onShowVersionHistory={(dataset) => {
            handleDatasetSelect(dataset);
            setShowVersionHistory(true);
          }}
          onUploadClick={() => {
            setShowUpload(true);
            setData([]);
            setHeaders([]);
            setError(null);
          }}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        />

        {/* Main Content Area */}
        {showUpload ? (
          <div className="flex-1">
            <UploadSection
              onDataLoaded={handleDataLoaded}
              savedDatasets={savedDatasets}
              storageInfo={storageInfo}
              isLoadingDatasets={isLoadingDatasets}
              onDatasetSelect={handleDatasetSelect}
              onDeleteDataset={handleDeleteDataset}
              onShowVersionHistory={(dataset) => {
                handleDatasetSelect(dataset);
                setShowVersionHistory(true);
              }}
              allVersions={allVersions}
              renderDatasetInfo={renderDatasetInfo}
            />
          </div>
        ) : (
          <div className="flex-1 flex flex-col overflow-hidden">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col h-screen"
            >
              <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex items-center gap-4 px-2">
                  <TabsList className="h-8">
                    <TabsTrigger value="visualize" className="flex items-center text-xs">
                      <BarChart className="h-3 w-3 mr-1" />
                      Visualize
                    </TabsTrigger>
                    <TabsTrigger value="table" className="flex items-center text-xs">
                      <Table className="h-3 w-3 mr-1" />
                      Table
                    </TabsTrigger>
                    <TabsTrigger value="correlation" className="flex items-center text-xs">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Trends
                    </TabsTrigger>
                  </TabsList>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="h-7 flex items-center gap-1">
                      <FileSpreadsheet className="h-3.5 w-3.5" />
                      <span className="text-xs">{fileName}</span>
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Button variant="outline" size="sm" className="h-7" onClick={() => setData(processData(data))}>
                        <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                        <span className="text-xs">Refresh</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7"
                        onClick={() => {
                          setShowUpload(true);
                          setData([]);
                          setHeaders([]);
                          setError(null);
                        }}
                      >
                        <Upload className="h-3.5 w-3.5 mr-1.5" />
                        <span className="text-xs">Upload</span>
                      </Button>
                      <Button variant="default" size="sm" className="h-7" onClick={handleSaveClick}>
                        <Save className="h-3.5 w-3.5 mr-1.5" />
                        <span className="text-xs">Save</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7"
                        onClick={() => setActiveTab('correlation')}
                      >
                        <TrendingUp className="h-3.5 w-3.5 mr-1.5" />
                        <span className="text-xs">Analyze Trends</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex-1 relative">
                <ScrollArea className="h-[calc(100vh-3rem)] overflow-auto">
                  <TabsContent
                    value="visualize"
                    className="p-2"
                  >
                    <div className="space-y-2">
                      <Suspense fallback={
                        <div className="flex items-center justify-center h-24">
                          <Loader2 className="h-6 w-6 animate-spin text-primary/80" />
                        </div>
                      }>
                        <DataOverview
                          data={data}
                          headers={headers}
                          title="Data Summary"
                          description="Overview of numeric columns"
                        />
                      </Suspense>

                      {/* Dataset Info Table right below the chart */}
                      <DatasetInfo
                        savedDatasets={savedDatasets}
                        allVersions={allVersions}
                        onDatasetSelect={handleDatasetSelect}
                        onDeleteDataset={handleDeleteDataset}
                        onShowVersionHistory={(dataset) => {
                          handleDatasetSelect(dataset);
                          setShowVersionHistory(true);
                        }}
                        loadVersionData={loadVersionData}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent
                    value="table"
                    className="mt-0 absolute inset-0 h-full"
                  >
                    <Suspense fallback={
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-10 w-10 animate-spin text-primary/80" />
                      </div>
                    }>
                      {data.length > 0 ? (
                        <div className="h-full">
                          <TableTab
                            // @ts-ignore
                            data={data}
                            headers={headers}
                            datasetId={currentDatasetId || undefined}
                            totalRows={data.length}
                            currentPage={1}
                            isLoadingMore={false}
                          />
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center h-full p-8">
                          <FileSpreadsheet className="h-10 w-10 text-muted-foreground mb-4 opacity-50" />
                          <h3 className="text-lg font-medium">No data to display</h3>
                          <p className="text-sm text-muted-foreground mt-2">
                            Upload a file or select a dataset to view the table
                          </p>
                        </div>
                      )}
                    </Suspense>
                  </TabsContent>

                  <TabsContent
                    value="correlation"
                    className="mt-0 absolute inset-0 h-full"
                  >
                    <Suspense fallback={
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-10 w-10 animate-spin text-primary/80" />
                      </div>
                    }>
                      <div className="p-4">
                        <CorrelationAnalysis
                          savedDatasets={savedDatasets}
                          isLoading={isLoadingDatasets}
                        />
                      </div>
                    </Suspense>
                  </TabsContent>
                </ScrollArea>
              </div>
            </Tabs>
          </div>
        )}
      </div>

      {/* Save Dialog */}
      <SaveDatasetDialog
        showSaveDialog={showSaveDialog}
        setShowSaveDialog={setShowSaveDialog}
        saveForm={saveForm}
        setSaveForm={setSaveForm}
        handleSaveToDb={handleSaveToDb}
        isSaving={isSaving}
      />

      {/* Version History Dialog */}
      <VersionHistory
        showVersionHistory={showVersionHistory}
        setShowVersionHistory={setShowVersionHistory}
        versionHistory={versionHistory}
        currentDatasetId={currentDatasetId}
        loadVersionData={loadVersionData}
      />
    </div>
  );
};

export default DataEditorMain;
