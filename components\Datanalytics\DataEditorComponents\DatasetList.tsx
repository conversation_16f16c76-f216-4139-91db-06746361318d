'use client'

import React from 'react';
import { Database, FileSpreadsheet, GitBranch, Trash2, Clock, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { formatBytes, formatDate, formatNumber } from './utils';


interface DatasetListProps {
  savedDatasets: any[];
  storageInfo: {
    used: number;
    total: number;
    percentage: number;
  };
  isLoadingDatasets: boolean;
  onDatasetSelect: (dataset: any) => void;
  onDeleteDataset: (datasetId: string) => void;
  onShowVersionHistory: (dataset: any) => void;
}

export const DatasetList: React.FC<DatasetListProps> = ({
  savedDatasets,
  storageInfo,
  isLoadingDatasets,
  onDatasetSelect,
  onDeleteDataset,
  onShowVersionHistory
}) => {
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b bg-muted/30">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-sm">DATASETS</h3>
          <Badge variant="outline" className="font-mono text-xs">
            {savedDatasets.length}
          </Badge>
        </div>
        <div className="space-y-1">
          <div className="text-xs text-muted-foreground">
            {formatBytes(storageInfo.used)} of {formatBytes(storageInfo.total)} used
          </div>
          <Progress value={storageInfo.percentage} className="h-1" />
        </div>
      </div>

      <ScrollArea className="flex-1">
        {isLoadingDatasets ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        ) : savedDatasets.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-center">
            <Database className="h-6 w-6 text-muted-foreground mb-2 opacity-40" />
            <p className="text-xs text-muted-foreground">No saved datasets</p>
          </div>
        ) : (
          <div className="py-2">
            {savedDatasets.map((dataset) => (
              <div
                key={dataset.id}
                className="group px-3 py-2 hover:bg-accent/40 transition-colors rounded-sm mx-1 cursor-pointer"
                onClick={() => onDatasetSelect(dataset)}
              >
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-2 min-w-0">
                    <FileSpreadsheet className="h-3.5 w-3.5 text-primary opacity-70" />
                    <span className="font-medium text-sm truncate">{dataset.name}</span>
                  </div>
                  <div className="flex opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        onShowVersionHistory(dataset);
                      }}
                    >
                      <GitBranch className="h-3 w-3" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-destructive"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Dataset</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete this dataset? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDeleteDataset(dataset.id)}
                            className="bg-destructive hover:bg-destructive/90"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-x-2 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 opacity-70" />
                    <span className="truncate">{formatDate(dataset.createdAt).split(' ')[0]}</span>
                  </div>
                  <div className="flex items-center gap-1 justify-end">
                    <span>{formatBytes(calculateDataSize(dataset.data))}</span>
                  </div>
                </div>
                <div className="mt-1 text-xs text-muted-foreground">
                  <span>{formatNumber(dataset.data.length)} rows × {dataset.headers.length} cols</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};

// Helper function to calculate data size
const calculateDataSize = (data: any): number => {
  return new Blob([JSON.stringify(data)]).size;
};
