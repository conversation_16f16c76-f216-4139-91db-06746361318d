'use client'

import React, { useState, useEffect } from 'react';
import { Database, FileSpreadsheet, GitBranch, Trash2, Clock, Loader2, Upload, ChevronLeft, ChevronRight, PanelLeftClose, PanelLeftOpen, FolderPlus, Folder, FolderOpen, Plus, MoreHorizontal, Move } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, <PERSON><PERSON>Tit<PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { formatBytes, formatDate, formatNumber, calculateDataSize } from './utils';
import { motion } from "framer-motion";
import { toast } from 'sonner';
import { cn } from "@/lib/utils";

export interface DatasetItem {
  id: string;
  name: string;
  description?: string;
  fileType: string;
  createdAt: string;
  data: any[];
  headers: string[];
  folderId?: string | null;
}

interface FolderItem {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  parentId?: string | null;
  children: FolderItem[];
  datasets: DatasetItem[];
}

interface MiniSidebarProps {
  savedDatasets: DatasetItem[];
  storageInfo: {
    used: number;
    total: number;
    percentage: number;
  };
  isLoadingDatasets: boolean;
  onDatasetSelect: (dataset: DatasetItem) => void;
  onDeleteDataset: (datasetId: string) => void;
  onShowVersionHistory: (dataset: DatasetItem) => void;
  onUploadClick: () => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

// Format values to MB with 1 decimal place
const formatToMB = (value: number) => {
  return `${value.toFixed(1)} MB`;
};

export const MiniSidebar: React.FC<MiniSidebarProps> = ({
  savedDatasets,
  storageInfo,
  isLoadingDatasets,
  onDatasetSelect,
  onDeleteDataset,
  onShowVersionHistory,
  onUploadClick,
  isCollapsed,
  onToggleCollapse
}) => {
  // State for folders
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [rootDatasets, setRootDatasets] = useState<DatasetItem[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderParentId, setNewFolderParentId] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<{ type: 'folder' | 'dataset', id: string } | null>(null);
  const [dragOverItem, setDragOverItem] = useState<{ type: 'folder' | 'root', id: string | null } | null>(null);

  // Store previous datasets length to compare
  const [prevDatasetsLength, setPrevDatasetsLength] = useState(0);
  const [hasFetchedFolders, setHasFetchedFolders] = useState(false);

  // Fetch folders on component mount only
  useEffect(() => {
    console.log('MiniSidebar: Initial mount, fetching folders');
    fetchFolders();
    setHasFetchedFolders(true);
  }, []); // Empty dependency array means this runs once on mount

  // Handle dataset changes separately, without re-fetching folders
  useEffect(() => {
    console.log('MiniSidebar: savedDatasets changed, length =', savedDatasets.length);
    
    // Only process datasets if we already have the folder structure
    // or if the dataset count has actually changed (not just references)
    if (hasFetchedFolders && savedDatasets.length !== prevDatasetsLength) {
      console.log('MiniSidebar: Processing datasets without re-fetching folders');
      setPrevDatasetsLength(savedDatasets.length);
      
      // If we have datasets but no folders, make sure to display them as root datasets
      if (savedDatasets.length > 0 && folders.length === 0 && rootDatasets.length === 0) {
        console.log('MiniSidebar: Setting root datasets directly from savedDatasets');
        setRootDatasets(savedDatasets);
      } else {
        // Just update the datasets within the existing folder structure
        organizeDatasetsByFolder();
      }
    }
  }, [savedDatasets, hasFetchedFolders, folders.length, rootDatasets.length, prevDatasetsLength]);

  // Function to fetch folders
  const fetchFolders = async () => {
    try {
      console.log('MiniSidebar: Fetching folders and datasets...');
      console.log('MiniSidebar: Current savedDatasets length =', savedDatasets.length);

      // First, make sure we have all datasets from the parent component
      if (savedDatasets.length > 0) {
        console.log('MiniSidebar: Using savedDatasets from parent component');

        // Organize datasets by folder client-side first to ensure we have something to display
        organizeDatasetsByFolder();
      }

      // Then fetch folders structure from API
      const response = await fetch('/api/datasets/folders');
      if (!response.ok) {
        throw new Error('Failed to fetch folders');
      }

      const data = await response.json();
      if (data.success) {
        console.log('MiniSidebar: Folders fetched successfully:', data.folders?.length || 0, 'folders');
        console.log('MiniSidebar: Root datasets fetched from API:', data.datasets?.length || 0, 'datasets');

        // Check if we have any datasets from the API
        if (data.datasets && data.datasets.length > 0) {
          console.log('MiniSidebar: Sample dataset from API:', data.datasets[0].name);
        }

        // Process the folder structure to ensure all subfolders are properly included
        const processedFolders = await processFolderStructure(data.folders || []);

        // Set folders from API
        setFolders(processedFolders);

        // Merge root datasets from API with any that might not be in folders
        const apiRootDatasets = data.datasets || [];

        // Find datasets that are in savedDatasets but not in apiRootDatasets or any folder
        const datasetsInFolders = new Set();

        // Add all datasets in folders to the set (including subfolders)
        const addDatasetIdsFromFolder = (folder: FolderItem) => {
          if (folder.datasets) {
            folder.datasets.forEach((dataset: DatasetItem) => {
              datasetsInFolders.add(dataset.id);
            });
          }

          if (folder.children) {
            folder.children.forEach(childFolder => {
              addDatasetIdsFromFolder(childFolder);
            });
          }
        };

        if (processedFolders) {
          processedFolders.forEach((folder: FolderItem) => {
            addDatasetIdsFromFolder(folder);
          });
        }

        // Add all root datasets to the set
        apiRootDatasets.forEach((dataset: DatasetItem) => {
          datasetsInFolders.add(dataset.id);
        });

        // Find datasets that are not in any folder
        const missingDatasets = savedDatasets.filter(
          dataset => !datasetsInFolders.has(dataset.id) && !dataset.folderId
        );

        console.log('MiniSidebar: Found', missingDatasets.length, 'datasets not in any folder');

        // Combine API root datasets with missing datasets
        const allRootDatasets = [...apiRootDatasets, ...missingDatasets];
        console.log('MiniSidebar: Total root datasets:', allRootDatasets.length);

        setRootDatasets(allRootDatasets);
      } else {
        console.error('MiniSidebar: API returned success: false', data);
        // If API fails, organize datasets by folder client-side
        organizeDatasetsByFolder();
      }
    } catch (error) {
      console.error('MiniSidebar: Error fetching folders:', error);
      // If API fails, organize datasets by folder client-side
      organizeDatasetsByFolder();
    }
  };

  // Helper function to process folder structure and fetch any missing subfolders
  const processFolderStructure = async (folders: FolderItem[]): Promise<FolderItem[]> => {
    // For each folder, ensure its children are fully populated
    const processedFolders = [...folders];

    // Function to fetch a folder's complete data including datasets
    const fetchFolderDetails = async (folderId: string): Promise<FolderItem | null> => {
      try {
        const response = await fetch(`/api/datasets/folders?folderId=${folderId}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch folder details for ${folderId}`);
        }

        const data = await response.json();
        if (data.success && data.folder) {
          return data.folder;
        }
        return null;
      } catch (error) {
        console.error(`Error fetching folder details for ${folderId}:`, error);
        return null;
      }
    };

    // Process each folder to ensure its children and datasets are fully populated
    for (let i = 0; i < processedFolders.length; i++) {
      const folder = processedFolders[i];

      // If the folder has children, process them recursively
      if (folder.children && folder.children.length > 0) {
        // Fetch complete details for each child folder
        const childPromises = folder.children.map(child => fetchFolderDetails(child.id));
        const childResults = await Promise.all(childPromises);

        // Replace the children with the fully populated versions
        folder.children = childResults.filter(Boolean) as FolderItem[];
      }
    }

    return processedFolders;
  };

  // Fallback function to organize datasets by folder client-side
  const organizeDatasetsByFolder = () => {
    console.log('MiniSidebar: Organizing datasets by folder client-side');
    console.log('MiniSidebar: Available datasets:', savedDatasets.length);

    if (savedDatasets.length === 0) {
      console.log('MiniSidebar: No datasets available to organize');
      return;
    }

    // Group datasets by folderId
    const datasetsByFolder: Record<string, DatasetItem[]> = {};
    const rootItems: DatasetItem[] = [];
    const folderMap: Record<string, FolderItem> = {};
    const rootFolders: FolderItem[] = [];

    // First pass: create folder objects and organize datasets
    savedDatasets.forEach(dataset => {
      // Make sure we have a valid dataset with all required fields
      if (!dataset.id || !dataset.name) {
        console.warn('MiniSidebar: Invalid dataset found:', dataset);
        return;
      }

      console.log(`MiniSidebar: Processing dataset ${dataset.name}, folderId=${dataset.folderId || 'none'}`);

      if (dataset.folderId) {
        if (!datasetsByFolder[dataset.folderId]) {
          datasetsByFolder[dataset.folderId] = [];
        }
        datasetsByFolder[dataset.folderId].push(dataset);
      } else {
        // Only add to root items if not already there
        if (!rootItems.some(item => item.id === dataset.id)) {
          rootItems.push(dataset);
        }
      }
    });

    console.log('MiniSidebar: Root datasets:', rootItems.length);
    console.log('MiniSidebar: Datasets by folder:', Object.keys(datasetsByFolder).length, 'folders');

    // Create a simple folder structure based on dataset folderIds
    // This is a fallback when the API call fails
    Object.keys(datasetsByFolder).forEach(folderId => {
      if (!folderMap[folderId]) {
        folderMap[folderId] = {
          id: folderId,
          name: `Folder ${folderId.substring(0, 5)}...`, // Create a placeholder name
          createdAt: new Date().toISOString(),
          children: [],
          datasets: datasetsByFolder[folderId]
        };
        rootFolders.push(folderMap[folderId]);
      }
    });

    // If we have no folders but have datasets, make sure they're displayed as root items
    if (rootItems.length === 0 && savedDatasets.length > 0) {
      console.log('MiniSidebar: No root items found, adding all datasets as root items');
      // Add all datasets as root items
      savedDatasets.forEach(dataset => {
        if (!dataset.id || !dataset.name) return;
        if (!rootItems.some(item => item.id === dataset.id)) {
          rootItems.push(dataset);
        }
      });
    }

    console.log('MiniSidebar: Setting folders:', rootFolders.length);
    console.log('MiniSidebar: Setting root datasets:', rootItems.length);

    setFolders(rootFolders);
    setRootDatasets(rootItems);

    console.log('MiniSidebar: Client-side organization complete');
  };

  // Function to create a new folder
  const createFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error('Folder name cannot be empty');
      return;
    }

    try {
      // Show loading toast
      toast.loading('Creating folder...', { id: 'create-folder' });

      console.log('Creating folder with name:', newFolderName, 'parentId:', newFolderParentId);

      const response = await fetch('/api/datasets/folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newFolderName,
          parentId: newFolderParentId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create folder');
      }

      const data = await response.json();
      console.log('Create folder response:', data);

      if (data.success) {
        toast.success('Folder created successfully', { id: 'create-folder' });
        setIsCreatingFolder(false);
        setNewFolderName('');
        setNewFolderParentId(null);

        // Expand the parent folder if one was specified
        if (newFolderParentId) {
          setExpandedFolders(prev => ({
            ...prev,
            [newFolderParentId]: true
          }));
        }

        // Refresh folders
        await fetchFolders();
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error(`Failed to create folder: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: 'create-folder' });
    }
  };

  // Function to delete a folder
  const deleteFolder = async (folderId: string) => {
    try {
      // Show loading toast
      toast.loading('Deleting folder...', { id: 'delete-folder' });

      console.log('Deleting folder with ID:', folderId);

      const response = await fetch(`/api/datasets/folders?folderId=${folderId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete folder');
      }

      const data = await response.json();
      console.log('Delete folder response:', data);

      if (data.success) {
        toast.success('Folder deleted successfully', { id: 'delete-folder' });

        // Remove from expanded folders state
        setExpandedFolders(prev => {
          const newState = { ...prev };
          delete newState[folderId];
          return newState;
        });

        // Refresh folders
        await fetchFolders();
      }
    } catch (error) {
      console.error('Error deleting folder:', error);
      toast.error(`Failed to delete folder: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: 'delete-folder' });
    }
  };

  // Function to toggle folder expansion
  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, type: 'folder' | 'dataset', id: string) => {
    setDraggedItem({ type, id });
    e.dataTransfer.setData('text/plain', JSON.stringify({ type, id }));
    e.dataTransfer.effectAllowed = 'move';
    console.log(`Started dragging ${type} with id ${id}`);
  };

  const handleDragOver = (e: React.DragEvent, type: 'folder' | 'root', id: string | null) => {
    e.preventDefault();
    e.stopPropagation();

    if (!draggedItem) return;

    // Prevent dropping a folder into itself or its descendants
    if (draggedItem.type === 'folder' && type === 'folder' && draggedItem.id === id) {
      return;
    }

    setDragOverItem({ type, id });
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragLeave = () => {
    setDragOverItem(null);
  };

  const handleDrop = async (e: React.DragEvent, targetType: 'folder' | 'root', targetId: string | null) => {
    e.preventDefault();
    e.stopPropagation();

    if (!draggedItem) return;

    // Reset drag states
    setDragOverItem(null);

    // Prevent dropping a folder into itself
    if (draggedItem.type === 'folder' && targetType === 'folder' && draggedItem.id === targetId) {
      setDraggedItem(null);
      return;
    }

    console.log(`Dropping ${draggedItem.type} with id ${draggedItem.id} into ${targetType} with id ${targetId || 'root'}`);

    try {
      if (draggedItem.type === 'dataset') {
        // Show loading toast
        toast.loading('Moving dataset...', { id: 'move-dataset' });

        // If the target is a folder, expand it to show the moved dataset
        if (targetType === 'folder' && targetId) {
          setExpandedFolders(prev => ({
            ...prev,
            [targetId]: true
          }));
        }

        // Move dataset to folder
        const response = await fetch('/api/datasets/move', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            datasetId: draggedItem.id,
            folderId: targetType === 'folder' ? targetId : null
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to move dataset');
        }

        const result = await response.json();
        console.log('Move dataset result:', result);

        // Update the UI immediately to reflect the change
        if (result.success && result.dataset) {
          // If moving to root, add to root datasets
          if (targetType === 'root') {
            // Find the dataset in the savedDatasets array
            const movedDataset = savedDatasets.find(d => d.id === draggedItem.id);
            if (movedDataset) {
              // Update the dataset's folderId
              const updatedDataset = { ...movedDataset, folderId: null };

              // Add to root datasets if not already there
              setRootDatasets(prev => {
                if (!prev.some(d => d.id === updatedDataset.id)) {
                  return [...prev, updatedDataset];
                }
                return prev;
              });
            }
          }
        }

        toast.success('Dataset moved successfully', { id: 'move-dataset' });
      } else if (draggedItem.type === 'folder') {
        // Show loading toast
        toast.loading('Moving folder...', { id: 'move-folder' });

        // If the target is a folder, expand it to show the moved subfolder
        if (targetType === 'folder' && targetId) {
          setExpandedFolders(prev => ({
            ...prev,
            [targetId]: true
          }));
        }

        // Move folder to another folder
        const response = await fetch('/api/datasets/move', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            folderId: draggedItem.id,
            parentId: targetType === 'folder' ? targetId : null
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to move folder');
        }

        const result = await response.json();
        console.log('Move folder result:', result);

        toast.success('Folder moved successfully', { id: 'move-folder' });
      }

      // Refresh folders to get the updated structure
      await fetchFolders();
    } catch (error) {
      console.error('Error moving item:', error);
      toast.error(`Failed to move item: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        id: draggedItem.type === 'dataset' ? 'move-dataset' : 'move-folder'
      });
    }

    setDraggedItem(null);
  };

  // Render a folder with its children
  const renderFolder = (folder: FolderItem, depth: number) => {
    const isExpanded = expandedFolders[folder.id];
    const isBeingDraggedOver = dragOverItem?.type === 'folder' && dragOverItem.id === folder.id;
    const isBeingDragged = draggedItem?.type === 'folder' && draggedItem.id === folder.id;

    // Check if this folder is a descendant of the dragged folder to prevent circular references
    const isDescendantOfDragged = (currentFolder: FolderItem): boolean => {
      if (!draggedItem || draggedItem.type !== 'folder') return false;

      if (currentFolder.id === draggedItem.id) return true;

      if (currentFolder.children && currentFolder.children.length > 0) {
        return currentFolder.children.some(child => isDescendantOfDragged(child));
      }

      return false;
    };

    // Determine if this folder can be a drop target
    const canBeDropTarget = !isBeingDragged &&
      !(draggedItem?.type === 'folder' && isDescendantOfDragged(folder));

    return (
      <div key={folder.id} className={`ml-${depth * 2}`}>
        <div
          className={cn(
            "group relative rounded-sm mx-1 transition-colors",
            isBeingDraggedOver ? "bg-primary/10 border-2 border-dashed border-primary/50" : "hover:bg-accent/40",
            isBeingDragged ? "opacity-50" : "opacity-100",
            !canBeDropTarget && draggedItem ? "cursor-not-allowed" : ""
          )}
          draggable
          onDragStart={(e) => handleDragStart(e, 'folder', folder.id)}
          onDragOver={(e) => {
            if (canBeDropTarget) {
              handleDragOver(e, 'folder', folder.id);
            } else {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
          onDragLeave={handleDragLeave}
          onDrop={(e) => {
            if (canBeDropTarget) {
              handleDrop(e, 'folder', folder.id);
            } else {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
        >
          {isCollapsed ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="p-2 flex flex-col items-center">
                    <Folder className="h-4 w-4 text-primary/70" />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="max-w-[200px]">
                  <p className="font-medium text-sm">{folder.name}</p>
                  <div className="text-xs text-muted-foreground mt-1">
                    <div>{folder.children.length} folders</div>
                    <div>{folder.datasets.length} datasets</div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            <div className="px-3 py-2">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-2 min-w-0 cursor-pointer"
                  onClick={() => toggleFolder(folder.id)}
                >
                  <Button variant="ghost" size="icon" className="h-5 w-5 p-0">
                    <ChevronRight className={`h-3.5 w-3.5 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
                  </Button>
                  {isExpanded ? (
                    <FolderOpen className="h-3.5 w-3.5 text-primary/70" />
                  ) : (
                    <Folder className="h-3.5 w-3.5 text-primary/70" />
                  )}
                  <span className="font-medium text-sm truncate">{folder.name}</span>
                  <Badge variant="outline" className="text-[10px] px-1 h-4">
                    {folder.datasets?.length || 0}
                  </Badge>
                </div>

                <div className="flex opacity-0 group-hover:opacity-100 transition-opacity">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => {
                          setIsCreatingFolder(true);
                          setNewFolderParentId(folder.id);
                        }}
                      >
                        <FolderPlus className="h-3.5 w-3.5 mr-2" />
                        <span>New Subfolder</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-destructive"
                        onClick={() => {
                          if (confirm(`Are you sure you want to delete the folder "${folder.name}"? This will move all datasets to the root.`)) {
                            deleteFolder(folder.id);
                          }
                        }}
                      >
                        <Trash2 className="h-3.5 w-3.5 mr-2" />
                        <span>Delete Folder</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Render children if expanded */}
        {isExpanded && !isCollapsed && (
          <div className={`ml-${depth > 0 ? 4 : 2}`}>
            {/* Render subfolders */}
            {folder.children?.map((childFolder) => renderFolder(childFolder, depth + 1))}

            {/* Render datasets in this folder */}
            {folder.datasets?.map((dataset, index) => renderDataset(dataset, index, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  // Render a dataset item
  const renderDataset = (dataset: DatasetItem, index: number, depth: number = 0) => {
    const isBeingDragged = draggedItem?.type === 'dataset' && draggedItem.id === dataset.id;

    // Log dataset information to help debug
    console.log(`MiniSidebar: Rendering dataset ${index}:`, {
      id: dataset.id,
      name: dataset.name,
      folderId: dataset.folderId,
      dataLength: dataset.data?.length || 0
    });

    // Safety check for required dataset properties
    if (!dataset.id || !dataset.name) {
      console.error('Invalid dataset encountered:', dataset);
      return null;
    }

    // Calculate dataset size and row/column counts safely
    const dataSize = dataset.data ? calculateDataSize(dataset.data) : 0;
    const rowCount = dataset.data?.length || 0;
    const colCount = dataset.headers?.length || 0;
    const dateStr = dataset.createdAt ? formatDate(dataset.createdAt).split(' ')[0] : 'Unknown date';

    return (
      <TooltipProvider key={dataset.id}>
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.05 }}
          className={cn(
            "group rounded-sm mx-1 cursor-pointer transition-colors",
            isCollapsed ? "px-2 py-2" : `px-3 py-2 ml-${depth > 0 ? depth * 4 : 0}`,
            isBeingDragged ? "opacity-50" : "opacity-100",
            "hover:bg-accent/40"
          )}
          draggable
          // @ts-ignore
          onDragStart={(e) => handleDragStart(e, 'dataset', dataset.id)}
          onDragOver={(e) => e.stopPropagation()} // Prevent dataset from being a drop target
          onClick={() => onDatasetSelect(dataset)}
        >
          {isCollapsed ? (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex flex-col items-center">
                  <FileSpreadsheet className="h-4 w-4 text-primary mb-1" />
                  <div className="w-6 h-6 flex items-center justify-center bg-primary/10 rounded-sm">
                    <span className="text-[10px] font-medium">{dataset.name.substring(0, 2).toUpperCase()}</span>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="right" className="max-w-[200px]">
                <p className="font-medium text-sm">{dataset.name}</p>
                <div className="text-xs text-muted-foreground mt-1">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 opacity-70" />
                    <span>{dateStr}</span>
                  </div>
                  <div>{formatBytes(dataSize)}</div>
                  <div>{formatNumber(rowCount)} rows × {colCount} cols</div>
                </div>
              </TooltipContent>
            </Tooltip>
          ) : (
            <>
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-2 min-w-0">
                  <FileSpreadsheet className="h-3.5 w-3.5 text-primary opacity-70" />
                  <span className="font-medium text-sm truncate">{dataset.name}</span>
                </div>
                <div className="flex opacity-0 group-hover:opacity-100 transition-opacity">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          onShowVersionHistory(dataset);
                        }}
                      >
                        <GitBranch className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Version History</p>
                    </TooltipContent>
                  </Tooltip>

                  <AlertDialog>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-destructive"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </AlertDialogTrigger>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs">Delete Dataset</p>
                      </TooltipContent>
                    </Tooltip>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Dataset</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this dataset? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDeleteDataset(dataset.id)}
                          className="bg-destructive hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 opacity-70" />
                  <span className="truncate">{dateStr}</span>
                </div>
                <div className="flex items-center gap-1 justify-end">
                  <span>{formatBytes(dataSize)}</span>
                </div>
              </div>
              <div className="mt-1 text-xs text-muted-foreground">
                <span>{formatNumber(rowCount)} rows × {colCount} cols</span>
              </div>
            </>
          )}
        </motion.div>
      </TooltipProvider>
    );
  };
  return (
    <div
      className={`flex flex-col h-screen border-r bg-background/95 transition-all duration-300 ${
        isCollapsed ? "w-12" : "w-64"
      }`}
      style={{
        width: isCollapsed ? '48px' : '256px',
        minWidth: isCollapsed ? '48px' : '256px',
        maxWidth: isCollapsed ? '48px' : '256px',
      }}
    >
      <div className={`${isCollapsed ? "p-2" : "p-3"} border-b bg-muted/30 relative`}>
        <div className="absolute right-0 top-0">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 rounded-none rounded-bl"
            onClick={onToggleCollapse}
          >
            {isCollapsed ? <PanelLeftOpen className="h-3.5 w-3.5" /> : <PanelLeftClose className="h-3.5 w-3.5" />}
          </Button>
        </div>

        {!isCollapsed && (
          <>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-sm">DATASETS</h3>
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="font-mono text-xs">
                  {savedDatasets.length}
                </Badge>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => {
                          setIsCreatingFolder(true);
                          setNewFolderParentId(null);
                        }}
                      >
                        <FolderPlus className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Create Folder</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground flex justify-between">
                <span>{formatToMB(storageInfo.used)} / {formatToMB(storageInfo.total)}</span>
              </div>
              <Progress value={storageInfo.percentage} className="h-1" />
            </div>
            <div className="flex gap-1 mt-3">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 text-xs h-8"
                onClick={onUploadClick}
              >
                <Upload className="h-3.5 w-3.5 mr-1.5" />
                Upload Dataset
              </Button>
            </div>
          </>
        )}

        {isCollapsed && (
          <div className="flex flex-col items-center py-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 mb-1"
                    onClick={onUploadClick}
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p className="text-xs">Upload New Dataset</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 mb-1"
                    onClick={() => {
                      setIsCreatingFolder(true);
                      setNewFolderParentId(null);
                    }}
                  >
                    <FolderPlus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p className="text-xs">Create Folder</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Badge variant="outline" className="font-mono text-xs">
              {savedDatasets.length}
            </Badge>
          </div>
        )}
      </div>

      <ScrollArea className="flex-1 h-[calc(100vh-120px)]">
        {isLoadingDatasets ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        ) : savedDatasets.length === 0 && folders.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-center">
            <Database className="h-6 w-6 text-muted-foreground mb-2 opacity-40" />
            {!isCollapsed && <p className="text-xs text-muted-foreground">No saved datasets</p>}
          </div>
        ) : (
          <div
            className="py-2 overflow-y-auto"
            onDragOver={(e) => handleDragOver(e, 'root', null)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, 'root', null)}
          >
            {/* Root area highlight when dragging */}
            {dragOverItem?.type === 'root' && (
              <div className="mx-2 mb-2 border-2 border-dashed border-primary/50 rounded-md p-1 bg-primary/5">
                <div className="text-xs text-center text-muted-foreground">Drop here to move to root</div>
              </div>
            )}

            {/* Render folders */}
            {folders.map((folder) => renderFolder(folder, 0))}

            {/* Render root datasets */}
            {rootDatasets.map((dataset, index) => renderDataset(dataset, index))}
          </div>
        )}
      </ScrollArea>

      {/* Create Folder Dialog */}
      <Dialog open={isCreatingFolder} onOpenChange={setIsCreatingFolder}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Enter a name for your new folder.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                className="col-span-3"
                placeholder="My Folder"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreatingFolder(false)}>Cancel</Button>
            <Button onClick={createFolder}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
