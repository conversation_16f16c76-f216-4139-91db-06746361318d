'use client'

import React from 'react';
import { Loader2 } from "lucide-react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface SaveFormState {
  name: string;
  description: string;
}

interface SaveDatasetDialogProps {
  showSaveDialog: boolean;
  setShowSaveDialog: (show: boolean) => void;
  saveForm: SaveFormState;
  setSaveForm: React.Dispatch<React.SetStateAction<SaveFormState>>;
  handleSaveToDb: () => Promise<void>;
  isSaving: boolean;
}

export const SaveDatasetDialog: React.FC<SaveDatasetDialogProps> = ({
  showSaveDialog,
  setShowSaveDialog,
  saveForm,
  setSaveForm,
  handleSaveToDb,
  isSaving
}) => {
  return (
    <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Save Dataset</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={saveForm.name}
              onChange={(e) => setSaveForm((prev) => ({ ...prev, name: e.target.value }))}
              placeholder="Enter dataset name"
              disabled={isSaving}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={saveForm.description}
              onChange={(e) => setSaveForm((prev) => ({ ...prev, description: e.target.value }))}
              placeholder="Enter dataset description"
              disabled={isSaving}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setShowSaveDialog(false)}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveToDb}
            disabled={isSaving || !saveForm.name}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
