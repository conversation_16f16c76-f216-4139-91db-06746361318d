import { format } from 'date-fns';

// Format bytes to human-readable format
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// Format number with locale
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('fr-MA', {
    maximumFractionDigits: 2
  }).format(num);
};

// Format date
export const formatDate = (date: Date | string | undefined) => {
  if (!date) return 'Never';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'MMM dd, yyyy HH:mm');
};

// Format cell value
export const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) return '—';
  if (typeof value === 'number') {
    return new Intl.NumberFormat('fr-MA').format(value);
  }
  return String(value);
};

// Calculate data size
export const calculateDataSize = (data: any): number => {
  return new Blob([JSON.stringify(data)]).size;
};

// Sanitize Excel data
export interface ExcelCell {
  result?: any;
  text?: string;
  richText?: Array<{ text: string }>;
}

export const sanitizeExcelData = (data: any[]): any[] => {
  return data.map(row => {
    const sanitizedRow: any = {};
    for (const [key, value] of Object.entries(row)) {
      // Handle different value types
      if (value === null || value === undefined) {
        sanitizedRow[key] = null;
      } else if (typeof value === 'object') {
        // If the value is an object (might be an Excel cell with formatting)
        const cellValue = value as ExcelCell;
        if ('result' in cellValue) {
          // Excel cell with a result property
          sanitizedRow[key] = cellValue.result;
        } else if ('text' in cellValue || 'richText' in cellValue) {
          // Excel cell with text or richText
          sanitizedRow[key] = cellValue.text || cellValue.richText?.map(rt => rt.text).join('');
        } else if (value instanceof Date) {
          // Handle Date objects
          sanitizedRow[key] = value.toISOString();
        } else {
          // For other objects, try to convert to string
          try {
            sanitizedRow[key] = JSON.stringify(value);
          } catch {
            sanitizedRow[key] = String(value);
          }
        }
      } else {
        // For primitive types, ensure string for certain types
        if (typeof value === 'string') {
          // Handle potential null characters or invalid chars
          sanitizedRow[key] = value.replace(/[\u0000-\u001F]/g, '');
        } else {
          sanitizedRow[key] = value;
        }
      }
    }
    return sanitizedRow;
  });
};

// Validate dataset
export const validateDataset = (data: any[], maxSize: number = 5 * 1024 * 1024): { valid: boolean; error?: string } => {
  try {
    // Check if data is empty
    if (!data || data.length === 0) {
      return { valid: false, error: 'Dataset is empty' };
    }

    // Check data size
    const dataSize = new TextEncoder().encode(JSON.stringify(data)).length;
    if (dataSize > maxSize) {
      return {
        valid: false,
        error: `Dataset size (${formatBytes(dataSize)}) exceeds maximum allowed size (${formatBytes(maxSize)})`
      };
    }

    // Check for invalid values
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      for (const [key, value] of Object.entries(row)) {
        if (value !== null && value !== undefined) {
          if (typeof value === 'function') {
            return { valid: false, error: `Invalid function found in row ${i + 1}, column "${key}"` };
          }
          if (value instanceof Error) {
            return { valid: false, error: `Error object found in row ${i + 1}, column "${key}"` };
          }
          if (typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
            if (Object.keys(value).some(k => k.startsWith('_'))) {
              return { valid: false, error: `Invalid Excel formatting found in row ${i + 1}, column "${key}"` };
            }
          }
        }
      }
    }

    return { valid: true };
  } catch (error) {
    return { valid: false, error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};
