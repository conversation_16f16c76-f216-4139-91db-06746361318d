"use client";

import { useMemo } from "react";
import { cn } from "@/lib/utils";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  Too<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ColumnBarChartProps {
  data: any[];
  accessor: string;
  width?: number;
  height?: number;
}

interface ColumnStats {
  min: number;
  max: number;
  mean: number;
  median: number;
  stdDev: number;
  values: number[];
  hasData: boolean;
  count: number;
}

export function ColumnBarChart({
  data,
  accessor,
  width = 80, // Reduced from 100 to 80
  height = 24
}: ColumnBarChartProps) {
  // Calculate statistics for the column
  const stats = useMemo<ColumnStats>(() => {
    // Filter out non-numeric values and convert string numbers to numbers
    const numericValues = data
      .map(row => {
        const value = row[accessor];
        if (typeof value === 'number') return value;
        if (typeof value === 'string' && !isNaN(Number(value))) return Number(value);
        return null;
      })
      .filter((val): val is number => val !== null);

    if (numericValues.length === 0) {
      return {
        min: 0,
        max: 0,
        mean: 0,
        median: 0,
        stdDev: 0,
        values: [],
        hasData: false,
        count: 0
      };
    }

    const min = Math.min(...numericValues);
    const max = Math.max(...numericValues);

    // Calculate mean (average)
    const sum = numericValues.reduce((acc, val) => acc + val, 0);
    const mean = sum / numericValues.length;

    // Calculate median
    const sortedValues = [...numericValues].sort((a, b) => a - b);
    const midIndex = Math.floor(sortedValues.length / 2);
    const median = sortedValues.length % 2 === 0
      ? (sortedValues[midIndex - 1] + sortedValues[midIndex]) / 2
      : sortedValues[midIndex];

    // Calculate standard deviation
    const squaredDifferences = numericValues.map(val => Math.pow(val - mean, 2));
    const variance = squaredDifferences.reduce((acc, val) => acc + val, 0) / numericValues.length;
    const stdDev = Math.sqrt(variance);

    // Create normalized values for the chart (between 0 and 1)
    const range = max - min;
    const normalizedValues = range === 0
      ? numericValues.map(() => 0.5) // If all values are the same, show middle bars
      : numericValues.map(val => (val - min) / range);

    return {
      min,
      max,
      mean,
      median,
      stdDev,
      values: normalizedValues,
      hasData: true,
      count: numericValues.length
    };
  }, [data, accessor]);

  // Format number to 2 decimal places
  const formatNumber = (num: number) => {
    return Math.round(num * 100) / 100;
  };

  // If there's no numeric data, show a placeholder
  if (!stats.hasData) {
    return (
      <div
        className="flex items-center justify-center text-xs text-muted-foreground"
        style={{ width, height }}
      >
        No numeric data
      </div>
    );
  }

  // Calculate how many bars to show (max 10)
  const barCount = Math.min(10, stats.values.length);
  const sampleInterval = Math.max(1, Math.floor(stats.values.length / barCount));
  const sampledValues = stats.values.filter((_, i) => i % sampleInterval === 0).slice(0, barCount);

  return (
    <div className="relative w-full">
      <TooltipProvider>
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <div className="absolute top-0 right-0 cursor-help">
              <Info className="h-3 w-3 text-muted-foreground/60" />
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" align="center" className="text-xs p-2 max-w-[200px]">
            <div className="space-y-1">
              <div className="font-medium">{accessor} Statistics</div>
              <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                <span className="text-muted-foreground">Count:</span>
                <span>{stats.count}</span>
                <span className="text-muted-foreground">Min:</span>
                <span>{formatNumber(stats.min)}</span>
                <span className="text-muted-foreground">Max:</span>
                <span>{formatNumber(stats.max)}</span>
                <span className="text-muted-foreground">Mean:</span>
                <span>{formatNumber(stats.mean)}</span>
                <span className="text-muted-foreground">Median:</span>
                <span>{formatNumber(stats.median)}</span>
                <span className="text-muted-foreground">Std Dev:</span>
                <span>{formatNumber(stats.stdDev)}</span>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div
        className="flex items-end justify-between gap-[1px]"
        style={{ width, height }}
      >
        {sampledValues.map((value, index) => (
          <div
            key={index}
            className={cn(
              "bg-primary/60 hover:bg-primary/80 transition-all rounded-sm w-full",
              "relative group"
            )}
            style={{
              height: `${Math.max(4, value * height)}px`,
            }}
          >
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity bg-popover text-popover-foreground text-xs rounded px-1 py-0.5 whitespace-nowrap pointer-events-none z-50">
              {formatNumber(value * (stats.max - stats.min) + stats.min)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ColumnBarChart;

