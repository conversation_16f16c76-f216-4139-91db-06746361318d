"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>umn, Row } from "@tanstack/react-table";
import { <PERSON><PERSON><PERSON>, Trash2, Co<PERSON> } from "lucide-react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Define base data type
interface TableData {
  [key: string]: string | number | boolean;
}

// Define props interface with proper typing
interface TableCellProps<TData extends TableData> {
  row: Row<TData>;
  column: Column<TData>;
  value: any;
  onEdit: (value: any) => void;
  onDeleteRow: (index: number) => void;
  onDuplicateRow: (index: number) => void;
}

// Helper function for cell value formatting
const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) return '—';
  if (typeof value === 'number') {
    return new Intl.NumberFormat('fr-MA').format(value);
  }
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  if (value instanceof Date) {
    try {
      return value.toLocaleDateString();
    } catch {
      return value.toString();
    }
  }
  return String(value);
};

export function TableCell<TData extends TableData>({
  row,
  // column is required by the type but not used in this component
  column: _,
  value,
  onEdit,
  onDeleteRow,
  onDuplicateRow
}: TableCellProps<TData>) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value?.toString() || '');
  const inputRef = useRef<HTMLInputElement>(null);

  // Update handleEditClick to properly handle both mouse and keyboard events
  const handleEditClick = (e?: React.MouseEvent | React.KeyboardEvent | MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Set editing state
    setIsEditing(true);

    // Use a slightly longer timeout to ensure the context menu is fully closed
    // before focusing the input element
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.select();
      }
    }, 50);
  };

  const handleSave = () => {
    if (editValue !== value?.toString()) {
      onEdit(editValue);
    }
    setIsEditing(false);
  };

  useEffect(() => {
    if (!isEditing) return;

    // Add a small delay before adding the click outside handler
    // to prevent immediate closing when opening from context menu
    const timeoutId = setTimeout(() => {
      const handleClickOutside = (e: MouseEvent) => {
        const target = e.target as Node;
        if (inputRef.current && !inputRef.current.contains(target)) {
          handleSave();
        }
      };

      document.addEventListener('mousedown', handleClickOutside);

      // Store the cleanup function to be called when the component unmounts or the effect reruns
      const cleanup = () => document.removeEventListener('mousedown', handleClickOutside);
      // Store the cleanup function on the ref so we can access it in our own cleanup
      (inputRef.current as any)._clickOutsideCleanup = cleanup;
    }, 200);

    return () => {
      clearTimeout(timeoutId);
      // Call the cleanup function if it exists
      if (inputRef.current && (inputRef.current as any)._clickOutsideCleanup) {
        (inputRef.current as any)._clickOutsideCleanup();
      }
    };
  }, [isEditing, editValue, value]);

  if (isEditing) {
    return (
      <div className="relative w-full h-full" onClick={e => e.stopPropagation()} onMouseDown={e => e.stopPropagation()}>
        <input
          ref={inputRef}
          type="text"
          value={editValue}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditValue(e.target.value)}
          onBlur={(e) => {
            // Add a small delay to prevent immediate blur when opening from context menu
            setTimeout(() => {
              // Only save if the element is still in the DOM
              if (document.contains(e.target)) {
                handleSave();
              }
            }, 100);
          }}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
            e.stopPropagation();
            if (e.key === 'Enter') handleSave();
            if (e.key === 'Escape') {
              setEditValue(value?.toString() || '');
              setIsEditing(false);
            }
          }}
          className="w-full h-full px-1 py-0.5 text-xs border-0 bg-background dark:bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
          style={{ minWidth: '100px', maxWidth: '300px', width: '100%', height: '28px' }}
          autoFocus
        />
      </div>
    );
  }

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div
          className="truncate text-xs px-1 py-1 cursor-default min-w-[100px] max-w-[300px] h-[28px]
                     hover:bg-muted/30 active:bg-muted/50 transition-colors
                     flex items-center group relative"
          onDoubleClick={(e: React.MouseEvent<HTMLDivElement>) => handleEditClick(e)}
        >
          <TooltipProvider>
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <div className="truncate max-w-full">{formatCellValue(value)}</div>
              </TooltipTrigger>
              <TooltipContent side="top" align="start" className="max-w-xs text-xs p-2">
                <p className="break-words">{formatCellValue(value)}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Pencil className="h-3 w-3 text-muted-foreground/50 opacity-0 group-hover:opacity-100 absolute right-2 transition-opacity" />
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        <ContextMenuItem
          onSelect={(e) => {
            e.preventDefault();
            // Use setTimeout to delay the edit action until after the context menu is closed
            setTimeout(() => {
              handleEditClick();
            }, 100);
          }}
        >
          <Pencil className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Edit Cell</span>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onEdit("")}>
          <Trash2 className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Clear Cell</span>
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={() => onDeleteRow(row.index)}>
          <Trash2 className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Delete Row</span>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onDuplicateRow(row.index)}>
          <Copy className="mr-2 h-3.5 w-3.5" />
          <span className="text-xs">Duplicate Row</span>
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}

export default TableCell;