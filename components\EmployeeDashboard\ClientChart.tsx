"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LabelList } from "recharts";
import { <PERSON>, <PERSON><PERSON><PERSON>, Cell } from "recharts";
import { Line, LineChart, Area, AreaChart } from "recharts";
import { TrendingUp, DollarSign, PieChartIcon, LineChartIcon, BarChartIcon, AlertCircle } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";

const chartConfig: ChartConfig = {
  value: {
    label: "Total Salary",
    color: "hsl(var(--chart-1))",
  },
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

interface ClientSideChartProps {
  data: Array<{ name: string; value: number }>;
  isLoading: boolean;
}

const EmptyState = () => (
  <Alert className="mb-4">
    <AlertCircle className="h-4 w-4" />
    <AlertDescription>
      No data available yet. Add some employees to see your dashboard statistics.
    </AlertDescription>
  </Alert>
);

const ClientSideChart: React.FC<ClientSideChartProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="w-full">
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="w-full h-[200px]" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-4 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Show empty state if no data
  if (!data || data.length === 0) {
    return (
      <>
        <EmptyState />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-10">
          {[...Array(4)].map((_, index) => (
            <Card key={index} className="w-full">
              <CardHeader className="p-2">
                <CardTitle className="text-xs flex items-center">
                  {index === 0 && <BarChartIcon className="mr-1 h-3 w-3 text-muted-foreground" />}
                  {index === 1 && <PieChartIcon className="mr-1 h-3 w-3 text-muted-foreground" />}
                  {index === 2 && <LineChartIcon className="mr-1 h-3 w-3 text-muted-foreground" />}
                  {index === 3 && <BarChartIcon className="mr-1 h-3 w-3 text-muted-foreground" />}
                  No Data Available
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 flex items-center justify-center h-[200px] text-muted-foreground">
                <p className="text-sm">Add employees to see statistics</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Bar Chart */}
      <Card className="w-full">
        <CardHeader className="p-2">
          <CardTitle className="text-xs flex items-center">
            <BarChartIcon className="mr-1 h-3 w-3 text-blue-500" /> Salary by Department
          </CardTitle>
          <CardDescription className="text-[10px]">Comparing salary allocation across departments</CardDescription>
        </CardHeader>
        <CardContent className="p-2">
          <ChartContainer config={chartConfig} className="w-full h-[200px]">
            <BarChart data={data} margin={{ top: 5, right: 5, left: 0, bottom: 5 }} width={300} height={200}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" tick={{ fontSize: 8 }} />
              <YAxis tick={{ fontSize: 8 }} />
              <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
              <Bar dataKey="value" fill="var(--color-value)">
                <LabelList dataKey="value" position="top" fontSize={8} />
              </Bar>
            </BarChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="text-[10px] text-muted-foreground p-1">
          <TrendingUp className="h-2 w-2 mr-1" /> Distribution across departments
        </CardFooter>
      </Card>

      {/* Pie Chart */}
      <Card className="w-full">
        <CardHeader className="p-2">
          <CardTitle className="text-xs flex items-center">
            <PieChartIcon className="mr-1 h-3 w-3 text-blue-500" /> Salary Proportion
          </CardTitle>
          <CardDescription className="text-[10px]">Percentage breakdown of salary by department</CardDescription>
        </CardHeader>
        <CardContent className="p-2">
          <ChartContainer config={chartConfig} className="w-full h-[200px]">
            <PieChart width={300} height={200}>
              <Pie
                data={data}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label={{ fontSize: 8 }}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
            </PieChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="text-[10px] text-muted-foreground p-1">
          <DollarSign className="h-2 w-2 mr-1" /> Salary distribution by percentage
        </CardFooter>
      </Card>

      {/* Line Chart */}
      <Card className="w-full">
        <CardHeader className="p-2">
          <CardTitle className="text-xs flex items-center ">
            <LineChartIcon className="mr-1 h-3 w-3 text-blue-500" /> Salary Trends
          </CardTitle>
          <CardDescription className="text-[10px]">How salaries change across departments</CardDescription>
        </CardHeader>
        <CardContent className="p-2">
          <ChartContainer config={chartConfig} className="w-full h-[200px]">
            <LineChart data={data} margin={{ top: 5, right: 5, left: 0, bottom: 5 }} width={300} height={200}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" tick={{ fontSize: 8 }} />
              <YAxis tick={{ fontSize: 8 }} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line type="monotone" dataKey="value" stroke="var(--color-value)" strokeWidth={2} />
            </LineChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="text-[10px] text-muted-foreground p-1">
          <TrendingUp className="h-2 w-2 mr-1" /> Salary trends over time
        </CardFooter>
      </Card>

      {/* Area Chart */}
      <Card className="w-full">
        <CardHeader className="p-2">
          <CardTitle className="text-xs flex items-center">
            <BarChartIcon className="mr-1 h-3 w-3 text-blue-500" /> Cumulative Salary
          </CardTitle>
          <CardDescription className="text-[10px]">Total salary accumulation across departments</CardDescription>
        </CardHeader>
        <CardContent className="p-2">
          <ChartContainer config={chartConfig} className="w-full h-[200px]">
            <AreaChart data={data} margin={{ top: 5, right: 5, left: 0, bottom: 5 }} width={300} height={200}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" tick={{ fontSize: 8 }} />
              <YAxis tick={{ fontSize: 8 }} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <defs>
                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="var(--color-value)" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="var(--color-value)" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <Area type="monotone" dataKey="value" stroke="var(--color-value)" fillOpacity={1} fill="url(#colorValue)" />
            </AreaChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="text-[10px] text-muted-foreground p-1">
          <DollarSign className="h-2 w-2 mr-1" /> Cumulative salary distribution
        </CardFooter>
      </Card>
    </div>
  );
};

export default ClientSideChart;