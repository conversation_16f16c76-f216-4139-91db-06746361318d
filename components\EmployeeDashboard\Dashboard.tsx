"use client"

import React, { useState, useEffect } from 'react';
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  Search,
  DollarSign,
  Users,
  TrendingUp,
  Calendar as CalendarIcon,
  ClipboardList,
  Building2,
  FileText,
  Settings,
  BarChart3
} from 'lucide-react';
import ClientSideChart from './ClientChart';
import EmployeeStatsCharts from './EmployeeStatsCharts';
import EmployeeMetricsCard from './EmployeeMetricsCard';
import EmployeeStatusCard from './EmployeeStatusCard';
import EmployeeTrendChart from './EmployeeTrendChart';
import { ScrollArea } from "@/components/ui/scroll-area";
import { fetchDashboardData } from './actions';
import { fetchEmployeeTrendData } from './actions';
import { toast } from 'sonner';
import { Skeleton } from "@/components/ui/skeleton";
import { SankeyChart } from './SankeyChart';
import { Separator } from "@/components/ui/separator";
import { LineChart, Line } from 'recharts';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { cn } from '@/lib/utils';
import { startOfMonth, endOfMonth, subMonths } from 'date-fns';
import prisma from '@/lib/db';

type Attendance = {
  id: string;
  employeeId: string;
  date: Date;
  status: string;
  checkInTime?: Date | null;
  checkOutTime?: Date | null;
  notes?: string | null;
};

type AttendanceStats = {
  currentRate: number;
  lastRate: number;
  change: number;
};

// Directory navigation items
const directories = [
  {
    title: "Employee Management",
    description: "Manage employee profiles, documents and information",
    icon: Users,
    href: "/hr/employee",

  },
  {
    title: "Management",
    description: "Manage your employee, leaves and working hours",
    icon: CalendarIcon,
    href: "/hr/workspace/callend",

  },
  {
    title: "RAG System",
    description: "Create and manage RAG system",
    icon: BarChart3,
    href: "/hr/ragbook",

  },
  {
    title: "Bilans",
    description: "This is bilan Report",
    icon: Settings,
    href: "/hr/bilan",

  }
]

type DashboardData = {
  employees: {
    id: string;
    prenom: string;
    nom: string;
    poste: string;
    departement: string;
    salaire: number;
    typeEmploi?: string;
    genre?: string;
    contratType?: string | null;
    dateDebut?: Date;
    dateFinContrat?: Date | null;
  }[];
  totalPayroll: number;
  employeeCount: number;
  avgSalary: number;
  salaryDistribution: { name: string; value: number }[];
  contractTypeData: { name: string; value: number }[];
  genderData: { name: string; value: number }[];
  departmentData: { name: string; value: number }[];
};


type ActivityData = {
  day: number;
  value: number;
}[];

export default function PayrollHRDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    employees: [],
    totalPayroll: 0,
    employeeCount: 0,
    avgSalary: 0,
    salaryDistribution: [],
    contractTypeData: [],
    genderData: [],
    departmentData: [],
  });

  // Add new states for real data
  const [employeeTrends, setEmployeeTrends] = useState<{
    month: string;
    employees: number;
    totalSalary: number;
    newHires: number;
  }[]>([]);

  type TrendType = 'neutral' | 'up' | 'down';

  const [hrMetrics, setHrMetrics] = useState({
    retention: { value: 0, trend: 'neutral' as TrendType },
    turnover: { value: 0, trend: 'neutral' as TrendType },
    avgTenure: { value: 0 },
    newHires: { value: 0 }
  });

  const [employeeStatus, setEmployeeStatus] = useState({
    active: 0,
    onLeave: 0,
    remote: 0,
    probation: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [employeeActivities, setEmployeeActivities] = useState<Record<string, ActivityData>>({});
  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats>({
    currentRate: 0,
    lastRate: 0,
    change: 0
  });

  useEffect(() => {
    const loadAllData = async () => {
      try {
        setIsLoading(true);

        // Fetch dashboard data
        const dashData = await fetchDashboardData();
        setDashboardData(dashData);

        // Calculate attendance stats
        const now = new Date();
        const currentMonth = {
          start: startOfMonth(now),
          end: endOfMonth(now)
        };
        const lastMonth = {
          start: startOfMonth(subMonths(now, 1)),
          end: endOfMonth(subMonths(now, 1))
        };

        let currentMonthTotal = 0;
        let lastMonthTotal = 0;
        let currentMonthPresent = 0;
        let lastMonthPresent = 0;

        // Get attendance for each employee
        for (const employee of dashData.employees) {
          const attendances = await prisma.attendance.findMany({
            where: {
              employeeId: employee.id,
              date: {
                gte: lastMonth.start,
                lte: currentMonth.end
              }
            }
          });

          // Count current month attendance
          const currentMonthAttendances = attendances.filter(a =>
            new Date(a.date) >= currentMonth.start &&
            new Date(a.date) <= currentMonth.end
          );
          currentMonthTotal += currentMonthAttendances.length;
          currentMonthPresent += currentMonthAttendances.filter(a => a.status === 'Present').length;

          // Count last month attendance
          const lastMonthAttendances = attendances.filter(a =>
            new Date(a.date) >= lastMonth.start &&
            new Date(a.date) <= lastMonth.end
          );
          lastMonthTotal += lastMonthAttendances.length;
          lastMonthPresent += lastMonthAttendances.filter(a => a.status === 'Present').length;
        }

        // Calculate rates
        const currentRate = currentMonthTotal ? (currentMonthPresent / currentMonthTotal) * 100 : 0;
        const lastRate = lastMonthTotal ? (lastMonthPresent / lastMonthTotal) * 100 : 0;

        setAttendanceStats({
          currentRate: Math.round(currentRate * 10) / 10,
          lastRate: Math.round(lastRate * 10) / 10,
          change: Math.round((currentRate - lastRate) * 10) / 10
        });

        // Fetch employee trends
        const trends = await fetchEmployeeTrendData();
        console.log('Employee trends data:', trends);

        // Make sure we have valid trend data
        if (trends && Array.isArray(trends) && trends.length > 0) {
          setEmployeeTrends(trends);
        } else {
          console.warn('No valid employee trend data received');
          // Set empty array to trigger the empty state in the chart
          setEmployeeTrends([]);
        }

        // Calculate HR metrics from real data
        if (dashData.employees.length > 0) {
          const now = new Date();

          // Calculate retention rate
          const totalEmployees = dashData.employees.length;
          const oneYearAgo = new Date();
          oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

          const employeesOverOneYear = dashData.employees.filter(emp =>
            emp.dateDebut && new Date(emp.dateDebut) <= oneYearAgo
          ).length;
          const retentionRate = totalEmployees > 0 ? Math.round((employeesOverOneYear / totalEmployees) * 100) : 0;

          // Calculate average tenure in months
          const avgTenureMonths = totalEmployees > 0 ? Math.round(
            dashData.employees.reduce((acc, emp) => {
              if (!emp.dateDebut) return acc;
              const startDate = new Date(emp.dateDebut);
              const currentDate = new Date(); // Use a fresh date object
              const monthsDiff = (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
                               (currentDate.getMonth() - startDate.getMonth());
              return acc + monthsDiff;
            }, 0) / totalEmployees
          ) : 0;

          // Get new hires (last 3 months)
          const threeMonthsAgo = new Date();
          threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

          console.log('Three months ago:', threeMonthsAgo);

          const newHires = dashData.employees.filter(emp => {
            if (!emp.dateDebut) return false;
            const hireDate = new Date(emp.dateDebut);
            const isNewHire = hireDate >= threeMonthsAgo;
            console.log(`Employee ${emp.prenom} ${emp.nom}, hire date: ${hireDate}, is new hire: ${isNewHire}`);
            return isNewHire;
          });

          const newHiresCount = newHires.length;
          console.log(`Found ${newHiresCount} new hires in the last 3 months`);

          setHrMetrics({
            retention: {
              value: retentionRate,
              trend: (retentionRate >= 90 ? 'up' : retentionRate >= 70 ? 'neutral' : 'down') as TrendType
            },
            turnover: {
              value: Math.round(100 - retentionRate),
              trend: (retentionRate >= 90 ? 'down' : retentionRate >= 70 ? 'neutral' : 'up') as TrendType
            },
            avgTenure: { value: avgTenureMonths },
            newHires: { value: newHiresCount }
          });

          // Calculate employee status
          const activeEmployees = dashData.employees.filter(emp =>
            !emp.dateFinContrat || new Date(emp.dateFinContrat) > now
          ).length;

          // Note: You might want to add proper fields in your database
          // to track these statuses accurately
          setEmployeeStatus({
            active: activeEmployees,
            onLeave: 0, // Add proper leave tracking
            remote: dashData.employees.filter(emp =>
              emp.typeEmploi?.toLowerCase().includes('remote')
            ).length,
            probation: dashData.employees.filter(emp => {
              if (!emp.dateDebut) return false;
              const startDate = new Date(emp.dateDebut);
              const monthsSinceStart = (now.getFullYear() - startDate.getFullYear()) * 12 +
                                     (now.getMonth() - startDate.getMonth());
              return monthsSinceStart <= 3; // Assuming 3 months probation
            }).length
          });
        }

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        toast.error('Failed to load dashboard data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadAllData();
  }, []);

  useEffect(() => {
    if (dashboardData.employees.length > 0) {
      // Initialize with real attendance data instead of mock data
      const loadEmployeeActivities = async () => {
        try {
          const now = new Date();
          const activities: Record<string, ActivityData> = {};

          // For each employee, get their attendance data for the last 7 days
          for (const employee of dashboardData.employees) {
            const attendanceData = await prisma.attendance.findMany({
              where: {
                employeeId: employee.id,
                date: {
                  gte: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7),
                  lte: now
                }
              },
              orderBy: { date: 'asc' }
            });

            // Convert attendance data to activity format
            const employeeActivity: ActivityData = Array.from({ length: 7 }, (_, i) => {
              const day = i + 1;
              const date = new Date(now.getFullYear(), now.getMonth(), now.getDate() - (7 - day));

              // Find attendance for this day
              const dayAttendance = attendanceData.find(a =>
                new Date(a.date).getDate() === date.getDate() &&
                new Date(a.date).getMonth() === date.getMonth() &&
                new Date(a.date).getFullYear() === date.getFullYear()
              );

              // Value is 100 if present, 0 if absent, 50 if no data
              const value = dayAttendance
                ? (dayAttendance.status === 'Present' ? 100 : 0)
                : 50;

              return { day, value };
            });

            activities[employee.id] = employeeActivity;
          }

          setEmployeeActivities(activities);
        } catch (error) {
          console.error('Error loading employee activities:', error);
          // Fallback to empty activities if there's an error
          setEmployeeActivities({});
        }
      };

      loadEmployeeActivities();
    }
  }, [dashboardData.employees]);

  const filteredEmployees = dashboardData.employees.filter(employee =>
    `${employee.prenom} ${employee.nom}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.poste.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.departement.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const paginatedEmployees = filteredEmployees.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);

  // Add function to calculate percentage changes
  const calculatePercentageChange = (current: number, previous: number) => {
    const change = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(change).toFixed(1),
      isPositive: change >= 0
    };
  };

  // Function to render summary card with skeleton
  const renderSummaryCard = (title: string, value: string | number, icon: React.ReactNode, description: string, previousValue?: number, currentValue?: number) => (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" /> {/* Title skeleton */}
            <Skeleton className="h-8 w-32" /> {/* Value skeleton */}
            <Skeleton className="h-3 w-20" /> {/* Trend skeleton */}
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="mt-1 text-3xl font-semibold">{value}</p>
              <p className="text-xs text-muted-foreground mt-2">{description}</p>
            </div>
            <div className="p-3 rounded-full bg-primary/10">
              {React.cloneElement(icon as React.ReactElement, { className: "h-6 w-6 text-primary" })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto p-3 space-y-3 pb-8">
      {/* Quick Access Directory - New Section at the top */}
      <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-4">
        {directories.map((directory) => {
          const Icon = directory.icon;
          return (
            <Link href={directory.href} key={directory.href}>
              <Card className="hover:bg-accent/10 hover:text-accent-foreground transition-all duration-200 border-dashed border-2 shadow-sm hover:shadow-md bg-card/50">
                <CardHeader className="flex flex-row items-center gap-4">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center relative`}>
                    <Icon className={`h-4 w-4`} />
                    <div className="absolute -top-1 -right-1 w-2 h-2 rounded-full bg-primary animate-pulse" />
                  </div>
                  <div>
                    <CardTitle className="text-sm font-medium">{directory.title}</CardTitle>
                    <CardDescription className="text-xs mt-1">
                      {directory.description}
                    </CardDescription>
                  </div>
                </CardHeader>
              </Card>
            </Link>
          );
        })}
      </div>

      <Separator className="my-2" />

      {/* Existing Dashboard Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
        {renderSummaryCard(
          "Total Payroll",
          `$${dashboardData.totalPayroll.toLocaleString()}`,
          <DollarSign />,
          "Sum of all employee salaries across the organization"
        )}
        {renderSummaryCard(
          "Total Employees",
          dashboardData.employeeCount,
          <Users />,
          "Current number of active employees in the company"
        )}
        {renderSummaryCard(
          "Avg. Salary",
          `$${dashboardData.avgSalary.toLocaleString(undefined, { maximumFractionDigits: 0 })}`,
          <TrendingUp />,
          "Average salary across all employees in the organization"
        )}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardHeader>
            <CardTitle>Attendance Rate</CardTitle>
            <CardDescription>
              {attendanceStats.currentRate}%
              <span className={cn(
                "ml-2 text-sm",
                attendanceStats.change > 0 ? "text-green-600" : "text-red-600"
              )}>
                {attendanceStats.change > 0 ? "+" : ""}{attendanceStats.change}% from last month
              </span>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Percentage of employees present at work this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Salary Distribution Charts - Full Width */}
      <Card className="shadow-sm hover:shadow-md transition-shadow duration-200 w-full">
        <CardHeader className="py-3 px-4">
          <CardTitle className="text-base font-semibold flex items-center">
            <DollarSign className="h-4 w-4 mr-2 text-primary" /> Salary Distribution
          </CardTitle>
          <CardDescription className="text-sm">
            Analysis of salary allocation across departments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ClientSideChart isLoading={isLoading} data={dashboardData.salaryDistribution} />
        </CardContent>
      </Card>

      {/* Employee Trends Chart - Full Width */}
      <Card className="shadow-sm hover:shadow-md transition-shadow duration-200 w-full">
        <CardHeader className="py-3 px-4">
          <CardTitle className="text-base font-semibold flex items-center">
            <TrendingUp className="h-4 w-4 mr-2 text-primary" /> Employee Trends
          </CardTitle>
          <CardDescription className="text-sm">
            Monthly new hires and employee growth trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmployeeTrendChart
            isLoading={isLoading}
            trendData={employeeTrends}
            allEmployees={dashboardData.employees}
          />
        </CardContent>
      </Card>

      {/* Employee Stats Charts - Full Width */}
      <Card className="shadow-sm hover:shadow-md transition-shadow duration-200 w-full">
        <CardHeader className="py-3 px-4">
          <CardTitle className="text-base font-semibold flex items-center">
            <Users className="h-4 w-4 mr-2 text-primary" /> Employee Demographics
          </CardTitle>
          <CardDescription className="text-sm">
            Distribution by contract type, gender, and department
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmployeeStatsCharts
            isLoading={isLoading}
            contractTypeData={dashboardData.contractTypeData}
            genderData={dashboardData.genderData}
            departmentData={dashboardData.departmentData}
            allEmployees={dashboardData.employees}
          />
        </CardContent>
      </Card>

      {/* Employee Metrics and Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Employee Metrics Card */}
        <div className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="p-3">
            <EmployeeMetricsCard
              isLoading={isLoading}
              metrics={hrMetrics}
              // @ts-ignore
              trendData={employeeTrends}
              allEmployees={dashboardData.employees}
            />
          </div>
        </div>

        {/* Employee Status Card */}
        <div className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="p-3">
            <EmployeeStatusCard
              isLoading={isLoading}
              statusData={employeeStatus}
              allEmployees={dashboardData.employees}
            />
          </div>
        </div>
      </div>

      {/* Resource Flow Analysis (SankeyChart) - Full Width */}
      <div className="shadow-sm hover:shadow-md transition-shadow duration-200">
        <div className="p-4">
          <div className="max-w-[900px] mx-auto">
            <SankeyChart />
          </div>
        </div>
      </div>

      {/* Employee List Section */}

      {/* Employee Attendance Timeline */}

    </div>
  );
}







