"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { getEmployeesWithAttendance } from '@/actions/actions';
import { Employee, Attendance } from '@/types';
import { format, isSameDay, isToday, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar, Search, BarChart2, X } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

type EmployeeAttendanceData = {
  employee: Employee;
  attendances: Attendance[];
};

type AttendanceWithEmployee = Attendance & {
  employee: Employee;
};

type AttendanceSummary = {
  employeeId: string;
  employeeName: string;
  present: number;
  absent: number;
  late: number;
};

const StatusColorMap: { [key: string]: string } = {
  'Present': 'bg-emerald-500 text-primary-foreground',
  'Absent': 'bg-red-500 text-destructive-foreground',
  'Late': 'bg-amber-500 text-primary-foreground',
};

const EmployeeAttendanceCalendar: React.FC = () => {
  const [employeesData, setEmployeesData] = useState<EmployeeAttendanceData[]>([]);
  const [filteredEmployeesData, setFilteredEmployeesData] = useState<EmployeeAttendanceData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [showStats, setShowStats] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedAttendances, setSelectedAttendances] = useState<AttendanceWithEmployee[]>([]);
  const [attendanceSummary, setAttendanceSummary] = useState<AttendanceSummary[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const data = await getEmployeesWithAttendance();
        const typedData = data as EmployeeAttendanceData[]; // Type assertion to match expected type
        setEmployeesData(typedData);
        setFilteredEmployeesData(typedData);
        calculateAttendanceSummary(typedData);
      } catch (error) {
        console.error('Failed to fetch employee data:', error);
        toast.error('Failed to load employee data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const filtered = employeesData.filter(({ employee }) =>
      `${employee.prenom} ${employee.nom}`.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredEmployeesData(filtered);
    calculateAttendanceSummary(filtered);
  }, [searchTerm, employeesData, statusFilter]);

  const calculateAttendanceSummary = (data: EmployeeAttendanceData[]) => {
    const summary = data.map(({ employee, attendances }) => {
      const counts = attendances.reduce((acc, attendance) => {
        acc[attendance.status.toLowerCase()] = (acc[attendance.status.toLowerCase()] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        employeeId: employee.id,
        employeeName: `${employee.prenom} ${employee.nom}`,
        present: counts['present'] || 0,
        absent: counts['absent'] || 0,
        late: counts['late'] || 0,
      };
    });

    setAttendanceSummary(summary);
  };

  const handlePreviousMonth = () => {
    setCurrentDate(prevDate => subMonths(prevDate, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(prevDate => addMonths(prevDate, 1));
  };

  if (isLoading) {
    return (
      <Card className="w-full shadow-lg rounded-lg overflow-hidden">
        <CardHeader className="px-6 py-4 bg-gradient-to-r from-primary to-secondary">
          <Skeleton className="h-8 w-64 bg-primary-foreground/20" />
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-8 w-24" />
          </div>
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-[180px]" />
          </div>
          <div className="grid grid-cols-7 gap-1 font-semibold text-center mb-2">
            {Array(7).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-8 w-full" />
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1">
            {Array(35).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-24 w-full rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getAttendanceColor = (attendances: Attendance[]) => {
    if (attendances.length === 0) return '';
    const statusCounts = attendances.reduce((acc, curr) => {
      acc[curr.status] = (acc[curr.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    if (statusCounts['Present'] > 0 && statusCounts['Absent'] === 0) {
      return 'bg-emerald-100 border-emerald-500';
    } else if (statusCounts['Absent'] > 0 && statusCounts['Present'] === 0) {
      return 'bg-red-100 border-red-500';
    } else if (statusCounts['Late'] > statusCounts['Present'] && statusCounts['Late'] > statusCounts['Absent']) {
      return 'bg-amber-100 border-amber-500';
    }
    return 'border-blue-500'; // Mixed status
  };

  const handleDateClick = (date: Date, attendances: AttendanceWithEmployee[]) => {
    setSelectedDate(date);
    setSelectedAttendances(attendances);
  };

  const renderCalendar = () => {
    const start = startOfMonth(currentDate);
    const end = endOfMonth(currentDate);
    const days = eachDayOfInterval({ start, end });

    return days.map((date, index) => {
      const dayAttendances = filteredEmployeesData.flatMap(({ employee, attendances }) =>
        attendances.filter(a => {
          const attendanceDate = a.date instanceof Date ? a.date : new Date(a.date);
          return isSameDay(attendanceDate, date) && (statusFilter === 'All' || a.status === statusFilter);
        })
          .map(a => ({ ...a, employee }))
      );

      const dayColor = getAttendanceColor(dayAttendances);

      return (
        <div
          key={date.toISOString()}
          className={`h-16 border ${dayColor} p-1 overflow-hidden transition-colors ${isToday(date) ? 'ring-1 ring-primary' : ''} rounded-lg cursor-pointer hover:opacity-80`}
          onClick={() => handleDateClick(date, dayAttendances)}
        >
          <div className={`font-semibold text-xs ${isToday(date) ? 'text-primary' : 'text-foreground'}`}>
            {format(date, 'd')}
          </div>
          <div className="h-[calc(100%-16px)] overflow-hidden">
            <div className="flex flex-col gap-1">
              {dayAttendances.slice(0, 3).map((attendance, index) => (
                <Badge
                  key={`${attendance.employee.id}-${index}`}
                  variant="outline"
                  className={`text-xs truncate ${StatusColorMap[attendance.status]}`}
                >
                  {`${attendance.employee.prenom} ${attendance.employee.nom.charAt(0)}.`}
                </Badge>
              ))}
              {dayAttendances.length > 3 && (
                <Badge variant="outline" className="text-xs truncate">
                  +{dayAttendances.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        </div>
      );
    });
  };

  return (
    <Card className="w-full shadow-sm rounded-lg overflow-hidden">
      <CardHeader className="px-4 py-2 bg-gradient-to-r from-primary to-secondary">
        <CardTitle className="text-base font-bold text-primary-foreground flex items-center justify-between">
          <div className="flex items-center">
            <Calendar className="mr-2 h-4 w-4" /> Employee Attendance
          </div>
          <Button variant="secondary" size="sm" className="h-8 text-xs" onClick={() => setShowStats(!showStats)}>
            {showStats ? 'Hide Stats' : 'Show Stats'} <BarChart2 className="ml-1 h-3 w-3" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-3">
          <Button variant="outline" size="sm" onClick={handlePreviousMonth} className="text-muted-foreground h-8 text-xs">
            <ChevronLeft className="h-3 w-3 mr-1" /> Prev
          </Button>
          <span className="font-bold text-base text-foreground">{format(currentDate, 'MMMM yyyy')}</span>
          <Button variant="outline" size="sm" onClick={handleNextMonth} className="text-muted-foreground h-8 text-xs">
            Next <ChevronRight className="h-3 w-3 ml-1" />
          </Button>
        </div>
        <div className="flex items-center gap-2 mb-3">
          <div className="relative flex-grow">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-3 w-3" />
            <Input
              type="text"
              placeholder="Search employees..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-7 pr-2 py-1 w-full h-8 text-xs"
            />
          </div>
          <Select value={statusFilter} onValueChange={(value) => {
            setStatusFilter(value);
            const filtered = employeesData.filter(({ employee, attendances }) =>
              `${employee.prenom} ${employee.nom}`.toLowerCase().includes(searchTerm.toLowerCase()) &&
              (value === 'All' || attendances.some(a => a.status === value))
            );
            setFilteredEmployeesData(filtered);
            calculateAttendanceSummary(filtered);
          }}>
            <SelectTrigger className="w-[140px] h-8 text-xs">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All Statuses</SelectItem>
              <SelectItem value="Present">Present</SelectItem>
              <SelectItem value="Absent">Absent</SelectItem>
              <SelectItem value="Late">Late</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {showStats && (
          <Card className="mb-3">
            <CardHeader className="py-2 px-3">
              <CardTitle className="text-sm">Attendance Summary</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs py-2">Employee</TableHead>
                    <TableHead className="text-xs py-2">Present</TableHead>
                    <TableHead className="text-xs py-2">Absent</TableHead>
                    <TableHead className="text-xs py-2">Late</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {attendanceSummary.map((summary) => (
                    <TableRow key={summary.employeeId} className="text-sm">
                      <TableCell className="py-1">{summary.employeeName}</TableCell>
                      <TableCell className="py-1">{summary.present}</TableCell>
                      <TableCell className="py-1">{summary.absent}</TableCell>
                      <TableCell className="py-1">{summary.late}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
        <div className="grid grid-cols-7 gap-1 font-semibold text-center mb-1 text-muted-foreground rounded-t-lg">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-1 text-xs">{day}</div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-1 rounded-b-lg shadow-inner">
          {renderCalendar()}
        </div>
      </CardContent>

      <Dialog open={!!selectedDate} onOpenChange={() => setSelectedDate(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Attendance for {selectedDate && format(selectedDate, 'MMMM d, yyyy')}</DialogTitle>
            <DialogDescription>
              Total Attendances: {selectedAttendances.length}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 max-h-[60vh] overflow-auto pr-2">
            {selectedAttendances.map((attendance, index) => (
              <div key={index} className="mb-4 p-4 rounded-lg border">
                <h3 className="font-semibold">{`${attendance.employee.prenom} ${attendance.employee.nom}`}</h3>
                <p>Status: <Badge className={StatusColorMap[attendance.status]}>{attendance.status}</Badge></p>
                <p>Time: {format(new Date(attendance.date), 'HH:mm')}</p>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default EmployeeAttendanceCalendar;