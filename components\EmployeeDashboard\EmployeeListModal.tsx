"use client"

import React from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface Employee {
  id: string;
  prenom: string;
  nom: string;
  poste: string;
  departement: string;
  salaire: number;
  dateDebut?: Date;
  genre?: string;
  typeEmploi?: string;
  contratType?: string | null;
}

interface EmployeeListModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  employees: Employee[];
  modalType?: 'contract' | 'gender' | 'department' | 'default';
}

const EmployeeListModal: React.FC<EmployeeListModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  employees,
  modalType = 'default'
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');

  const filteredEmployees = employees.filter(employee => {
    const nameMatch = `${employee.prenom} ${employee.nom}`.toLowerCase().includes(searchTerm.toLowerCase());
    const posteMatch = employee.poste?.toLowerCase().includes(searchTerm.toLowerCase());
    const deptMatch = employee.departement?.toLowerCase().includes(searchTerm.toLowerCase());
    const contractMatch = (employee.contratType || employee.typeEmploi || '')
      .toLowerCase().includes(searchTerm.toLowerCase());
    const genreMatch = employee.genre?.toLowerCase().includes(searchTerm.toLowerCase());

    return nameMatch || posteMatch || deptMatch || contractMatch || genreMatch;
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="relative mb-4">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search employees..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8 pr-4 py-2"
          />
        </div>

        <ScrollArea className="h-[300px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                {modalType === 'default' && (
                  <>
                    <TableHead>Position</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Contract Type</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>Salary</TableHead>
                  </>
                )}
                {modalType === 'contract' && (
                  <>
                    <TableHead>Contract Type</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Salary</TableHead>
                  </>
                )}
                {modalType === 'gender' && (
                  <>
                    <TableHead>Gender</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Start Date</TableHead>
                  </>
                )}
                {modalType === 'department' && (
                  <>
                    <TableHead>Department</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Contract Type</TableHead>
                    <TableHead>Salary</TableHead>
                  </>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEmployees.length > 0 ? (
                filteredEmployees.map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell className="font-medium">{`${employee.prenom} ${employee.nom}`}</TableCell>

                    {modalType === 'default' && (
                      <>
                        <TableCell>{employee.poste}</TableCell>
                        <TableCell>{employee.departement}</TableCell>
                        <TableCell>{employee.contratType || employee.typeEmploi || 'Not specified'}</TableCell>
                        <TableCell>{employee.dateDebut ? new Date(employee.dateDebut).toLocaleDateString() : 'Not specified'}</TableCell>
                        <TableCell>${employee.salaire.toLocaleString()}</TableCell>
                      </>
                    )}

                    {modalType === 'contract' && (
                      <>
                        <TableCell className="font-medium">{employee.contratType || employee.typeEmploi || 'Not specified'}</TableCell>
                        <TableCell>{employee.dateDebut ? new Date(employee.dateDebut).toLocaleDateString() : 'Not specified'}</TableCell>
                        <TableCell>{employee.poste}</TableCell>
                        <TableCell>${employee.salaire.toLocaleString()}</TableCell>
                      </>
                    )}

                    {modalType === 'gender' && (
                      <>
                        <TableCell className="font-medium">{employee.genre || 'Not specified'}</TableCell>
                        <TableCell>{employee.poste}</TableCell>
                        <TableCell>{employee.departement}</TableCell>
                        <TableCell>{employee.dateDebut ? new Date(employee.dateDebut).toLocaleDateString() : 'Not specified'}</TableCell>
                      </>
                    )}

                    {modalType === 'department' && (
                      <>
                        <TableCell className="font-medium">{employee.departement}</TableCell>
                        <TableCell>{employee.poste}</TableCell>
                        <TableCell>{employee.contratType || employee.typeEmploi || 'Not specified'}</TableCell>
                        <TableCell>${employee.salaire.toLocaleString()}</TableCell>
                      </>
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={modalType === 'default' ? 6 : 5} className="text-center py-4 text-muted-foreground">
                    No employees found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </ScrollArea>

        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EmployeeListModal;
