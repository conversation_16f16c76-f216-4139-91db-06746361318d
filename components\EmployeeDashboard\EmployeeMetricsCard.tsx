"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, TrendingUp, Clock, Calendar, Users, Eye } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Button } from "@/components/ui/button";
import EmployeeListModal from "./EmployeeListModal";

interface EmployeeMetric {
  value: number;
  total?: number;
  change?: number;
  trend?: 'up' | 'down' | 'neutral';
}

interface Employee {
  id: string;
  prenom: string;
  nom: string;
  poste: string;
  departement: string;
  salaire: number;
  dateDebut?: Date;
  genre?: string;
  typeEmploi?: string;
  contratType?: string | null;
}

interface EmployeeMetricsCardProps {
  metrics: {
    retention: EmployeeMetric;
    turnover: EmployeeMetric;
    avgTenure: EmployeeMetric;
    newHires: EmployeeMetric;
  };
  trendData: Array<{ name: string; employees: number }>;
  isLoading: boolean;
  allEmployees: Employee[];
}

const EmptyState = () => (
  <Alert>
    <AlertCircle className="h-4 w-4" />
    <AlertDescription>
      No metrics data available yet. Add more employee history to see HR metrics.
    </AlertDescription>
  </Alert>
);

const EmployeeMetricsCard: React.FC<EmployeeMetricsCardProps> = ({ metrics, trendData, isLoading, allEmployees }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState('');
  const [selectedEmployees, setSelectedEmployees] = useState<Employee[]>([]);
  const [modalType, setModalType] = useState<'contract' | 'gender' | 'department' | 'default'>('default');

  const openEmployeeModal = (
    title: string,
    description: string,
    filterFn: (employee: Employee) => boolean,
    type: 'contract' | 'gender' | 'department' | 'default' = 'default'
  ) => {
    const filteredEmployees = allEmployees.filter(filterFn);
    setModalTitle(title);
    setModalDescription(description);
    setSelectedEmployees(filteredEmployees);
    setModalType(type);
    setModalOpen(true);
  };

  // Get new hires (employees who joined in the last 3 months)
  const getNewHires = () => {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    return allEmployees.filter(emp => emp.dateDebut && new Date(emp.dateDebut) >= threeMonthsAgo);
  };
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="py-2 px-3">
        <CardTitle className="text-sm">HR Metrics</CardTitle>
        <CardDescription className="text-xs">Key performance indicators for HR management</CardDescription>
      </CardHeader>
      <CardContent className="p-3">
        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-3 pt-2">
            {/* Retention Rate */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium">Employee Retention Rate</h4>
                  <p className="text-xs text-muted-foreground">Percentage of employees retained</p>
                </div>
                <div className="flex items-center">
                  <span className="text-lg font-bold">{metrics.retention.value}%</span>
                  {metrics.retention.trend && (
                    <TrendingUp className={`ml-2 h-4 w-4 ${
                      metrics.retention.trend === 'up' ? 'text-green-500 rotate-0' :
                      metrics.retention.trend === 'down' ? 'text-red-500 rotate-180' : 'text-yellow-500'
                    }`} />
                  )}
                </div>
              </div>
              <Progress value={metrics.retention.value} className="h-2" />
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-1 h-7 text-xs"
                onClick={() => openEmployeeModal(
                  'Retained Employees',
                  'Employees who have been with the company for more than 1 year',
                  (emp) => {
                    if (!emp.dateDebut) return false;
                    const oneYearAgo = new Date();
                    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
                    return new Date(emp.dateDebut) <= oneYearAgo;
                  }
                )}
              >
                <Eye className="h-3 w-3 mr-1" /> View Employees
              </Button>
            </div>

            {/* Turnover Rate */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium">Employee Turnover Rate</h4>
                  <p className="text-xs text-muted-foreground">Percentage of employees who left</p>
                </div>
                <div className="flex items-center">
                  <span className="text-lg font-bold">{metrics.turnover.value}%</span>
                  {metrics.turnover.trend && (
                    <TrendingUp className={`ml-2 h-4 w-4 ${
                      metrics.turnover.trend === 'down' ? 'text-green-500 rotate-180' :
                      metrics.turnover.trend === 'up' ? 'text-red-500 rotate-0' : 'text-yellow-500'
                    }`} />
                  )}
                </div>
              </div>
              <Progress value={metrics.turnover.value} className="h-2" />
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-1 h-7 text-xs"
                onClick={() => openEmployeeModal(
                  'Recent Departures',
                  'Employees who left in the last 6 months',
                  (emp) => {
                    // This is a placeholder since we don't have actual departure data
                    // In a real app, you would filter based on departure date
                    return false;
                  }
                )}
              >
                <Eye className="h-3 w-3 mr-1" /> View Employees
              </Button>
            </div>

            {/* Average Tenure */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium">Average Tenure</h4>
                  <p className="text-xs text-muted-foreground">Average time employees stay</p>
                </div>
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span className="text-lg font-bold">{metrics.avgTenure.value} months</span>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-1 h-7 text-xs"
                onClick={() => openEmployeeModal(
                  'Long-Term Employees',
                  'Employees who have been with the company for more than 2 years',
                  (emp) => {
                    if (!emp.dateDebut) return false;
                    const twoYearsAgo = new Date();
                    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
                    return new Date(emp.dateDebut) <= twoYearsAgo;
                  }
                )}
              >
                <Eye className="h-3 w-3 mr-1" /> View Employees
              </Button>
            </div>

            {/* New Hires */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium">New Hires (Last 3 Months)</h4>
                  <p className="text-xs text-muted-foreground">Recently added employees</p>
                </div>
                <div className="flex items-center">
                  <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span className="text-lg font-bold">{metrics.newHires.value}</span>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-1 h-7 text-xs"
                onClick={() => openEmployeeModal(
                  'New Hires',
                  'Employees who joined in the last 3 months',
                  (emp) => {
                    if (!emp.dateDebut) return false;
                    const threeMonthsAgo = new Date();
                    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                    return new Date(emp.dateDebut) >= threeMonthsAgo;
                  }
                )}
              >
                <Eye className="h-3 w-3 mr-1" /> View Employees
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="trends" className="pt-2">
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={trendData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="employees"
                    stroke="hsl(var(--primary))"
                    activeDot={{ r: 8 }}
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <EmployeeListModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title={modalTitle}
        description={modalDescription}
        employees={selectedEmployees}
        modalType={modalType}
      />
    </Card>
  );
};

export default EmployeeMetricsCard;
