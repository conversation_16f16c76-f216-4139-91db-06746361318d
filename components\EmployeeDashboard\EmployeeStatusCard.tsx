"use client"

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Users, Clock, Calendar, Briefcase, Eye } from "lucide-react";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import { Button } from "@/components/ui/button";
import EmployeeListModal from "./EmployeeListModal";

interface Employee {
  id: string;
  prenom: string;
  nom: string;
  poste: string;
  departement: string;
  salaire: number;
  dateDebut?: Date;
  genre?: string;
  typeEmploi?: string;
  contratType?: string | null;
}

interface EmployeeStatusCardProps {
  statusData: {
    active: number;
    onLeave: number;
    remote: number;
    probation: number;
  };
  isLoading: boolean;
  allEmployees?: Employee[];
}

// Define colors that work well in both light and dark mode
const COLORS = ['hsl(217, 91%, 60%)', 'hsl(158, 64%, 52%)', 'hsl(43, 96%, 58%)', 'hsl(16, 94%, 57%)'];

const EmptyState = () => (
  <Alert>
    <AlertCircle className="h-4 w-4" />
    <AlertDescription>
      No employee status data available yet.
    </AlertDescription>
  </Alert>
);

const EmployeeStatusCard: React.FC<EmployeeStatusCardProps> = ({ statusData, isLoading, allEmployees = [] }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState('');
  const [selectedEmployees, setSelectedEmployees] = useState<Employee[]>([]);

  const openEmployeeModal = (title: string, description: string, filterFn: (employee: Employee) => boolean) => {
    const filteredEmployees = allEmployees.filter(filterFn);
    setModalTitle(title);
    setModalDescription(description);
    setSelectedEmployees(filteredEmployees);
    setModalOpen(true);
  };
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[200px] w-full" />
        </CardContent>
      </Card>
    );
  }

  const chartData = [
    { name: 'Active', value: statusData.active },
    { name: 'On Leave', value: statusData.onLeave },
    { name: 'Remote', value: statusData.remote },
    { name: 'Probation', value: statusData.probation },
  ];

  const totalEmployees = chartData.reduce((sum, item) => sum + item.value, 0);

  if (totalEmployees === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Employee Status</CardTitle>
          <CardDescription>Current status of all employees</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="py-2 px-3">
        <CardTitle className="text-sm">Employee Status</CardTitle>
        <CardDescription className="text-xs">Current status of all employees</CardDescription>
      </CardHeader>
      <CardContent className="p-3">
        <div className="flex flex-col md:flex-row items-start justify-between gap-4">
          {/* Pie Chart with Legend */}
          <div className="w-full md:w-1/2">
            <div className="h-[180px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={false} // Remove labels from pie segments for cleaner look
                    outerRadius={70}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} employees`]}
                    contentStyle={{ backgroundColor: 'hsl(var(--background))', borderColor: 'hsl(var(--border))' }}
                    labelStyle={{ color: 'hsl(var(--foreground))' }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Custom Legend */}
            <div className="flex flex-wrap justify-center gap-3 mt-2">
              {chartData.map((entry, index) => (
                <div key={`legend-${index}`} className="flex items-center gap-1">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className="text-xs">{entry.name}: {entry.value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Status Cards */}
          <div className="w-full md:w-1/2 grid grid-cols-2 gap-2 mt-2 md:mt-0">
            <div className="flex flex-col items-center p-1.5 border rounded-lg bg-background/50 hover:bg-background/80 transition-colors">
              <Users className="h-4 w-4 text-blue-500 mb-1" />
              <span className="text-xs font-medium">Active</span>
              <span className="text-lg font-bold">{statusData.active}</span>
              <Button
                variant="ghost"
                size="sm"
                className="mt-1 h-5 text-[9px] w-full"
                onClick={() => openEmployeeModal(
                  'Active Employees',
                  'Currently active employees',
                  (emp) => true // All employees are considered active in this demo
                )}
              >
                <Eye className="h-2 w-2 mr-1" /> View
              </Button>
            </div>

            <div className="flex flex-col items-center p-1.5 border rounded-lg bg-background/50 hover:bg-background/80 transition-colors">
              <Calendar className="h-4 w-4 text-green-500 mb-1" />
              <span className="text-xs font-medium">On Leave</span>
              <span className="text-lg font-bold">{statusData.onLeave}</span>
              <Button
                variant="ghost"
                size="sm"
                className="mt-1 h-5 text-[9px] w-full"
                onClick={() => openEmployeeModal(
                  'Employees on Leave',
                  'Employees currently on leave',
                  (emp) => false // No employees on leave in this demo
                )}
              >
                <Eye className="h-2 w-2 mr-1" /> View
              </Button>
            </div>

            <div className="flex flex-col items-center p-1.5 border rounded-lg bg-background/50 hover:bg-background/80 transition-colors">
              <Briefcase className="h-4 w-4 text-yellow-500 mb-1" />
              <span className="text-xs font-medium">Remote</span>
              <span className="text-lg font-bold">{statusData.remote}</span>
              <Button
                variant="ghost"
                size="sm"
                className="mt-1 h-5 text-[9px] w-full"
                onClick={() => openEmployeeModal(
                  'Remote Employees',
                  'Employees working remotely',
                  (emp) => emp.typeEmploi === 'Remote' || emp.contratType === 'Remote'
                )}
              >
                <Eye className="h-2 w-2 mr-1" /> View
              </Button>
            </div>

            <div className="flex flex-col items-center p-1.5 border rounded-lg bg-background/50 hover:bg-background/80 transition-colors">
              <Clock className="h-4 w-4 text-orange-500 mb-1" />
              <span className="text-xs font-medium">Probation</span>
              <span className="text-lg font-bold">{statusData.probation}</span>
              <Button
                variant="ghost"
                size="sm"
                className="mt-1 h-5 text-[9px] w-full"
                onClick={() => openEmployeeModal(
                  'Employees on Probation',
                  'Employees in probation period',
                  (emp) => {
                    if (!emp.dateDebut) return false;
                    const threeMonthsAgo = new Date();
                    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                    return new Date(emp.dateDebut) >= threeMonthsAgo;
                  }
                )}
              >
                <Eye className="h-2 w-2 mr-1" /> View
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      <EmployeeListModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title={modalTitle}
        description={modalDescription}
        employees={selectedEmployees}
      />
    </Card>
  );
};

export default EmployeeStatusCard;
