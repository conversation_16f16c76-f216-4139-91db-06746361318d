"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, TrendingUp, Search, Users, DollarSign, Eye, Table as TableIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import * as echarts from 'echarts';
import { fetchEmployeesBySearch } from './actions';
import EmployeeListModal from "./EmployeeListModal";

interface Employee {
  id: string;
  prenom: string;
  nom: string;
  poste: string;
  departement: string;
  salaire: number;
  dateDebut?: Date;
  genre?: string;
  typeEmploi?: string;
  contratType?: string | null;
}

interface TrendData {
  month: string;
  employees: number;
  totalSalary: number;
  newHires: number;
  name?: string; // Added for compatibility
}

interface EmployeeTrendChartProps {
  trendData: TrendData[];
  isLoading: boolean;
  allEmployees?: Employee[];
}

const EmptyState = () => (
  <Alert>
    <AlertCircle className="h-4 w-4" />
    <AlertDescription>
      No new hires data available yet. Add employees with recent start dates to see them appear here.
    </AlertDescription>
  </Alert>
);

const EmployeeTrendChart: React.FC<EmployeeTrendChartProps> = ({ trendData, isLoading, allEmployees = [] }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<'employees' | 'totalSalary' | 'newHires'>('newHires');
  const [chartInitialized, setChartInitialized] = useState(false);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState('');
  const [selectedEmployees, setSelectedEmployees] = useState<Employee[]>([]);
  const [modalType, setModalType] = useState<'contract' | 'gender' | 'department' | 'default'>('default');

  // Function to open the employee modal
  const openEmployeeModal = () => {
    // Create a title based on the selected metric
    const title = selectedMetric === 'employees' ? 'Employee Count' :
                 selectedMetric === 'totalSalary' ? 'Total Salary' : 'New Hires';

    // Create a description based on the selected metric
    const description = `Monthly ${title.toLowerCase()} data for the last 6 months`;

    // Filter employees based on the selected metric
    let filteredEmployees: Employee[] = [];

    if (selectedMetric === 'newHires' && allEmployees.length > 0) {
      // For new hires, show employees hired in the last 6 months
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      filteredEmployees = allEmployees.filter(emp =>
        emp.dateDebut && new Date(emp.dateDebut) >= sixMonthsAgo
      );
    } else {
      // For other metrics, show all employees
      filteredEmployees = [...allEmployees];
    }

    // Sort employees by start date (newest first) for better relevance
    filteredEmployees.sort((a, b) => {
      if (!a.dateDebut) return 1;
      if (!b.dateDebut) return -1;
      return new Date(b.dateDebut).getTime() - new Date(a.dateDebut).getTime();
    });

    setModalTitle(title);
    setModalDescription(description);
    setSelectedEmployees(filteredEmployees);
    setModalType('default'); // Use default layout for trend data
    setModalOpen(true);
  };

  // Function to initialize and render the chart
  const initializeChart = useCallback(() => {
    if (!chartRef.current || !trendData || trendData.length === 0) return;

    // Dispose existing chart if any
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // Create new chart instance
    chartInstance.current = echarts.init(chartRef.current);
    setChartInitialized(true);

    // Prepare data for the selected metric
    const seriesData = trendData.map(item => [item.month, item[selectedMetric]]);

    // Set chart options
    const option = {
      color: ['hsl(var(--chart-1))'],
      grid: {
        top: 30,
        right: 20,
        bottom: 30,
        left: 50,
        containLabel: true
      },
      title: {
        text: selectedMetric === 'employees' ? 'Employee Count' :
              selectedMetric === 'totalSalary' ? 'Total Salary' : 'New Hires',
        left: 'center',
        top: 0,
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
          color: 'hsl(var(--foreground))'
        }
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params: any) => {
          const param = params[0];
          const metricLabel = selectedMetric === 'employees' ? 'Employees' :
                           selectedMetric === 'totalSalary' ? 'Total Salary' : 'New Hires';
          const value = selectedMetric === 'totalSalary' ?
                     `$${param.value[1].toLocaleString()}` : param.value[1];

          return `${param.value[0]}<br/>
               <span style="display:inline-block;margin-right:4px;border-radius:50%;width:8px;height:8px;background-color:${param.color};"></span>
               ${metricLabel}: ${value}`;
        }
      },
      xAxis: {
        type: 'category',
        data: trendData.map(item => item.month),
        axisLabel: { fontSize: 10 },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
          formatter: (value: number) => {
            if (selectedMetric === 'totalSalary') {
              return value >= 1000 ? `$${value/1000}k` : `$${value}`;
            }
            return value;
          }
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            opacity: 0.3
          }
        }
      },
      series: [{
        name: selectedMetric === 'employees' ? 'Employees' :
              selectedMetric === 'totalSalary' ? 'Total Salary' : 'New Hires',
        type: 'line',
        data: seriesData,
        showSymbol: true,
        symbolSize: 6,
        smooth: true,
        lineStyle: { width: 2 },
        areaStyle: { opacity: 0.2 }
      }]
    };

    // Apply options
    chartInstance.current.setOption(option);
  }, [trendData, selectedMetric]);

  // Initialize chart when component mounts or data changes
  useEffect(() => {
    if (!isLoading && trendData && trendData.length > 0) {
      // Use a small timeout to ensure the DOM is ready
      const timer = setTimeout(() => {
        initializeChart();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [trendData, isLoading, initializeChart]);

  // Update chart when metric changes
  useEffect(() => {
    if (chartInitialized) {
      initializeChart();
    }
  }, [selectedMetric, chartInitialized, initializeChart]);

  // Add resize handler
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div className="mb-2 md:mb-0">
            <Skeleton className="h-6 w-24 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
          <Skeleton className="h-8 w-40" />
        </div>
        <Skeleton className="h-[200px] w-full" />
        <Skeleton className="h-4 w-full mt-3" />
      </div>
    );
  }

  // Only show empty state if specifically looking at new hires and there are none
  if (selectedMetric === 'newHires' && (!trendData || trendData.length === 0 || trendData.every(item => item.newHires === 0))) {
    return (
      <div className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div className="mb-2 md:mb-0">
            <div className="text-lg font-semibold">No New Hires</div>
            <div className="text-xs text-muted-foreground">No employees have been added recently</div>
          </div>
          <div className="flex space-x-2 bg-muted/30 p-1 rounded-md">
            <button
              className="flex items-center gap-1 px-2 py-1 rounded-md text-xs bg-primary/20 text-primary"
              onClick={() => setSelectedMetric('employees')}
            >
              <Users className="h-3 w-3" />
              <span>Show Employees</span>
            </button>
            <button
              className="flex items-center gap-1 px-2 py-1 rounded-md text-xs"
              onClick={() => setSelectedMetric('totalSalary')}
            >
              <DollarSign className="h-3 w-3" />
              <span>Show Salary</span>
            </button>
          </div>
        </div>
        <div className="w-full border-t pt-2 h-[200px] flex items-center justify-center">
          <EmptyState />
        </div>
      </div>
    );
  }

  // If there's no data at all, show a general empty state
  if (!trendData || trendData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div className="mb-2 md:mb-0">
            <div className="text-lg font-semibold">No Data Available</div>
            <div className="text-xs text-muted-foreground">Employee trend data is not available</div>
          </div>
          <div className="flex space-x-2 bg-muted/30 p-1 rounded-md">
            <button className="flex items-center gap-1 px-2 py-1 rounded-md text-xs bg-primary/20 text-primary">
              <TrendingUp className="h-3 w-3" />
              <span>Trends</span>
            </button>
          </div>
        </div>
        <div className="w-full border-t pt-2 h-[200px] flex items-center justify-center">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No employee trend data available yet. Add employees to see trends.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  // Calculate summary statistics
  const getSummaryStats = () => {
    if (!trendData || trendData.length < 2) return { value: 0, change: 0, isPositive: true };

    const currentValue = trendData[trendData.length - 1][selectedMetric];
    const previousValue = trendData[trendData.length - 2][selectedMetric];
    const change = ((currentValue - previousValue) / previousValue) * 100;

    return {
      value: currentValue,
      change: Math.abs(change).toFixed(1),
      isPositive: change >= 0
    };
  };

  const stats = getSummaryStats();

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
        {/* Summary section - always visible */}
        <div className="mb-2 md:mb-0">
          <div className="flex items-baseline gap-2">
            <span className="text-lg font-semibold">
              {selectedMetric === 'employees' ? stats.value :
               selectedMetric === 'totalSalary' ? `$${stats.value.toLocaleString()}` :
               stats.value}
            </span>
            <span className={`text-xs ${stats.isPositive ? 'text-green-500' : 'text-red-500'}`}>
              {stats.isPositive ? '+' : '-'}{stats.change}%
            </span>
          </div>
          <div className="text-xs text-muted-foreground">
            {selectedMetric === 'employees' ? 'Current employee count' :
             selectedMetric === 'totalSalary' ? 'Current monthly payroll' :
             'New hires this month'}
          </div>
        </div>

        {/* Metric selector buttons */}
        <div className="flex space-x-2 bg-muted/30 p-1 rounded-md">
          <button
            className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs ${selectedMetric === 'employees' ? 'bg-primary/20 text-primary' : ''}`}
            onClick={() => setSelectedMetric('employees')}
          >
            <Users className="h-3 w-3" />
            <span>Employees</span>
          </button>
          <button
            className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs ${selectedMetric === 'totalSalary' ? 'bg-primary/20 text-primary' : ''}`}
            onClick={() => setSelectedMetric('totalSalary')}
          >
            <DollarSign className="h-3 w-3" />
            <span>Salary</span>
          </button>
          <button
            className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs ${selectedMetric === 'newHires' ? 'bg-primary/20 text-primary' : ''}`}
            onClick={() => setSelectedMetric('newHires')}
          >
            <TrendingUp className="h-3 w-3" />
            <span>New Hires</span>
          </button>
        </div>
      </div>

      {/* Chart */}
      <div ref={chartRef} className="w-full border-t pt-2" style={{ height: "200px" }} />

      {/* Description and View Table Button */}
      <div className="mt-3 text-xs text-muted-foreground border-t pt-2 flex justify-between items-center">
        <div className="flex items-center gap-1">
          <TrendingUp className="h-3 w-3" />
          <span>
            {selectedMetric === 'employees' ? 'Employee count' :
             selectedMetric === 'totalSalary' ? 'Total salary' : 'New hires'} over the last 6 months
          </span>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="h-7 text-xs"
          onClick={openEmployeeModal}
        >
          <TableIcon className="h-3 w-3 mr-1" /> View Table
        </Button>
      </div>

      {/* Employee List Modal */}
      <EmployeeListModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title={modalTitle}
        description={modalDescription}
        employees={selectedEmployees}
        modalType={modalType}
      />
    </div>
  );
};

export default EmployeeTrendChart;
