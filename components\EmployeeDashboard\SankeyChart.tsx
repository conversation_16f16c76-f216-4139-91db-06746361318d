"use client"
import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ResponsiveSankey } from '@nivo/sankey'
import { AlertCircle, DollarSign, Users } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { fetchSankeyData } from "@/actions/sankeyActions"
import { toast } from "sonner"
import { useResizeObserver } from "@/hooks/use-resize-observer"

interface Employee {
  id: string
  departement: string
  poste: string
  salaire: number
}

interface SankeyNode {
  id: string
  nodeColor: string
  label?: string
}

interface SankeyLink {
  source: string
  target: string
  value: number
}

interface FormattedSankeyData {
  nodes: SankeyNode[]
  links: SankeyLink[]
}

export function SankeyChart() {
  const [data, setData] = useState<FormattedSankeyData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)
  const { width = 800 } = useResizeObserver(containerRef)

  // Adjusted margins for smaller width
  const margins = {
    top: 20,
    right: width > 768 ? 100 : 60,
    bottom: 20,
    left: width > 768 ? 100 : 60
  }

  // Adjusted dimensions for smaller chart
  const nodeThickness = width > 768 ? 18 : 14
  const nodeSpacing = width > 768 ? 24 : 18
  const labelPadding = width > 768 ? 16 : 12

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true)
        const employees = await fetchSankeyData() as Employee[]

        // Process nodes to ensure unique departments and positions
        const departments = new Set<string>()
        const positions = new Set<string>()
        const links = new Map<string, number>()

        // First pass: collect unique departments and positions
        employees.forEach((employee) => {
          const dept = employee.departement || 'Non assigné'
          const pos = employee.poste || 'Non assigné'
          departments.add(dept)
          positions.add(pos)

          // Create a unique key for this department-position combination
          const key = `${dept}|${pos}`
          const currentValue = links.get(key) || 0
          links.set(key, currentValue + (Number(employee.salaire) || 0))
        })

        // Create formatted data with distinct node IDs
        const formattedData = {
          nodes: [
            // Department nodes with prefix to avoid ID conflicts
            ...Array.from(departments).map(dept => ({
              id: `dept_${dept}`,
              nodeColor: 'hsl(var(--primary))',
              label: dept // Keep original name for display
            })),
            // Position nodes with prefix
            ...Array.from(positions).map(pos => ({
              id: `pos_${pos}`,
              nodeColor: 'hsl(var(--secondary))',
              label: pos // Keep original name for display
            }))
          ],
          links: Array.from(links.entries()).map(([key, value]) => {
            const [dept, pos] = key.split('|')
            return {
              source: `dept_${dept}`,
              target: `pos_${pos}`,
              value: Math.max(value, 1000) // Ensure minimum value for visibility
            }
          }).filter(link => link.source !== link.target) // Remove self-links
        }

        setData(formattedData)
      } catch (error) {
        console.error('Failed to fetch Sankey data:', error)
        toast.error('Failed to load resource flow data')
      } finally {
        setIsLoading(false)
      }
    }
    loadData()
  }, [])

  if (isLoading) return (
    <div className="w-full">
      <Skeleton className="h-[400px] w-full" />
    </div>
  )

  if (!data || !data.nodes.length) return (
    <div className="w-full">
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Start by adding employees with departments and positions to see resource distribution.
        </AlertDescription>
      </Alert>
    </div>
  )

  return (
    <div className="w-full">
      <div ref={containerRef} className="relative h-[300px] md:h-[400px]">
        <div className="absolute -left-4 top-1/2 -translate-y-1/2 -translate-x-full transform rotate-[-90deg] text-xs md:text-sm font-medium text-muted-foreground">
          Departments
        </div>
        <div className="absolute -right-4 top-1/2 -translate-y-1/2 translate-x-full transform rotate-90 text-xs md:text-sm font-medium text-muted-foreground">
          Positions
        </div>
        <div className="absolute left-1/2 -bottom-6 -translate-x-1/2 text-xs md:text-sm font-medium text-muted-foreground">
          Salary Flow
        </div>

        <ResponsiveSankey
          data={data}
          margin={margins}
          align="justify"
          colors={{ scheme: 'pastel1' }}
          theme={{
            labels: {
              text: {
                fill: 'hsl(var(--foreground))',
                fontSize: 12
              }
            },
            tooltip: {
              container: {
                background: 'hsl(var(--background))',
                color: 'hsl(var(--foreground))',
                fontSize: 12
              }
            }
          }}
          nodeOpacity={1}
          nodeHoverOthersOpacity={0.35}
          nodeThickness={nodeThickness}
          nodeSpacing={nodeSpacing}
          nodeBorderWidth={0}
          nodeBorderRadius={3}
          linkOpacity={0.5}
          linkHoverOthersOpacity={0.1}
          linkContract={2}
          enableLinkGradient={true}
          labelPosition="outside"
          labelOrientation="horizontal"
          labelPadding={labelPadding}
          labelTextColor={{
            from: 'color',
            modifiers: [['darker', 1]]
          }}
          // @ts-ignore
          label={d => `${d.label || d.id.replace(/^(dept_|pos_)/, '')} (${new Intl.NumberFormat('fr-MA', {
            style: 'currency',
            currency: 'MAD',
            notation: 'compact',
            maximumFractionDigits: 0
          }).format(d.value)})`}
          animate={false}
          nodeTooltip={({ node }) => (
            <div className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/85 p-2 rounded-lg border shadow-lg">
              <div className="flex items-center gap-2 font-semibold text-sm">
                <div className="w-2 h-2 rounded-full" style={{ backgroundColor: node.color }} />
                {node.label || node.id.replace(/^(dept_|pos_)/, '')}
              </div>
              <div className="flex items-center gap-2 mt-1 text-xs">
                <DollarSign className="w-3 h-3" />
                <span>
                  {new Intl.NumberFormat('fr-MA', {
                    style: 'currency',
                    currency: 'MAD',
                    maximumFractionDigits: 0
                  }).format(node.value)}
                </span>
              </div>
              <div className="flex items-center gap-2 mt-1 text-xs">
                <Users className="w-3 h-3" />
                <span>{Math.round(node.value / 50000)} Employees</span>
              </div>
            </div>
          )}
          linkTooltip={({ link }) => (
            <div className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/85 p-2 rounded-lg border shadow-lg">
              <div className="font-semibold text-sm">
                {link.source.id.replace(/^dept_/, '')} → {link.target.id.replace(/^pos_/, '')}
              </div>
              <div className="flex items-center gap-2 mt-1 text-xs">
                <DollarSign className="w-3 h-3" />
                <span>
                  {new Intl.NumberFormat('fr-MA', {
                    style: 'currency',
                    currency: 'MAD',
                    maximumFractionDigits: 0
                  }).format(link.value)}
                </span>
              </div>
            </div>
          )}
        />
      </div>
    </div>
  )
}
