'use server';

import prisma from '@/lib/db';
import { ensureUserInDatabase } from '@/lib/ensure-user';
import { subMonths, format } from 'date-fns';

export async function fetchEmployeeTrendData() {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return [];
    }

    // Get current date and calculate dates for the last 6 months
    const now = new Date();
    const monthlyData = [];

    // Generate data for the last 6 months
    for (let i = 5; i >= 0; i--) {
      const targetDate = subMonths(now, i);
      const monthStart = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
      const monthEnd = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);

      // Count employees who were active during this month
      const employeeCount = await prisma.employee.count({
        where: {
          userId: user.id,
          dateDebut: {
            lte: monthEnd
          },
          OR: [
            { dateFinContrat: null },
            { dateFinContrat: { gte: monthStart } }
          ]
        }
      });

      // Get salary data for the month
      const salaryData = await prisma.employee.aggregate({
        where: {
          userId: user.id,
          dateDebut: {
            lte: monthEnd
          },
          OR: [
            { dateFinContrat: null },
            { dateFinContrat: { gte: monthStart } }
          ]
        },
        _sum: {
          salaire: true
        }
      });

      // Format month name
      const monthName = format(targetDate, 'MMM yyyy'); // Added year for better clarity

      // Get new hires for the month - with detailed logging
      const newHiresQuery = {
        userId: user.id,
        dateDebut: {
          gte: monthStart,
          lte: monthEnd
        }
      };

      console.log(`Querying new hires for ${monthName} with date range:`, {
        monthStart: monthStart.toISOString(),
        monthEnd: monthEnd.toISOString()
      });

      const newHiresEmployees = await prisma.employee.findMany({
        where: newHiresQuery,
        select: {
          id: true,
          prenom: true,
          nom: true,
          dateDebut: true
        }
      });

      const newHires = newHiresEmployees.length;

      // Log detailed new hires info for debugging
      console.log(`New hires for ${monthName}: ${newHires}`);
      if (newHires > 0) {
        console.log('New hire details:', newHiresEmployees.map(emp => ({
          name: `${emp.prenom} ${emp.nom}`,
          dateDebut: emp.dateDebut
        })));
      }

      monthlyData.push({
        month: monthName,
        employees: employeeCount,
        totalSalary: salaryData._sum.salaire || 0,
        newHires: newHires
      });
    }

    console.log('Monthly Data:', monthlyData); // Add this for debugging

    // If we have no data with new hires, create at least one entry with zero new hires
    // This ensures the chart shows something instead of "No Data"
    if (monthlyData.length === 0 || monthlyData.every(item => item.newHires === 0)) {
      console.log('No new hires data found, adding placeholder data');

      // If we have monthly data but no new hires, we'll keep the data
      if (monthlyData.length === 0) {
        // Create a placeholder entry for the current month
        const now = new Date();
        const monthName = format(now, 'MMM yyyy');

        monthlyData.push({
          month: monthName,
          employees: 0,
          totalSalary: 0,
          newHires: 0
        });
      }
    }

    return monthlyData;
  } catch (error) {
    console.error('Error fetching employee trend data:', error);
    return [];
  }
}

export async function fetchEmployeesBySearch(searchTerm: string) {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return [];
    }

    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id,
        OR: [
          { prenom: { contains: searchTerm, mode: 'insensitive' } },
          { nom: { contains: searchTerm, mode: 'insensitive' } },
          { poste: { contains: searchTerm, mode: 'insensitive' } },
          { departement: { contains: searchTerm, mode: 'insensitive' } },
          { email: { contains: searchTerm, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        prenom: true,
        nom: true,
        poste: true,
        departement: true,
        salaire: true,
        email: true,
        genre: true,
        typeEmploi: true,
        contratType: true
      }
    });

    return employees;
  } catch (error) {
    console.error('Error searching employees:', error);
    return [];
  }
}

export async function fetchDashboardData() {
  try {
    const user = await ensureUserInDatabase();
    if (!user) {
      return {
        employees: [],
        totalPayroll: 0,
        employeeCount: 0,
        avgSalary: 0,
        salaryDistribution: [],
        contractTypeData: [],
        genderData: [],
        departmentData: []
      };
    }

    // Fetch all employee data with necessary fields for dashboard
    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id
      },
      select: {
        id: true,
        prenom: true,
        nom: true,
        poste: true,
        salaire: true,
        departement: true,
        dateDebut: true,
        dateFinContrat: true,
        typeEmploi: true,
        genre: true,
        contratType: true,
        situationFamiliale: true,
        nombreEnfants: true,
        periodeEssai: true
      },
    });

    // Calculate total payroll and average salary
    const totalPayroll = employees.reduce((sum, emp) => sum + (emp.salaire || 0), 0);
    const avgSalary = employees.length > 0 ? totalPayroll / employees.length : 0;

    // Calculate salary distribution by department
    const salaryByDepartment = employees.reduce((acc, emp) => {
      const dept = emp.departement || 'Unknown';
      acc[dept] = (acc[dept] || 0) + (emp.salaire || 0);
      return acc;
    }, {} as Record<string, number>);

    const salaryDistribution = Object.entries(salaryByDepartment).map(([name, value]) => ({
      name,
      value
    }));

    // Calculate contract type distribution
    const contractTypes = employees.reduce((acc, emp) => {
      // Use contratType if available, otherwise fall back to typeEmploi
      const contractType = emp.contratType || emp.typeEmploi || 'Unknown';
      acc[contractType] = (acc[contractType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const contractTypeData = Object.entries(contractTypes).map(([name, value]) => ({
      name,
      value
    }));

    // Calculate gender distribution
    const genders = employees.reduce((acc, emp) => {
      const gender = emp.genre || 'Unknown';
      acc[gender] = (acc[gender] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const genderData = Object.entries(genders).map(([name, value]) => ({
      name,
      value
    }));

    // Calculate department size distribution
    const departments = employees.reduce((acc, emp) => {
      const department = emp.departement || 'Unknown';
      acc[department] = (acc[department] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const departmentData = Object.entries(departments).map(([name, value]) => ({
      name,
      value
    }));

    // Log data for debugging
    console.log('Dashboard Data:', {
      employeeCount: employees.length,
      totalPayroll,
      avgSalary,
      contractTypeData,
      genderData,
      departmentData
    });

    return {
      employees,
      totalPayroll,
      employeeCount: employees.length,
      avgSalary,
      salaryDistribution,
      contractTypeData,
      genderData,
      departmentData
    };
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    // Return empty data instead of throwing
    return {
      employees: [],
      totalPayroll: 0,
      employeeCount: 0,
      avgSalary: 0,
      salaryDistribution: [],
      contractTypeData: [],
      genderData: [],
      departmentData: []
    };
  }
}

