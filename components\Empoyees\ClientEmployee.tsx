'use client';

import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Mail, Phone, MapPin, Briefcase, DollarSign, Users, Clock, User, AlertCircle, FileText, Globe, CreditCard, Building, GraduationCap, Languages, VenetianMaskIcon, Home, Droplet, Truck, Map, CalendarIcon, Percent, Heart, Settings } from 'lucide-react';
import { Employee, Gender, Department, EmploymentType } from '@/types';
import { Card, CardContent, CardHeader } from "../ui/card";
import { updateEmployee } from '@/actions/actions';
import { toast, Toaster } from 'sonner';
import AttendanceHistoryBar from './AttendanceHistoryBar';
import AttendancePage from './CreateAttendance';
import { BLOOD_GROUP_OPTIONS, CONTRACT_TYPE_OPTIONS, DEPARTMENT_OPTIONS, EditableField, EDUCATION_LEVEL_OPTIONS, EMPLOYMENT_TYPE_OPTIONS, GENDER_OPTIONS, InfoSection, MODE_TRAVAIL_OPTIONS, PAYMENT_MODE_OPTIONS, SITUATION_FAMILIALE_OPTIONS, SkillsSection, TRANSPORT_OPTIONS } from './EmployeeComponents';

// ... rest of the imports

interface EmployeeProfileProps {
  employee: Employee;
}

const EditableHeader: React.FC<{
  employee: Employee;
  onSave: (name: string, value: any) => void;
}> = ({ employee, onSave }) => (
  <div className="relative flex items-center gap-4 p-3 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg">
    <div className="relative">
      <Avatar className="h-16 w-16 border-2 border-primary/20 overflow-hidden transition-transform duration-200 hover:scale-105 shadow-sm">
        <AvatarImage
          src={`https://api.dicebear.com/6.x/initials/svg?seed=${employee.prenom} ${employee.nom}`}
          className="object-cover"
        />
        <AvatarFallback className="text-xl">
          {employee.prenom[0]}{employee.nom[0]}
        </AvatarFallback>
      </Avatar>
    </div>
    <div className="flex-1">
      <div className="flex flex-wrap items-baseline gap-2 mb-1">
        <EditableField
          icon={<User className="h-4 w-4" />}
          label="First Name"
          value={employee.prenom}
          name="prenom"
          type="text"
          onSave={onSave}
          className="text-xl"
        />
        <EditableField
          icon={<User className="h-4 w-4" />}
          label="Last Name"
          value={employee.nom}
          name="nom"
          type="text"
          onSave={onSave}
          className="text-xl"
        />
      </div>
      <div className="flex flex-wrap gap-2">
        <EditableField
          icon={<Briefcase className="h-4 w-4" />}
          label="Position"
          value={employee.poste}
          name="poste"
          type="text"
          onSave={onSave}
          className="text-sm"
        />
        <EditableField
          icon={<Building className="h-4 w-4" />}
          label="Department"
          value={employee.departement}
          name="departement"
          type="select"
          options={Object.values(Department).map(dept => ({ value: dept, label: dept }))}
          onSave={onSave}
          className="text-sm"
        />
      </div>
    </div>
  </div>
);

const ClientEmployee: React.FC<EmployeeProfileProps> = ({ employee: initialEmployee }) => {
  const [employee, setEmployee] = useState<Employee>(initialEmployee);

  const handleFieldSave = async (name: string, value: any) => {
    try {
      let processedValue = value;

      const numberFields = [
        'salaire',
        'salaireBrut',
        'salaireNet',
        'tauxIR',
        'tauxCNSS',
        'tauxAMO',
        'periodeEssai',
        'congesPayes',
        'congesMaladie',
        'congesMaternite',
        'distanceDomicileTravail',
        'droitConge',
        'nombreEnfants'
      ];

      if (numberFields.includes(name)) {
        processedValue = value === '' ? null : Number(value);
      } else if (value instanceof Date) {
        processedValue = value.toISOString();
      }

      const result = await updateEmployee({
        id: employee.id,
        [name]: processedValue
      });

      if (result.success) {
        setEmployee(prev => ({ ...prev, [name]: value }));
        toast.success(`${name} updated successfully`);
      } else {
        toast.error(result.error || "Failed to update field");
      }
    } catch (error) {
      console.error('Error updating field:', error);
      toast.error("An unexpected error occurred");
    }
  };

  return (
    <>
      <Toaster />
      <div className="bg-background">
        <Card className="shadow-md rounded-lg overflow-hidden border mx-auto">
          <CardHeader className="relative p-3 border-b">
            <EditableHeader employee={employee} onSave={handleFieldSave} />
          </CardHeader>
          <CardContent className="p-3">
            <Tabs defaultValue="personal" className="w-full">
              <TabsList className="flex w-full overflow-x-auto mb-4 bg-muted/30 p-1 rounded-md scrollbar-thin">
                {[
                  { value: "personal", label: "Personnel", icon: <User className="w-3.5 h-3.5" /> },
                  { value: "professional", label: "Professionnel", icon: <Briefcase className="w-3.5 h-3.5" /> },
                  { value: "financial", label: "Financier", icon: <DollarSign className="w-3.5 h-3.5" /> },
                  { value: "benefits", label: "Avantages", icon: <Heart className="w-3.5 h-3.5" /> },
                  { value: "documents", label: "Documents", icon: <FileText className="w-3.5 h-3.5" /> },
                  { value: "config", label: "Configuration", icon: <Settings className="w-3.5 h-3.5" /> },
                  { value: "attendance", label: "Présence", icon: <Clock className="w-3.5 h-3.5" /> },
                  { value: "education", label: "Formation", icon: <GraduationCap className="w-3.5 h-3.5" /> },
                  { value: "medical", label: "Médical", icon: <Heart className="w-3.5 h-3.5" /> },
                ].map((tab) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    className="flex items-center gap-1.5 whitespace-nowrap text-xs data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-sm transition-all"
                  >
                    {tab.icon}
                    <span>{tab.label}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
                <TabsContent value="personal" className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Informations de Base">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<User className="h-4 w-4" />}
                          label="Prénom"
                          value={employee.prenom}
                          name="prenom"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<User className="h-4 w-4" />}
                          label="Nom"
                          value={employee.nom}
                          name="nom"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Mail className="h-4 w-4" />}
                          label="Email"
                          value={employee.email}
                          name="email"
                          type="email"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Phone className="h-4 w-4" />}
                          label="Téléphone"
                          value={employee.telephone}
                          name="telephone"
                          type="tel"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<CalendarIcon className="h-4 w-4" />}
                          label="Date de Naissance"
                          value={employee.dateNaissance}
                          name="dateNaissance"
                          type="date"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<User className="h-4 w-4" />}
                          label="Genre"
                          value={employee.genre}
                          name="genre"
                          type="select"
                          options={GENDER_OPTIONS}
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Identification">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<VenetianMaskIcon className="h-4 w-4" />}
                          label="CIN"
                          value={employee.cin}
                          name="cin"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Globe className="h-4 w-4" />}
                          label="Numéro Passeport"
                          value={employee.numeroPasseport}
                          name="numeroPasseport"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Permis de Travail"
                          value={employee.permisTravaill}
                          name="permisTravaill"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        {/* Only show Date Expiration Permis if Permis de Travail is provided */}
                        {employee.permisTravaill && (
                          <EditableField
                            icon={<CalendarIcon className="h-4 w-4" />}
                            label="Date Expiration Permis"
                            value={employee.dateExpirationPermis}
                            name="dateExpirationPermis"
                            type="date"
                            onSave={handleFieldSave}
                            className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                          />
                        )}
                      </div>
                    </InfoSection>

                    <InfoSection title="Adresse">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Home className="h-4 w-4" />}
                          label="Adresse"
                          value={employee.adresse}
                          name="adresse"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<MapPin className="h-4 w-4" />}
                          label="Ville"
                          value={employee.ville}
                          name="ville"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Globe className="h-4 w-4" />}
                          label="Pays"
                          value={employee.pays}
                          name="pays"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<MapPin className="h-4 w-4" />}
                          label="Zone de Résidence"
                          value={employee.zoneResidence}
                          name="zoneResidence"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Situation Familiale">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<User className="h-4 w-4" />}
                          label="Situation Familiale"
                          value={employee.situationFamiliale}
                          name="situationFamiliale"
                          type="select"
                          options={SITUATION_FAMILIALE_OPTIONS}
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        {/* Only show Nombre d'Enfants if not célibataire */}
                        {employee.situationFamiliale !== 'celibataire' && (
                          <EditableField
                            icon={<Users className="h-4 w-4" />}
                            label="Nombre d'Enfants"
                            value={employee.nombreEnfants}
                            name="nombreEnfants"
                            type="number"
                            onSave={handleFieldSave}
                            className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                          />
                        )}
                      </div>
                    </InfoSection>
                  </div>
                </TabsContent>

                <TabsContent value="professional">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Informations Professionnelles">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Building className="h-4 w-4" />}
                          label="Société"
                          value={employee.societe}
                          name="societe"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Matricule"
                          value={employee.matricule}
                          name="matricule"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Briefcase className="h-4 w-4" />}
                          label="Poste"
                          value={employee.poste}
                          name="poste"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Building className="h-4 w-4" />}
                          label="Département"
                          value={employee.departement}
                          name="departement"
                          type="select"
                          options={DEPARTMENT_OPTIONS}
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Contrat et Emploi">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Type de Contrat"
                          value={employee.contratType}
                          name="contratType"
                          type="select"
                          options={CONTRACT_TYPE_OPTIONS}
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Briefcase className="h-4 w-4" />}
                          label="Type d'Emploi"
                          value={employee.typeEmploi}
                          name="typeEmploi"
                          type="select"
                          options={EMPLOYMENT_TYPE_OPTIONS}
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<CalendarIcon className="h-4 w-4" />}
                          label="Date de Début"
                          value={employee.dateDebut}
                          name="dateDebut"
                          type="date"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Clock className="h-4 w-4" />}
                          label="Période d'Essai (jours)"
                          value={employee.periodeEssai}
                          name="periodeEssai"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        {/* Only show Date de Fin de Contrat if contract type is CDD, Stage, or Freelance */}
                        {(employee.contratType === 'cdd' ||
                          employee.contratType === 'stage' ||
                          employee.contratType === 'freelance' ||
                          employee.contratType === 'occasionnel') && (
                          <EditableField
                            icon={<CalendarIcon className="h-4 w-4" />}
                            label="Date de Fin de Contrat"
                            value={employee.dateFinContrat}
                            name="dateFinContrat"
                            type="date"
                            onSave={handleFieldSave}
                            className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                          />
                        )}
                      </div>
                    </InfoSection>

                    <InfoSection title="Service et Division">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Building className="h-4 w-4" />}
                          label="Service"
                          value={employee.service}
                          name="service"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Building className="h-4 w-4" />}
                          label="Nom du Service"
                          value={employee.nomService}
                          name="nomService"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Building className="h-4 w-4" />}
                          label="Division"
                          value={employee.division}
                          name="division"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Mode de Travail">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Briefcase className="h-4 w-4" />}
                          label="Mode de Travail"
                          value={employee.modeTravaill}
                          name="modeTravaill"
                          type="select"
                          options={MODE_TRAVAIL_OPTIONS}
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<CalendarIcon className="h-4 w-4" />}
                          label="Date de Départ"
                          value={employee.dDepart}
                          name="dDepart"
                          type="date"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>
                  </div>

                  <InfoSection title="Compétences">
                    <SkillsSection employee={employee} onSave={handleFieldSave} />
                  </InfoSection>
                </TabsContent>

                <TabsContent value="financial">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Salaire">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<DollarSign className="h-4 w-4" />}
                          label="Salaire de Base"
                          value={employee.salaire}
                          name="salaire"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<DollarSign className="h-4 w-4" />}
                          label="Salaire Brut"
                          value={employee.salaireBrut}
                          name="salaireBrut"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<DollarSign className="h-4 w-4" />}
                          label="Salaire Net"
                          value={employee.salaireNet}
                          name="salaireNet"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<DollarSign className="h-4 w-4" />}
                          label="Échelon Salaire"
                          value={employee.echelonSalaire}
                          name="echelonSalaire"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Taux">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Percent className="h-4 w-4" />}
                          label="Taux IR"
                          value={employee.tauxIR}
                          name="tauxIR"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Percent className="h-4 w-4" />}
                          label="Taux CNSS"
                          value={employee.tauxCNSS}
                          name="tauxCNSS"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Percent className="h-4 w-4" />}
                          label="Taux AMO"
                          value={employee.tauxAMO}
                          name="tauxAMO"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>
                  </div>
                  <InfoSection title="Informations Bancaires">
                    <div className="space-y-0.5">
                      <EditableField
                        icon={<Building className="h-4 w-4" />}
                        label="Banque"
                        value={employee.banque}
                        name="banque"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<Building className="h-4 w-4" />}
                        label="Agence Bancaire"
                        value={employee.agenceBancaire}
                        name="agenceBancaire"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<CreditCard className="h-4 w-4" />}
                        label="RIB"
                        value={employee.rib}
                        name="rib"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<CreditCard className="h-4 w-4" />}
                        label="Mode de Paiement"
                        value={employee.modePaiement}
                        name="modePaiement"
                        type="select"
                        options={PAYMENT_MODE_OPTIONS}
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<CreditCard className="h-4 w-4" />}
                        label="Nom Paiement"
                        value={employee.nomPaiement}
                        name="nomPaiement"
                        type="text"
                        onSave={handleFieldSave}
                      />
                    </div>
                  </InfoSection>

                  <InfoSection title="Informations Comptables">
                    <div className="space-y-0.5">
                      <EditableField
                        icon={<Building className="h-4 w-4" />}
                        label="Compte Comptable"
                        value={employee.compteCompt}
                        name="compteCompt"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<CreditCard className="h-4 w-4" />}
                        label="Grid"
                        value={employee.grid}
                        name="grid"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<CreditCard className="h-4 w-4" />}
                        label="Numéro CIMR"
                        value={employee.numeroCIMR}
                        name="numeroCIMR"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<FileText className="h-4 w-4" />}
                        label="Motif Modifications"
                        value={employee.mot}
                        name="mot"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<FileText className="h-4 w-4" />}
                        label="Relica"
                        value={employee.relicaC}
                        name="relicaC"
                        type="text"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<CalendarIcon className="h-4 w-4" />}
                        label="Date Relica"
                        value={employee.dateRelica}
                        name="dateRelica"
                        type="date"
                        onSave={handleFieldSave}
                      />
                      <EditableField
                        icon={<FileText className="h-4 w-4" />}
                        label="COS"
                        value={employee.cos}
                        name="cos"
                        type="text"
                        onSave={handleFieldSave}
                      />
                    </div>
                  </InfoSection>
                </TabsContent>

                <TabsContent value="benefits">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Congés">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Clock className="h-4 w-4" />}
                          label="Congés Payés"
                          value={employee.congesPayes}
                          name="congesPayes"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Clock className="h-4 w-4" />}
                          label="Congés Maladie"
                          value={employee.congesMaladie}
                          name="congesMaladie"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        {/* Only show Congés Maternité for female employees */}
                        {employee.genre === 'FEMALE' && (
                          <EditableField
                            icon={<Clock className="h-4 w-4" />}
                            label="Congés Maternité"
                            value={employee.congesMaternite}
                            name="congesMaternite"
                            type="number"
                            onSave={handleFieldSave}
                            className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                          />
                        )}
                        <EditableField
                          icon={<Clock className="h-4 w-4" />}
                          label="Droit Congé"
                          value={employee.droitConge}
                          name="droitConge"
                          type="number"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Logement">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Home className="h-4 w-4" />}
                          label="Est Locataire"
                          value={employee.estLocataire}
                          name="estLocataire"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<Home className="h-4 w-4" />}
                          label="Possède Appartement"
                          value={employee.possedeAppartement}
                          name="possedeAppartement"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Transport">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Truck className="h-4 w-4" />}
                          label="Utilise Transport"
                          value={employee.utiliseTransport}
                          name="utiliseTransport"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        {/* Only show transport details if employee uses transport */}
                        {employee.utiliseTransport && (
                          <>
                            <EditableField
                              icon={<Truck className="h-4 w-4" />}
                              label="Type de Transport"
                              value={employee.typeTransport}
                              name="typeTransport"
                              type="select"
                              options={TRANSPORT_OPTIONS}
                              onSave={handleFieldSave}
                              className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                            />
                            <EditableField
                              icon={<Map className="h-4 w-4" />}
                              label="Distance Domicile-Travail"
                              value={employee.distanceDomicileTravail}
                              name="distanceDomicileTravail"
                              type="number"
                              onSave={handleFieldSave}
                              className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                            />
                          </>
                        )}
                      </div>
                    </InfoSection>
                  </div>
                </TabsContent>

                <TabsContent value="config">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Configurations">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config IGR"
                          value={employee.confIGR}
                          name="confIGR"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config CNSS"
                          value={employee.confCNSS}
                          name="confCNSS"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config Mutuelle"
                          value={employee.confMutuel}
                          name="confMutuel"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config AT"
                          value={employee.confAT}
                          name="confAT"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config BourB"
                          value={employee.confBourB}
                          name="confBourB"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config ALLFAM"
                          // @ts-ignore
                          value={employee.confALLFAM}
                          name="confALLFAM"
                          type="text"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config CIMR"
                          value={employee.confCIMR}
                          name="confCIMR"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Config COS"
                          value={employee.confCos}
                          name="confCos"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                        <EditableField
                          icon={<FileText className="h-4 w-4" />}
                          label="Annulation Ancienneté"
                          value={employee.anulAnc}
                          name="anulAnc"
                          type="boolean"
                          onSave={handleFieldSave}
                          className="focus-within:ring-2 focus-within:ring-primary/20 rounded-md"
                        />
                      </div>
                    </InfoSection>
                  </div>
                </TabsContent>

                <TabsContent value="attendance">
                  <div className="space-y-0.5">
                    <AttendancePage employeeId={employee.id} />
                    <AttendanceHistoryBar
                      employeeId={employee.id}
                      employeeName={`${employee.prenom} ${employee.nom}`}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="education">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Formation et Éducation">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<GraduationCap className="h-4 w-4" />}
                          label="Niveau d'Éducation"
                          value={employee.niveauEducation}
                          name="niveauEducation"
                          type="select"
                          options={EDUCATION_LEVEL_OPTIONS}
                          onSave={handleFieldSave}
                        />
                        <EditableField
                          icon={<GraduationCap className="h-4 w-4" />}
                          label="Diplômes"
                          value={employee.diplomes?.join(", ")}
                          name="diplomes"
                          type="textarea"
                          onSave={handleFieldSave}
                        />
                        <EditableField
                          icon={<Languages className="h-4 w-4" />}
                          label="Langues Parlées"
                          value={employee.languesParlees?.join(", ")}
                          name="languesParlees"
                          type="textarea"
                          onSave={handleFieldSave}
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Formations Continues">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<GraduationCap className="h-4 w-4" />}
                          label="Formations Continues"
                          value={employee.formationsContinues?.join(", ")}
                          name="formationsContinues"
                          type="textarea"
                          onSave={handleFieldSave}
                        />
                      </div>
                    </InfoSection>
                  </div>
                </TabsContent>

                <TabsContent value="medical">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <InfoSection title="Informations Médicales">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<Droplet className="h-4 w-4" />}
                          label="Groupe Sanguin"
                          value={employee.groupeSanguin}
                          name="groupeSanguin"
                          type="select"
                          options={BLOOD_GROUP_OPTIONS}
                          onSave={handleFieldSave}
                        />
                        <EditableField
                          icon={<VenetianMaskIcon className="h-4 w-4" />}
                          label="Numéro Mutuelle"
                          value={employee.mutuelle}
                          name="mutuelle"
                          type="text"
                          onSave={handleFieldSave}
                        />
                      </div>
                    </InfoSection>

                    <InfoSection title="Contact d'Urgence">
                      <div className="space-y-0.5">
                        <EditableField
                          icon={<AlertCircle className="h-4 w-4" />}
                          label="Contact d'Urgence"
                          value={employee.contactUrgence}
                          name="contactUrgence"
                          type="text"
                          onSave={handleFieldSave}
                        />
                        <EditableField
                          icon={<Phone className="h-4 w-4" />}
                          label="Téléphone d'Urgence"
                          value={employee.telephoneUrgence}
                          name="telephoneUrgence"
                          type="tel"
                          onSave={handleFieldSave}
                        />
                      </div>
                    </InfoSection>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
    </>
  );
};

export default ClientEmployee;