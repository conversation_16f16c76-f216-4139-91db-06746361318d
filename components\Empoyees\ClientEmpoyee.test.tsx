import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ClientEmployee from './ClientEmpoyee';
import { Employee } from '@/types';
import { updateEmployee } from '@/actions/actions';
import { toast } from 'sonner';

// Mock the dependencies
jest.mock('@/actions/actions', () => ({
  updateEmployee: jest.fn(),
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock Recharts components
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  BarChart: () => <div data-testid="mock-bar-chart" />,
  Bar: () => <div />,
  XAxis: () => <div />,
  YAxis: () => <div />,
  Tooltip: () => <div />,
  Legend: () => <div />,
  LineChart: () => <div data-testid="mock-line-chart" />,
  Line: () => <div />,
}));

// Mock Calendar component
jest.mock('../ui/calendar', () => ({
  Calendar: () => <div data-testid="mock-calendar" />,
}));

// Mock AttendanceHistoryBar component
jest.mock('./AttendanceHistoryBar', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-attendance-history-bar" />,
}));

// Mock CreateAttendance component
jest.mock('./CreateAttendance', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-create-attendance" />,
}));

// Sample employee data
const mockEmployee: Employee = {
  id: '1',
  prenom: 'John',
  nom: 'Doe',
  email: '<EMAIL>',
  telephone: '1234567890',
  dateNaissance: new Date('1990-01-01'),
  genre: 'Homme',
  situationFamiliale: 'Non marié(e)',
  nombreEnfants: 0,
  adresse: '123 Main St',
  departement: 'IT',
  poste: 'Developer',
  dateDebut: new Date('2020-01-01'),
  salaire: 50000,
  typeEmploi: 'Full-time',
  contactUrgence: 'Jane Doe',
  telephoneUrgence: '0987654321',
  competences: ['JavaScript', 'React'],
  cin: 'AB123456',
  cnss: '123456789',
  fonction: 'Software Engineer',
  categorie: 'Cadre',
  matricule: 'EMP001',
};

describe('ClientEmployee', () => {
  it('renders employee information correctly', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    expect(screen.getByText(`${mockEmployee.prenom} ${mockEmployee.nom}`)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.poste)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.departement)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.email)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.telephone)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.adresse)).toBeInTheDocument();
  });

  it('opens edit dialog when edit button is clicked', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    const editButton = screen.getByText('Edit Profile');
    fireEvent.click(editButton);

    expect(screen.getByText('Update Employee Profile')).toBeInTheDocument();
  });

  it('updates employee information when form is submitted', async () => {
    (updateEmployee as jest.Mock).mockResolvedValue({ success: true });

    render(<ClientEmployee employee={mockEmployee} />);
    
    const editButton = screen.getByText('Edit Profile');
    fireEvent.click(editButton);

    const prenomInput = screen.getByPlaceholderText('First Name');
    fireEvent.change(prenomInput, { target: { value: 'Jane' } });

    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(updateEmployee).toHaveBeenCalledWith(expect.objectContaining({ prenom: 'Jane' }));
      expect(toast.success).toHaveBeenCalledWith('Profile updated successfully');
    });
  });

  it('displays error toast when update fails', async () => {
    (updateEmployee as jest.Mock).mockResolvedValue({ success: false, error: 'Update failed' });

    render(<ClientEmployee employee={mockEmployee} />);
    
    const editButton = screen.getByText('Edit Profile');
    fireEvent.click(editButton);

    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Update failed');
    });
  });

  it('renders skills correctly', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    mockEmployee.competences.forEach(skill => {
      expect(screen.getByText((content, element) => {
        return element?.textContent?.includes(skill) ?? false;
      })).toBeInTheDocument();
    });
  });

  it('renders emergency contact information', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    expect(screen.getByText((content, element) => {
      return element?.textContent?.includes(mockEmployee.contactUrgence) ?? false;
    })).toBeInTheDocument();
    expect(screen.getByText((content, element) => {
      return element?.textContent?.includes(mockEmployee.telephoneUrgence) ?? false;
    })).toBeInTheDocument();
  });

  it('renders attendance history bar', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    expect(screen.getByTestId('mock-attendance-history-bar')).toBeInTheDocument();
  });

  it('renders employment information correctly', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    const employmentTab = screen.getByRole('tab', { name: /employment/i });
    fireEvent.click(employmentTab);

    expect(screen.getByText(mockEmployee.poste)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.departement)).toBeInTheDocument();
    expect(screen.getByText(new RegExp(mockEmployee.salaire.toString()))).toBeInTheDocument();
  });

  it('renders personal information correctly', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    const personalTab = screen.getByRole('tab', { name: /personal/i });
    fireEvent.click(personalTab);

    expect(screen.getByText(mockEmployee.email)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.telephone)).toBeInTheDocument();
    expect(screen.getByText(mockEmployee.adresse)).toBeInTheDocument();
  });

  it('renders skills tab correctly', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    const skillsTab = screen.getByRole('tab', { name: /skills/i });
    fireEvent.click(skillsTab);

    mockEmployee.competences.forEach(skill => {
      expect(screen.getByText((content, element) => {
        return element?.textContent?.includes(skill) ?? false;
      })).toBeInTheDocument();
    });
  });

  it('renders emergency tab correctly', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    const emergencyTab = screen.getByRole('tab', { name: /emergency/i });
    fireEvent.click(emergencyTab);

    expect(screen.getByText((content, element) => {
      return element?.textContent?.includes(mockEmployee.contactUrgence) ?? false;
    })).toBeInTheDocument();
    expect(screen.getByText((content, element) => {
      return element?.textContent?.includes(mockEmployee.telephoneUrgence) ?? false;
    })).toBeInTheDocument();
  });

  it('renders mocked charts', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    expect(screen.getByTestId('mock-bar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('mock-line-chart')).toBeInTheDocument();
  });

  it('renders mocked calendar', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    expect(screen.getByTestId('mock-calendar')).toBeInTheDocument();
  });

  it('renders mocked create attendance component', () => {
    render(<ClientEmployee employee={mockEmployee} />);
    
    expect(screen.getByTestId('mock-create-attendance')).toBeInTheDocument();
  });
});