"use client"

import React, { useState, useCallback } from 'react';
import { Attendance } from '@prisma/client';
import { getAttendanceByEmployeeId, updateAttendance, addAttendance, deleteAttendance } from '@/actions/actions';
import { toast } from 'sonner'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { PlusIcon, Pencil, Trash2, Clock, CheckCircle, XCircle, AlertCircle, CalendarIcon } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { QueryClient, QueryClientProvider, useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

const queryClient = new QueryClient();

interface AttendanceManagerProps {
  employeeId: string;
}

const AttendanceManager: React.FC<AttendanceManagerProps> = ({ employeeId }) => {
  const [currentAttendance, setCurrentAttendance] = useState<Attendance | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  const { data: attendances, isLoading, error } = useQuery<Attendance[], Error>({
    queryKey: ['attendances', employeeId],
    queryFn: () => getAttendanceByEmployeeId(employeeId),
    enabled: !!employeeId,
  });

  const addMutation = useMutation({
    mutationFn: async (newAttendance: Omit<Attendance, 'id'>) => {
      const promise = addAttendance(
        employeeId,
        newAttendance.date,
        newAttendance.status,
        newAttendance.checkInTime,
        newAttendance.checkOutTime,
        newAttendance.notes
      );
      return promise;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendances', employeeId] });
      toast.success('Attendance added successfully');
      setIsDialogOpen(false);
    },
    onError: (error) => {
      toast.error(`Failed to add attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  const updateMutation = useMutation({
    mutationFn: updateAttendance,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendances', employeeId] });
      toast.success('Attendance updated successfully');
      setIsDialogOpen(false);
    },
    onError: (error) => {
      toast.error(`Failed to update attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteAttendance,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendances', employeeId] });
      toast.success('Attendance record deleted successfully');
    },
    onError: (error) => {
      toast.error(`Failed to delete attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  const handleSave = async (updatedAttendance: Attendance) => {
    if (updatedAttendance.id) {
      updateMutation.mutate(updatedAttendance);
    } else {
      addMutation.mutate(updatedAttendance);
    }
  };

  const handleDelete = async (attendanceId: string) => {
    if (window.confirm('Are you sure you want to delete this attendance record?')) {
      deleteMutation.mutate(attendanceId);
    }
  };

  const openAddForm = () => {
    setCurrentAttendance({
      id: '',
      employeeId,
      date: new Date(),
      status: 'PRESENT',
      checkInTime: null,
      checkOutTime: null,
      notes: '',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    setIsDialogOpen(true);
  };

  const openEditForm = (attendance: Attendance) => {
    setCurrentAttendance(attendance);
    setIsDialogOpen(true);
  };

  const AttendanceForm = ({ attendance, onSave }: { attendance: Attendance, onSave: (attendance: Attendance) => Promise<void> }) => {
    const [formData, setFormData] = useState({
      ...attendance,
      date: attendance.date || new Date(),
      status: attendance.status || 'PRESENT'
    });

    const handleChange = (field: keyof Attendance, value: any) => {
      setFormData(prev => ({ ...prev, [field]: value }));
    };

    const DatePickerField = () => {
      const [date, setDate] = useState<Date | undefined>(formData.date || undefined);
    
      return (
        <div className="grid gap-2">
          <label className="text-sm font-medium">Attendance Date</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !date && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                onSelect={(newDate: Date | undefined) => {
                  setDate(newDate);
                  handleChange('date', newDate);
                }}
                disabled={(date) => {
                  const day = date.getDay();
                  return day === 0 || day === 6;
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      );
    };

    return (
      <form onSubmit={(e) => {
        e.preventDefault();
        if (!formData.date) {
          toast.error('Please select a date');
          return;
        }
        onSave(formData);
      }} 
      className="space-y-4"
      >
        <DatePickerField />

        <div className="grid gap-2">
          <label className="text-sm font-medium">Status</label>
          <Select
            value={formData.status}
            onValueChange={(value) => handleChange('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PRESENT">
                <div className="flex items-center">
                  <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                  Present
                </div>
              </SelectItem>
              <SelectItem value="ABSENT">
                <div className="flex items-center">
                  <XCircle className="mr-2 h-4 w-4 text-red-500" />
                  Absent
                </div>
              </SelectItem>
              <SelectItem value="LATE">
                <div className="flex items-center">
                  <AlertCircle className="mr-2 h-4 w-4 text-yellow-500" />
                  Late
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-2">
          <label className="text-sm font-medium">Check-in Time</label>
          <Input
            type="time"
            value={formData.checkInTime ? format(new Date(formData.checkInTime), 'HH:mm') : ''}
            onChange={(e) => {
              const [hours, minutes] = e.target.value.split(':').map(Number);
              const date = new Date();
              date.setHours(hours, minutes);
              handleChange('checkInTime', date);
            }}
          />
        </div>

        <div className="grid gap-2">
          <label className="text-sm font-medium">Check-out Time</label>
          <Input
            type="time"
            value={formData.checkOutTime ? format(new Date(formData.checkOutTime), 'HH:mm') : ''}
            onChange={(e) => {
              const [hours, minutes] = e.target.value.split(':').map(Number);
              const date = new Date();
              date.setHours(hours, minutes);
              handleChange('checkOutTime', date);
            }}
          />
        </div>

        <div className="grid gap-2">
          <label className="text-sm font-medium">Notes</label>
          <Input
            placeholder="Add any notes here..."
            value={formData.notes || ''}
            onChange={(e) => handleChange('notes', e.target.value)}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
            Cancel
          </Button>
          <Button type="submit">Save</Button>
        </div>
      </form>
    );
  };

  if (isLoading) return <div className="flex justify-center items-center h-screen">Loading...</div>;
  if (error) return <div className="text-red-500 text-center">Error: {error.message}</div>;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex justify-between items-center">
          Employee Attendance
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openAddForm} className="bg-blue-500 hover:bg-blue-600 text-white">
                <PlusIcon className="mr-2 h-4 w-4" /> Add Attendance
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{currentAttendance?.id ? 'Edit Attendance' : 'Add Attendance'}</DialogTitle>
              </DialogHeader>
              {currentAttendance && (
                <AttendanceForm 
                  key={currentAttendance.id || 'new'}
                  attendance={currentAttendance} 
                  onSave={handleSave} 
                />
              )}
            </DialogContent>
          </Dialog>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {attendances && attendances.length > 0 ? (
          <div className="w-full overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead><CalendarIcon className="inline-block mr-2" /> Date</TableHead>
                  <TableHead><AlertCircle className="inline-block mr-2" /> Status</TableHead>
                  <TableHead><Clock className="inline-block mr-2" /> Check-in</TableHead>
                  <TableHead><Clock className="inline-block mr-2" /> Check-out</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {attendances.map((attendance) => (
                  <TableRow key={attendance.id}>
                    <TableCell>{new Date(attendance.date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Badge
                        variant={attendance.status === 'PRESENT' ? 'default' : attendance.status === 'ABSENT' ? 'destructive' : 'secondary'}
                      >
                        {attendance.status === 'PRESENT' && <CheckCircle className="inline-block mr-2" />}
                        {attendance.status === 'ABSENT' && <XCircle className="inline-block mr-2" />}
                        {attendance.status === 'LATE' && <AlertCircle className="inline-block mr-2" />}
                        {attendance.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{attendance.checkInTime ? new Date(attendance.checkInTime).toLocaleTimeString() : 'N/A'}</TableCell>
                    <TableCell>{attendance.checkOutTime ? new Date(attendance.checkOutTime).toLocaleTimeString() : 'N/A'}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => openEditForm(attendance)}>
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm" 
                          onClick={() => handleDelete(attendance.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <p className="text-center text-gray-500">No attendance records found.</p>
        )}
      </CardContent>
    </Card>
  );
};

const WrappedAttendanceManager: React.FC<AttendanceManagerProps> = (props) => (
  <QueryClientProvider client={queryClient}>
    <AttendanceManager {...props} />
  </QueryClientProvider>
);

export default WrappedAttendanceManager;