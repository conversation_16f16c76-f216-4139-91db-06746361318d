'use client';

import React, { useState, useRef, useEffect } from 'react';
import { format } from "date-fns";
import { Calendar as CalendarIcon, Edit2, Check, X, VenetianMask, Home, Droplet, Truck, Map, User, Mail, Phone, Briefcase, Building2, Clock, Wallet, Percent, Building, CreditCard, Flag, FileText } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Label } from '../ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Calendar } from "../ui/calendar";
import { cn } from '@/lib/utils';
import { Employee } from '@/types';

interface EditableFieldProps {
  icon: React.ReactNode;
  label: string;
  value: string | number | boolean | Date | null | undefined;
  name: string;
  type?: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'textarea' | 'email' | 'tel';
  options?: { value: string; label: string }[];
  onSave: (name: string, value: any) => void;
  className?: string;
  condition?: boolean; // Optional condition to determine if field should be rendered
}

export const EditableField: React.FC<EditableFieldProps> = ({
  icon,
  label,
  value,
  name,
  type = 'text',
  options = [],
  onSave,
  className = '',
  condition = true // Default to true if not provided
}) => {
  // If condition is false, don't render the component
  if (condition === false) {
    return null;
  }
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState<typeof value>(value);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleSave = () => {
    onSave(name, tempValue);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempValue(value);
    setIsEditing(false);
  };

  const renderEditControl = () => {
    switch (type) {
      case 'select':
        return (
          <Select
            value={String(tempValue)}
            onValueChange={(value) => setTempValue(value)}
          >
            <SelectTrigger className="h-7 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {options.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case 'boolean':
        return (
          <Select
            value={String(tempValue)}
            onValueChange={(value) => setTempValue(value === 'true')}
          >
            <SelectTrigger className="h-7 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        );
      case 'textarea':
        return (
          <Textarea
            value={tempValue?.toString() || ''}
            onChange={(e) => setTempValue(e.target.value)}
            className="h-20 min-h-[80px]"
          />
        );
      case 'date':
        return (
          <DatePickerField
            label={label}
            date={tempValue instanceof Date ? tempValue : null}
            onSelect={(date) => setTempValue(date)}
          />
        );
      default:
        return (
          <Input
            ref={inputRef}
            type={type}
            value={tempValue?.toString() || ''}
            onChange={(e) => setTempValue(e.target.value)}
            className="h-7 text-xs"
          />
        );
    }
  };

  const formatDisplayValue = (val: typeof value): string => {
    if (val instanceof Date) {
      return format(val, "PPP");
    }
    if (typeof val === 'boolean') {
      return val ? 'Yes' : 'No';
    }
    return val?.toString() || 'Not set';
  };

  return (
    <div className={`group relative p-2 rounded-md transition-all duration-200 hover:bg-accent/5 ${className}`}>
      <div className="flex items-center gap-2">
        <div className="text-muted-foreground group-hover:text-primary transition-colors">
          {icon}
        </div>
        <div className="flex-grow min-w-0">
          <p className="text-xs font-medium text-muted-foreground mb-0.5">{label}</p>
          {isEditing ? (
            <div className="flex items-center gap-1">
              <div className="flex-grow">
                {renderEditControl()}
              </div>
              <div className="flex items-center gap-0.5">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleSave}
                  className="h-6 w-6 p-0 hover:text-primary hover:bg-primary/10"
                >
                  <Check className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCancel}
                  className="h-6 w-6 p-0 hover:text-destructive hover:bg-destructive/10"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <p className={`text-xs font-medium text-foreground ${className}`}>
                {formatDisplayValue(value)}
              </p>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setIsEditing(true)}
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-all hover:text-primary hover:bg-primary/10"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const DatePickerField: React.FC<{
  label: string;
  date: Date | null;
  onSelect: (date: Date | null) => void;
}> = ({ label, date, onSelect }) => {
  const [open, setOpen] = useState(false);

  const handleSelect = (day: Date | undefined) => {
    onSelect(day ?? null);
    setOpen(false);
  };

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-sm font-medium">{label}</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal h-7 text-xs",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 shrink-0" />
            {date ? format(date, "PPP") : "Pick a date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0"
          align="start"
          sideOffset={5}
          style={{ zIndex: 999 }}
        >
          <Calendar
            mode="single"
            selected={date ?? undefined}
            onSelect={handleSelect}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export const InfoSection: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="border border-border/20 rounded-lg p-3 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-colors">
    <h3 className="text-sm font-semibold mb-2 text-primary">
      {title}
    </h3>
    <div className="grid grid-cols-1 gap-2">{children}</div>
  </div>
);

interface SkillsSectionProps {
  employee: Employee;
  onSave: (name: string, value: any) => void;
}

export const SkillsSection: React.FC<SkillsSectionProps> = ({ employee, onSave }) => {
  return (
    <div className="flex flex-wrap gap-2">
      {employee.competences?.map((skill, index) => (
        <span
          key={index}
          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary-foreground"
        >
          {skill}
        </span>
      ))}
    </div>
  );
};

// Add these department options
export const DEPARTMENT_OPTIONS = [
  { value: "rh", label: "Ressources Humaines" },
  { value: "it", label: "Technologie de l'Information" },
  { value: "finance", label: "Finance" },
  { value: "marketing", label: "Marketing" },
  { value: "operations", label: "Opérations" }
];

// Add transport options
export const TRANSPORT_OPTIONS = [
  { value: "PUBLIC", label: "Public" },
  { value: "PRIVATE", label: "Privé" },
  { value: "COMPANY", label: "Entreprise" }
];

// Add situation familiale options
export const SITUATION_FAMILIALE_OPTIONS = [
  { value: "celibataire", label: "Célibataire" },
  { value: "marie", label: "Marié(e)" },
  { value: "divorce", label: "Divorcé(e)" },
  { value: "veuf", label: "Veuf/Veuve" }
];

// Add employment type options
export const EMPLOYMENT_TYPE_OPTIONS = [
  { value: "FULL_TIME", label: "Temps Plein" },
  { value: "PART_TIME", label: "Temps Partiel" },
  { value: "CONTRACT", label: "Contractuel" },
  { value: "TEMP", label: "Temporaire" },
  { value: "INTERN", label: "Stagiaire" }
];

// Add education level options
export const EDUCATION_LEVEL_OPTIONS = [
  { value: "primaire", label: "Primaire" },
  { value: "college", label: "Collège" },
  { value: "lycee", label: "Lycée" },
  { value: "bac", label: "Baccalauréat" },
  { value: "bac+2", label: "Bac+2" },
  { value: "bac+3", label: "Bac+3/Licence" },
  { value: "bac+5", label: "Bac+5/Master" },
  { value: "doctorat", label: "Doctorat" }
];

// Add blood group options
export const BLOOD_GROUP_OPTIONS = [
  { value: "A+", label: "A+" },
  { value: "A-", label: "A-" },
  { value: "B+", label: "B+" },
  { value: "B-", label: "B-" },
  { value: "AB+", label: "AB+" },
  { value: "AB-", label: "AB-" },
  { value: "O+", label: "O+" },
  { value: "O-", label: "O-" }
];

// Add contract type options
export const CONTRACT_TYPE_OPTIONS = [
  { value: "cdi", label: "CDI" },
  { value: "cdd", label: "CDD" },
  { value: "stage", label: "Stage" },
  { value: "freelance", label: "Freelance" },
  { value: "occasionnel", label: "Occasionnel" }
];

// Add payment mode options
export const PAYMENT_MODE_OPTIONS = [
  { value: "virement", label: "Virement Bancaire" },
  { value: "cheque", label: "Chèque" },
  { value: "especes", label: "Espèces" }
];

// Add new options arrays
export const MODE_TRAVAIL_OPTIONS = [
  { value: "presentiel", label: "Présentiel" },
  { value: "hybride", label: "Hybride" },
  { value: "remote", label: "À distance" }
];

export const NATIONALITE_OPTIONS = [
  { value: "marocaine", label: "Marocaine" },
  { value: "francaise", label: "Française" },
  { value: "autre", label: "Autre" }
];

export const ZONE_RESIDENCE_OPTIONS = [
  { value: "urbaine", label: "Zone Urbaine" },
  { value: "rurale", label: "Zone Rurale" },
  { value: "suburbaine", label: "Zone Suburbaine" }
];

export const SOCIETE_OPTIONS = [
  { value: "principale", label: "Société Principale" },
  { value: "filiale", label: "Filiale" },
  { value: "autre", label: "Autre" }
];

// Add gender options
export const GENDER_OPTIONS = [
  { value: "MALE", label: "Homme" },
  { value: "FEMALE", label: "Femme" },
  { value: "OTHER", label: "Autre" }
];

// Add new EditableField components for all schema fields
export const EditableEmployeeFields: React.FC<{employee: Employee, onSave: (name: string, value: any) => void}> =
  ({employee, onSave}) => {
  return (
    <>
      {/* Basic Information Section */}
      <InfoSection title="Informations de Base">
        <EditableField
          icon={<User className="h-4 w-4"/>}
          label="Prénom"
          value={employee.prenom}
          name="prenom"
          onSave={onSave}
        />

        <EditableField
          icon={<User className="h-4 w-4"/>}
          label="Nom"
          value={employee.nom}
          name="nom"
          onSave={onSave}
        />

        <EditableField
          icon={<Mail className="h-4 w-4"/>}
          label="Email"
          value={employee.email}
          name="email"
          type="email"
          onSave={onSave}
        />

        <EditableField
          icon={<Phone className="h-4 w-4"/>}
          label="Téléphone"
          value={employee.telephone}
          name="telephone"
          type="tel"
          onSave={onSave}
        />
      </InfoSection>

      {/* Employment Details Section */}
      <InfoSection title="Détails Professionnels">
        <EditableField
          icon={<Briefcase className="h-4 w-4"/>}
          label="Poste"
          value={employee.poste}
          name="poste"
          onSave={onSave}
        />

        <EditableField
          icon={<Building2 className="h-4 w-4"/>}
          label="Département"
          value={employee.departement}
          name="departement"
          type="select"
          options={DEPARTMENT_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<Clock className="h-4 w-4"/>}
          label="Type d'Emploi"
          value={employee.typeEmploi}
          name="typeEmploi"
          type="select"
          options={EMPLOYMENT_TYPE_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<FileText className="h-4 w-4"/>}
          label="Type de Contrat"
          value={employee.contratType}
          name="contratType"
          type="select"
          options={CONTRACT_TYPE_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<FileText className="h-4 w-4"/>}
          label="Matricule"
          value={employee.matricule}
          name="matricule"
          onSave={onSave}
        />

        <EditableField
          icon={<Calendar className="h-4 w-4"/>}
          label="Date de Début"
          value={employee.dateDebut}
          name="dateDebut"
          type="date"
          onSave={onSave}
        />

        <EditableField
          icon={<Calendar className="h-4 w-4"/>}
          label="Date de Fin de Contrat"
          value={employee.dateFinContrat}
          name="dateFinContrat"
          type="date"
          onSave={onSave}
          condition={employee.contratType === 'cdd' ||
                    employee.contratType === 'stage' ||
                    employee.contratType === 'freelance' ||
                    employee.contratType === 'occasionnel'}
        />

        <EditableField
          icon={<Clock className="h-4 w-4"/>}
          label="Période d'Essai (jours)"
          value={employee.periodeEssai}
          name="periodeEssai"
          type="number"
          onSave={onSave}
        />
      </InfoSection>

      {/* Financial Information Section */}
      <InfoSection title="Informations Financières">
        <EditableField
          icon={<Wallet className="h-4 w-4"/>}
          label="Salaire de Base"
          value={employee.salaire}
          name="salaire"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Wallet className="h-4 w-4"/>}
          label="Salaire Brut"
          value={employee.salaireBrut}
          name="salaireBrut"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Wallet className="h-4 w-4"/>}
          label="Salaire Net"
          value={employee.salaireNet}
          name="salaireNet"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Wallet className="h-4 w-4"/>}
          label="Échelon Salaire"
          value={employee.echelonSalaire}
          name="echelonSalaire"
          onSave={onSave}
        />

        <EditableField
          icon={<Percent className="h-4 w-4"/>}
          label="Taux IR"
          value={employee.tauxIR}
          name="tauxIR"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Percent className="h-4 w-4"/>}
          label="Taux CNSS"
          value={employee.tauxCNSS}
          name="tauxCNSS"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Percent className="h-4 w-4"/>}
          label="Taux AMO"
          value={employee.tauxAMO}
          name="tauxAMO"
          type="number"
          onSave={onSave}
        />
      </InfoSection>

      {/* Banking Information Section */}
      <InfoSection title="Informations Bancaires">
        <EditableField
          icon={<Building className="h-4 w-4"/>}
          label="Banque"
          value={employee.banque}
          name="banque"
          onSave={onSave}
        />

        <EditableField
          icon={<Building className="h-4 w-4"/>}
          label="Agence Bancaire"
          value={employee.agenceBancaire}
          name="agenceBancaire"
          onSave={onSave}
        />

        <EditableField
          icon={<CreditCard className="h-4 w-4"/>}
          label="RIB"
          value={employee.rib}
          name="rib"
          onSave={onSave}
        />

        <EditableField
          icon={<CreditCard className="h-4 w-4"/>}
          label="Mode de Paiement"
          value={employee.modePaiement}
          name="modePaiement"
          type="select"
          options={PAYMENT_MODE_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<CreditCard className="h-4 w-4"/>}
          label="Nom Paiement"
          value={employee.nomPaiement}
          name="nomPaiement"
          onSave={onSave}
        />
      </InfoSection>

      {/* Accounting Information Section */}
      <InfoSection title="Informations Comptables">
        <EditableField
          icon={<Building className="h-4 w-4"/>}
          label="Compte Comptable"
          value={employee.compteCompt}
          name="compteCompt"
          onSave={onSave}
        />

        <EditableField
          icon={<CreditCard className="h-4 w-4"/>}
          label="Grid"
          value={employee.grid}
          name="grid"
          onSave={onSave}
        />

        <EditableField
          icon={<CreditCard className="h-4 w-4"/>}
          label="Numéro CIMR"
          value={employee.numeroCIMR}
          name="numeroCIMR"
          onSave={onSave}
        />

        <EditableField
          icon={<FileText className="h-4 w-4"/>}
          label="Motif Modifications"
          value={employee.mot}
          name="mot"
          onSave={onSave}
        />

        <EditableField
          icon={<FileText className="h-4 w-4"/>}
          label="Relica"
          value={employee.relicaC}
          name="relicaC"
          onSave={onSave}
        />

        <EditableField
          icon={<Calendar className="h-4 w-4"/>}
          label="Date Relica"
          value={employee.dateRelica}
          name="dateRelica"
          type="date"
          onSave={onSave}
        />

        <EditableField
          icon={<FileText className="h-4 w-4"/>}
          label="COS"
          value={employee.cos}
          name="cos"
          onSave={onSave}
        />
      </InfoSection>

      {/* Personal Information Section */}
      <InfoSection title="Informations Personnelles">
        <EditableField
          icon={<User className="h-4 w-4"/>}
          label="Date de Naissance"
          value={employee.dateNaissance}
          name="dateNaissance"
          type="date"
          onSave={onSave}
        />

        <EditableField
          icon={<User className="h-4 w-4"/>}
          label="Genre"
          value={employee.genre}
          name="genre"
          type="select"
          options={GENDER_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<Flag className="h-4 w-4"/>}
          label="Nationalité"
          value={employee.nationalite}
          name="nationalite"
          type="select"
          options={NATIONALITE_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<VenetianMask />}
          label="CIN"
          value={employee.cin}
          name="cin"
          onSave={onSave}
        />

        <EditableField
          icon={<Home />}
          label="Adresse"
          value={employee.adresse}
          name="adresse"
          onSave={onSave}
        />

        <EditableField
          icon={<Home />}
          label="Zone de Résidence"
          value={employee.zoneResidence}
          name="zoneResidence"
          type="select"
          options={ZONE_RESIDENCE_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<User />}
          label="Situation Familiale"
          value={employee.situationFamiliale}
          name="situationFamiliale"
          type="select"
          options={SITUATION_FAMILIALE_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<User />}
          label="Nombre d'Enfants"
          value={employee.nombreEnfants}
          name="nombreEnfants"
          type="number"
          onSave={onSave}
          condition={employee.situationFamiliale !== 'celibataire'}
        />
      </InfoSection>

      {/* Medical Information Section */}
      <InfoSection title="Informations Médicales">
        <EditableField
          icon={<Droplet />}
          label="Groupe Sanguin"
          value={employee.groupeSanguin}
          name="groupeSanguin"
          type="select"
          options={BLOOD_GROUP_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<VenetianMask />}
          label="Numéro Mutuelle"
          value={employee.mutuelle}
          name="mutuelle"
          onSave={onSave}
        />

        <EditableField
          icon={<User />}
          label="Contact d'Urgence"
          value={employee.contactUrgence}
          name="contactUrgence"
          onSave={onSave}
        />

        <EditableField
          icon={<Phone />}
          label="Téléphone d'Urgence"
          value={employee.telephoneUrgence}
          name="telephoneUrgence"
          type="tel"
          onSave={onSave}
        />
      </InfoSection>

      {/* Housing and Transport Section */}
      <InfoSection title="Logement et Transport">
        <EditableField
          icon={<Home />}
          label="Est Locataire"
          value={employee.estLocataire}
          name="estLocataire"
          type="boolean"
          onSave={onSave}
        />

        <EditableField
          icon={<Home />}
          label="Possède Appartement"
          value={employee.possedeAppartement}
          name="possedeAppartement"
          type="boolean"
          onSave={onSave}
        />

        <EditableField
          icon={<Truck />}
          label="Utilise Transport"
          value={employee.utiliseTransport}
          name="utiliseTransport"
          type="boolean"
          onSave={onSave}
        />

        <EditableField
          icon={<Truck />}
          label="Type de Transport"
          value={employee.typeTransport}
          name="typeTransport"
          type="select"
          options={TRANSPORT_OPTIONS}
          onSave={onSave}
          condition={employee.utiliseTransport === true}
        />

        <EditableField
          icon={<Map />}
          label="Distance Domicile-Travail (km)"
          value={employee.distanceDomicileTravail}
          name="distanceDomicileTravail"
          type="number"
          onSave={onSave}
          condition={employee.utiliseTransport === true}
        />
      </InfoSection>

      {/* Benefits Section */}
      <InfoSection title="Avantages et Congés">
        <EditableField
          icon={<Clock />}
          label="Congés Payés"
          value={employee.congesPayes}
          name="congesPayes"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Clock />}
          label="Congés Maladie"
          value={employee.congesMaladie}
          name="congesMaladie"
          type="number"
          onSave={onSave}
        />

        <EditableField
          icon={<Clock />}
          label="Congés Maternité"
          value={employee.congesMaternite}
          name="congesMaternite"
          type="number"
          onSave={onSave}
          condition={employee.genre === 'FEMALE'}
        />

        <EditableField
          icon={<Clock />}
          label="Droit Congé"
          value={employee.droitConge}
          name="droitConge"
          type="number"
          onSave={onSave}
        />
      </InfoSection>

      {/* Mode de Travail Section */}
      <InfoSection title="Mode de Travail">
        <EditableField
          icon={<Briefcase />}
          label="Mode de Travail"
          value={employee.modeTravaill}
          name="modeTravaill"
          type="select"
          options={MODE_TRAVAIL_OPTIONS}
          onSave={onSave}
        />

        <EditableField
          icon={<Calendar />}
          label="Date de Départ"
          value={employee.dDepart}
          name="dDepart"
          type="date"
          onSave={onSave}
        />
      </InfoSection>

      {/* Additional fields can be added following the same pattern */}
    </>
  );
};