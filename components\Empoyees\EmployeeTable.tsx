"use client"

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, ChevronLeft, ChevronRight, MoreHorizontal, Download, Upload, Database, X, Trash2 } from "lucide-react";

import Link from "next/link";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { InputWithPrefix } from "@/components/ui/input-with-prefix";
import { <PERSON>rollA<PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState, useEffect } from "react"
import { createPortal } from "react-dom"
import { toast } from "sonner";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { deleteEmployees } from "@/actions/actions";
import { Loader2 } from "lucide-react";
import ErrorBoundary from "@/components/ErrorBoundary";
import { SaveEmployeesResult } from "@/app/hr/employee/actions";
import HeaderMappingDialog from "@/components/Empoyees/HeaderMappingDialog";

// Using SaveEmployeesResult imported from actions.ts

interface DataTableDemoProps {
  data: Employee[];
  saveEmployees: (data: any[], mappedFields: Record<string, string>) => Promise<SaveEmployeesResult>;
}

export type Employee = {
  id: string;
  prenom: string;
  nom: string;
  email: string;
  telephone: string;
  dateNaissance: Date;
  genre: string;
  adresse: string;
  departement: string;
  poste: string;
  dateDebut: Date;
  salaire: number | string;
  typeEmploi: string;
  contactUrgence: string;
  telephoneUrgence: string;
  competences: string[];
  notesAdditionnelles?: string;
  cin: string;
  cnss: string;
  rib?: string;
  mutuelle?: string;
  nationalite: string;
  numeroPasseport?: string;
  permisTravaill?: string;
  dateExpirationPermis?: Date;
  statutMatrimonial?: string;
  niveauEducation?: string;
  diplomes?: string[];
  languesParlees?: string[];
  salaireBrut?: number;
  salaireNet?: number;
  tauxIR?: number;
  tauxCNSS?: number;
  tauxAMO?: number;
  agenceBancaire?: string;
  contratType?: string;
  dateFinContrat?: Date;
  periodeEssai?: number;
  congesPayes?: number;
  congesMaladie?: number;
  congesMaternite?: number;
  groupeSanguin?: string;
  situationFamiliale?: string;
  nombreEnfants?: number;
  banque?: string;
  modeTravaill?: string;
};

export const columns: ColumnDef<Employee>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "matricule",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Matricule
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("matricule") || "-"}</div>,
  },
  {
    accessorKey: "nom",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Last Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div className="capitalize">{row.getValue("nom")}</div>,
  },
  {
    accessorKey: "prenom",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        First Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div className="capitalize">{row.getValue("prenom")}</div>,
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Email
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div className="max-w-[200px]">
        <span className="truncate">{row.getValue("email")}</span>
      </div>
    ),
  },
  {
    accessorKey: "telephone",
    header: "Phone",
    cell: ({ row }) => <div>{row.getValue("telephone")}</div>,
  },
  {
    accessorKey: "poste",
    header: "Position",
    cell: ({ row }) => <div className="capitalize">{row.getValue("poste")}</div>,
  },
  {
    accessorKey: "departement",
    header: "Department",
    cell: ({ row }) => <div className="capitalize">{row.getValue("departement")}</div>,
  },
  {
    accessorKey: "dateDebut",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Start Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <div>{new Date(row.getValue("dateDebut")).toLocaleDateString()}</div>,
  },
  {
    accessorKey: "typeEmploi",
    header: "Employment Type",
    cell: ({ row }) => <div className="capitalize">{row.getValue("typeEmploi")}</div>,
  },
  {
    accessorKey: "salaire",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Salary
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const value = row.original.salaire;
      // console.log("Salary value:", value, typeof value);

      // Convert to number regardless of input type
      const salary = Number(value);

      if (isNaN(salary)) {
        // console.log("Invalid salary:", value);
        return <div>-</div>;
      }

      // Format with French-Moroccan locale
      return (
        <div className="font-medium">
          {new Intl.NumberFormat('fr-MA', {
            style: 'decimal',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          }).format(salary)} MAD
        </div>
      );
    },
  },
  {
    accessorKey: "dateNaissance",
    header: "Birth Date",
    cell: ({ row }) => <div>{new Date(row.getValue("dateNaissance")).toLocaleDateString()}</div>,
  },
  {
    accessorKey: "genre",
    header: "Gender",
    cell: ({ row }) => <div className="capitalize">{row.getValue("genre")}</div>,
  },
  {
    accessorKey: "nationalite",
    header: "Nationality",
    cell: ({ row }) => <div className="capitalize">{row.getValue("nationalite")}</div>,
  },
  {
    accessorKey: "statutMatrimonial",
    header: "Marital Status",
    cell: ({ row }) => <div className="capitalize">{row.getValue("statutMatrimonial")}</div>,
  },
  {
    accessorKey: "niveauEducation",
    header: "Education Level",
    cell: ({ row }) => <div className="capitalize">{row.getValue("niveauEducation")}</div>,
  },
  {
    accessorKey: "contratType",
    header: "Contract Type",
    cell: ({ row }) => <div className="capitalize">{row.getValue("contratType")}</div>,
  },
  {
    accessorKey: "dateFinContrat",
    header: "Contract End Date",
    cell: ({ row }) => {
      const rawDate = row.getValue("dateFinContrat");

      // Handle null/undefined
      if (!rawDate) return <div>-</div>;

      try {
        // Parse ISO string to Date
        const date = new Date(rawDate as string);

        // Validate date
        if (isNaN(date.getTime())) {
          return <div>-</div>;
        }

        // Format date using fr-MA locale
        return (
          <div>
            {date.toLocaleDateString('fr-MA', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </div>
        );
      } catch {
        return <div>-</div>;
      }
    },
  },
  {
    accessorKey: "cin",
    header: "CIN",
    cell: ({ row }) => <div>{row.getValue("cin")}</div>,
  },
  {
    accessorKey: "cnss",
    header: "CNSS",
    cell: ({ row }) => <div>{row.getValue("cnss")}</div>,
  },
  {
    accessorKey: "rib",
    header: "RIB",
    cell: ({ row }) => <div>{row.getValue("rib") || "-"}</div>,
  },
  {
    accessorKey: "banque",
    header: "Bank",
    cell: ({ row }) => <div className="capitalize">{row.getValue("banque") || "-"}</div>,
  },
  {
    accessorKey: "competences",
    header: "Skills",
    cell: ({ row }) => {
      const skills = row.getValue("competences") as string[];
      return (
        <div className="flex flex-wrap gap-1">
          {(skills || []).slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ring-1 ring-inset"
              title={skills.join(', ')}
            >
              {skill}
            </span>
          ))}
          {skills?.length > 3 && (
            <span className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium">
              +{skills.length - 3}
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "languesParlees",
    header: "Languages",
    cell: ({ row }) => {
      const languages = row.getValue("languesParlees") as string[];
      return languages ? (
        <div className="flex flex-wrap gap-1">
          {(languages || []).slice(0, 3).map((lang, index) => (
            <span
              key={index}
              className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ring-1 ring-inset"
              title={languages.join(', ')}
            >
              {lang}
            </span>
          ))}
          {languages?.length > 3 && (
            <span className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium">
              +{languages.length - 3}
            </span>
          )}
        </div>
      ) : null;
    },
  },
  {
    accessorKey: "societe",
    header: "Company",
    cell: ({ row }) => <div className="capitalize">{row.getValue("societe") || "-"}</div>,
  },
  {
    accessorKey: "service",
    header: "Service",
    cell: ({ row }) => <div className="capitalize">{row.getValue("service") || "-"}</div>,
  },
  {
    accessorKey: "modeTravaill",
    header: "Work Mode",
    cell: ({ row }) => <div className="capitalize">{row.getValue("modeTravaill") || "-"}</div>,
  },
  {
    accessorKey: "droitConge",
    header: "Leave Days",
    cell: ({ row }) => <div>{row.getValue("droitConge") || "0"}</div>,
  },
  {
    accessorKey: "situationFamiliale",
    header: "Marital Status",
    cell: ({ row }) => <div className="capitalize">{row.getValue("situationFamiliale") || "-"}</div>,
  },
  {
    accessorKey: "nombreEnfants",
    header: "Children",
    cell: ({ row }) => <div>{row.getValue("nombreEnfants") || "0"}</div>,
  },
  {
    accessorKey: "numeroCIMR",
    header: "CIMR Number",
    cell: ({ row }) => <div>{row.getValue("numeroCIMR") || "-"}</div>,
  },
  {
    accessorKey: "modePaiement",
    header: "Payment Method",
    cell: ({ row }) => <div className="capitalize">{row.getValue("modePaiement") || "-"}</div>,
  },
  {
    accessorKey: "grid",
    header: "Grid",
    cell: ({ row }) => <div>{row.getValue("grid") || "-"}</div>,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const employee = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(employee.email)}
            >
              Copy email
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Link href={`/hr/employee/${employee.id}`}>View details</Link>
            </DropdownMenuItem>
            <DropdownMenuItem>Edit employee</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

const exportToCSV = (data: Employee[]) => {
  // Define headers
  const headers = [
    'First Name',
    'Last Name',
    'Email',
    'Phone',
    'Birth Date',
    'Gender',
    'Address',
    'Department',
    'Position',
    'Start Date',
    'Contract End Date',
    'Salary',
    'Employment Type',
    'Emergency Contact',
    'Emergency Phone',
    'Skills',
    'Additional Notes',
    'CIN',
    'CNSS',
    'RIB',
    'Insurance',
    'Nationality',
    'Passport Number',
    'Work Permit',
    'Permit Expiry Date'
  ];

  // Convert data to CSV format
  const csvData = data.map(employee => [
    employee.prenom,
    employee.nom,
    employee.email,
    employee.telephone,
    employee.dateNaissance ? new Date(employee.dateNaissance).toLocaleDateString('fr-MA') : '',
    employee.genre,
    employee.adresse,
    employee.departement,
    employee.poste,
    employee.dateDebut ? new Date(employee.dateDebut).toLocaleDateString('fr-MA') : '',
    employee.dateFinContrat ? new Date(employee.dateFinContrat).toLocaleDateString('fr-MA') : '',
    employee.salaire ? Number(employee.salaire).toLocaleString('fr-MA') : '',
    employee.typeEmploi,
    employee.contactUrgence,
    employee.telephoneUrgence,
    Array.isArray(employee.competences) ? employee.competences.join(', ') : employee.competences,
    employee.notesAdditionnelles || '',
    employee.cin,
    employee.cnss,
    employee.rib || '',
    employee.mutuelle || '',
    employee.nationalite,
    employee.numeroPasseport || '',
    employee.permisTravaill || '',
    employee.dateExpirationPermis ? new Date(employee.dateExpirationPermis).toLocaleDateString('fr-MA') : ''
  ]);

  // Combine headers and data
  const csvContent = [
    headers.join(','),
    ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');

  // Create and trigger download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', 'employees.csv');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

type ImportModalProps = {
  onImport: (data: any[], mappedFields: Record<string, string>) => void;
}

// Add these types
// Using the DataSet type from HeaderMappingDialog.tsx
type DataSet = {
  id: string;
  name: string;
  headers: string[];
  data: any[];
};

// Modify the ImportModal component
const ImportModal = ({ onImport, data, loading }: ImportModalProps & { data: any[]; loading: boolean }) => {
  const [datasets, setDatasets] = useState<DataSet[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDataset, setSelectedDataset] = useState<DataSet | null>(null);
  const [open, setOpen] = useState(false);

  // Add useEffect to load datasets when modal opens
  useEffect(() => {
    if (open) {
      loadDatasets();
    }
  }, [open]);

  // Platform headers based on your Employee type
  const platformHeaders = [
    "prenom",
    "nom",
    "email",
    "telephone",
    "dateNaissance",
    "genre",
    "adresse",
    "departement",
    "poste",
    "dateDebut",
    "salaire",
    "typeEmploi",
    "contactUrgence",
    "telephoneUrgence",
    "competences",
    "notesAdditionnelles",
    "cin",
    "cnss",
    "rib",
    "mutuelle",
    "numeroCIMR",
    "nationalite",
    "numeroPasseport",
    "permisTravaill",
    "dateExpirationPermis",
    "statutMatrimonial",
    "niveauEducation",
    "diplomes",
    "languesParlees",
    "salaireBrut",
    "salaireNet",
    "tauxIR",
    "tauxCNSS",
    "tauxAMO",
    "banque",
    "agenceBancaire",
    "modePaiement",
    "echelonSalaire",
    "contratType",
    "dateFinContrat",
    "periodeEssai",
    "congesPayes",
    "congesMaladie",
    "congesMaternite",
    "estLocataire",
    "possedeAppartement",
    "utiliseTransport",
    "typeTransport",
    "distanceDomicileTravail",
    "zoneResidence",
    "groupeSanguin",
    "situationFamiliale",
    "formationsContinues",
    "nombreEnfants",
    "modeTravaill"
  ];

  const loadDatasets = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/datasets');
      const data = await response.json();
      if (data.success) {
        setDatasets(data.datasets);
      } else {
        toast.error("Failed to load datasets");
      }
    } catch (error) {
      console.error('Error loading datasets:', error);
      toast.error("Failed to load datasets");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileType = file.name.split('.').pop()?.toLowerCase();
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        let data: any[] = [];
        let headers: string[] = [];

        if (fileType === 'json') {
          // Handle JSON file
          data = JSON.parse(e.target?.result as string);
          if (!Array.isArray(data)) {
            toast.error("Invalid JSON format. Expected an array of employee records.");
            return;
          }

          if (data.length > 0) {
            headers = Object.keys(data[0]);
          }
        } else if (fileType === 'csv') {
          // Handle CSV file
          const csvText = e.target?.result as string;
          const lines = csvText.split('\n').filter(line => line.trim());

          if (lines.length === 0) {
            toast.error("CSV file is empty");
            return;
          }

          // Parse headers (first line)
          headers = lines[0].split(',').map(header => header.trim().replace(/^"|"$/g, ''));

          // Parse data rows
          for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(value => value.trim().replace(/^"|"$/g, ''));
            if (values.length === headers.length) {
              const record: any = {};
              headers.forEach((header, index) => {
                record[header] = values[index];
              });
              data.push(record);
            }
          }
        } else {
          toast.error("Unsupported file format. Please upload a JSON or CSV file.");
          return;
        }

        if (data.length === 0) {
          toast.error("No data found in the file");
          return;
        }

        // Create a dataset object and show the mapping dialog
        setSelectedDataset({
          id: 'uploaded-file',
          name: file.name,
          headers,
          data
        });
      } catch (error) {
        console.error('Error parsing file:', error);
        toast.error("Failed to parse file. Please check the file format.");
      }
    };

    reader.readAsText(file);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="ml-2" disabled={loading || isLoading}>
          {(loading || isLoading) ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Upload className="mr-2 h-4 w-4" />
          )}
          Import
        </Button>
      </DialogTrigger>
      {selectedDataset ? (
        // Render the full-screen HeaderMappingDialog when a dataset is selected
        // We use a portal to render it at the root level of the DOM to avoid z-index issues
        createPortal(
          <div
            className="fixed top-0 left-0 right-0 bottom-0 z-[9999999] w-screen h-screen"
            style={{
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              margin: 0,
              padding: 0,
              position: 'fixed',
              transform: 'none'
            }}>
            <HeaderMappingDialog
              dataset={selectedDataset}
              platformHeaders={platformHeaders}
              onConfirm={(mappedData, mappedFields) => {
                onImport(mappedData, mappedFields);
                setSelectedDataset(null);
                setOpen(false);
              }}
              onCancel={() => setSelectedDataset(null)}
              existingData={data}
            />
          </div>,
          document.body
        )
      ) : (
        // Render the regular dialog content when no dataset is selected
        <DialogContent className="sm:max-w-[825px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Import Data</DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="database" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="file">Local File</TabsTrigger>
              <TabsTrigger value="database">Saved Datasets</TabsTrigger>
            </TabsList>

            {/* File upload tab */}
            <TabsContent value="file" className="mt-4">
              <div className="flex flex-col gap-4">
                <Input
                  type="file"
                  accept=".json,.csv"
                  onChange={handleFileUpload}
                />
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Upload a JSON or CSV file containing your employee data
                  </p>
                  <p className="text-xs text-muted-foreground">
                    <strong>Note:</strong> For CSV files, the first row should contain column headers.
                    After uploading, you'll need to map these columns to the appropriate employee fields.
                  </p>
                  <p className="text-xs text-muted-foreground">
                    <strong>Required fields:</strong> At minimum, map fields for first name (prenom),
                    last name (nom), and email to successfully import employees.
                  </p>
                </div>
              </div>
            </TabsContent>

            {/* Database tab */}
            <TabsContent value="database" className="mt-4">
              <div className="flex flex-col gap-4">
                {isLoading ? (
                  <div className="text-center p-4">Loading...</div>
                ) : datasets.length > 0 ? (
                  <ScrollArea className="h-[300px] rounded-md border p-4">
                    {datasets.map((dataset) => (
                      <Button
                        key={dataset.id}
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => setSelectedDataset(dataset)}
                      >
                        <Database className="mr-2 h-4 w-4" />
                        {dataset.name} - {dataset.data.length} records
                      </Button>
                    ))}
                  </ScrollArea>
                ) : (
                  <p className="text-center text-muted-foreground">
                    No saved datasets found
                  </p>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      )}
    </Dialog>
  );
};

export function DataTableDemo({ data: initialData, saveEmployees }: DataTableDemoProps) {
  // State to track if component is mounted (client-side)
  const [isMounted, setIsMounted] = useState(false);

  // State to hold the employee data that can be updated without page refresh
  const [data, setData] = useState<Employee[]>(initialData);

  // Set isMounted to true when component mounts
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Update data when initialData changes (e.g., from parent component)
  useEffect(() => {
    setData(initialData);
  }, [initialData]);

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({
    telephone: false,
    dateNaissance: false,
    genre: false,
    adresse: false,
    salaire: false,
    contactUrgence: false,
    telephoneUrgence: false,
    competences: false,
    cin: false,
    cnss: false,
    rib: false,
    mutuelle: false,
    nationalite: false,
    numeroPasseport: false,
    permisTravaill: false,
    dateExpirationPermis: false,
    statutMatrimonial: false,
    niveauEducation: false,
    diplomes: false,
    languesParlees: false,
    salaireBrut: false,
    salaireNet: false,
    tauxIR: false,
    tauxCNSS: false,
    tauxAMO: false,
    agenceBancaire: false,
    dateFinContrat: false,
    periodeEssai: false,
    congesPayes: false,
    congesMaladie: false,
    congesMaternite: false,
    groupeSanguin: false,
    situationFamiliale: false,
    nombreEnfants: false,
    banque: false,
    modeTravaill: false,
  });
  const [rowSelection, setRowSelection] = React.useState({});
  const [loading, setLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getRowId: (row) => row.id,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // Update the useEffect for row selection
  useEffect(() => {
    // Get the selected row IDs from the table's row selection state
    const selectedIds = table.getSelectedRowModel().rows.map(row => row.original.id);
    setSelectedRows(selectedIds);
  }, [rowSelection, table]);

  const handleImport = async (importedData: any[], mappedFields: Record<string, string>) => {
    try {
      setLoading(true);
      const result = await saveEmployees(importedData, mappedFields);

      if (result.success) {
        toast.success(result.message || "Successfully imported employees");

        // If there were some errors but overall success, show a warning with details
        if (result.errorDetails && result.errorDetails.length > 0) {
          // Show first 3 errors and indicate if there are more
          const displayErrors = result.errorDetails.slice(0, 3);
          const remainingCount = result.errorDetails.length - 3;

          toast.warning(
            <div>
              <p>Some records had issues:</p>
              <ul className="list-disc pl-4 mt-2">
                {displayErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
                {remainingCount > 0 && (
                  <li className="text-sm">...and {remainingCount} more issues</li>
                )}
              </ul>
            </div>,
            { duration: 8000 }
          );
        }

        // Update the table data with the new employee list
        if (result.updatedEmployees) {
          setData(result.updatedEmployees);
        }
      } else {
        // Show detailed error message
        if (result.errorDetails && result.errorDetails.length > 0) {
          // Show first 3 errors and indicate if there are more
          const displayErrors = result.errorDetails.slice(0, 3);
          const remainingCount = result.errorDetails.length - 3;

          toast.error(
            <div>
              <p>{result.error}</p>
              <ul className="list-disc pl-4 mt-2">
                {displayErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
                {remainingCount > 0 && (
                  <li className="text-sm">...and {remainingCount} more issues</li>
                )}
              </ul>
            </div>,
            { duration: 8000 }
          );
        } else {
          toast.error(result.error || "Failed to import employees");
        }
      }
    } catch (error: unknown) {
      console.error('Error importing employees:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to import employees";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const result = await deleteEmployees(selectedRows);
      if (result.success) {
        toast.success(`Successfully deleted ${selectedRows.length} employees`);
        setShowDeleteDialog(false);
        setRowSelection({}); // Clear row selection
        setSelectedRows([]); // Clear selected rows

        // Update the table data with the new employee list
        if (result.updatedEmployees) {
          setData(result.updatedEmployees);
        }
      } else {
        toast.error(result.error || "Failed to delete employees");
      }
    } catch (error) {
      console.error('Error deleting employees:', error);
      toast.error("An error occurred while deleting employees");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Search and Filter Controls */}
      <div className="flex-none p-4 flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="flex items-center py-4">
          <Input
            placeholder="Filter by name..."
            value={(table.getColumn("nom")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("nom")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
        </div>
        <div className="flex items-center space-x-2 py-4">
          <ImportModal onImport={handleImport} data={data} loading={loading} />
          {table.getSelectedRowModel().rows.length > 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setShowDeleteDialog(true)}
              disabled={loading}
              className="gap-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete ({table.getSelectedRowModel().rows.length})
                </>
              )}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            className="ml-auto"
            onClick={() => exportToCSV(data)}
          >
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="h-8 border-dashed">
                <ChevronDown className="h-4 w-4" />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[150px]">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className="overflow-y-auto max-h-[300px]">
                {table
                  .getAllColumns()
                  .filter(
                    (column) =>
                      typeof column.accessorFn !== "undefined" && column.getCanHide()
                  )
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id.replace(/([A-Z])/g, ' $1').trim()}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete {selectedRows.length} selected employee(s) and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={loading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Table Container */}
      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full border rounded-md">
          <div className="min-w-[800px]">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      const content = header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          );
                      return (
                        <TableHead
                          key={header.id}
                          className="h-12 [&:has([role=checkbox])]:pl-3"
                          style={{
                            width: header.column.id === 'email' ? '250px' :
                                  header.column.id === 'competences' || header.column.id === 'languesParlees' ? '200px' :
                                  header.column.id === 'actions' ? '100px' : '150px'
                          }}
                        >
                          {content}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          style={{
                            width: cell.column.id === 'email' ? '250px' :
                                  cell.column.id === 'competences' || cell.column.id === 'languesParlees' ? '200px' :
                                  cell.column.id === 'actions' ? '100px' : '150px'
                          }}
                        >
                          <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* Pagination */}
      <div className="flex-none p-4 flex flex-col sm:flex-row items-center gap-2 sm:justify-end">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
            className="h-8 w-[70px] rounded-md border border-input bg-transparent"
          >
            {[10, 20, 30, 40, 50].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// Wrap the component with ErrorBoundary for better UX
export default function EmployeeTableWithErrorBoundary(props: DataTableDemoProps) {
  return (
    <ErrorBoundary>
      <DataTableDemo {...props} />
    </ErrorBoundary>
  );
}
