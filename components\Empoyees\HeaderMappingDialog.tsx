'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { InputWithPrefix } from "@/components/ui/input-with-prefix";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, X, Check, Search, ArrowRight, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";

export type DataSet = {
  id: string;
  name: string;
  headers: string[];
  data: any[];
};

export type HeaderMapping = {
  [key: string]: string;
};

interface HeaderMappingDialogProps {
  dataset: DataSet;
  platformHeaders: string[];
  onConfirm: (mappedData: any[], mappedFields: Record<string, string>) => void;
  onCancel: () => void;
  existingData?: any[];
}

const HeaderMappingDialog: React.FC<HeaderMappingDialogProps> = ({
  dataset,
  platformHeaders,
  onConfirm,
  onCancel,
  existingData = [],
}) => {
  const [headerMapping, setHeaderMapping] = useState<HeaderMapping>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAction, setSelectedAction] = useState<'append' | 'replace'>('append');
  const [selectedSourceHeader, setSelectedSourceHeader] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>("mapping");
  const [autoMappedCount, setAutoMappedCount] = useState(0);

  // Filter headers based on search
  const filteredPlatformHeaders = platformHeaders.filter(header =>
    header.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Auto-map headers on initial load
  useEffect(() => {
    const initialMapping: HeaderMapping = {};
    let mappedCount = 0;

    // Try to auto-map headers that match exactly or are similar
    dataset.headers.forEach(sourceHeader => {
      // Try exact match (case insensitive)
      const exactMatch = platformHeaders.find(
        targetHeader => targetHeader.toLowerCase() === sourceHeader.toLowerCase()
      );

      if (exactMatch) {
        initialMapping[sourceHeader] = exactMatch;
        mappedCount++;
        return;
      }

      // Try to match without spaces and special characters
      const normalizedSourceHeader = sourceHeader
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '');

      const similarMatch = platformHeaders.find(targetHeader => {
        const normalizedTargetHeader = targetHeader
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '');

        return normalizedTargetHeader === normalizedSourceHeader;
      });

      if (similarMatch) {
        initialMapping[sourceHeader] = similarMatch;
        mappedCount++;
      }
    });

    setHeaderMapping(initialMapping);
    setAutoMappedCount(mappedCount);

    // Set preview data
    if (dataset.data.length > 0) {
      setPreviewData(dataset.data.slice(0, 5));
    }
  }, [dataset, platformHeaders]);

  const handleMappingChange = (sourceHeader: string, targetHeader: string) => {
    setHeaderMapping(prev => ({
      ...prev,
      [sourceHeader]: targetHeader
    }));
  };

  const applyMapping = () => {
    // Create mappedFields object from headerMapping
    const mappedFields: Record<string, string> = {};

    // Convert the headerMapping to the format expected by saveEmployees
    Object.entries(headerMapping).forEach(([sourceHeader, targetHeader]) => {
      if (targetHeader) {
        mappedFields[sourceHeader] = targetHeader;
      }
    });

    const mappedData = dataset.data.map(item => {
      const newItem: any = {};
      Object.keys(headerMapping).forEach(sourceHeader => {
        const targetHeader = headerMapping[sourceHeader];
        if (targetHeader) {
          newItem[targetHeader] = item[sourceHeader];
        }
      });
      return newItem;
    });

    if (selectedAction === 'append') {
      onConfirm([...existingData, ...mappedData], mappedFields);
    } else {
      onConfirm(mappedData, mappedFields);
    }
  };

  const clearMapping = () => {
    setHeaderMapping({});
  };

  const getMappingCompletionPercentage = () => {
    const mappedHeaders = Object.keys(headerMapping).filter(key => !!headerMapping[key]).length;
    return Math.round((mappedHeaders / dataset.headers.length) * 100);
  };

  const getHeaderSuggestions = (sourceHeader: string) => {
    // Normalize the source header
    const normalizedSource = sourceHeader.toLowerCase().replace(/[^a-z0-9]/g, '');

    // Return suggestions sorted by relevance
    return platformHeaders
      .filter(header => {
        const normalizedTarget = header.toLowerCase().replace(/[^a-z0-9]/g, '');
        return normalizedTarget.includes(normalizedSource) ||
               normalizedSource.includes(normalizedTarget);
      })
      .slice(0, 5);
  };

  return (
    <>
      {/* Backdrop - Higher z-index than any sidebar */}
      <div className="fixed inset-0 bg-black/80 z-[9999999]" />

      {/* Dialog - Even higher z-index */}
      <div
        className="fixed inset-0 bg-background z-[10000000] flex flex-col overflow-hidden w-screen h-screen"
        style={{
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          margin: 0,
          padding: 0,
          position: 'fixed',
          transform: 'none'
        }}>
        {/* Header - Fixed at the top */}
        <div className="border-b p-4 flex items-center justify-between bg-card flex-shrink-0">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Map Headers</h2>
          <Badge variant="outline" className="ml-2">
            {dataset.name}
          </Badge>
          <Badge variant={getMappingCompletionPercentage() === 100 ? "default" : "destructive"}>
            {getMappingCompletionPercentage()}% Mapped
          </Badge>
          {autoMappedCount > 0 && (
            <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Auto-mapped: {autoMappedCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            onClick={applyMapping}
            disabled={Object.keys(headerMapping).filter(k => !!headerMapping[k]).length === 0}
            className="gap-2"
          >
            <Check className="h-4 w-4" />
            Apply Mapping
          </Button>
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="border-b px-4">
            <TabsList className="w-full justify-start h-12">
              <TabsTrigger value="mapping" className="data-[state=active]:bg-background">
                Field Mapping
              </TabsTrigger>
              <TabsTrigger value="preview" className="data-[state=active]:bg-background">
                Data Preview
              </TabsTrigger>
              <TabsTrigger value="settings" className="data-[state=active]:bg-background">
                Import Settings
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="mapping" className="flex-1 overflow-auto p-0 m-0">
            <div className="grid grid-cols-1 md:grid-cols-12 min-h-0 h-full">
              {/* Source Headers Column */}
              <div className="md:col-span-3 border-r h-full flex flex-col min-h-0">
                <div className="p-4 border-b bg-muted/30">
                  <h3 className="font-medium mb-2">Source Headers</h3>
                  <InputWithPrefix
                    placeholder="Search source headers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="mb-2"
                    prefix={<Search className="h-4 w-4 text-muted-foreground" />}
                  />
                </div>
                <ScrollArea className="flex-1 h-0">
                  <div className="p-2">
                    {dataset.headers.map((sourceHeader) => {
                      const isMapped = !!headerMapping[sourceHeader];
                      const isSelected = selectedSourceHeader === sourceHeader;

                      return (
                        <div
                          key={sourceHeader}
                          className={`p-2 rounded-md mb-1 cursor-pointer transition-colors ${
                            isSelected ? 'bg-primary text-primary-foreground' :
                            isMapped ? 'bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50' :
                            'hover:bg-muted'
                          }`}
                          onClick={() => setSelectedSourceHeader(sourceHeader)}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium truncate">{sourceHeader}</span>
                            {isMapped && (
                              <Badge variant="outline" className={isSelected ? "bg-primary-foreground/20 text-primary-foreground" : "bg-green-200 dark:bg-green-800"}>
                                Mapped
                              </Badge>
                            )}
                          </div>
                          {isMapped && (
                            <div className="flex items-center text-xs mt-1 gap-1">
                              <span className={isSelected ? "text-primary-foreground/80" : "text-muted-foreground"}>→</span>
                              <span className={isSelected ? "text-primary-foreground/80" : "text-muted-foreground"}>
                                {headerMapping[sourceHeader]}
                              </span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </div>

              {/* Mapping Area */}
              <div className="md:col-span-9 h-full flex flex-col min-h-0">
                {selectedSourceHeader ? (
                  <div className="p-3 sm:p-6 flex-1 overflow-auto">
                    <div className="max-w-3xl mx-auto">
                      <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">Map "{selectedSourceHeader}" to a target field</h3>
                        <p className="text-muted-foreground mb-4">
                          Select the corresponding field in your system that matches the source data.
                          You only need to map the fields you want to import - other fields can be left unmapped and edited later.
                        </p>

                        <div className="flex flex-col sm:flex-row items-center gap-2 mb-6">
                          <div className="w-full sm:flex-1 p-3 bg-muted rounded-md">
                            <div className="text-sm text-muted-foreground">Source Field</div>
                            <div className="font-medium">{selectedSourceHeader}</div>
                          </div>
                          <ArrowRight className="hidden sm:block h-6 w-6 text-muted-foreground" />
                          <div className="w-full sm:flex-1 p-3 bg-muted rounded-md">
                            <div className="text-sm text-muted-foreground">Target Field</div>
                            <div className="font-medium">
                              {headerMapping[selectedSourceHeader] || "Not mapped yet"}
                            </div>
                          </div>
                        </div>

                        {/* Sample data preview */}
                        <Card className="mb-6">
                          <CardContent className="p-4">
                            <h4 className="text-sm font-medium mb-2">Sample Data</h4>
                            <div className="text-sm">
                              {previewData.slice(0, 3).map((item, idx) => (
                                <div key={idx} className="py-1 border-b last:border-0">
                                  {item[selectedSourceHeader] !== undefined ?
                                    String(item[selectedSourceHeader]) :
                                    <span className="text-muted-foreground italic">Empty</span>
                                  }
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        {/* Search for target field */}
                        <div className="mb-4">
                          <label className="text-sm font-medium mb-1 block">
                            Search for a target field
                          </label>
                          <InputWithPrefix
                            placeholder="Type to search fields..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="mb-2"
                            prefix={<Search className="h-4 w-4 text-muted-foreground" />}
                          />
                        </div>

                        {/* Suggestions */}
                        {getHeaderSuggestions(selectedSourceHeader).length > 0 && (
                          <div className="mb-6">
                            <h4 className="text-sm font-medium mb-2">Suggested Mappings</h4>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                              {getHeaderSuggestions(selectedSourceHeader).map(suggestion => (
                                <Button
                                  key={suggestion}
                                  variant={headerMapping[selectedSourceHeader] === suggestion ? "default" : "outline"}
                                  className="justify-start h-auto py-2"
                                  onClick={() => handleMappingChange(selectedSourceHeader, suggestion)}
                                >
                                  <div className="flex items-center">
                                    {headerMapping[selectedSourceHeader] === suggestion && (
                                      <Check className="h-4 w-4 mr-2 text-primary-foreground" />
                                    )}
                                    <span>{suggestion}</span>
                                  </div>
                                </Button>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* All available fields */}
                        <div>
                          <h4 className="text-sm font-medium mb-2">All Available Fields</h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 max-h-[300px] overflow-y-auto">
                            {filteredPlatformHeaders.map(header => (
                              <Button
                                key={header}
                                variant={headerMapping[selectedSourceHeader] === header ? "default" : "outline"}
                                className="justify-start h-auto py-2 text-sm"
                                onClick={() => handleMappingChange(selectedSourceHeader, header)}
                              >
                                <div className="flex items-center">
                                  {headerMapping[selectedSourceHeader] === header && (
                                    <Check className="h-3 w-3 mr-2 text-primary-foreground" />
                                  )}
                                  <span className="truncate">{header}</span>
                                </div>
                              </Button>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <h3 className="text-lg font-medium mb-2">Select a source header to map</h3>
                      <p>Click on a header from the left panel to start mapping</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="flex-1 overflow-auto p-0 m-0">
            <div className="h-full flex flex-col">
              <div className="p-4 border-b bg-muted/30">
                <h3 className="font-medium">Data Preview</h3>
                <p className="text-sm text-muted-foreground">
                  Preview how your data will be imported with the current mapping.
                  You only need to map the fields you want to import - unmapped fields will be left empty and can be edited later.
                </p>
              </div>
              <div className="flex-1 overflow-auto p-4">
                <div className="border rounded-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-muted">
                          <th className="px-4 py-2 text-left text-sm font-medium">#</th>
                          {dataset.headers.map(header => (
                            <th key={header} className="px-4 py-2 text-left text-sm font-medium">
                              <div className="flex flex-col">
                                <span>{header}</span>
                                {headerMapping[header] && (
                                  <span className="text-xs text-muted-foreground flex items-center gap-1">
                                    <ChevronRight className="h-3 w-3" />
                                    {headerMapping[header]}
                                  </span>
                                )}
                              </div>
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {previewData.map((row, rowIndex) => (
                          <tr key={rowIndex} className="border-t hover:bg-muted/50">
                            <td className="px-4 py-2 text-sm text-muted-foreground">{rowIndex + 1}</td>
                            {dataset.headers.map(header => (
                              <td key={header} className="px-4 py-2 text-sm">
                                {row[header] !== undefined ? String(row[header]) : '-'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="flex-1 overflow-auto p-3 sm:p-6 m-0">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-lg font-medium mb-4">Import Settings</h3>

              {existingData.length > 0 && (
                <Card className="mb-6">
                  <CardContent className="p-4">
                    <h4 className="font-medium mb-2">Data Handling</h4>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          How should we handle the imported data?
                        </label>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          <Button
                            variant={selectedAction === 'append' ? "default" : "outline"}
                            className="justify-start h-auto py-3"
                            onClick={() => setSelectedAction('append')}
                          >
                            <div>
                              <div className="flex items-center">
                                {selectedAction === 'append' && (
                                  <Check className="h-4 w-4 mr-2 text-primary-foreground" />
                                )}
                                <span className="font-medium">Append Data</span>
                              </div>
                              <p className="text-xs text-muted-foreground text-left mt-1">
                                Add to existing {existingData.length} records
                              </p>
                            </div>
                          </Button>
                          <Button
                            variant={selectedAction === 'replace' ? "default" : "outline"}
                            className="justify-start h-auto py-3"
                            onClick={() => setSelectedAction('replace')}
                          >
                            <div>
                              <div className="flex items-center">
                                {selectedAction === 'replace' && (
                                  <Check className="h-4 w-4 mr-2 text-primary-foreground" />
                                )}
                                <span className="font-medium">Replace Data</span>
                              </div>
                              <p className="text-xs text-muted-foreground text-left mt-1">
                                Replace all existing data
                              </p>
                            </div>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2">Mapping Status</h4>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Mapping completion</span>
                        <span className="text-sm font-medium">{getMappingCompletionPercentage()}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2.5">
                        <div
                          className="bg-primary h-2.5 rounded-full"
                          style={{ width: `${getMappingCompletionPercentage()}%` }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium mb-2">Mapped Fields ({Object.keys(headerMapping).filter(k => !!headerMapping[k]).length}/{dataset.headers.length})</h5>
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(headerMapping)
                          .filter(([_, target]) => !!target)
                          .map(([source, target]) => (
                            <Badge key={source} variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 py-1 px-2">
                              {source} → {target}
                            </Badge>
                          ))
                        }
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium mb-2">Unmapped Fields ({dataset.headers.length - Object.keys(headerMapping).filter(k => !!headerMapping[k]).length})</h5>
                      <div className="flex flex-wrap gap-2">
                        {dataset.headers
                          .filter(header => !headerMapping[header])
                          .map(header => (
                            <Badge key={header} variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 py-1 px-2">
                              {header}
                            </Badge>
                          ))
                        }
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Note: You don't need to map all fields. Unmapped fields will be left empty and can be edited later.
                      </p>
                    </div>

                    <div className="pt-2">
                      <Button
                        variant="outline"
                        onClick={clearMapping}
                        className="w-full"
                      >
                        Clear All Mappings
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
    </>
  );
};

export default HeaderMappingDialog;
