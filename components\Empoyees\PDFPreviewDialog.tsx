"use client"

import React from 'react';
import { Dialog, DialogContent } from '../ui/dialog';
import { PDFViewer } from '@react-pdf/renderer';
import MoroccanPaySlipPDF from './SalaryPdf';
import { PaySlipData } from '@/types';

interface PDFPreviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  data: PaySlipData;
}

const PDFPreviewDialog: React.FC<PDFPreviewDialogProps> = ({
  isOpen,
  onClose,
  data,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] h-[90vh]">
        <div className="w-full h-full">
          <PDFViewer width="100%" height="100%" showToolbar={true}>
            <MoroccanPaySlipPDF data={data} />
          </PDFViewer>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PDFPreviewDialog;
