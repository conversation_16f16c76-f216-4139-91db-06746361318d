"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table";
import { Input } from "../ui/input";
import { <PERSON><PERSON> } from "../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Card, CardHeader, CardContent } from "../ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "../ui/dialog";
import { differenceInMonths } from 'date-fns';
import * as math from 'mathjs';
import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import MoroccanPaySlipPDF from "./SalaryPdf";
import { Employee } from "@/types";
import { Label } from "../ui/label";
import { ScrollArea } from "../ui/scroll-area";

interface PayrollItem {
  id: string;
  label: string;
  value: number;
  formula: string;
  category: 'earning' | 'deduction' | 'calculated';
  description?: string;
  taux?: number;
  base?: number;
  nombre?: number;
  reference?: string; // For Excel-like cell references
  format?: string; // For number formatting
}

const formatValue = (value: number, format?: string) => {
  if (!format) return value.toFixed(2);
  
  switch (format) {
    case 'currency':
      return new Intl.NumberFormat('fr-MA', {
        style: 'currency',
        currency: 'MAD',
        minimumFractionDigits: 2
      }).format(value);
    case 'percentage':
      return new Intl.NumberFormat('fr-MA', {
        style: 'percent',
        minimumFractionDigits: 2
      }).format(value / 100);
    default:
      return value.toFixed(2);
  }
};

const calculateFormula = (formula: string, items: PayrollItem[]): number => {
  try {
    // Replace references with values
    let processedFormula = formula;
    items.forEach(item => {
      if (item.reference) {
        const regex = new RegExp(item.reference, 'g');
        processedFormula = processedFormula.replace(regex, item.value.toString());
      }
    });

    // Replace common Excel functions
    processedFormula = processedFormula
      .replace(/SUM\((.*?)\)/gi, '($1)')
      .replace(/MULTIPLY\((.*?),(.*?)\)/gi, '($1)*($2)')
      .replace(/DIVIDE\((.*?),(.*?)\)/gi, '($1)/($2)')
      .replace(/ROUND\((.*?),(.*?)\)/gi, 'round($1,$2)');

    return math.evaluate(processedFormula);
  } catch (error) {
    console.error('Formula calculation error:', error);
    return 0;
  }
};

const SalaryBreakdown: React.FC<{
  payrollData: PayrollItem[];
  handleValueChange: (id: string, newValue: number) => void;
  handleTauxChange: (id: string, newTaux: number) => void;
  openFormulaForm: (item: PayrollItem) => void;
  handleRemoveItem: (id: string) => void;
  showNewItemForm: boolean;
  setShowNewItemForm: (show: boolean) => void;
  newItem: PayrollItem;
  handleNewItemChange: (field: string, value: string | number) => void;
  handleAddItem: () => void;
  setIsPdfPreviewOpen: (open: boolean) => void;
  showFormulaDialog: boolean;
  setShowFormulaDialog: (show: boolean) => void;
  selectedItemForFormula: PayrollItem | null;
  formulaBuilder: string;
  setFormulaBuilder: (formula: string) => void;
  addToFormula: (item: string) => void;
  handleFormulaChange: (id: string, newFormula: string) => void;
}> = React.memo(({
  payrollData,
  handleValueChange,
  handleTauxChange,
  openFormulaForm,
  handleRemoveItem,
  showNewItemForm,
  setShowNewItemForm,
  newItem,
  handleNewItemChange,
  handleAddItem,
  setIsPdfPreviewOpen,
  showFormulaDialog,
  setShowFormulaDialog,
  selectedItemForFormula,
  formulaBuilder,
  setFormulaBuilder,
  addToFormula,
  handleFormulaChange
}) => {
  const categories = [
    { id: 'earnings', label: 'Gains' },
    { id: 'social', label: 'Cotisations Sociales' },
    { id: 'deductions', label: 'Déductions' },
    { id: 'calculated', label: 'Calculés' },
  ];

  const renderTable = (categoryId: string, items: PayrollItem[]) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Rubrique</TableHead>
          <TableHead>Base</TableHead>
          <TableHead>Taux</TableHead>
          <TableHead>Nombre</TableHead>
          <TableHead>Montant</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => {
          const isCalculated = item.category === 'calculated';
          const value = isCalculated ? calculateFormula(item.formula, payrollData) : item.value;
          
          return (
            <TableRow key={item.id}>
              <TableCell className="font-medium">{item.label}</TableCell>
              <TableCell>
                {item.base !== undefined && (
                  <Input
                    type="number"
                    value={item.base}
                    onChange={(e) => handleNewItemChange("base", parseFloat(e.target.value) || 0)}
                    className="w-full text-right"
                  />
                )}
              </TableCell>
              <TableCell>
                {item.taux !== undefined && (
                  <Input
                    type="number"
                    value={item.taux}
                    onChange={(e) => handleTauxChange(item.id, parseFloat(e.target.value) || 0)}
                    className="w-full text-right"
                    step="0.01"
                  />
                )}
              </TableCell>
              <TableCell>
                {item.nombre !== undefined && (
                  <Input
                    type="number"
                    value={item.nombre}
                    onChange={(e) => handleNewItemChange("nombre", parseFloat(e.target.value) || 0)}
                    className="w-full text-right"
                  />
                )}
              </TableCell>
              <TableCell className="text-right">
                {isCalculated ? (
                  <span className="font-mono">{formatValue(value, item.format)}</span>
                ) : (
                  <Input
                    type="number"
                    value={value}
                    onChange={(e) => handleValueChange(item.id, parseFloat(e.target.value) || 0)}
                    className="text-right font-mono"
                    step="0.01"
                  />
                )}
              </TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openFormulaForm(item)}
                    className="flex-1"
                  >
                    {isCalculated ? 'Formule' : 'Éditer'}
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleRemoveItem(item.id)}
                    className="flex-1"
                  >
                    ×
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          );
        })}
        {/* Category Total Row */}
        <TableRow>
          <TableCell className="font-bold">Total {categories.find(c => c.id === categoryId)?.label}</TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell className="text-right font-bold">
            {formatValue(
              items.reduce((sum, item) => 
                sum + (item.category === 'calculated' 
                  ? calculateFormula(item.formula, payrollData)
                  : item.value),
                0
              ),
              'currency'
            )}
          </TableCell>
          <TableCell></TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );

  const getCategoryItems = (category: string) => {
    return payrollData.filter(item => {
      if (category === 'social') {
        return ['cnss', 'amo', 'cimr'].includes(item.id);
      } else if (category === 'deductions') {
        return item.category === 'deduction' && !['cnss', 'amo', 'cimr'].includes(item.id);
      } else if (category === 'earnings') {
        return item.category === 'earning';
      } else if (category === 'calculated') {
        return item.category === 'calculated';
      }
      return false;
    });
  };

  return (
    <div className="space-y-8">
      {categories.map((category) => {
        const items = getCategoryItems(category.id);
        if (items.length === 0) return null;
        return (
          <Card key={category.id}>
            <CardHeader>
              <h3 className="text-lg font-semibold">{category.label}</h3>
            </CardHeader>
            <CardContent>
              {renderTable(category.id, items)}
            </CardContent>
          </Card>
        );
      })}
      <div className="mt-4 space-y-4">
        <Button onClick={() => setShowNewItemForm(true)}>Ajouter un élément</Button>
        
        {showNewItemForm && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Ajouter un nouvel élément</h3>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right">Titre</Label>
                  <Input
                    id="title"
                    value={newItem.label}
                    onChange={(e) => handleNewItemChange("label", e.target.value)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="formula" className="text-right">Formule</Label>
                  <Input
                    id="formula"
                    value={newItem.formula}
                    onChange={(e) => handleNewItemChange("formula", e.target.value)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="value" className="text-right">Valeur</Label>
                  <Input
                    id="value"
                    type="number"
                    value={newItem.value}
                    onChange={(e) => handleNewItemChange("value", parseFloat(e.target.value) || 0)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="category" className="text-right">Catégorie</Label>
                  <Select
                    value={newItem.category}
                    onValueChange={(value: 'earning' | 'deduction' | 'calculated') => handleNewItemChange("category", value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="earning">Gain</SelectItem>
                      <SelectItem value="deduction">Déduction</SelectItem>
                      <SelectItem value="calculated">Calculé</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button onClick={() => setShowNewItemForm(false)} variant="outline">Annuler</Button>
                  <Button onClick={handleAddItem}>Ajouter</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        <Button className="m-9" onClick={() => setIsPdfPreviewOpen(true)}>
          Générer le PDF
        </Button>
      </div>
    </div>
  );
});

const SalaryCalculation: React.FC<{ employee: Employee }> = ({ employee }) => {
  const [payrollData, setPayrollData] = useState<PayrollItem[]>([
    { id: "base", label: "Salaire de base", value: employee.salaire, formula: "", category: 'earning', taux: 100, base: employee.salaire, nombre: 1 },
    { id: "anciennete", label: "Prime d'ancienneté", value: 0, formula: "calculateAnciennete(base, moisAnciennete)", category: 'earning', taux: 0, base: employee.salaire },
    { id: "heuresSupplementaires", label: "Heures supplémentaires", value: 0, formula: "", category: 'earning', taux: 125, base: 0, nombre: 0 },
    { id: "primesImposables", label: "Primes et indemnités imposables", value: 0, formula: "", category: 'earning', taux: 100 },
    { id: "primesNonImposables", label: "Primes et indemnités non imposables", value: 0, formula: "", category: 'earning', taux: 100 },
    { id: "avantagesNature", label: "Avantages en nature", value: 0, formula: "", category: 'earning', taux: 100 },
    { id: "indemniteTransport", label: "Indemnité de transport", value: 250, formula: "", category: 'earning', taux: 100 },
    { id: "indemniteRepresentation", label: "Indemnité de représentation", value: 0, formula: "", category: 'earning', taux: 100 },
    { id: "indemniteDeplacement", label: "Indemnité de déplacement", value: 0, formula: "", category: 'earning', taux: 100 },
    { id: "salaireBrutImposable", label: "Salaire brut imposable", value: 0, formula: "base + anciennete + primesImposables + avantagesNature + heuresSupplementaires", category: 'calculated' },
    { id: "salaireBrutGlobal", label: "Salaire brut global", value: 0, formula: "salaireBrutImposable + primesNonImposables + indemniteTransport + indemniteRepresentation + indemniteDeplacement", category: 'calculated' },
    // Updated CNSS calculation with correct ceiling
    { id: "cnss", label: "CNSS", value: 0, formula: "min(salaireBrutImposable, 6000) * 0.0448", category: 'deduction', taux: 4.48, base: 6000 },
    // Updated AMO calculation
    { id: "amo", label: "AMO", value: 0, formula: "min(salaireBrutImposable, 6000) * 0.0226", category: 'deduction', taux: 2.26, base: 6000 },
    { id: "cimr", label: "CIMR", value: 0, formula: "salaireBrutImposable * 0.06", category: 'deduction', taux: 6, base: 0 },
    { id: "fraisProfessionnels", label: "Frais professionnels", value: 0, formula: "min(salaireBrutImposable * 0.2, 2500)", category: 'deduction', taux: 20, base: 0 },
    { id: "mutuelleComplementaire", label: "Mutuelle complémentaire", value: 0, formula: "", category: 'deduction', taux: 0, base: 0 },
    { id: "assuranceGroupe", label: "Assurance groupe", value: 0, formula: "", category: 'deduction', taux: 0, base: 0 },
    { id: "interetsCreditLogement", label: "Intérêts sur crédit logement", value: 0, formula: "", category: 'deduction', taux: 0, base: 0 },
    { id: "retenuePret", label: "Retenue prêt", value: 0, formula: "", category: 'deduction' },
    { id: "retenueAbsence", label: "Retenue pour absence", value: 0, formula: "", category: 'deduction', nombre: 0, taux: 100, base: 0 },
    { id: "salaireNetImposable", label: "Salaire net imposable", value: 0, formula: "salaireBrutImposable - (cnss + amo + cimr + mutuelleComplementaire + assuranceGroupe + fraisProfessionnels + interetsCreditLogement)", category: 'calculated' },
    { id: "ir", label: "IR (Impôt sur le Revenu)", value: 0, formula: "calculateIR(salaireNetImposable, employee.situationFamiliale, employee.nombreEnfants)", category: 'deduction' },
    { id: "salaireNet", label: "Salaire net", value: 0, formula: "salaireNetImposable - ir - retenuePret - retenueAbsence", category: 'calculated' },
    { id: "arrondi", label: "Arrondi", value: 0, formula: "round(salaireNet) - salaireNet", category: 'calculated' },
    { id: "netAPayer", label: "Net à payer", value: 0, formula: "round(salaireNet)", category: 'calculated' },
  ]);


  const [moisAnciennete, setMoisAnciennete] = useState(0);
  const [newItem, setNewItem] = useState<PayrollItem>({
    id: "",
    label: "",
    value: 0,
    formula: "",
    category: 'earning',
    taux: 0,
    base: 0,
    nombre: 0,
    description: "",
    format: "currency"
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isFormulaDialogOpen, setIsFormulaDialogOpen] = useState(false);
  const [selectedItemForFormula, setSelectedItemForFormula] = useState<PayrollItem | null>(null);
  const [formulaBuilder, setFormulaBuilder] = useState<string>("");
  const [isPdfPreviewOpen, setIsPdfPreviewOpen] = useState(false);
  const [periodStart, setPeriodStart] = useState(new Date());
  const [periodEnd, setPeriodEnd] = useState(new Date());
  const [showNewItemForm, setShowNewItemForm] = useState(false);
  const [showFormulaDialog, setShowFormulaDialog] = useState(false);
  const [changedValues, setChangedValues] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    setMoisAnciennete(differenceInMonths(new Date(), employee.dateDebut));
  }, [employee.dateDebut]);

  const calculateAnciennete = useCallback((baseSalary: number, months: number): number => {
    if (months < 24) return 0;
    
    const years = Math.floor(months / 12);
    let percentage = 0;
    
    if (years >= 20) percentage = 25;
    else if (years >= 12) percentage = 15;
    else if (years >= 5) percentage = 10;
    else if (years >= 2) percentage = 5;
    
    return (baseSalary * percentage) / 100;
  }, []);

  const calculateIR = useCallback((salaireNetImposable: number, situationFamiliale: string, nombreEnfants: number): number => {
    // Convert to annual salary
    const annualSalary = salaireNetImposable * 12;
    
    // Apply family deductions
    let deductions = 0;
    if (situationFamiliale === 'marie') {
      deductions += 360;
    }
    // Maximum of 6 children for deductions
    deductions += Math.min(nombreEnfants, 6) * 360;
    
    const taxableIncome = Math.max(annualSalary - deductions, 0);
    
    // 2023 Moroccan Tax Brackets
    let tax = 0;
    if (taxableIncome <= 30000) {
      tax = 0;
    } else if (taxableIncome <= 50000) {
      tax = (taxableIncome - 30000) * 0.10;
    } else if (taxableIncome <= 60000) {
      tax = 2000 + (taxableIncome - 50000) * 0.20;
    } else if (taxableIncome <= 80000) {
      tax = 4000 + (taxableIncome - 60000) * 0.30;
    } else if (taxableIncome <= 180000) {
      tax = 10000 + (taxableIncome - 80000) * 0.34;
    } else {
      tax = 44000 + (taxableIncome - 180000) * 0.38;
    }
    
    // Convert annual tax to monthly
    return Math.max(tax / 12, 0);
  }, []);

  const evaluateFormula = useCallback((formula: string, data: PayrollItem[]): number => {
    try {
      // Create context with all available values
      const context: { [key: string]: number } = {
        moisAnciennete,
        situationFamiliale: employee.situationFamiliale === 'marie' ? 1 : 0,
        nombreEnfants: employee.nombreEnfants || 0,
      };

      // First pass: get all non-calculated values
      data.forEach(item => {
        if (item.category !== 'calculated') {
          context[item.id] = item.value;
        }
      });

      // Second pass: evaluate calculated values in order
      const calculatedItems = data.filter(item => item.category === 'calculated');
      calculatedItems.forEach(item => {
        if (item.formula && !context[item.id]) {
          try {
            const parser = math.parser();
            Object.entries(context).forEach(([key, value]) => parser.set(key, value));
            Object.entries(customFunctions).forEach(([key, func]) => parser.set(key, func));
            context[item.id] = parser.evaluate(item.formula);
          } catch (error) {
            console.error(`Error evaluating ${item.id}:`, error);
            context[item.id] = 0;
          }
        }
      });

      // Now evaluate the final formula with all context
      const parser = math.parser();
      Object.entries(context).forEach(([key, value]) => parser.set(key, value));
      Object.entries(customFunctions).forEach(([key, func]) => parser.set(key, func));
      
      const result = parser.evaluate(formula);
      return typeof result === 'number' && !isNaN(result) ? result : 0;
    } catch (error) {
      console.error("Error evaluating formula:", error, formula);
      return 0;
    }
  }, [moisAnciennete, employee.situationFamiliale, employee.nombreEnfants]);

  const customFunctions = useMemo(() => ({
    calculateAnciennete,
    calculateIR,
    min: Math.min,
    max: Math.max,
    round: Math.round,
    abs: Math.abs,
    if: (condition: boolean, trueValue: number, falseValue: number) => condition ? trueValue : falseValue,
  }), [calculateAnciennete, calculateIR]);

  const updateCalculations = useCallback(() => {
    setPayrollData(prevData => {
      let updatedData = [...prevData];
      let changes = false;
      let iterations = 0;
      const maxIterations = 10; // Prevent infinite loops

      // Sort calculated items by dependency order
      const calculatedItems = updatedData.filter(item => item.category === 'calculated');
      const sortedCalculated = calculatedItems.sort((a, b) => {
        const aDeps = a.formula?.match(/[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
        const bDeps = b.formula?.match(/[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
        return aDeps.length - bDeps.length;
      });

      do {
        changes = false;
        // First update non-calculated items
        updatedData = updatedData.map(item => {
          if (item.category !== 'calculated') {
            return item;
          }
          return item;
        });

        // Then update calculated items in order
        sortedCalculated.forEach(calcItem => {
          if (calcItem.formula) {
            const newValue = evaluateFormula(calcItem.formula, updatedData);
            const itemIndex = updatedData.findIndex(item => item.id === calcItem.id);
            if (itemIndex !== -1 && Math.abs(newValue - updatedData[itemIndex].value) > 0.01) {
              changes = true;
              updatedData[itemIndex] = { ...updatedData[itemIndex], value: newValue };
            }
          }
        });

        iterations++;
      } while (changes && iterations < maxIterations);

      return updatedData;
    });
  }, [evaluateFormula]);

  // Initial calculation when component mounts
  useEffect(() => {
    updateCalculations();
  }, [updateCalculations, employee]);

  // Update when any value changes
  useEffect(() => {
    if (Object.keys(changedValues).length > 0) {
      updateCalculations();
      setChangedValues({});
    }
  }, [changedValues, updateCalculations]);

  const handleValueChange = useCallback((id: string, newValue: number) => {
    setPayrollData(prevData =>
      prevData.map(item =>
        item.id === id ? { ...item, value: newValue } : item
      )
    );
    setChangedValues(prev => ({ ...prev, [id]: true }));
  }, []);

  const openFormulaForm = useCallback((item: PayrollItem) => {
    setSelectedItemForFormula(item);
    setFormulaBuilder(item.formula);
    setShowFormulaDialog(true);
  }, []);

  const addToFormula = useCallback((item: string) => {
    setFormulaBuilder(prev => prev + item);
  }, []);

  const handleTauxChange = useCallback((id: string, newTaux: number) => {
    setPayrollData(prevData =>
      prevData.map(item =>
        item.id === id ? { ...item, taux: newTaux } : item
      )
    );
    updateCalculations();
  }, [updateCalculations]);

  const handleFormulaChange = useCallback((id: string, newFormula: string) => {
    setPayrollData(prevData => {
      const updatedData = prevData.map(item => {
        if (item.id === id) {
          const newValue = evaluateFormula(newFormula, prevData);
          return { ...item, formula: newFormula, value: newValue };
        }
        return item;
      });
      return updatedData;
    });
    updateCalculations();
  }, [evaluateFormula, updateCalculations]);

  const handleNewItemChange = useCallback((field: string, value: string | number) => {
    setNewItem(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleAddItem = useCallback(() => {
    if (newItem.label) {
      setPayrollData(prevData => [
        ...prevData,
        {
          ...newItem,
          id: `custom_${Date.now()}`
        }
      ]);
      setNewItem({
        id: "",
        label: "",
        value: 0,
        formula: "",
        category: 'earning',
        taux: 0,
        base: 0,
        nombre: 0,
        description: "",
        format: "currency"
      });
      setShowNewItemForm(false);
      updateCalculations();
    }
  }, [newItem, updateCalculations]);

  const handleRemoveItem = useCallback((id: string) => {
    setPayrollData(prevData => prevData.filter(item => item.id !== id));
    updateCalculations();
  }, [updateCalculations]);

  const FormulaEditorDialog = ({ isOpen, onClose, item, onSave }: {
    isOpen: boolean;
    onClose: () => void;
    item: PayrollItem | null;
    onSave: (formula: string) => void;
  }) => {
    const [formula, setFormula] = useState(item?.formula || "");
    const [previewValue, setPreviewValue] = useState<number>(0);
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedCategory, setSelectedCategory] = useState<string>("all");
    const [showHistory, setShowHistory] = useState(false);
    const [formulaHistory, setFormulaHistory] = useState<string[]>([]);

    useEffect(() => {
      if (formula) {
        try {
          const result = evaluateFormula(formula, payrollData);
          setPreviewValue(result);
        } catch (error) {
          console.error("Preview calculation error:", error);
        }
      }
    }, [formula, payrollData]);

    const handleSave = () => {
      if (item) {
        setPayrollData(prevData =>
          prevData.map(p =>
            p.id === item.id
              ? { ...p, formula, value: previewValue }
              : p
          )
        );
        onSave(formula);
        updateCalculations(); 
      }
      onClose();
    };

    const addToFormula = (value: string) => {
      setFormula(prev => {
        const newFormula = prev + " " + value;
        setFormulaHistory(hist => [...hist, newFormula]);
        return newFormula;
      });
    };

    const commonOperations = [
      { label: "Addition (+)", value: "+", category: "basic" },
      { label: "Soustraction (-)", value: "-", category: "basic" },
      { label: "Multiplication (*)", value: "*", category: "basic" },
      { label: "Division (/)", value: "/", category: "basic" },
      { label: "Minimum", value: "min(", category: "function" },
      { label: "Maximum", value: "max(", category: "function" },
      { label: "Si condition", value: "if(", category: "function" },
      { label: "Arrondi", value: "round(", category: "function" },
      { label: "Pourcentage", value: "* 0.01", category: "function" },
      { label: "Somme", value: "sum(", category: "function" },
      { label: "Moyenne", value: "mean(", category: "function" },
      { label: "Valeur absolue", value: "abs(", category: "function" },
    ];

    const availableReferences = payrollData
      .filter(p => p.id !== item?.id)
      .map(p => ({
        label: p.label,
        value: p.id,
        category: p.category,
        currentValue: formatValue(p.value, 'currency')
      }));

    const filteredOperations = commonOperations.filter(op =>
      op.label.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (selectedCategory === "all" || op.category === selectedCategory)
    );

    const filteredReferences = availableReferences.filter(ref =>
      ref.label.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (selectedCategory === "all" || ref.category === selectedCategory)
    );

    const categories = [
      { label: "Tout", value: "all" },
      { label: "Gains", value: "earning" },
      { label: "Déductions", value: "deduction" },
      { label: "Calculés", value: "calculated" },
      { label: "Fonctions", value: "function" },
      { label: "Opérations de base", value: "basic" },
    ];

    const getTableTotals = (category: string) => {
      const items = payrollData.filter(item => item.category === category);
      return items.reduce((sum, item) => {
        if (item.category === 'calculated') {
          return sum + calculateFormula(item.formula, payrollData);
        }
        return sum + item.value;
      }, 0);
    };

    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Éditeur de formule - {item?.label}</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[calc(90vh-8rem)]">
            <div className="grid gap-4 p-4">
              <div className="flex items-center justify-between">
                <Label>Rechercher</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowHistory(!showHistory)}
                >
                  {showHistory ? "Masquer l'historique" : "Afficher l'historique"}
                </Button>
              </div>

              <div className="flex gap-4 items-center">
                <div className="flex-1">
                  <Input
                    placeholder="Rechercher une opération ou une référence..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <Label>Catégorie</Label>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((cat) => (
                        <SelectItem key={cat.value} value={cat.value}>
                          {cat.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Opérations</Label>
                  <ScrollArea className="h-[200px] rounded-md border p-2">
                    {filteredOperations.map((op) => (
                      <Button
                        key={op.value}
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => addToFormula(op.value)}
                      >
                        {op.label}
                      </Button>
                    ))}
                  </ScrollArea>
                </div>
                <div>
                  <Label>Références disponibles</Label>
                  <ScrollArea className="h-[200px] rounded-md border p-2">
                    {filteredReferences.map((ref) => (
                      <Button
                        key={ref.value}
                        variant="ghost"
                        className="w-full justify-start flex items-center gap-2"
                        onClick={() => addToFormula(ref.value)}
                      >
                        <span className="flex-1">{ref.label}</span>
                        <span className="text-sm text-gray-500">{ref.currentValue}</span>
                      </Button>
                    ))}
                  </ScrollArea>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Total Gains</Label>
                  <div className="rounded-md border p-2">
                    {formatValue(getTableTotals('earning'), 'currency')}
                  </div>
                </div>
                <div>
                  <Label>Total Déductions</Label>
                  <div className="rounded-md border p-2">
                    {formatValue(getTableTotals('deduction'), 'currency')}
                  </div>
                </div>
                <div>
                  <Label>Net à Payer</Label>
                  <div className="rounded-md border p-2">
                    {formatValue(getTableTotals('earning') - getTableTotals('deduction'), 'currency')}
                  </div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <Label>Formule</Label>
                </div>
                <div className="grid gap-2">
                  <Input
                    value={formula}
                    onChange={(e) => setFormula(e.target.value)}
                    className="font-mono"
                  />
                  {showHistory && (
                    <ScrollArea className="h-[100px] rounded-md border p-2">
                      {formulaHistory.map((f, i) => (
                        <Button
                          key={i}
                          variant="ghost"
                          className="w-full justify-start text-sm"
                          onClick={() => setFormula(f)}
                        >
                          {f}
                        </Button>
                      ))}
                    </ScrollArea>
                  )}
                </div>
              </div>

              <div>
                <Label>Aperçu du résultat</Label>
                <div className="rounded-md border p-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-500">Valeur calculée</span>
                      <div className="font-semibold">{formatValue(previewValue, 'currency')}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Impact sur le net</span>
                      <div className={`font-semibold ${previewValue >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {previewValue >= 0 ? '+' : ''}{formatValue(previewValue * 0.77, 'currency')}
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-gray-500">
                    <div className="flex justify-between">
                      <span>Après charges sociales (~23%)</span>
                      <span>{formatValue(previewValue * 0.77, 'currency')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Impact mensuel IR</span>
                      <span>~{formatValue(previewValue * 0.77 * 0.38, 'currency')}</span>
                    </div>
                    <div className="flex justify-between mt-1 pt-1 border-t">
                      <span>Coût employeur</span>
                      <span>{formatValue(previewValue * 1.20, 'currency')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
          <DialogFooter className="flex justify-end gap-2 mt-4">
            <div className="flex justify-between w-full">
              <div className="text-sm">
                <span className="font-semibold">Aperçu du résultat: </span>
                {formatValue(previewValue, 'currency')}
              </div>
              <div className="space-x-2">
                <Button variant="outline" onClick={onClose}>
                  Annuler
                </Button>
                <Button onClick={handleSave}>
                  Enregistrer
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const EmployeeInfo: React.FC = () => (
    <div>
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">Informations de l'employé</h3>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p><strong>Nom:</strong> {employee.nom}</p>
            <p><strong>Prénom:</strong> {employee.prenom}</p>
            <p><strong>Date de naissance:</strong> {employee.dateNaissance.toLocaleString()}</p>
            <p><strong>Genre:</strong> {employee.genre}</p>
          </div>
          <div>
            <p><strong>Date d'embauche:</strong> {employee.dateDebut.toLocaleString()}</p>
            <p><strong>Ancienneté:</strong> {Math.floor(moisAnciennete / 12)} ans et {moisAnciennete % 12} mois</p>
          </div>
        </div>
      </CardContent>
    </Card>
    </div>
  );

  const Explanations: React.FC = () => (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">Explications</h3>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="anciennete">
          <TabsList>
            <TabsTrigger value="anciennete">Prime d'ancienneté</TabsTrigger>
            <TabsTrigger value="ir">IR</TabsTrigger>
            <TabsTrigger value="fraisPro">Frais Professionnels</TabsTrigger>
            <TabsTrigger value="cotisations">Cotisations</TabsTrigger>
          </TabsList>
          <TabsContent value="anciennete">
            <h4 className="font-semibold">Prime d'ancienneté</h4>
            <p>La prime d'ancienneté est calculée selon le barème suivant :</p>
            <ul className="list-disc list-inside">
              <li>Moins de 2 ans : 0%</li>
              <li>Entre 2 et 5 ans : 5%</li>
              <li>Entre 5 et 12 ans : 10%</li>
              <li>Entre 12 et 20 ans : 15%</li>
              <li>Entre 20 et 25 ans : 20%</li>
              <li>Plus de 25 ans : 25%</li>
            </ul>
          </TabsContent>
          <TabsContent value="ir">
            <h4 className="font-semibold">Impôt sur le Revenu (IR)</h4>
            <p>L'IR est calculé selon le barème mensuel suivant :</p>
            <ul className="list-disc list-inside">
              <li>Jusqu'à 2 500 DH : 0%</li>
              <li>2 501 à 4 166,67 DH : 10%</li>
              <li>4 167 à 5 000 DH : 20%</li>
              <li>5 001 à 6 666,67 DH : 30%</li>
              <li>6 667 à 15 000 DH : 34%</li>
              <li>Au-delà de 15 000 DH : 38%</li>
            </ul>
          </TabsContent>
          <TabsContent value="fraisPro">
            <h4 className="font-semibold">Frais Professionnels</h4>
            <p>Les frais professionnels sont calculés au taux de 20% du revenu brut imposable, plafonnés à 2 500 DH par mois (30 000 DH par an).</p>
          </TabsContent>
          <TabsContent value="cotisations">
            <h4 className="font-semibold">Cotisations sociales</h4>
            <ul className="list-disc list-inside">
              <li>CNSS : 4.48% du salaire brut, plafonné à 6000 DH</li>
              <li>AMO : 2.26% du salaire brut</li>
              <li>CIMR : Taux variable selon le contrat</li>
              <li>Assurance maladie complémentaire : Taux variable selon le contrat</li>
            </ul>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );

  const UserGuide: React.FC = () => (
    <Card className="mt-8">
      <CardHeader>
        <h3 className="text-xl font-bold">Guide d'utilisation : Calculateur de salaire marocain avancé</h3>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <section>
            <h4 className="text-lg font-semibold">Aperçu</h4>
            <p>Ce calculateur est conçu pour calculer les salaires selon les lois du travail marocaines et les pratiques courantes. Il est hautement personnalisable et peut être adapté à diverses politiques d'entreprise.</p>
          </section>
          <section>
            <h4 className="text-lg font-semibold">Fonctionnalités clés</h4>
            <ul className="list-disc list-inside">
              <li>Calcul dynamique des composantes du salaire</li>
              <li>Prise en compte de la situation familiale et de l'ancienneté</li>
              <li>Calcul automatique des cotisations sociales et de l'impôt sur le revenu</li>
              <li>Possibilité d'ajouter des primes et indemnités personnalisées</li>
              <li>Explications détaillées sur les différentes rubriques du bulletin de paie</li>
              <li>Ajout et suppression facile d'éléments de paie</li>
              <li>Modification directe des formules de calcul</li>
            </ul>
          </section>
          <section>
            <h4 className="text-lg font-semibold">Comment utiliser</h4>
            <ol className="list-decimal list-inside">
              <li>Vérifiez et mettez à jour les informations de l'employé si nécessaire</li>
              <li>Entrez le salaire de base et les autres valeurs fixes</li>
              <li>Ajoutez les primes et indemnités spécifiques à l'employé</li>
              <li>Vérifiez les cotisations sociales et ajustez si nécessaire</li>
              <li>Modifiez les formules directement dans le tableau si besoin</li>
              <li>Utilisez le bouton "Ajouter un élément" pour inclure de nouvelles composantes</li>
              <li>Supprimez les éléments non nécessaires avec le bouton "Supprimer"</li>
              <li>Consultez le récapitulatif du salaire et les explications pour comprendre chaque composante</li>
            </ol>
          </section>
          <section>
            <h4 className="text-lg font-semibold">Personnalisation</h4>
            <p>Vous pouvez personnaliser ce calculateur en :</p>
            <ul className="list-disc list-inside">
              <li>Ajoutant de nouvelles composantes salariales</li>
              <li>Modifiant les formules de calcul existantes directement dans le tableau</li>
              <li>Adaptant les taux de cotisation selon les spécificités de votre entreprise</li>
              <li>Supprimant les éléments non pertinents pour votre structure</li>
            </ul>
          </section>
        </div>
      </CardContent>
    </Card>
  );

  interface PdfPreviewDialogProps {
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    paySlipData: {
      employee: {
        nom: string;
        prenom: string;
        matricule: string;
        cin: string;
        cnss: string;
        fonction: string;
        categorie: string;
      };
      payrollItems: Array<{
        id: string;
        label: string;
        value: number;
        formula: string;
        category: 'earning' | 'deduction' | 'calculated';
        description?: string;
        taux: number;
        base: number;
        nombre: number;
      }>;
      totals: {
        totalGains: number;
        totalDeductions: number;
        netAPayer: number;
      };
      periodStart: Date;
      periodEnd: Date;
      baseSalary: number;
      overtime: number;
      transportation: number;
      familyAllowance: number;
      otherAllowances: number;
      grossSalary: number;
      cnss: number;
      cimr: number;
      mutuelle: number;
      ir: number;
      irPercentage: number;
      salaryAdvance: number;
      netSalary: number;
    };
  }

  const PdfPreviewDialog: React.FC<PdfPreviewDialogProps> = ({ isOpen, setIsOpen, paySlipData }) => {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] w-[1000px]">
          <DialogHeader>
            <DialogTitle>Aperçu du bulletin de paie</DialogTitle>
          </DialogHeader>
          <div className="h-[80vh]">
            <PDFViewer width="100%" height="100%" className="rounded-lg">
              {/* @ts-ignore */}
              <MoroccanPaySlipPDF data={paySlipData} />
            </PDFViewer>
          </div>
          <DialogFooter>
            <PDFDownloadLink
            // @ts-ignore
              document={<MoroccanPaySlipPDF data={paySlipData} />}
              fileName={`bulletin_${paySlipData.employee.nom}_${paySlipData.employee.prenom}_${new Date().toISOString().split('T')[0]}.pdf`}
            >
              {({ loading }) => (
                <Button disabled={loading}>
                  {loading ? 'Génération...' : 'Télécharger PDF'}
                </Button>
              )}
            </PDFDownloadLink>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Fermer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const generatePaySlipData = () => {
    const findValue = (id: string) => payrollData.find(item => item.id === id)?.value || 0;

    return {
      employee: {
        ...employee,
        matricule: employee.matricule,
        cin: employee.cin,
        cnss: employee.cnss,
        fonction: employee.poste,
        categorie: employee.departement,

      },
      payrollItems: payrollData.map(item => ({
        ...item,
        base: item.base || 0,
        taux: item.taux || 0,
        nombre: item.nombre || 0,
      })),
      totals: {
        totalGains: payrollData.filter(item => item.category === 'earning').reduce((sum, item) => sum + item.value, 0),
        totalDeductions: payrollData.filter(item => item.category === 'deduction').reduce((sum, item) => sum + item.value, 0),
        netAPayer: findValue('netAPayer'),
      },
      periodStart,
      periodEnd,
      baseSalary: findValue('base'),
      overtime: findValue('heuresSupplementaires'),
      transportation: findValue('indemniteTransport'),
      familyAllowance: findValue('allocationsFamiliales') || 0,
      otherAllowances: findValue('autresPrimes') || 0,
      grossSalary: findValue('salaireBrutImposable'),
      cnss: findValue('cnss'),
      cimr: findValue('cimr'),
      mutuelle: findValue('amo'),
      ir: findValue('ir'),
      irPercentage: findValue('ir') / findValue('salaireNetImposable'),
      salaryAdvance: findValue('avancesSurSalaire') || 0,
      netSalary: findValue('salaireNet'),
    };
  };

  const memoizedSalaryBreakdown = useMemo(() => (
    <SalaryBreakdown
      payrollData={payrollData}
      handleValueChange={handleValueChange}
      handleTauxChange={handleTauxChange}
      openFormulaForm={openFormulaForm}
      handleRemoveItem={handleRemoveItem}
      showNewItemForm={showNewItemForm}
      setShowNewItemForm={setShowNewItemForm}
      newItem={newItem}
      handleNewItemChange={handleNewItemChange}
      handleAddItem={handleAddItem}
      setIsPdfPreviewOpen={setIsPdfPreviewOpen}
      showFormulaDialog={showFormulaDialog}
      setShowFormulaDialog={setShowFormulaDialog}
      selectedItemForFormula={selectedItemForFormula}
      formulaBuilder={formulaBuilder}
      setFormulaBuilder={setFormulaBuilder}
      addToFormula={addToFormula}
      handleFormulaChange={handleFormulaChange}
    />
  ), [payrollData, handleValueChange, handleTauxChange, openFormulaForm, handleRemoveItem, showNewItemForm, setShowNewItemForm, newItem, handleNewItemChange, handleAddItem, setIsPdfPreviewOpen, showFormulaDialog, setShowFormulaDialog, selectedItemForFormula, formulaBuilder, setFormulaBuilder, addToFormula, handleFormulaChange]);

  return (
    <div className="space-y-8 p-4">
      <EmployeeInfo />
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Bulletin de paie</h2>
        <div className="space-x-2">
          <Button onClick={() => setIsPdfPreviewOpen(true)}>
            Générer PDF
          </Button>
        </div>
      </div>
      {memoizedSalaryBreakdown}
      <Explanations />
      <UserGuide />
      <PdfPreviewDialog 
        isOpen={isPdfPreviewOpen}
        setIsOpen={setIsPdfPreviewOpen}
        // @ts-ignore
        paySlipData={generatePaySlipData()}
      />
      <FormulaEditorDialog 
        isOpen={showFormulaDialog}
        onClose={() => setShowFormulaDialog(false)}
        item={selectedItemForFormula}
        onSave={(formula) => handleFormulaChange(selectedItemForFormula?.id || '', formula)}
      />
      <Dialog open={showNewItemForm} onOpenChange={setShowNewItemForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Nouvelle Rubrique</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4">
            <div>
              <Label>Libellé</Label>
              <Input
                value={newItem.label}
                onChange={(e) => handleNewItemChange("label", e.target.value)}
                placeholder="Nom de la rubrique"
              />
            </div>
            <div>
              <Label>Catégorie</Label>
              <Select
                value={newItem.category}
                onValueChange={(value) => handleNewItemChange("category", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="earning">Gain</SelectItem>
                  <SelectItem value="deduction">Déduction</SelectItem>
                  <SelectItem value="calculated">Calculé</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {newItem.category === 'calculated' ? (
              <div>
                <Label>Formule</Label>
                <Input
                  value={newItem.formula}
                  onChange={(e) => handleNewItemChange("formula", e.target.value)}
                  placeholder="Exemple: salaire_base * 0.1"
                />
              </div>
            ) : (
              <>
                <div>
                  <Label>Base</Label>
                  <Input
                    type="number"
                    value={newItem.base}
                    onChange={(e) => handleNewItemChange("base", parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div>
                  <Label>Taux (%)</Label>
                  <Input
                    type="number"
                    value={newItem.taux}
                    onChange={(e) => handleNewItemChange("taux", parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div>
                  <Label>Nombre</Label>
                  <Input
                    type="number"
                    value={newItem.nombre}
                    onChange={(e) => handleNewItemChange("nombre", parseFloat(e.target.value) || 0)}
                  />
                </div>
              </>
            )}
            <div>
              <Label>Description</Label>
              <Input
                value={newItem.description || ''}
                onChange={(e) => handleNewItemChange("description", e.target.value)}
                placeholder="Description optionnelle"
              />
            </div>
            <div>
              <Label>Format</Label>
              <Select
                value={newItem.format}
                onValueChange={(value) => handleNewItemChange("format", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="currency">Monnaie (MAD)</SelectItem>
                  <SelectItem value="percentage">Pourcentage (%)</SelectItem>
                  <SelectItem value="number">Nombre</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNewItemForm(false)}>
              Annuler
            </Button>
            <Button onClick={handleAddItem}>
              Ajouter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default React.memo(SalaryCalculation);