"use client"
import React from 'react';
import { Page, Text, View, Document, StyleSheet, Font, Image } from '@react-pdf/renderer';
import { Employee } from '@/types';

// Register custom fonts
Font.register({
  family: 'Roboto',
  fonts: [
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-light-webfont.ttf', fontWeight: 300 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf', fontWeight: 400 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf', fontWeight: 500 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf', fontWeight: 700 },
  ],
});

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontFamily: 'Roboto',
  },
  header: {
    marginBottom: 30,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  logo: {
    width: 120,
    height: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 20,
    textTransform: 'uppercase',
  },
  subtitle: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 10,
  },
  infoSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
    padding: 15,
    backgroundColor: '#f8fafc',
  },
  infoColumn: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 10,
    color: '#64748b',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 11,
    color: '#1e293b',
    fontWeight: 'medium',
    marginBottom: 8,
  },
  table: {
    width: '100%',
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#1e293b',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  tableHeaderCell: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'left' as const,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    padding: 8,
    minHeight: 30,
  },
  tableCell: {
    fontSize: 10,
    color: '#1e293b',
  },
  tableCellRight: {
    fontSize: 10,
    color: '#1e293b',
    textAlign: 'right' as const,
  },
  col1: { width: '30%' },
  col2: { width: '17.5%' },
  col3: { width: '17.5%' },
  col4: { width: '17.5%' },
  col5: { width: '17.5%' },
  summarySection: {
    marginTop: 30,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    paddingTop: 20,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  summaryLabel: {
    fontSize: 11,
    color: '#64748b',
  },
  summaryValue: {
    fontSize: 11,
    color: '#1e293b',
    fontWeight: 'medium',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 2,
    borderTopColor: '#1e293b',
  },
  totalLabel: {
    fontSize: 14,
    color: '#1e293b',
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 14,
    color: '#1e293b',
    fontWeight: 'bold',
  },
  footer: {
    position: 'absolute',
    bottom: 40,
    left: 40,
    right: 40,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    paddingTop: 20,
  },
  footerText: {
    fontSize: 8,
    color: '#64748b',
    textAlign: 'center',
  },
});

interface PayrollItem {
  id: string;
  label: string;
  value: number;
  formula: string;
  category: 'earning' | 'deduction' | 'calculated';
  description?: string;
  taux?: number;
  base?: number;
  nombre?: number;
}

interface PaySlipData {
  employee: Employee;
  payrollItems: PayrollItem[];
  totals: {
    totalGains: number;
    totalDeductions: number;
    netAPayer: number;
  };
  periodStart: Date;
  periodEnd: Date;
  baseSalary: number;
  overtime: number;
  transportation: number;
  familyAllowance: number;
  otherAllowances: number;
  grossSalary: number;
  cnss: number;
  cimr: number;
  mutuelle: number;
  ir: number;
  irPercentage: number;
  salaryAdvance: number;
  netSalary: number;
}

interface MoroccanPaySlipPDFProps {
  data: PaySlipData;
}

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('fr-MA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num);
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('fr-MA', {
    year: 'numeric',
    month: 'long',
  }).format(date);
};

const MoroccanPaySlipPDF: React.FC<MoroccanPaySlipPDFProps> = ({ data }) => {
  const period = `${formatDate(data.periodStart)} - ${formatDate(data.periodEnd)}`;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View>
              <Text style={styles.infoValue}>SOCIETE XYZ</Text>
              <Text style={styles.infoLabel}>ICE: 123456789000054</Text>
              <Text style={styles.infoLabel}>RC: 123456</Text>
            </View>
            <View>
              <Text style={styles.infoValue}>BULLETIN DE PAIE</Text>
              <Text style={styles.infoLabel}>{period}</Text>
            </View>
          </View>
        </View>

        <View style={styles.infoSection}>
          <View style={styles.infoColumn}>
            <Text style={styles.infoLabel}>Employé(e)</Text>
            <Text style={styles.infoValue}>{data.employee.prenom} {data.employee.nom}</Text>
            <Text style={styles.infoLabel}>Matricule</Text>
            <Text style={styles.infoValue}>{data.employee.matricule || '-'}</Text>
          </View>
          <View style={styles.infoColumn}>
            <Text style={styles.infoLabel}>CIN</Text>
            <Text style={styles.infoValue}>{data.employee.cin}</Text>
            <Text style={styles.infoLabel}>CNSS</Text>
            <Text style={styles.infoValue}>{data.employee.cnss}</Text>
          </View>
          <View style={styles.infoColumn}>
            <Text style={styles.infoLabel}>Fonction</Text>
            <Text style={styles.infoValue}>{data.employee.poste}</Text>
            <Text style={styles.infoLabel}>Département</Text>
            <Text style={styles.infoValue}>{data.employee.departement}</Text>
          </View>
        </View>

        <View style={styles.table}>
          <View style={styles.tableHeader}>
            <Text style={[styles.tableHeaderCell, styles.col1]}>Rubrique</Text>
            <Text style={[styles.tableHeaderCell, styles.col2]}>Base</Text>
            <Text style={[styles.tableHeaderCell, styles.col3]}>Taux</Text>
            <Text style={[styles.tableHeaderCell, styles.col4]}>Nombre</Text>
            <Text style={[styles.tableHeaderCell, styles.col5]}>Montant</Text>
          </View>

          {data.payrollItems.map((item, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.tableCell, styles.col1]}>{item.label}</Text>
              <Text style={[styles.tableCellRight, styles.col2]}>{item.base ? formatNumber(item.base) : '-'}</Text>
              <Text style={[styles.tableCellRight, styles.col3]}>{item.taux ? `${item.taux}%` : '-'}</Text>
              <Text style={[styles.tableCellRight, styles.col4]}>{item.nombre || '-'}</Text>
              <Text style={[styles.tableCellRight, styles.col5]}>{formatNumber(item.value)}</Text>
            </View>
          ))}
        </View>

        <View style={styles.summarySection}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Gains</Text>
            <Text style={styles.summaryValue}>{formatNumber(data.totals.totalGains)} MAD</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Déductions</Text>
            <Text style={styles.summaryValue}>{formatNumber(data.totals.totalDeductions)} MAD</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>NET A PAYER</Text>
            <Text style={styles.totalValue}>{formatNumber(data.totals.netAPayer)} MAD</Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Ce bulletin de paie est un document officiel à conserver sans limitation de durée
          </Text>
        </View>
      </Page>
    </Document>
  );
};

export default MoroccanPaySlipPDF;
