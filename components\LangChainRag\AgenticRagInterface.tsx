"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Brain,
  Database,
  Settings,
  ChevronDown,
  ChevronUp,
  Sparkles,
  FileText,
  Gem
} from 'lucide-react';
import { useLangChainRag } from '@/hooks/useLangChainRag';
import AgenticChatInterface from './AgenticChatInterface';
import CompactDatasetSelector from './CompactDatasetSelector';
import CompactPDFSelector from './CompactPDFSelector';
import ModelSelector from './ModelSelector';
import EmbeddingModelInfo from './EmbeddingModelInfo';
import TroubleshootingGuide from './TroubleshootingGuide';
import VectorEmbeddingVisualization from './VectorEmbeddingVisualization';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const AgenticRagInterface: React.FC = () => {
  const [isSettingsExpanded, setIsSettingsExpanded] = useState(false);
  const [isDataSourcesExpanded, setIsDataSourcesExpanded] = useState(true);
  const [isVectorVisualizationVisible, setIsVectorVisualizationVisible] = useState(false);
  const [lastQuery, setLastQuery] = useState('');
  
  const {
    selectedDatasets,
    selectedPDFs,
    isLoading,
    isEmbedding,
    error,
    chatMessages,
    selectedModel,
    pineconeStatus,
    sourceDocuments,
    useDeepResearch,
    maxIterations,
    currentIterations,
    retrievalK,
    setSelectedDatasets,
    setSelectedPDFs,
    setSelectedModel,
    setUseDeepResearch,
    setMaxIterations,
    setRetrievalK,
    embedDataset,
    embedPDF,
    embedExistingPDF,
    deleteEmbedding,
    clearChat,
    sendMessage,
    checkPineconeStatus,
    fetchDatasets,
    fetchPDFs
  } = useLangChainRag();

  // Check for errors and display toast
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Get provider color for model badge
  const getProviderInfo = (model: string) => {
    if (model.startsWith('gemini')) {
      return { color: 'bg-amber-100 text-amber-800 border-amber-200', icon: Gem };
    } else if (model.startsWith('command')) {
      return { color: 'bg-green-100 text-green-800 border-green-200', icon: Brain };
    } else {
      return { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: Sparkles };
    }
  };

  const providerInfo = getProviderInfo(selectedModel);
  const totalSources = selectedDatasets.length + selectedPDFs.length;

  return (
    <div className="h-full flex flex-col max-w-7xl mx-auto p-4 gap-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-primary" />
            <h1 className="text-xl font-semibold">AI Research Assistant</h1>
          </div>
          <Badge variant="outline" className="text-xs">
            RAG-Powered
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Model Badge */}
          <Badge 
            variant="outline" 
            className={cn("text-xs border", providerInfo.color)}
          >
            <providerInfo.icon className="h-3 w-3 mr-1" />
            {selectedModel}
          </Badge>
          
          {/* Vector DB Status */}
          <Badge
            variant={pineconeStatus.isInitialized ? "default" : "outline"}
            className={cn(
              "text-xs",
              pineconeStatus.isInitialized
                ? "bg-green-100 text-green-800 border-green-200"
                : "bg-gray-100 text-gray-600"
            )}
          >
            <Database className="h-3 w-3 mr-1" />
            {pineconeStatus.recordCount} vectors
          </Badge>

          {/* Embedding Model Info */}
          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
            Cohere v3.0
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 grid grid-cols-12 gap-4 min-h-0">
        {/* Sidebar */}
        <div className="col-span-3 space-y-3">
          {/* Data Sources */}
          <Card className="border-l-4 border-l-primary">
            <CardHeader className="pb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsDataSourcesExpanded(!isDataSourcesExpanded)}
                className="w-full justify-between p-0 h-auto font-medium"
              >
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Data Sources ({totalSources})
                </div>
                {isDataSourcesExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CardHeader>
            
            {isDataSourcesExpanded && (
              <CardContent className="pt-0 space-y-3">
                <CompactDatasetSelector
                  selectedDatasets={selectedDatasets}
                  onSelectDatasets={setSelectedDatasets}
                  onEmbedDataset={embedDataset}
                  onDeleteEmbedding={(datasetId) => deleteEmbedding('dataset', datasetId)}
                  isEmbedding={isEmbedding}
                />
                
                <Separator />
                
                <CompactPDFSelector
                  selectedPDFs={selectedPDFs}
                  onSelectPDFs={setSelectedPDFs}
                  onEmbedPDF={embedExistingPDF} // Use the function for existing PDFs
                  onDeleteEmbedding={(pdfId) => deleteEmbedding('pdf', pdfId)}
                  isEmbedding={isEmbedding}
                />
              </CardContent>
            )}
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader className="pb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSettingsExpanded(!isSettingsExpanded)}
                className="w-full justify-between p-0 h-auto font-medium"
              >
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  AI Settings
                </div>
                {isSettingsExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CardHeader>
            
            {isSettingsExpanded && (
              <CardContent className="pt-0 space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Model</label>
                  <ModelSelector
                    value={selectedModel}
                    onChange={setSelectedModel}
                    disabled={isLoading}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium">Deep Research</label>
                    <p className="text-xs text-muted-foreground">Multi-step analysis</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={useDeepResearch}
                    onChange={(e) => setUseDeepResearch(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </div>
                
                {useDeepResearch && (
                  <div>
                    <label className="text-sm font-medium block mb-1">
                      Iterations: {maxIterations}
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="5"
                      value={maxIterations}
                      onChange={(e) => setMaxIterations(parseInt(e.target.value))}
                      className="w-full"
                    />
                  </div>
                )}
                
                <div>
                  <label className="text-sm font-medium block mb-1">
                    Retrieval Depth: {retrievalK}
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="30"
                    value={retrievalK}
                    onChange={(e) => setRetrievalK(parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              </CardContent>
            )}
          </Card>

          {/* Vector Embedding Visualization */}
          <VectorEmbeddingVisualization
            embeddings={sourceDocuments.map((doc, index) => ({
              id: `doc-${index}`,
              content: doc.content,
              source: doc.metadata?.source === 'pdf' ? 'pdf' : 'dataset',
              sourceName: doc.metadata?.datasetName || doc.metadata?.fileName || 'Unknown',
              similarity: doc.metadata?.similarity || Math.random() * 0.3 + 0.7, // Mock similarity for demo
              metadata: doc.metadata
            }))}
            query={lastQuery}
            isVisible={isVectorVisualizationVisible}
            onToggleVisibility={() => setIsVectorVisualizationVisible(!isVectorVisualizationVisible)}
          />

          {/* Embedding Model Info */}
          <EmbeddingModelInfo />

          {/* Troubleshooting Guide */}
          <TroubleshootingGuide />
        </div>

        {/* Chat Area */}
        <div className="col-span-9">
          <AgenticChatInterface
            messages={chatMessages}
            onSendMessage={sendMessage}
            onClearChat={clearChat}
            isLoading={isLoading}
            disabled={totalSources === 0}
            sourceDocuments={sourceDocuments}
            useDeepResearch={useDeepResearch}
            deepResearchIterations={currentIterations}
            selectedModel={selectedModel}
            onQuerySent={(query) => {
              setLastQuery(query);
              setIsVectorVisualizationVisible(true); // Auto-show when query is sent
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default AgenticRagInterface;
