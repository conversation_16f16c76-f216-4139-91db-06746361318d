"use client"

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  Database, 
  ExternalLink, 
  ChevronDown, 
  ChevronUp,
  Copy,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface SourceDocument {
  content: string;
  metadata: {
    source: string;
    datasetId?: string;
    pdfId?: string;
    datasetName?: string;
    fileName?: string;
    page?: number;
    rowIndex?: number;
    [key: string]: any;
  };
  index: number;
}

interface AgenticSourceReferenceProps {
  sourceDocuments: SourceDocument[];
  className?: string;
}

const AgenticSourceReference: React.FC<AgenticSourceReferenceProps> = ({
  sourceDocuments,
  className
}) => {
  const [expandedSources, setExpandedSources] = useState<Set<number>>(new Set());
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const toggleSource = (index: number) => {
    const newExpanded = new Set(expandedSources);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedSources(newExpanded);
  };

  const copyToClipboard = async (content: string, index: number) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedIndex(index);
      toast.success('Content copied to clipboard');
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      toast.error('Failed to copy content');
    }
  };

  const getSourceIcon = (source: string) => {
    return source === 'pdf' ? FileText : Database;
  };

  const getSourceLabel = (doc: SourceDocument) => {
    const { metadata } = doc;
    if (metadata.source === 'pdf') {
      return {
        title: metadata.fileName || 'PDF Document',
        subtitle: metadata.page ? `Page ${metadata.page}` : undefined,
        color: 'bg-blue-100 text-blue-800 border-blue-200'
      };
    } else {
      return {
        title: metadata.datasetName || 'Dataset',
        subtitle: metadata.rowIndex !== undefined ? `Row ${metadata.rowIndex + 1}` : undefined,
        color: 'bg-green-100 text-green-800 border-green-200'
      };
    }
  };

  if (!sourceDocuments || sourceDocuments.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center gap-2">
        <h4 className="text-sm font-medium">Source References</h4>
        <Badge variant="outline" className="text-xs">
          {sourceDocuments.length} sources
        </Badge>
      </div>
      
      <ScrollArea className="max-h-64">
        <div className="space-y-2">
          {sourceDocuments.map((doc, index) => {
            const sourceInfo = getSourceLabel(doc);
            const SourceIcon = getSourceIcon(doc.metadata.source);
            const isExpanded = expandedSources.has(index);
            
            return (
              <Card key={index} className="border-l-4 border-l-primary/30">
                <div className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <SourceIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium truncate">
                            {sourceInfo.title}
                          </span>
                          <Badge 
                            variant="outline" 
                            className={cn("text-xs border", sourceInfo.color)}
                          >
                            #{doc.index}
                          </Badge>
                        </div>
                        {sourceInfo.subtitle && (
                          <p className="text-xs text-muted-foreground">
                            {sourceInfo.subtitle}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(doc.content, index)}
                        className="h-6 w-6 p-0"
                      >
                        {copiedIndex === index ? (
                          <Check className="h-3 w-3 text-green-500" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleSource(index)}
                        className="h-6 w-6 p-0"
                      >
                        {isExpanded ? (
                          <ChevronUp className="h-3 w-3" />
                        ) : (
                          <ChevronDown className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>
                  
                  {isExpanded && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="bg-muted/30 rounded-md p-3">
                        <p className="text-xs text-muted-foreground leading-relaxed">
                          {doc.content.length > 500 
                            ? `${doc.content.substring(0, 500)}...` 
                            : doc.content
                          }
                        </p>
                      </div>
                      
                      {doc.content.length > 500 && (
                        <div className="mt-2 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(doc.content, index)}
                            className="text-xs"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View Full Content
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Card>
            );
          })}
        </div>
      </ScrollArea>
      
      <div className="text-xs text-muted-foreground">
        <p>
          These sources were used to generate the AI response. Click to expand and view the relevant content.
        </p>
      </div>
    </div>
  );
};

export default AgenticSourceReference;
