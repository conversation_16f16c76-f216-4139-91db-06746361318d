"use client"

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  Sparkles, 
  Zap, 
  Loader2,
  Search,
  BookOpen,
  Lightbulb
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AgenticThinkingIndicatorProps {
  isVisible: boolean;
  iterations?: number;
  model: string;
  className?: string;
}

const AgenticThinkingIndicator: React.FC<AgenticThinkingIndicatorProps> = ({
  isVisible,
  iterations = 0,
  model,
  className
}) => {
  if (!isVisible) return null;

  const getModelIcon = (model: string) => {
    if (model.startsWith('gemini')) return Sparkles;
    if (model.startsWith('command')) return Brain;
    return Zap;
  };

  const getThinkingSteps = (iterations: number) => {
    const steps = [
      { icon: Search, text: "Analyzing your question", active: iterations >= 1 },
      { icon: Book<PERSON><PERSON>, text: "Searching through documents", active: iterations >= 2 },
      { icon: Brain, text: "Processing information", active: iterations >= 3 },
      { icon: Lightbulb, text: "Generating insights", active: iterations >= 4 },
    ];
    return steps;
  };

  const ModelIcon = getModelIcon(model);
  const thinkingSteps = getThinkingSteps(iterations);

  return (
    <div className={cn("flex gap-3 justify-start", className)}>
      <div className="flex-shrink-0">
        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
          <ModelIcon className="h-4 w-4 text-primary animate-pulse" />
        </div>
      </div>
      
      <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20 mr-12">
        <div className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Brain className="h-4 w-4 text-primary animate-pulse" />
            <span className="text-sm font-medium text-primary">Deep Research Mode</span>
            <Badge variant="outline" className="text-xs">
              Step {iterations}/4
            </Badge>
          </div>
          
          <div className="space-y-2">
            {thinkingSteps.map((step, index) => (
              <div
                key={index}
                className={cn(
                  "flex items-center gap-2 text-sm transition-all duration-300",
                  step.active 
                    ? "text-primary opacity-100" 
                    : "text-muted-foreground opacity-50"
                )}
              >
                {step.active ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <step.icon className="h-3 w-3" />
                )}
                <span>{step.text}</span>
                {step.active && (
                  <div className="ml-auto">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-primary rounded-full animate-bounce" />
                      <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-3 pt-3 border-t border-primary/20">
            <p className="text-xs text-muted-foreground">
              The AI is performing multi-step analysis to provide you with the most accurate and comprehensive answer.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AgenticThinkingIndicator;
