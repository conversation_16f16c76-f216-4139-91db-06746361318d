"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar } from '@/components/ui/avatar';
import { Loader2, Send, Trash2, BrainCircuit } from 'lucide-react';
import { LangChainChatMessage } from '@/hooks/useLangChainRag';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/lib/utils';
import SourceReference from './SourceReference';
import ThinkingAnimation from './ThinkingAnimation';

interface ChatInterfaceProps {
  messages: LangChainChatMessage[];
  onSendMessage: (message: string) => Promise<void>;
  onClearChat: () => void;
  isLoading: boolean;
  disabled?: boolean;
  sourceDocuments?: any[];
  useDeepResearch?: boolean;
  deepResearchIterations?: number;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  onClearChat,
  isLoading,
  disabled = false,
  sourceDocuments = [],
  useDeepResearch = false,
  deepResearchIterations = 0
}) => {
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    if (!disabled) {
      inputRef.current?.focus();
    }
  }, [disabled]);

  const handleSendMessage = async () => {
    if (input.trim() && !isLoading && !disabled) {
      const message = input;
      setInput('');
      await onSendMessage(message);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium">AI Assistant</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearChat}
          disabled={messages.length === 0 || isLoading || disabled}
          className="h-8 px-2 text-xs"
        >
          <Trash2 className="h-3 w-3 mr-1" />
          Clear
        </Button>
      </div>

      <div className="flex-1 border rounded-md overflow-hidden flex flex-col">
        <ScrollArea className="flex-1 p-2">
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
              <p>No messages yet. Start a conversation!</p>
              {disabled ? (
                <p className="text-sm mt-2 text-amber-600">
                  Please select and embed a dataset or PDF document first to start chatting.
                </p>
              ) : (
                <p className="text-sm mt-2">
                  Ask questions about your data to get insights with source references.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              {/* Thinking animation for deep research */}
              {isLoading && useDeepResearch && (
                <ThinkingAnimation
                  isVisible={true}
                  iterations={deepResearchIterations}
                  className="ml-10"
                />
              )}

              {messages.map((message, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex items-start gap-2 rounded-lg py-2 px-3",
                    message.role === 'user'
                      ? "bg-primary/10 ml-6"
                      : "bg-muted/40 mr-6"
                  )}
                >
                  <Avatar className="h-6 w-6">
                    <div className={cn(
                      "flex h-full w-full items-center justify-center rounded-full",
                      message.role === 'user' ? "bg-primary" : "bg-muted"
                    )}>
                      {message.role === 'user' ? 'U' : 'AI'}
                    </div>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <div className="prose dark:prose-invert max-w-none prose-sm">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          code({ node, className, children, ...props }: any) {
                            // Safely handle code blocks to prevent regex issues
                            try {
                              const match = /language-(\w+)/.exec(className || '');
                              const isInline = !match;
                              const childrenStr = typeof children === 'string' ? children : String(children || '');
                              
                              // Safely handle newline replacement
                              const sanitizedChildren = childrenStr.endsWith('\n') ? 
                                childrenStr.substring(0, childrenStr.length - 1) : 
                                childrenStr;
                                
                              return !isInline && match ? (
                                <SyntaxHighlighter
                                  language={match[1]}
                                  // @ts-ignore - Known issue with type definitions
                                  style={vscDarkPlus}
                                  PreTag="div"
                                  {...props}
                                >
                                  {sanitizedChildren}
                                </SyntaxHighlighter>
                              ) : (
                                <code className={className} {...props}>
                                  {childrenStr}
                                </code>
                              );
                            } catch (error) {
                              console.error('Error in code renderer:', error);
                              return <code className={className || ''} {...props}>{String(children || '')}</code>;
                            }
                          }
                        }}
                      >
                        {typeof message.content === 'string'
                          ? message.content
                          : JSON.stringify(message.content)}
                      </ReactMarkdown>

                      {/* Show source references for assistant messages */}
                      {message.role === 'assistant' && sourceDocuments.length > 0 && (
                        <SourceReference sourceDocuments={sourceDocuments} />
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>

        <div className="p-2 border-t">
          {disabled && (
            <div className="mb-2 p-1.5 text-xs bg-amber-50/50 dark:bg-amber-950/10 border border-amber-100 dark:border-amber-900/50 rounded-md">
              <p className="text-amber-700 dark:text-amber-300 flex items-center gap-1">
                <BrainCircuit className="h-3 w-3" />
                Select and embed data to start chatting
              </p>
            </div>
          )}
          <div className="flex items-center gap-1.5">
            <Input
              ref={inputRef}
              placeholder={disabled ? "Select a dataset or PDF first..." : "Type your message..."}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isLoading || disabled}
              className="flex-1 text-sm h-8"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading || disabled}
              size="sm"
              className="h-8 px-2"
            >
              {isLoading ? (
                <Loader2 className="h-3.5 w-3.5 animate-spin" />
              ) : (
                <Send className="h-3.5 w-3.5" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
