"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Database,
  Upload,
  Trash2,
  Loader2,
  CheckCircle,
  AlertCircle,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Dataset {
  id: string;
  name: string;
  description?: string;
  rowCount?: number;
  isEmbedded?: boolean;
  embeddingModel?: string;
  createdAt?: string;
}

interface CompactDatasetSelectorProps {
  selectedDatasets: string[];
  onSelectDatasets: (datasets: string[]) => void;
  onEmbedDataset: (datasetId: string) => Promise<boolean>;
  onDeleteEmbedding: (datasetId: string) => Promise<boolean>;
  isEmbedding: boolean;
}

const CompactDatasetSelector: React.FC<CompactDatasetSelectorProps> = ({
  selectedDatasets,
  onSelectDatasets,
  onEmbedDataset,
  onDeleteEmbedding,
  isEmbedding
}) => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch datasets from API
  const fetchDatasets = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/datasets', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch datasets: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && Array.isArray(data.datasets)) {
        setDatasets(data.datasets);
      } else {
        throw new Error(data.error || 'Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch datasets');
      console.error('Error fetching datasets:', err);
      toast.error(`Error fetching datasets: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDatasets();
  }, []);

  const handleDatasetToggle = (datasetId: string) => {
    if (selectedDatasets.includes(datasetId)) {
      onSelectDatasets(selectedDatasets.filter(id => id !== datasetId));
    } else {
      onSelectDatasets([...selectedDatasets, datasetId]);
    }
  };

  const handleEmbedClick = async (datasetId: string) => {
    const success = await onEmbedDataset(datasetId);
    if (success) {
      fetchDatasets(); // Refresh to update embedding status
    }
  };

  const handleDeleteEmbedding = async (datasetId: string) => {
    const success = await onDeleteEmbedding(datasetId);
    if (success) {
      fetchDatasets(); // Refresh to update embedding status
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm text-muted-foreground">Loading datasets...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 py-2 text-sm text-red-600">
        <AlertCircle className="h-4 w-4" />
        <span>{error}</span>
      </div>
    );
  }

  if (datasets.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p>No datasets available</p>
        <p className="text-xs">Upload datasets to get started</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium flex items-center gap-1">
          <BarChart3 className="h-3 w-3" />
          Datasets
        </h4>
        <Badge variant="outline" className="text-xs">
          {datasets.length}
        </Badge>
      </div>
      
      <ScrollArea className="max-h-48">
        <div className="space-y-1">
          {datasets.map((dataset) => (
            <div
              key={dataset.id}
              className={cn(
                "flex items-center gap-2 p-2 rounded-md border transition-colors",
                selectedDatasets.includes(dataset.id)
                  ? "bg-primary/5 border-primary/20"
                  : "hover:bg-muted/50",
                dataset.isEmbedded && "border-l-4 border-l-green-500"
              )}
            >
              <Checkbox
                checked={selectedDatasets.includes(dataset.id)}
                onCheckedChange={() => handleDatasetToggle(dataset.id)}
                className="h-3 w-3"
              />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium truncate">
                    {dataset.name}
                  </span>
                  {dataset.isEmbedded && (
                    <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                  )}
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  {dataset.rowCount && (
                    <span>{dataset.rowCount.toLocaleString()} rows</span>
                  )}
                  {dataset.isEmbedded && dataset.embeddingModel && (
                    <Badge variant="outline" className="text-xs h-4 px-1">
                      Embedded
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-1">
                {dataset.isEmbedded ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteEmbedding(dataset.id)}
                    disabled={isEmbedding}
                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEmbedClick(dataset.id)}
                    disabled={isEmbedding}
                    className="h-6 w-6 p-0 text-blue-500 hover:text-blue-700"
                  >
                    {isEmbedding ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Upload className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
      
      {selectedDatasets.length > 0 && (
        <div className="pt-1">
          <Badge variant="secondary" className="text-xs">
            {selectedDatasets.length} selected
          </Badge>
        </div>
      )}
    </div>
  );
};

export default CompactDatasetSelector;
