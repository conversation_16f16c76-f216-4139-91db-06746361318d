'use client'

import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Database, RefreshCw, Upload, MessageSquare, Trash2, Check, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';

// Define Dataset type locally to avoid import issues
interface Dataset {
  id: string;
  name: string;
  data: any[];
  headers?: string[];
  fileType: string;
  createdAt: string | Date;
  description?: string;
  embedding?: boolean;
  embeddingModel?: string;
}

interface DatasetSelectorProps {
  selectedDatasets: string[];
  onSelectDatasets: (datasetIds: string[]) => void;
  onEmbedDataset: (datasetId: string) => Promise<boolean>;
  onDeleteEmbedding?: (datasetId: string) => Promise<boolean>;
  isEmbedding: boolean;
}

const DatasetSelector: React.FC<DatasetSelectorProps> = ({
  selectedDatasets,
  onSelectDatasets,
  onEmbedDataset,
  onDeleteEmbedding,
  isEmbedding
}) => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch datasets directly from the API
  const fetchDatasets = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/datasets', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch datasets: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.datasets) {
        console.log('Fetched datasets:', data.datasets);
        // Check if datasets have embedding field
        if (data.datasets.length > 0) {
          console.log('First dataset embedding status:', data.datasets[0].embedding);
        }
        setDatasets(data.datasets);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch datasets');
      console.error('Error fetching datasets:', err);
      toast.error(`Error fetching datasets: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch datasets on component mount
  useEffect(() => {
    fetchDatasets();
  }, []);

  const handleEmbedClick = async (datasetId: string) => {
    const success = await onEmbedDataset(datasetId);
    if (success) {
      // Refresh datasets to update embedding status
      fetchDatasets();
    }
  };

  const handleDatasetToggle = (datasetId: string) => {
    if (selectedDatasets.includes(datasetId)) {
      onSelectDatasets(selectedDatasets.filter(id => id !== datasetId));
    } else {
      onSelectDatasets([...selectedDatasets, datasetId]);
    }
  };

  const handleSelectAll = () => {
    const embeddedDatasets = datasets.filter(d => d.embedding === true).map(d => d.id);
    onSelectDatasets(embeddedDatasets);
  };

  const handleClearAll = () => {
    onSelectDatasets([]);
  };

  const handleDeleteEmbeddingClick = async (datasetId: string) => {
    if (onDeleteEmbedding) {
      if (window.confirm('Are you sure you want to delete the embeddings for this dataset? This action cannot be undone.')) {
        const success = await onDeleteEmbedding(datasetId);
        if (success) {
          fetchDatasets();
          toast.success('Dataset embeddings deleted successfully');
        }
      }
    }
  };

  // Count embedded datasets - handle case where embedding field might be missing
  const embeddedDatasetsCount = datasets.filter(d => d.embedding === true).length;

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Database className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-medium">Dataset Selection</h3>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={embeddedDatasetsCount > 0 ? "default" : "outline"}>
            {embeddedDatasetsCount} Embedded
          </Badge>
          <Badge variant={selectedDatasets.length > 0 ? "default" : "outline"}>
            {selectedDatasets.length} Selected
          </Badge>
        </div>
      </div>

      <div className="flex flex-wrap items-center gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                onClick={fetchDatasets}
                disabled={isLoading || isEmbedding}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Refresh datasets</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {embeddedDatasetsCount > 0 && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              disabled={isLoading || isEmbedding}
            >
              <Check className="h-4 w-4 mr-2" />
              Select All
            </Button>
            
            {selectedDatasets.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                disabled={isLoading || isEmbedding}
              >
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}
          </>
        )}
      </div>

      <ScrollArea className="h-[300px] w-full border rounded-md p-4">
        {datasets.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No datasets available</p>
            <p className="text-sm">Upload datasets to get started</p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Embedded datasets first */}
            {datasets.filter(d => d.embedding === true).length > 0 && (
              <>
                <div className="text-sm font-medium text-muted-foreground mb-2">
                  Embedded Datasets (Ready to Use)
                </div>
                {datasets.filter(d => d.embedding === true).map((dataset) => (
                  <div key={dataset.id} className="flex items-start space-x-3 p-3 border rounded-lg bg-green-50/50 dark:bg-green-950/20">
                    <Checkbox
                      id={`dataset-${dataset.id}`}
                      checked={selectedDatasets.includes(dataset.id)}
                      onCheckedChange={() => handleDatasetToggle(dataset.id)}
                      disabled={isLoading || isEmbedding}
                    />
                    <div className="flex-1 min-w-0">
                      <label
                        htmlFor={`dataset-${dataset.id}`}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {dataset.name}
                      </label>
                      {dataset.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {dataset.description}
                        </p>
                      )}
                      <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                        <span>Rows: {Array.isArray(dataset.data) ? dataset.data.length : 0}</span>
                        <span>Columns: {dataset.headers?.length || 0}</span>
                        <span>Type: {dataset.fileType}</span>
                      </div>
                      <Badge variant="outline" className="mt-2 bg-green-100 text-green-800 text-xs">
                        Embedded with {dataset.embeddingModel || 'Cohere'}
                      </Badge>
                    </div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteEmbeddingClick(dataset.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Delete embeddings</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                ))}
              </>
            )}

            {/* Non-embedded datasets */}
            {datasets.filter(d => d.embedding !== true).length > 0 && (
              <>
                {datasets.filter(d => d.embedding === true).length > 0 && (
                  <div className="border-t pt-3 mt-3" />
                )}
                <div className="text-sm font-medium text-muted-foreground mb-2">
                  Other Datasets (Need Embedding)
                </div>
                {datasets.filter(d => d.embedding !== true).map((dataset) => (
                  <div key={dataset.id} className="flex items-start space-x-3 p-3 border rounded-lg bg-muted/20">
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-muted-foreground">
                        {dataset.name}
                      </div>
                      {dataset.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {dataset.description}
                        </p>
                      )}
                      <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                        <span>Rows: {Array.isArray(dataset.data) ? dataset.data.length : 0}</span>
                        <span>Columns: {dataset.headers?.length || 0}</span>
                        <span>Type: {dataset.fileType}</span>
                      </div>
                    </div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => handleEmbedClick(dataset.id)}
                            disabled={isEmbedding || isLoading}
                          >
                            {isEmbedding ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Embedding...
                              </>
                            ) : (
                              <>
                                <Upload className="h-4 w-4 mr-2" />
                                Embed
                              </>
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Embed this dataset to make it searchable</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                ))}
              </>
            )}
          </div>
        )}
      </ScrollArea>

      {/* Selected datasets summary */}
      {selectedDatasets.length > 0 && (
        <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="text-sm font-medium mb-2">
            Selected Datasets ({selectedDatasets.length})
          </div>
          <div className="flex flex-wrap gap-1">
            {selectedDatasets.map(datasetId => {
              const dataset = datasets.find(d => d.id === datasetId);
              return dataset ? (
                <Badge key={datasetId} variant="secondary" className="text-xs">
                  {dataset.name}
                </Badge>
              ) : null;
            })}
          </div>
        </div>
      )}

      {error && (
        <div className="text-sm text-red-500 p-2 border border-red-200 rounded-md bg-red-50">
          {error}
        </div>
      )}
    </div>
  );
};

export default DatasetSelector;
