"use client";

import React, { useState } from 'react';
import {
  Hover<PERSON>ard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Database,
  ExternalLink,
  Copy,
  Check,
  Maximize2,
  X,
  Info
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SourceReferenceProps {
  sourceDocuments: any[];
}

const SourceReference: React.FC<SourceReferenceProps> = ({ sourceDocuments }) => {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [openDialogIndex, setOpenDialogIndex] = useState<number | null>(null);

  if (!sourceDocuments || sourceDocuments.length === 0) {
    return null;
  }

  // Get unique source names for summary
  const uniqueSources = new Set<string>();
  sourceDocuments.forEach(doc => {
    const source = doc.metadata?.source || 'unknown';
    if (source === 'dataset' && doc.metadata?.datasetName) {
      uniqueSources.add(`Dataset: ${doc.metadata.datasetName}`);
    } else if (source === 'pdf' && doc.metadata?.fileName) {
      uniqueSources.add(`PDF: ${doc.metadata.fileName}`);
    }
  });

  const sourceSummary = Array.from(uniqueSources).join(', ');

  // Function to copy content to clipboard
  const copyToClipboard = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <>
      <div className="mt-2 space-y-2">
        <div className="text-xs text-muted-foreground bg-muted/30 p-1.5 rounded border border-muted">
          <span className="font-medium">Sources used:</span> {sourceSummary || 'Unknown sources'}
        </div>
        <div className="flex flex-wrap gap-1.5">
          <span className="text-xs text-muted-foreground mr-1 mt-1">References:</span>
          {sourceDocuments.map((doc, index) => {
          const source = doc.metadata?.source || 'unknown';
          const isDataset = source === 'dataset';
          const isPDF = source === 'pdf';

          let sourceLabel = '';
          let sourceDetail = '';

          if (isDataset) {
            sourceLabel = `Dataset: ${doc.metadata?.datasetName || 'Unknown'}`;
            sourceDetail = `Row ${doc.metadata?.rowIndex !== undefined ? doc.metadata.rowIndex + 1 : 'N/A'}`;
          } else if (isPDF) {
            sourceLabel = `PDF: ${doc.metadata?.fileName || 'Unknown'}`;
            sourceDetail = `Page ${doc.metadata?.page || 'N/A'}`;
          } else {
            sourceLabel = `Source ${doc.index}`;
            sourceDetail = source;
          }

          return (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-muted flex items-center gap-1 text-xs"
                    onClick={() => setOpenDialogIndex(index)}
                  >
                    {isDataset ? (
                      <Database className="h-3 w-3" />
                    ) : isPDF ? (
                      <FileText className="h-3 w-3" />
                    ) : (
                      <ExternalLink className="h-3 w-3" />
                    )}
                    {doc.index}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-sm p-0">
                  <div className="bg-popover p-3 rounded-md shadow-md border">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="text-sm font-semibold">{sourceLabel}</h4>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => copyToClipboard(doc.content, index)}
                        >
                          {copiedIndex === index ? (
                            <Check className="h-3 w-3" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => setOpenDialogIndex(index)}
                        >
                          <Maximize2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">{sourceDetail}</p>
                    <div className="text-xs border-l-2 border-muted-foreground/50 pl-2 py-1 mt-1 bg-muted/50 rounded-sm max-h-40 overflow-y-auto">
                      {doc.content.substring(0, 200)}{doc.content.length > 200 ? '...' : ''}
                    </div>
                    <div className="mt-2 text-xs text-center text-muted-foreground">
                      <Info className="h-3 w-3 inline mr-1" />
                      Click badge to view full content
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
        </div>
      </div>

      {/* Full-screen dialog for viewing source content */}
      {sourceDocuments.map((doc, index) => {
        const source = doc.metadata?.source || 'unknown';
        const isDataset = source === 'dataset';
        const isPDF = source === 'pdf';

        let sourceLabel = '';
        let sourceDetail = '';

        if (isDataset) {
          sourceLabel = `Dataset: ${doc.metadata?.datasetName || 'Unknown'}`;
          sourceDetail = `Row ${doc.metadata?.rowIndex !== undefined ? doc.metadata.rowIndex + 1 : 'N/A'}`;
        } else if (isPDF) {
          sourceLabel = `PDF: ${doc.metadata?.fileName || 'Unknown'}`;
          sourceDetail = `Page ${doc.metadata?.page || 'N/A'}`;
        } else {
          sourceLabel = `Source ${doc.index}`;
          sourceDetail = source;
        }

        return (
          <Dialog key={`dialog-${index}`} open={openDialogIndex === index} onOpenChange={(open) => {
            if (!open) setOpenDialogIndex(null);
          }}>
            <DialogContent className="max-w-3xl max-h-[80vh]">
              <DialogHeader>
                <div className="flex justify-between items-center">
                  <DialogTitle>{sourceLabel} - {sourceDetail}</DialogTitle>
                  <DialogClose asChild>
                    <Button variant="ghost" size="icon">
                      <X className="h-4 w-4" />
                    </Button>
                  </DialogClose>
                </div>
              </DialogHeader>
              <div className="mt-4 overflow-y-auto max-h-[60vh]">
                <div className="flex justify-end mb-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => copyToClipboard(doc.content, index + 100)}
                  >
                    {copiedIndex === index + 100 ? "Copied!" : "Copy Content"}
                    {copiedIndex === index + 100 ? (
                      <Check className="h-3 w-3 ml-2" />
                    ) : (
                      <Copy className="h-3 w-3 ml-2" />
                    )}
                  </Button>
                </div>
                <div className="border rounded-md p-4 bg-muted/20 whitespace-pre-wrap text-sm">
                  {doc.content}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        );
      })}
    </>
  );
};

export default SourceReference;
