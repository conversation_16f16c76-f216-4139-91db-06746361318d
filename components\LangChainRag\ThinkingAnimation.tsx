'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ThinkingAnimationProps {
  isVisible: boolean;
  iterations?: number;
  className?: string;
}

const ThinkingAnimation: React.FC<ThinkingAnimationProps> = ({
  isVisible,
  iterations = 0,
  className
}) => {
  if (!isVisible) return null;

  // Animation variants for the dots
  const dotVariants = {
    initial: { opacity: 0.3, y: 0 },
    animate: { opacity: 1, y: -5 }
  };

  // Animation variants for the thinking container
  const containerVariants = {
    initial: { opacity: 0, height: 0 },
    animate: { opacity: 1, height: 'auto' },
    exit: { opacity: 0, height: 0 }
  };

  return (
    <motion.div
      className={cn(
        "bg-muted/30 rounded-lg p-3 mb-3 border border-muted",
        className
      )}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={containerVariants}
      transition={{ duration: 0.3 }}
    >
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <span className="text-sm font-medium">Thinking</span>
            <div className="flex ml-2 space-x-1">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-1.5 h-1.5 bg-primary rounded-full"
                  variants={dotVariants}
                  initial="initial"
                  animate="animate"
                  transition={{
                    repeat: Infinity,
                    repeatType: "reverse",
                    duration: 0.4,
                    delay: i * 0.15
                  }}
                />
              ))}
            </div>
          </div>
        </div>
        
        {iterations > 0 && (
          <div className="text-xs text-muted-foreground">
            <div className="flex justify-between items-center">
              <span>Deep research in progress</span>
              <span className="font-medium">{iterations} iterations</span>
            </div>
            <div className="w-full bg-muted h-1.5 rounded-full mt-1 overflow-hidden">
              <motion.div
                className="h-full bg-primary rounded-full"
                initial={{ width: "5%" }}
                animate={{ width: "100%" }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut"
                }}
              />
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ThinkingAnimation;
