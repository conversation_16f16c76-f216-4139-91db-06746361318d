"use client"

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  ChevronDown, 
  ChevronUp, 
  CheckCircle, 
  XCircle,
  Database,
  Upload,
  Brain,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TroubleshootingGuideProps {
  className?: string;
}

const TroubleshootingGuide: React.FC<TroubleshootingGuideProps> = ({ className }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const troubleshootingSteps = [
    {
      issue: "Getting 'No relevant information found' error",
      icon: XCircle,
      color: "text-red-500",
      solutions: [
        {
          step: "Check Data Sources",
          description: "Ensure you have selected and embedded at least one dataset or PDF",
          action: "Go to sidebar → Select datasets/PDFs → Click upload button to embed"
        },
        {
          step: "Verify Embedding Status", 
          description: "Look for green checkmarks and 'Embedded' badges next to your data sources",
          action: "If not embedded, click the upload button next to each data source"
        },
        {
          step: "Check Query Relevance",
          description: "Make sure your question relates to the content in your embedded data",
          action: "Try broader questions or check if your data contains the information you're looking for"
        }
      ]
    },
    {
      issue: "Gemini models not working properly",
      icon: AlertTriangle,
      color: "text-amber-500",
      solutions: [
        {
          step: "Use Recommended Models",
          description: "Gemini integration is in progress. Use Cohere Command-R+ for best results",
          action: "Select 'Cohere Command-R+' from the model dropdown for optimal performance"
        },
        {
          step: "Check Model Compatibility",
          description: "Some models may have different response formats",
          action: "If issues persist, switch to 'Cohere Command-R' or 'Together AI' models"
        }
      ]
    },
    {
      issue: "Large datasets not being analyzed properly",
      icon: Database,
      color: "text-blue-500",
      solutions: [
        {
          step: "Increase Retrieval Depth",
          description: "Adjust the retrieval depth setting for better coverage",
          action: "Go to AI Settings → Increase 'Retrieval Depth' to 25-30 for large datasets"
        },
        {
          step: "Enable Deep Research",
          description: "Use deep research mode for complex analysis",
          action: "Go to AI Settings → Enable 'Deep Research' → Set iterations to 3-4"
        },
        {
          step: "Break Down Complex Queries",
          description: "Split complex questions into smaller, specific queries",
          action: "Ask one specific question at a time for better accuracy"
        }
      ]
    },
    {
      issue: "Slow response times",
      icon: Zap,
      color: "text-purple-500",
      solutions: [
        {
          step: "Optimize Model Selection",
          description: "Use faster models for quicker responses",
          action: "Try 'Cohere Command-R' (faster) instead of 'Command-R+' (more accurate)"
        },
        {
          step: "Reduce Retrieval Depth",
          description: "Lower retrieval depth for faster processing",
          action: "Set retrieval depth to 10-15 for faster responses"
        },
        {
          step: "Disable Deep Research",
          description: "Turn off deep research for simple queries",
          action: "Disable 'Deep Research' in AI Settings for faster responses"
        }
      ]
    }
  ];

  return (
    <Card className={cn("border-l-4 border-l-amber-500", className)}>
      <CardHeader className="pb-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full justify-between p-0 h-auto font-medium"
        >
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Troubleshooting Guide
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0 space-y-4">
          <div className="text-sm text-muted-foreground">
            <p className="mb-3">
              Common issues and solutions for the RAG system. Follow these steps to resolve problems.
            </p>
          </div>
          
          <div className="space-y-4">
            {troubleshootingSteps.map((item, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-start gap-2 mb-3">
                  <item.icon className={cn("h-4 w-4 mt-0.5 flex-shrink-0", item.color)} />
                  <h4 className="text-sm font-medium">{item.issue}</h4>
                </div>
                
                <div className="space-y-3">
                  {item.solutions.map((solution, sIndex) => (
                    <div key={sIndex} className="bg-muted/30 rounded-md p-3">
                      <div className="flex items-start gap-2 mb-2">
                        <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                        <h5 className="text-xs font-medium">{solution.step}</h5>
                      </div>
                      <p className="text-xs text-muted-foreground mb-2">
                        {solution.description}
                      </p>
                      <div className="bg-blue-50 dark:bg-blue-950/20 rounded p-2">
                        <p className="text-xs text-blue-800 dark:text-blue-200">
                          <strong>Action:</strong> {solution.action}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <h4 className="font-medium text-green-900 dark:text-green-100 mb-1">
                  Best Practices for Large Datasets
                </h4>
                <ul className="text-green-800 dark:text-green-200 text-xs space-y-1">
                  <li>• Always embed your datasets before asking questions</li>
                  <li>• Use specific, focused questions for better accuracy</li>
                  <li>• Enable deep research for complex analytical queries</li>
                  <li>• Check the source references to verify answer accuracy</li>
                  <li>• Use Cohere Command-R+ model for best results with business data</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="text-xs text-muted-foreground">
            <p>
              <strong>Still having issues?</strong> Check the browser console (F12) for detailed error messages 
              or try refreshing the page and re-embedding your data sources.
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default TroubleshootingGuide;
