import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Eye, 
  EyeOff, 
  Database, 
  FileText, 
  Search, 
  Zap,
  Activity,
  BarChart3,
  Layers
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface EmbeddingData {
  id: string;
  content: string;
  source: 'dataset' | 'pdf';
  sourceName: string;
  similarity?: number;
  vector?: number[];
  metadata?: Record<string, any>;
}

interface VectorEmbeddingVisualizationProps {
  embeddings: EmbeddingData[];
  query?: string;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  className?: string;
}

const VectorEmbeddingVisualization: React.FC<VectorEmbeddingVisualizationProps> = ({
  embeddings = [],
  query = '',
  isVisible = true,
  onToggleVisibility,
  className
}) => {
  const [selectedEmbedding, setSelectedEmbedding] = useState<string | null>(null);
  const [searchProgress, setSearchProgress] = useState(0);
  const [isSearching, setIsSearching] = useState(false);

  // Simulate search progress when query changes
  useEffect(() => {
    if (query) {
      setIsSearching(true);
      setSearchProgress(0);
      
      const interval = setInterval(() => {
        setSearchProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsSearching(false);
            return 100;
          }
          return prev + 10;
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [query]);

  // Calculate statistics
  const stats = {
    totalEmbeddings: embeddings.length,
    datasetEmbeddings: embeddings.filter(e => e.source === 'dataset').length,
    pdfEmbeddings: embeddings.filter(e => e.source === 'pdf').length,
    avgSimilarity: embeddings.length > 0 
      ? embeddings.reduce((sum, e) => sum + (e.similarity || 0), 0) / embeddings.length 
      : 0
  };

  // Get top embeddings by similarity
  const topEmbeddings = embeddings
    .filter(e => e.similarity !== undefined)
    .sort((a, b) => (b.similarity || 0) - (a.similarity || 0))
    .slice(0, 5);

  if (!isVisible) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Button
          variant="outline"
          size="sm"
          onClick={onToggleVisibility}
          className="flex items-center gap-2"
        >
          <Eye className="h-4 w-4" />
          Show Vector Analysis
        </Button>
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Layers className="h-5 w-5 text-blue-500" />
            Vector Embedding Analysis
          </CardTitle>
          {onToggleVisibility && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleVisibility}
              className="flex items-center gap-2"
            >
              <EyeOff className="h-4 w-4" />
              Hide
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search Progress */}
        {isSearching && query && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Search className="h-4 w-4 animate-pulse" />
              Searching embeddings for: "{query.substring(0, 50)}..."
            </div>
            <Progress value={searchProgress} className="h-2" />
          </div>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <Database className="h-4 w-4 text-blue-500" />
            <div>
              <div className="text-sm font-medium">{stats.totalEmbeddings}</div>
              <div className="text-xs text-muted-foreground">Total Vectors</div>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
            <BarChart3 className="h-4 w-4 text-green-500" />
            <div>
              <div className="text-sm font-medium">{stats.datasetEmbeddings}</div>
              <div className="text-xs text-muted-foreground">Datasets</div>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
            <FileText className="h-4 w-4 text-purple-500" />
            <div>
              <div className="text-sm font-medium">{stats.pdfEmbeddings}</div>
              <div className="text-xs text-muted-foreground">PDFs</div>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-950/20 rounded-lg">
            <Activity className="h-4 w-4 text-amber-500" />
            <div>
              <div className="text-sm font-medium">{(stats.avgSimilarity * 100).toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">Avg Similarity</div>
            </div>
          </div>
        </div>

        {/* Top Matching Embeddings */}
        {topEmbeddings.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              Top Matching Vectors
            </h4>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {topEmbeddings.map((embedding, index) => (
                <div
                  key={embedding.id}
                  className={cn(
                    "p-3 border rounded-lg cursor-pointer transition-colors",
                    selectedEmbedding === embedding.id 
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20" 
                      : "border-border hover:border-blue-300"
                  )}
                  onClick={() => setSelectedEmbedding(
                    selectedEmbedding === embedding.id ? null : embedding.id
                  )}
                >
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge 
                          variant={embedding.source === 'dataset' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {embedding.source === 'dataset' ? (
                            <Database className="h-3 w-3 mr-1" />
                          ) : (
                            <FileText className="h-3 w-3 mr-1" />
                          )}
                          {embedding.sourceName}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          #{index + 1}
                        </span>
                      </div>
                      
                      <p className="text-sm text-foreground line-clamp-2">
                        {embedding.content.substring(0, 120)}
                        {embedding.content.length > 120 && '...'}
                      </p>
                    </div>
                    
                    <div className="flex flex-col items-end gap-1">
                      <div className="text-sm font-medium text-green-600">
                        {((embedding.similarity || 0) * 100).toFixed(1)}%
                      </div>
                      <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-green-500 transition-all duration-300"
                          style={{ width: `${(embedding.similarity || 0) * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Expanded Details */}
                  {selectedEmbedding === embedding.id && (
                    <div className="mt-3 pt-3 border-t border-border space-y-2">
                      <div className="text-xs text-muted-foreground">
                        <strong>Full Content:</strong>
                      </div>
                      <p className="text-sm bg-gray-50 dark:bg-gray-900 p-2 rounded text-foreground">
                        {embedding.content}
                      </p>
                      
                      {embedding.metadata && Object.keys(embedding.metadata).length > 0 && (
                        <div className="text-xs space-y-1">
                          <div className="text-muted-foreground">
                            <strong>Metadata:</strong>
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded">
                            {Object.entries(embedding.metadata).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span className="text-muted-foreground">{key}:</span>
                                <span className="text-foreground">{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No Results */}
        {embeddings.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Database className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No vector embeddings found</p>
            <p className="text-xs">Select datasets or PDFs to see embedding analysis</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VectorEmbeddingVisualization;
