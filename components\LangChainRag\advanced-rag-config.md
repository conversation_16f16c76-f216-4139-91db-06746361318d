# Advanced RAG System Configuration

This document outlines the advanced Retrieval-Augmented Generation (RAG) techniques implemented in the HRatlas system to improve search accuracy and ensure the LLM can find specific keys in embedded data.

## Current Implementation

### Embedding Models
- **Primary Model**: Cohere's `embed-english-v3.0`
- **Configuration**: 
  - Document embeddings: `inputType: 'search_document'`
  - Query embeddings: `inputType: 'search_query'`
  - Format: `embeddingFormat: 'float'` for better precision

### Vector Database
- **Provider**: Pinecone
- **Namespace**: 'adeloop'
- **Index**: 'adeloop'

### LLM Providers
- Cohere: 'command-r', 'command-r-plus'
- TogetherAI: 'llama-3-70b', 'llama-3-8b', 'mixtral-8x7b', 'mistral-7b', 'qwen-72b', 'meta'

## Advanced RAG Enhancements

### 1. Hybrid Search
- **Implementation**: Combined vector similarity search with BM25 keyword search
- **Benefits**: Better handling of exact keyword matches alongside semantic similarity
- **Scoring**: Vector results weighted 1.5x higher than keyword results
- **Deduplication**: Results from both methods are combined and deduplicated

### 2. Query Expansion
- **Implementation**: LLM generates alternative phrasings of the original query
- **Process**: 
  - Original query is expanded into 3 alternative formulations
  - Each expanded query is searched separately
  - Results are combined and deduplicated
- **Benefits**: Captures different semantic aspects of the same information need

### 3. Document Re-ranking
- **Implementation**: LLM-based relevance scoring of retrieved documents
- **Process**:
  - Top retrieved documents are scored on a scale of 0-10 for relevance
  - Documents are re-ordered based on these scores
- **Benefits**: Improves precision by prioritizing the most relevant documents

### 4. Enhanced Document Processing
- **CSV/Dataset Processing**:
  - Identification of key columns (IDs, names, codes)
  - Extraction of key values for better retrieval
  - Chunking of large rows with overlap
  - Rich metadata including row titles and key values

- **PDF Processing**:
  - Content structure analysis (text vs. structured content)
  - Different chunking strategies based on content type
  - Feature extraction (numbers, names, dates)
  - Content previews for better filtering

### 5. Deep Research Mode
- **Implementation**: Multi-iteration research process
- **Process**:
  - Generate sub-questions from initial results
  - Perform separate searches for each sub-question
  - Accumulate context across iterations
  - Final synthesis using all accumulated information

## Future Improvement Opportunities

### 1. Multi-Vector Retrieval
- Store multiple embeddings per document (title, content, key fields)
- Query against each embedding type and combine results

### 2. Semantic Routing
- Analyze query intent to determine optimal retrieval strategy
- Route to specialized retrievers based on query type

### 3. Self-Query Mechanism
- Enable the LLM to generate structured filters for the vector database
- Convert natural language constraints to metadata filters

### 4. Contextual Compression
- Implement a compression step to remove irrelevant parts of retrieved documents
- Focus the context window on the most important information

### 5. Retrieval Augmentation Strategies
- Implement RAG fusion techniques (reciprocal rank fusion)
- Add query-by-query retrieval for complex questions

## Usage Guidelines

1. **For Exact Matches**: The hybrid search should now handle exact keyword matches much better. Use specific terms when searching for IDs or exact values.

2. **For Complex Queries**: Enable Deep Research mode for multi-step reasoning questions that require connecting information across multiple documents.

3. **For Data Analysis**: When analyzing CSV data, include specific column names or IDs in your query to help the system locate the relevant rows.

4. **For PDF Documents**: Be specific about the section or type of information you're looking for (e.g., tables, dates, names).

5. **Troubleshooting**: If the system still struggles to find specific information:
   - Try rephrasing your query with more specific keywords
   - Include exact identifiers if known
   - Break complex questions into simpler sub-questions
