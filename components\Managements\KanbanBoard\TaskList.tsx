"use client"

import { useState } from "react"
import { 
  <PERSON><PERSON>, 
  Avatar<PERSON><PERSON>back, 
  AvatarImage 
} from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  <PERSON><PERSON>ooter, 
  Card<PERSON>eader 
} from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Task, Priority, User } from "./types"
import { 
  CalendarClock, 
  Layers, 
  SlidersHorizontal,
  Edit,
  Trash,
  GitBranch,
  Clock
} from "lucide-react"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { format } from "date-fns"

interface TaskListProps {
  allTasks: Array<Task & { columnId: string; columnTitle: string }>
  filteredTasks: Array<Task & { columnId: string; columnTitle: string }>
  handleEditTask: (taskId: string, updates: Partial<Task>) => void
  handleDeleteTask: (taskId: string) => void
  setViewMode: (mode: "kanban" | "list") => void
  filterPriority: Priority | "all"
  users: User[]
}

export function TaskList({
  allTasks,
  filteredTasks,
  handleEditTask,
  handleDeleteTask,
  setViewMode,
  filterPriority,
  users
}: TaskListProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedTask, setEditedTask] = useState<Task & { columnId: string; columnTitle: string } | null>(null)
  const [sprintFilter, setSprintFilter] = useState<"all" | "current" | "backlog">("all")
  
  // Filter tasks by sprint
  const sprintFilteredTasks = filteredTasks.filter(task => {
    if (sprintFilter === "all") return true;
    return task.sprint === sprintFilter;
  });
  
  const openEditDialog = (task: Task & { columnId: string; columnTitle: string }) => {
    setEditedTask(task);
    setIsEditing(true);
  }
  
  const handleEdit = () => {
    if (!editedTask) return;
    handleEditTask(editedTask.id, editedTask);
    setIsEditing(false);
  };

  return (
    <>
      <Card>
        <CardHeader className="px-6 py-4 flex flex-row items-center justify-between space-y-0">
          <div className="space-y-1.5">
            <h3 className="font-semibold leading-none tracking-tight">Task Overview</h3>
            <p className="text-sm text-muted-foreground">
              Showing {sprintFilteredTasks.length} of {allTasks.length} tasks
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select 
              value={sprintFilter}
              onValueChange={(value) => setSprintFilter(value as "all" | "current" | "backlog")}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Tasks" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tasks</SelectItem>
                <SelectItem value="current">
                  <div className="flex items-center">
                    <GitBranch className="h-4 w-4 mr-2" />
                    Current Sprint
                  </div>
                </SelectItem>
                <SelectItem value="backlog">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Backlog
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <CalendarClock className="h-4 w-4 mr-2" />
              Timeline
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[calc(100vh-350px)]">
            <div className="space-y-0 divide-y">
              {sprintFilteredTasks.length === 0 ? (
                <div className="py-12 text-center">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                    <Layers className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="mt-4 text-lg font-semibold">No tasks found</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Try adjusting your search or filter to find what you're looking for.
                  </p>
                </div>
              ) : (
                sprintFilteredTasks.map(task => {
                  const isOverdue = new Date(task.dueDate) < new Date();
                  return (
                    <div key={task.id} className="task-list-item p-4 flex gap-4">
                      <div className="flex flex-col items-center mt-1">
                        <div className={`priority-dot ${task.priority}`} />
                        <div className="flex-1 w-px bg-border my-2"></div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-wrap gap-2 items-start justify-between mb-1">
                          <h4 className="font-medium text-base">{task.title}</h4>
                          <div className="flex gap-2 items-center">
                            {task.sprint && (
                              <Badge variant="outline" className={`${task.sprint === 'current' ? 'bg-primary/10 text-primary' : 'bg-secondary/10 text-secondary'}`}>
                                {task.sprint === 'current' ? '🏃 Sprint' : '📋 Backlog'}
                              </Badge>
                            )}
                            <Badge variant="outline" className="task-list-status">
                              {task.columnTitle}
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-sm text-muted-foreground mb-2">
                          {task.description}
                        </p>
                        
                        <div className="flex flex-wrap gap-x-4 gap-y-2 mt-3 text-sm">
                          <div className="flex items-center gap-1">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={task.assignee.avatar} alt={task.assignee.name} />
                              <AvatarFallback>{task.assignee.name[0]}</AvatarFallback>
                            </Avatar>
                            <span className="text-xs">{task.assignee.name}</span>
                          </div>
                          
                          <div className={`due-date flex items-center gap-1 ${isOverdue ? 'overdue' : ''}`}>
                            <CalendarClock className="h-3.5 w-3.5" />
                            <span>
                              {task.dueDate.toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 ml-auto">
                            <Button
                              variant="ghost"
                              size="icon" 
                              className="h-8 w-8"
                              onClick={() => openEditDialog(task)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-destructive"
                              onClick={() => handleDeleteTask(task.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </ScrollArea>
        </CardContent>
        <CardFooter className="border-t bg-muted/50 p-4">
          <div className="flex flex-wrap justify-between w-full">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-background">
                {sprintFilteredTasks.filter(t => t.priority === 'high').length} High
              </Badge>
              <Badge variant="outline" className="bg-background">
                {sprintFilteredTasks.filter(t => t.priority === 'medium').length} Medium
              </Badge>
              <Badge variant="outline" className="bg-background">
                {sprintFilteredTasks.filter(t => t.priority === 'low').length} Low
              </Badge>
            </div>
            <Button size="sm" onClick={() => setViewMode("kanban")}>
              Switch to Kanban
            </Button>
          </div>
        </CardFooter>
      </Card>
      
      {/* Task Edit Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>
          {editedTask && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  placeholder="Task title"
                  value={editedTask.title}
                  onChange={(e) => setEditedTask({ ...editedTask, title: e.target.value })}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  placeholder="Description"
                  value={editedTask.description}
                  onChange={(e) => setEditedTask({ ...editedTask, description: e.target.value })}
                  className="min-h-[100px]"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-priority">Priority</Label>
                  <Select
                    value={editedTask.priority}
                    onValueChange={(value: Priority) => 
                      setEditedTask({ ...editedTask, priority: value })
                    }
                  >
                    <SelectTrigger id="edit-priority">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="edit-type">Type</Label>
                  <Select
                    value={editedTask.type || 'task'}
                    onValueChange={(value) => 
                      setEditedTask({ ...editedTask, type: value as any })
                    }
                  >
                    <SelectTrigger id="edit-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="task">Task</SelectItem>
                      <SelectItem value="feature">Feature</SelectItem>
                      <SelectItem value="bug">Bug</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-sprint">Sprint</Label>
                  <Select
                    value={editedTask.sprint || 'none'}
                    onValueChange={(value) => 
                      setEditedTask({ ...editedTask, sprint: value as any })
                    }
                  >
                    <SelectTrigger id="edit-sprint">
                      <SelectValue placeholder="Select sprint" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="current">Current Sprint</SelectItem>
                      <SelectItem value="backlog">Backlog</SelectItem>
                      <SelectItem value="next">Next Sprint</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="edit-assignee">Assignee</Label>
                  <Select
                    value={editedTask.assignee.id}
                    onValueChange={(value) => {
                      const selectedUser = users.find(user => user.id === value);
                      if (selectedUser) {
                        setEditedTask({ 
                          ...editedTask, 
                          assignee: selectedUser
                        });
                      }
                    }}
                  >
                    <SelectTrigger id="edit-assignee">
                      <SelectValue placeholder="Select assignee" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit-dueDate">Due Date</Label>
                <Input
                  id="edit-dueDate"
                  type="date"
                  value={editedTask.dueDate instanceof Date ? 
                    format(editedTask.dueDate, 'yyyy-MM-dd') : 
                    format(new Date(), 'yyyy-MM-dd')
                  }
                  onChange={(e) => setEditedTask({ 
                    ...editedTask, 
                    dueDate: new Date(e.target.value) 
                  })}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
} 