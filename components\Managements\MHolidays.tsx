"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ChevronLeft, ChevronRight, Plus, Bell, Briefcase, GraduationCap, Coffee, Users, Trash2, <PERSON>, FileText, GitBranch, Users2, PlaneLanding, StickyNote, CheckSquare, MessageSquare, Activity, Calendar } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getHolidays } from "@/actions/calendar"
import { toast } from "sonner"
import { Badge } from '../ui/badge'
import { isSameDay, format } from 'date-fns'
import { getSavedDiagrams } from "@/app/actions/diagram"
import { getNotes } from "@/actions/actions"
import { cn } from '@/lib/utils'

interface Holiday {
  date: Date
  name: string
  type: 'fixed' | 'islamic'
}

interface Event {
  id: string
  title: string
  description: string
  date: Date
  type: 'event' | 'note' | 'workflow'
  priority: 'low' | 'medium' | 'high'
  reminder: boolean
  status: 'pending' | 'completed'
  createdAt: Date
}

const MHolidays = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [holidays, setHolidays] = useState<Holiday[]>([])
  const [workflowDates, setWorkflowDates] = useState<{ title: string, createdAt: Date }[]>([])
  const [notes, setNotes] = useState<{ title: string, createdAt: Date }[]>([])
  const [events, setEvents] = useState<Event[]>([])
  const [view, setView] = useState<'month' | 'week'>('month')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [selectedTab, setSelectedTab] = useState<'calendar' | 'events'>('calendar')
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null)
  const [newEvent, setNewEvent] = useState<Event>({
    id: '',
    title: '',
    description: '',
    date: new Date(),
    type: 'event',
    priority: 'medium',
    reminder: true,
    status: 'pending',
    createdAt: new Date()
  })
  const [upcomingEvents, setUpcomingEvents] = useState<Array<Event & { timeLeft: string }>>([])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const year = currentDate.getFullYear()
        
        // Fetch holidays
        const holidaysList = await getHolidays(year)
        setHolidays(holidaysList)

        // Fetch workflow dates
        const workflowResult = await getSavedDiagrams()
        if (workflowResult.success && workflowResult.data) {
          setWorkflowDates(workflowResult.data.map(diagram => ({
            title: diagram.title,
            createdAt: new Date(diagram.createdAt)
          })))
        }

        // Fetch notes
        const notesResult = await getNotes()
        if (notesResult.success && notesResult.notes) {
          setNotes(notesResult.notes.map(note => ({
            title: note.title,
            createdAt: new Date(note.createdAt)
          })))
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Failed to load calendar data')
      }
    }

    fetchData()
  }, [currentDate])

  useEffect(() => {
    // Load events from localStorage
    const loadEvents = () => {
      const savedEvents = localStorage.getItem('events')
      if (savedEvents) {
        const parsedEvents = JSON.parse(savedEvents).map((event: any) => ({
          ...event,
          date: new Date(event.date),
          createdAt: new Date(event.createdAt)
        }))
        setEvents(parsedEvents)
      }
    }

    loadEvents()
  }, [])

  useEffect(() => {
    const updateUpcomingEvents = () => {
      const now = new Date()
      const upcoming = events
        .filter(event => {
          const eventDate = new Date(event.date)
          const timeDiff = eventDate.getTime() - now.getTime()
          const hoursDiff = timeDiff / (1000 * 3600)
          return event.reminder && hoursDiff > 0 && hoursDiff <= 24
        })
        .map(event => {
          const eventDate = new Date(event.date)
          const timeDiff = eventDate.getTime() - now.getTime()
          const hours = Math.floor(timeDiff / (1000 * 60 * 60))
          const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
          const timeLeft = `${hours}h ${minutes}m`
          return { ...event, timeLeft }
        })
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

      setUpcomingEvents(upcoming)
    }

    const reminderInterval = setInterval(updateUpcomingEvents, 1000 * 60) // Update every minute
    updateUpcomingEvents() // Initial update

    return () => clearInterval(reminderInterval)
  }, [events])

  const getDayEvents = (date: Date) => {
    const dayHolidays = holidays.filter(holiday => isSameDay(new Date(holiday.date), date))
    const dayWorkflows = workflowDates.filter(workflow => isSameDay(new Date(workflow.createdAt), date))
    const dayNotes = notes.filter(note => isSameDay(new Date(note.createdAt), date))
    const dayEvents = events.filter(event => isSameDay(new Date(event.date), date))
    return { holidays: dayHolidays, workflows: dayWorkflows, notes: dayNotes, events: dayEvents }
  }

  const handleDateClick = (date: Date) => {
    const existingEvent = events.find(event => 
      isSameDay(new Date(event.date), date)
    )

    if (existingEvent) {
      setSelectedEvent(existingEvent)
      setIsViewModalOpen(true)
    } else {
      const newEventData = {
        id: crypto.randomUUID(),
        title: '',
        description: '',
        date: date,
        type: 'event' as const,
        priority: 'medium' as const,
        reminder: true,
        status: 'pending' as const,
        createdAt: new Date()
      }
      setNewEvent(newEventData)
      setSelectedDate(date)
      setIsCreateModalOpen(true)
    }
  }

  const handleCreateEvent = () => {
    if (!newEvent.title) {
      toast.error('Please fill in all required fields')
      return
    }

    const eventToAdd = { ...newEvent }
    setEvents(prevEvents => {
      const updatedEvents = [...prevEvents, eventToAdd]
      localStorage.setItem('events', JSON.stringify(updatedEvents))
      return updatedEvents
    })

    // Reset form
    const resetEvent = {
      id: '',
      title: '',
      description: '',
      date: new Date(),
      type: 'event' as const,
      priority: 'medium' as const,
      reminder: true,
      status: 'pending' as const,
      createdAt: new Date()
    }
    setNewEvent(resetEvent)
    setSelectedDate(null)
    setIsCreateModalOpen(false)
    toast.success('Event created successfully')
  }

  const renderDay = (date: Date | null) => {
    if (!date) return <div className="h-32 bg-muted/10 rounded-lg" />

    const isToday = isSameDay(date, new Date())
    const dayEvents = getDayEvents(date)
    const hasHolidays = dayEvents.holidays.length > 0
    const hasWorkflows = dayEvents.workflows.length > 0
    const hasNotes = dayEvents.notes.length > 0
    const hasEvents = dayEvents.events.length > 0

    // Determine background color based on event types
    const getBgColor = () => {
      if (dayEvents.holidays.some(h => h.type === 'islamic')) {
        return 'bg-emerald-50/70 hover:bg-emerald-100/80'
      }
      if (dayEvents.holidays.some(h => h.type === 'fixed')) {
        return 'bg-sky-50/70 hover:bg-sky-100/80'
      }
      if (hasWorkflows) {
        return 'bg-violet-50/50 hover:bg-violet-100/60'
      }
      if (hasNotes) {
        return 'bg-amber-50/50 hover:bg-amber-100/60'
      }
      if (hasEvents) {
        return 'bg-orange-50/50 hover:bg-orange-100/60'
      }
      return 'bg-card hover:bg-accent/5'
    }

    return (
      <motion.div 
        whileHover={{ scale: 1.02 }}
        className={cn(
          "h-32 relative p-2 transition-all duration-200 cursor-pointer",
          "rounded-lg shadow-sm hover:shadow-md",
          "border border-border/50",
          isToday && "ring-2 ring-primary ring-offset-2",
          getBgColor()
        )}
        onClick={() => handleDateClick(date)}
      >
        <div className="flex flex-col h-full">
          <div className="flex justify-between items-start">
            <span className={cn(
              "text-sm font-medium w-6 h-6 flex items-center justify-center rounded-full",
              isToday ? "bg-primary text-primary-foreground" : "bg-background"
            )}>
              {format(date, 'd')}
            </span>
          </div>

          <div className="space-y-1 mt-1 flex-grow">
            {dayEvents.holidays.map((holiday, idx) => (
              <Badge 
                key={idx} 
                variant="outline" 
                className={cn(
                  "text-[10px] w-full justify-start font-medium",
                  holiday.type === 'islamic' 
                    ? "bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-emerald-300" 
                    : "bg-sky-100 text-sky-800 hover:bg-sky-200 border-sky-300"
                )}
              >
                <div className="flex items-center gap-1 w-full">
                  {holiday.type === 'islamic' ? (
                    <PlaneLanding className="h-3 w-3" />
                  ) : (
                    <Calendar className="h-3 w-3" />
                  )}
                  <span className="truncate">{holiday.name}</span>
                </div>
              </Badge>
            ))}
            
            {dayEvents.workflows.map((workflow, idx) => (
              <div key={`workflow-${idx}`} 
                className="flex items-center gap-1 p-1 rounded-md bg-violet-100/80 text-xs hover:bg-violet-200/80 transition-colors border border-violet-200 text-violet-800">
                <GitBranch className="h-3 w-3" />
                <span className="truncate">{workflow.title}</span>
              </div>
            ))}
            
            {dayEvents.notes.map((note, idx) => (
              <div key={`note-${idx}`} 
                className="flex items-center gap-1 p-1 rounded-md bg-amber-100/80 text-xs hover:bg-amber-200/80 transition-colors border border-amber-200 text-amber-800">
                <StickyNote className="h-3 w-3" />
                <span className="truncate">{note.title}</span>
              </div>
            ))}
            
            {dayEvents.events.map((event, idx) => (
              <div key={`event-${idx}`} 
                className="flex items-center gap-1 p-1 rounded-md bg-orange-100/80 text-xs hover:bg-orange-200/80 transition-colors border border-orange-200 text-orange-800">
                <Calendar className="h-3 w-3" />
                <span className="truncate">{event.title}</span>
              </div>
            ))}
          </div>

          {(hasWorkflows || hasNotes || hasEvents) && (
            <div className="absolute bottom-1 right-1 flex gap-1">
              {hasWorkflows && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="h-5 bg-violet-100/80 hover:bg-violet-200/80 border-violet-200 text-violet-800">
                        <GitBranch className="h-3 w-3 mr-1" />
                        {dayEvents.workflows.length}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="space-y-1">
                        {dayEvents.workflows.map((workflow, idx) => (
                          <div key={idx} className="flex items-center gap-1">
                            <GitBranch className="h-3 w-3" />
                            {workflow.title}
                          </div>
                        ))}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              {hasNotes && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="h-5 bg-amber-100/80 hover:bg-amber-200/80 border-amber-200 text-amber-800">
                        <StickyNote className="h-3 w-3 mr-1" />
                        {dayEvents.notes.length}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="space-y-1">
                        {dayEvents.notes.map((note, idx) => (
                          <div key={idx} className="flex items-center gap-1">
                            <StickyNote className="h-3 w-3" />
                            {note.title}
                          </div>
                        ))}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              {hasEvents && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="h-5 bg-orange-100/80 hover:bg-orange-200/80 border-orange-200 text-orange-800">
                        <Calendar className="h-3 w-3 mr-1" />
                        {dayEvents.events.length}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="space-y-1">
                        {dayEvents.events.map((event, idx) => (
                          <div key={idx} className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {event.title}
                          </div>
                        ))}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          )}
        </div>
      </motion.div>
    )
  }

  const daysOfWeek = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam']

  return (
    <div className="space-y-4">
      <Tabs defaultValue="calendar" className="w-full" onValueChange={(value) => setSelectedTab(value as 'calendar' | 'events')}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="calendar" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Calendar
            </TabsTrigger>
            <TabsTrigger value="events" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Events
            </TabsTrigger>
          </TabsList>
          <Button onClick={() => setIsCreateModalOpen(true)} className="bg-primary hover:bg-primary/90">
            <Plus className="h-4 w-4 mr-2" />
            Create Event
          </Button>
        </div>

        <TabsContent value="calendar" className="mt-0">
          <Card className="w-full">
            <CardHeader className="space-y-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl font-bold">
                  {format(currentDate, 'MMMM yyyy')}
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentDate(new Date(currentDate.setMonth(currentDate.getMonth() - 1)))}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentDate(new Date())}
                  >
                    <Calendar className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentDate(new Date(currentDate.setMonth(currentDate.getMonth() + 1)))}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-4 p-4">
                {daysOfWeek.map((day, i) => (
                  <div key={i} className="p-2 text-center text-sm font-medium text-muted-foreground">
                    {day}
                  </div>
                ))}
                {Array.from({ length: 42 }, (_, i) => {
                  const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
                  const startingDay = firstDay.getDay()
                  const day = new Date(firstDay)
                  day.setDate(i - startingDay + 1)
                  return day.getMonth() === currentDate.getMonth() ? day : null
                }).map((date, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2, delay: i * 0.02 }}
                  >
                    {renderDay(date)}
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="mt-0">
          {upcomingEvents.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Bell className="h-5 w-5 text-yellow-600" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.map((event, idx) => (
                    <motion.div
                      key={idx}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: idx * 0.05 }}
                    >
                      <div className="flex items-center justify-between p-4 rounded-lg bg-yellow-50/50 border border-yellow-200">
                        <div className="flex items-center gap-4">
                          <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                            <Bell className="h-5 w-5 text-yellow-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-base">{event.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              {format(new Date(event.date), 'PPP')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="bg-yellow-100/80 text-yellow-800 border-yellow-300">
                            {event.timeLeft}
                          </Badge>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold flex items-center gap-2 px-2">
                <GitBranch className="h-5 w-5 text-violet-600" />
                Workflows
              </h3>
              <div className="space-y-4">
                {workflowDates.map((workflow, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: idx * 0.05 }}
                  >
                    <div className="relative overflow-hidden rounded-lg border border-violet-200 bg-gradient-to-br from-violet-50 to-violet-100/50 p-4 shadow-sm transition-all hover:shadow-md hover:translate-y-[-2px]">
                      <div className="absolute right-0 top-0 h-20 w-20">
                        <div className="absolute transform rotate-45 bg-violet-200/50 text-center text-white font-semibold py-1 right-[-50px] top-[20px] w-[170px]" />
                      </div>
                      <div className="relative">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <GitBranch className="h-4 w-4 text-violet-600" />
                              <h4 className="font-medium text-violet-900 text-base">{workflow.title}</h4>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-violet-600">
                              <Calendar className="h-3.5 w-3.5" />
                              {format(new Date(workflow.createdAt), 'PPP')}
                            </div>
                          </div>
                          <Badge variant="outline" className="bg-violet-100/80 text-violet-800 border-violet-300">
                            Workflow
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-lg font-semibold flex items-center gap-2 px-2">
                <StickyNote className="h-5 w-5 text-amber-600" />
                Notes
              </h3>
              <div className="space-y-4">
                {notes.map((note, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: idx * 0.05 }}
                  >
                    <div className="group relative">
                      {/* Decorative pin */}
                      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-zinc-300 rounded-full shadow-md z-10" />
                      {/* Note card */}
                      <div className="relative mt-2 bg-gradient-to-br from-amber-50 to-amber-100/50 p-4 rounded-lg shadow-md transform transition-all duration-200 group-hover:rotate-1 group-hover:scale-[1.02] border-b-4 border-amber-200">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <StickyNote className="h-4 w-4 text-amber-600" />
                              <h4 className="font-medium text-amber-900 text-base">{note.title}</h4>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-amber-600">
                              <Calendar className="h-3.5 w-3.5" />
                              {format(new Date(note.createdAt), 'PPP')}
                            </div>
                          </div>
                          <Badge variant="outline" className="bg-amber-100/80 text-amber-800 border-amber-300">
                            Note
                          </Badge>
                        </div>
                      </div>
                      {/* Decorative shadow */}
                      <div className="absolute inset-0 -z-10 bg-amber-200/20 rounded-lg blur-sm transform translate-y-1" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-lg font-semibold flex items-center gap-2 px-2">
                <Calendar className="h-5 w-5 text-orange-600" />
                Events
              </h3>
              <div className="space-y-4">
                {events.map((event, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: idx * 0.05 }}
                  >
                    <div className="group relative">
                      {/* Decorative pin */}
                      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-zinc-300 rounded-full shadow-md z-10" />
                      {/* Event card */}
                      <div className="relative mt-2 bg-gradient-to-br from-orange-50 to-orange-100/50 p-4 rounded-lg shadow-md transform transition-all duration-200 group-hover:rotate-1 group-hover:scale-[1.02] border-b-4 border-orange-200">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-orange-600" />
                              <h4 className="font-medium text-orange-900 text-base">{event.title}</h4>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-orange-600">
                              <Calendar className="h-3.5 w-3.5" />
                              {format(new Date(event.date), 'PPP')}
                            </div>
                          </div>
                          <Badge variant="outline" className="bg-orange-100/80 text-orange-800 border-orange-300">
                            Event
                          </Badge>
                        </div>
                      </div>
                      {/* Decorative shadow */}
                      <div className="absolute inset-0 -z-10 bg-orange-200/20 rounded-lg blur-sm transform translate-y-1" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create Event for {selectedDate ? format(selectedDate, 'PPP') : ''}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title <span className="text-red-500">*</span></Label>
              <Input
                id="title"
                value={newEvent.title}
                onChange={(e) => setNewEvent(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Event title"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newEvent.description}
                onChange={(e) => setNewEvent(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Event description"
                className="h-24"
              />
            </div>
            <div className="grid gap-2">
              <Label>Priority</Label>
              <Select
                value={newEvent.priority}
                onValueChange={(value: 'low' | 'medium' | 'high') => 
                  setNewEvent(prev => ({ ...prev, priority: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low Priority</SelectItem>
                  <SelectItem value="medium">Medium Priority</SelectItem>
                  <SelectItem value="high">High Priority</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="reminder" className="flex-grow">Enable Reminder</Label>
              <Switch
                id="reminder"
                checked={newEvent.reminder}
                onCheckedChange={(checked) => 
                  setNewEvent(prev => ({ ...prev, reminder: checked }))
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsCreateModalOpen(false)
              setNewEvent({
                id: '',
                title: '',
                description: '',
                date: new Date(),
                type: 'event',
                priority: 'medium',
                reminder: true,
                status: 'pending',
                createdAt: new Date()
              })
              setSelectedDate(null)
            }}>
              Cancel
            </Button>
            <Button onClick={handleCreateEvent}>
              Create Event
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>View Event</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>Title</Label>
              <p>{selectedEvent?.title}</p>
            </div>
            <div className="grid gap-2">
              <Label>Description</Label>
              <p>{selectedEvent?.description}</p>
            </div>
            <div className="grid gap-2">
              <Label>Type</Label>
              <p>{selectedEvent?.type}</p>
            </div>
            <div className="grid gap-2">
              <Label>Priority</Label>
              <p>{selectedEvent?.priority}</p>
            </div>
            <div className="grid gap-2">
              <Label>Reminder</Label>
              <p>{selectedEvent?.reminder ? 'Yes' : 'No'}</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsViewModalOpen(false)
              setSelectedEvent(null)
            }}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default MHolidays