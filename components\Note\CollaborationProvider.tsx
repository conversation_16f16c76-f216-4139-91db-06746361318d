"use client";

import { ReactNode, createContext, useContext, useState, useEffect, useMemo } from "react";
import { useUser } from "@clerk/nextjs";
import * as Y from "yjs";
import { WebrtcProvider } from "y-webrtc";

// Enhanced user colors with better contrast
const USER_COLORS = [
  "#FF5733", // Bright orange-red
  "#3498DB", // Clear blue
  "#9B59B6", // Purple
  "#2ECC71", // Green
  "#F1C40F", // Yellow
  "#E74C3C", // Red
  "#1ABC9C", // Turquoise
  "#D35400", // Dark orange
  "#8E44AD", // Dark purple
  "#27AE60", // Dark green
];

interface CollaborationProviderProps {
  children: ReactNode;
  roomId: string;
  isCollaborating: boolean;
}

// Define the context type
interface CollaborationContextType {
  user: {
    id: string;
    name: string;
    color: string;
  };
  isCollaborating: boolean;
  provider: <PERSON>rtc<PERSON>rovider | null;
  doc: Y.Doc | null;
  isInitializing: boolean;
}

// Create the context with default values
const CollaborationContext = createContext<CollaborationContextType>({
  user: {
    id: "anonymous",
    name: "Anonymous",
    color: USER_COLORS[0]
  },
  isCollaborating: false,
  provider: null,
  doc: null,
  isInitializing: false
});

// The main collaboration provider wrapper
export const CollaborationProvider = ({
  children,
  roomId,
  isCollaborating,
}: CollaborationProviderProps) => {
  const { user } = useUser();
  const [doc, setDoc] = useState<Y.Doc | null>(null);
  const [provider, setProvider] = useState<WebrtcProvider | null>(null);
  const [isInitializing, setIsInitializing] = useState(isCollaborating);
  
  // Generate a consistent user color from their ID
  const userColor = useMemo(() => {
    if (!user?.id) return USER_COLORS[0];
    // Use a hash function to get a more stable color
    const hash = user.id.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    return USER_COLORS[Math.abs(hash) % USER_COLORS.length];
  }, [user?.id]);
  
  // Set up Yjs when collaboration is active
  useEffect(() => {
    let yDoc: Y.Doc | null = null;
    let yProvider: WebrtcProvider | null = null;

    const setupCollaboration = async () => {
      if (isCollaborating && roomId) {
        try {
          console.log("Initializing Y.js collaboration with room:", roomId);
          
          // Create a new Y.js document
          yDoc = new Y.Doc();
          
          // Create a WebRTC provider for real-time sync
          yProvider = new WebrtcProvider(roomId, yDoc, {
            signaling: ["wss://y-webrtc-signaling.glitch.me", "wss://y-webrtc-signaling-eu.herokuapp.com"],
            password: roomId, // Use roomId as password for a bit more security
            peerOpts: {
              config: {
                iceServers: [
                  { urls: "stun:stun.l.google.com:19302" },
                  { urls: "stun:global.stun.twilio.com:3478" }
                ]
              }
            }
          });
          
          // Set awareness data for this user
          if (user) {
            yProvider.awareness.setLocalStateField("user", {
              name: user.fullName || user.firstName || "Anonymous",
              color: userColor,
              id: user.id || "anonymous"
            });
          }
          
          // Store the Y.js document and provider
          setDoc(yDoc);
          setProvider(yProvider);
          
          console.log("Y.js collaboration initialized successfully");
        } catch (error) {
          console.error("Failed to initialize Y.js collaboration:", error);
        } finally {
          setIsInitializing(false);
        }
      } else {
        setIsInitializing(false);
      }
    };

    if (isCollaborating && !doc && !provider) {
      setupCollaboration();
    } else if (!isCollaborating && (doc || provider)) {
      // Clean up when collaboration ends
      if (provider) {
        console.log("Destroying Y.js provider");
        provider.destroy();
        setProvider(null);
      }
      if (doc) {
        console.log("Destroying Y.js document");
        doc.destroy();
        setDoc(null);
      }
    }
    
    // Clean up when component unmounts
    return () => {
      if (yProvider) {
        console.log("Cleaning up Y.js provider");
        yProvider.destroy();
      }
      if (yDoc) {
        console.log("Cleaning up Y.js document");
        yDoc.destroy();
      }
    };
  }, [isCollaborating, roomId]);
  
  return (
    <CollaborationContext.Provider 
      value={{ 
        user: {
          id: user?.id || "anonymous",
          name: user?.fullName || user?.firstName || "Anonymous",
          color: userColor
        },
        isCollaborating,
        provider,
        doc,
        isInitializing
      }}
    >
      {children}
    </CollaborationContext.Provider>
  );
};

// Helper component to provide collaboration context to children
export function CollaborationContent({ children }: { children: ReactNode }) {
  return children;
}

export const useCollaboration = () => {
  return useContext(CollaborationContext);
};

export default CollaborationProvider; 