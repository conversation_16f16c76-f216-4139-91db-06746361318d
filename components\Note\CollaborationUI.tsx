"use client";

import { useState, useEffect } from "react";
import { Users, UserPlus, Co<PERSON>, Check, X } from "lucide-react";
import { useCollaboration } from "./CollaborationProvider";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { AvatarGroup } from "@/components/ui/avatar-group";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface CollaborationUIProps {
  noteId?: string;
  collaborationId?: string | null;
  isCollaborating: boolean;
  onStartCollaboration: () => void;
  onEndCollaboration: () => void;
  activeUsers?: Array<{ name: string; color: string }>;
}

export default function CollaborationUI({
  noteId = "",
  collaborationId = null,
  isCollaborating,
  onStartCollaboration,
  onEndCollaboration,
  activeUsers = [],
}: CollaborationUIProps) {
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);
  
  // Use destructuring with default values
  const { user, provider = null } = useCollaboration();
  
  // Generate collaboration link
  const collaborationLink = typeof window !== "undefined" && noteId && isCollaborating
    ? `${window.location.origin}/hr/workspace/note/${noteId}?collaborate=${collaborationId || "true"}`
    : '';
  
  // Handle copying link to clipboard
  const copyLinkToClipboard = () => {
    navigator.clipboard.writeText(collaborationLink);
    setLinkCopied(true);
    toast.success("Collaboration link copied to clipboard");
    
    setTimeout(() => {
      setLinkCopied(false);
    }, 3000);
  };
  
  if (!isCollaborating) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={onStartCollaboration}
        className="flex items-center gap-2"
      >
        <Users className="h-4 w-4" />
        Collaborate
      </Button>
    );
  }
  
  // Display collaborators
  return (
    <div className="flex items-center gap-2">
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" className="h-9 px-2 gap-2">
              <div className="flex -space-x-1 items-center">
                {activeUsers.slice(0, 3).map((user, i) => (
                  <div
                    key={i}
                    className="w-6 h-6 rounded-full border-2 border-background flex items-center justify-center text-[10px] text-white"
                    style={{ backgroundColor: user.color }}
                    title={user.name}
                  >
                    {user.name[0].toUpperCase()}
                  </div>
                ))}
                {activeUsers.length > 3 && (
                  <div className="w-6 h-6 rounded-full border-2 border-background bg-muted flex items-center justify-center text-[10px]">
                    +{activeUsers.length - 3}
                  </div>
                )}
              </div>
              <span className="text-xs font-medium">
                {activeUsers.length} {activeUsers.length === 1 ? 'user' : 'users'}
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <p className="font-medium mb-1">Active collaborators</p>
              <ul className="space-y-1">
                {activeUsers.map((user, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <div 
                      className="w-2 h-2 rounded-full" 
                      style={{ backgroundColor: user.color }}
                    />
                    <span>{user.name}</span>
                  </li>
                ))}
              </ul>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-9"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Invite
          </Button>
        </DialogTrigger>
        
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite collaborators</DialogTitle>
            <DialogDescription>
              Share this link with others to collaborate on this note in real-time.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex items-center gap-2 mt-4">
            <Input
              value={collaborationLink}
              readOnly
              className="flex-1"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={copyLinkToClipboard}
            >
              {linkCopied ? (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Copied
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </>
              )}
            </Button>
          </div>
          
          <div className="mt-4">
            <Label>Active collaborators ({activeUsers.length})</Label>
            <div className="mt-2 space-y-2">
              {activeUsers.map((user, i) => (
                <div key={i} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                  <div 
                    className="w-7 h-7 rounded-full flex items-center justify-center text-xs text-white"
                    style={{ backgroundColor: user.color }}
                  >
                    {user.name[0].toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{user.name}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="destructive"
              onClick={onEndCollaboration}
            >
              <X className="h-4 w-4 mr-2" />
              End collaboration
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 