"use client";

import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import {
  useBlockNote,
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
} from "@blocknote/react";
import {
  BlockNoteSchema,
  defaultBlockSpecs,
  filterSuggestionItems,
  insertOrUpdateBlock,
} from "@blocknote/core";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Upload, Save, Globe2, Database, GitMerge } from "lucide-react";
import { toast } from "sonner";
import { saveNote, getNotes, getNote, createFolder, deleteNote, publishNote } from "@/actions/actions";
import { Skeleton } from "@/components/ui/skeleton";
import { Note } from '@prisma/client';
import { useRouter, useSearchParams } from 'next/navigation';
import { DataAnalysisBlock } from "@/components/ChartBuilder/Dashnote/DataAnalysisBlock";
import { useCreateBlockNote } from "@blocknote/react";
import { CollaborationProvider, CollaborationContent, useCollaboration } from "./CollaborationProvider";
import { generateCollaborationLink } from "@/actions/collaboration-actions";
import "@/styles/collaboration.css";
import "@/styles/blocknote.css";
import * as Y from "yjs";
import { WebrtcProvider } from "y-webrtc";
import { HeaderNote } from "./HeaderNote";


interface EditorProps {
  noteId?: string;
}

// Type for editor to fix any type warnings
interface EditorType {
  insertBlocks: (blocks: any, targetBlock: any, placement: "before" | "after" | "nested") => void;
  getTextCursorPosition: () => { block: any };
}

export default function Editor({ noteId }: EditorProps) {
  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const searchParams = useSearchParams();

  const [mounted, setMounted] = useState(false);
  const [title, setTitle] = useState("");
  const [coverImage, setCoverImage] = useState<string | null>(null);
  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSidebarLoading, setIsSidebarLoading] = useState(true);
  const [editorContent, setEditorContent] = useState<any>(null);
  const [isPublished, setIsPublished] = useState(false)
  const [publicUrl, setPublicUrl] = useState<string | null>(null)
  const [isCollaborating, setIsCollaborating] = useState(false);
  const [collaborationId, setCollaborationId] = useState<string | null>(null);
  const [sidebarState, setSidebarState] = useState<'open' | 'closed' | 'none'>('open');

  // Create the schema with our custom blocks
  const schema = BlockNoteSchema.create({
    blockSpecs: {
      // Include default blocks
      ...defaultBlockSpecs,
      // Add our data analysis block
      dataAnalysis: DataAnalysisBlock,
    },
  });

  // Slash menu item for data analysis
  const insertDataAnalysis = (editor: EditorType) => ({
    title: "Data Analysis",
    onItemClick: () => {
      try {
        insertOrUpdateBlock(editor as any, {
          type: "dataAnalysis",
          props: { analysisType: "code" } as any
        });
      } catch (error: any) {
        console.error('Error inserting data analysis block:', error);
        if (error.message && error.message.includes('Position')) {
          // Handle position errors gracefully
          toast.error('Unable to insert block at current position. Please try again.');
        } else {
          toast.error('Failed to insert data analysis block');
        }
      }
    },
    aliases: ["data", "analysis", "code", "chart", "table", "cell"],
    group: "Data Tools",
    icon: <Database className="h-4 w-4 text-purple-600" />,
  });

  // Only create a non-collaborative editor initially, we'll create the collaborative one in CollaboratedEditorContent
  const editor = useBlockNote({
    schema,
    initialContent: editorContent,
    domAttributes: {
      editor: { class: 'bn-editor-no-scroll' }
    }
  });

  const customDarkTheme = {
    colors: {
      editor: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 10%, 3.9%)',
      },
      menu: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      tooltip: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 10%, 3.9%)',
      },
      hovered: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      selected: {
        text: 'hsl(240, 5.9%, 10%)',
        background: 'hsl(0, 0%, 98%)',
      },
      disabled: {
        text: 'hsl(240, 5%, 64.9%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      shadow: 'hsl(240, 3.7%, 15.9%)',
      border: 'hsl(240, 3.7%, 15.9%)',
    },
  };

  useEffect(() => {
    setMounted(true);
    fetchNotes();
    if (noteId) {
      fetchNote(noteId);
    } else {
      setIsLoading(false);
    }
  }, [noteId]);

  // Effect to detect sidebar state
  useEffect(() => {
    const detectSidebarState = () => {
      // Check for sidebar element
      const sidebar = document.querySelector('.note-sidebar');
      if (!sidebar) {
        setSidebarState('none');
        return;
      }

      // Check if sidebar has collapsed class
      if (sidebar.classList.contains('collapsed')) {
        setSidebarState('closed');
      } else {
        setSidebarState('open');
      }
    };

    // Initial detection
    detectSidebarState();

    // Set up mutation observer to detect sidebar changes
    const observer = new MutationObserver(detectSidebarState);
    const sidebar = document.querySelector('.note-sidebar');

    if (sidebar) {
      observer.observe(sidebar, {
        attributes: true,
        attributeFilter: ['class']
      });
    }

    // Clean up observer on unmount
    return () => observer.disconnect();
  }, [mounted]);

  useEffect(() => {
    const importId = searchParams.get('import');
    if (importId) {
      importPublicNote(importId);
    }
  }, [searchParams]);

  useEffect(() => {
    if (searchParams.has("collaborate") && noteId) {
      const collaborateParam = searchParams.get("collaborate");
      if (collaborateParam && collaborateParam !== "true") {
        // This is a collaboration with a specific ID
        setCollaborationId(collaborateParam);
        setIsCollaborating(true);
      } else {
        // Legacy support for ?collaborate=true
        startCollaboration();
      }
    }
  }, [searchParams, noteId]);

  const fetchNotes = async () => {
    setIsSidebarLoading(true);
    try {
      const response = await getNotes();
      // Check if the response was successful and contains notes
      if (response.success && response.notes) {
        setNotes(response.notes);
      } else {
        setNotes([]);
      }
    } catch (error) {
      toast.error("Failed to fetch notes");
      setNotes([]);
    } finally {
      setIsSidebarLoading(false);
    }
  };

  const fetchNote = async (id: string) => {
    setIsLoading(true);
    try {
      const note = await getNote(id);
      console.log("Fetched note:", note);

      if (note) {
        setSelectedNote(note);
        setTitle(note.title || "Untitled");
        setCoverImage(note.coverImage || null);

        // Check if the note is in collaboration mode
        if (note.metadata) {
          try {
            const metadata = typeof note.metadata === 'string'
              ? JSON.parse(note.metadata)
              : note.metadata;

            if (metadata.collaborationId) {
              // If URL has collaboration parameter that matches the note's collaboration ID
              const collaborateParam = searchParams.get("collaborate");
              if (collaborateParam === metadata.collaborationId) {
                // Start collaboration with the specific ID
                setCollaborationId(metadata.collaborationId);
                setIsCollaborating(true);
              }
            }
          } catch (e) {
            console.error("Failed to parse note metadata:", e);
          }
        }

        // Process content
        if (note.content) {
          try {
            let parsedContent;
            if (typeof note.content === 'string') {
              parsedContent = JSON.parse(note.content);
            } else {
              parsedContent = note.content; // It's already an object
            }

            // Process the content to ensure custom blocks are properly handled
            parsedContent = parsedContent.map((block: any) => {
              if (block.type === 'dataAnalysis') {
                // Preserve ALL properties of dataAnalysis blocks
                const dataBlock = { ...block };

                // Log the block for debugging
                console.log('Loading dataAnalysis block:', dataBlock);

                // Ensure all required props exist with fallbacks
                dataBlock.props = {
                  ...dataBlock.props,
                  // BlockNote required props
                  analysisType: dataBlock.props.analysisType || 'code',
                  textAlignment: dataBlock.props.textAlignment || 'left',
                  textColor: dataBlock.props.textColor || 'default',
                  // Cell props
                  savedQuery: dataBlock.props.savedQuery || '',
                  language: dataBlock.props.language || 'sql',
                  cellId: dataBlock.props.cellId || '',
                  datasetId: dataBlock.props.datasetId || 'ds1',
                  // Result data
                  resultData: dataBlock.props.resultData || '[]',
                  resultOutput: dataBlock.props.resultOutput || '',
                  resultPlots: dataBlock.props.resultPlots || '[]',
                  hasError: dataBlock.props.hasError || false,
                  errorMessage: dataBlock.props.errorMessage || '',
                  showGraphicWalker: dataBlock.props.showGraphicWalker || false
                };

                return dataBlock;
              }
              return block;
            });

            setEditorContent(parsedContent);
          } catch (error) {
            console.error("Failed to parse note content:", error);
            toast.error("Failed to load note content. The note may be corrupted.");
            setEditorContent([
              {
                type: "paragraph",
                content: "Welcome to your Notion-like editor",
              },
            ]);
          }
        }
      }
    } catch (error) {
      toast.error("Failed to fetch note");
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 300); // Small delay to ensure smooth transition
    }
  };

  const handleSelectNote = (note: any) => {
    router.push(`/hr/workspace/note/${note.id}`);
  };

  const handleNewNote = (parentId?: string) => {
    setSelectedNote(null);
    setTitle("");
    setCoverImage(null);
    editor.replaceBlocks(editor.topLevelBlocks, [
      {
        type: "paragraph",
        content: "Welcome to your new note",
      },
    ]);
    router.push('/hr/workspace/note');
    if (parentId) {
      // If parentId is provided, we're creating a note inside a folder
      toast.promise(
        saveNote('', 'Untitled', JSON.stringify(editor.topLevelBlocks), null, parentId),
        {
          loading: 'Creating new note...',
          success: (result) => {
            if (result.success) {
              router.push(`/hr/workspace/note/${result.noteId}`);
              return 'New note created successfully!';
            } else {
              throw new Error(result.error || 'Failed to create note');
            }
          },
          error: 'Failed to create note',
        }
      );
    }
  };

  const handleSave = async () => {
    const content = JSON.stringify(editor.topLevelBlocks);

    // If in collaboration mode, add metadata
    let metadata = null;
    if (isCollaborating && collaborationId) {
      metadata = JSON.stringify({
        collaborationId,
        collaborationStartedAt: new Date().toISOString()
      });
    }

    // Convert null to undefined for TypeScript type compatibility
    const coverImageForSave = coverImage === null ? undefined : coverImage;
    const parentIdForSave = selectedNote?.parentId === null ? undefined : selectedNote?.parentId;

    toast.promise(
      saveNote(
        selectedNote?.id || '',
        title,
        content,
        coverImage, // Use this instead of coverImage directly
        parentIdForSave,   // Use this instead of selectedNote?.parentId directly
        undefined,  // mergedFromPublicId
        false, // isMerged
        metadata
      ),
      {
        loading: 'Saving note...',
        success: (result) => {
          if (result.success) {
            if (!selectedNote) {
              router.push(`/hr/workspace/note/${result.noteId}`);
            }
            return `Note ${title} saved successfully!`;
          } else {
            throw new Error(result.error || 'Failed to save note');
          }
        },
        error: 'Failed to save note',
      }
    );
    await fetchNotes();
  };

  const handleImageUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setCoverImage(e.target.result as string);
        toast.success('Image uploaded successfully!');
      }
    };
    reader.readAsDataURL(file);
  };

  useEffect(() => {
    if (editorContent && editor) {
      try {
        // Ensure custom blocks are properly formatted before replacing
        const processedContent = Array.isArray(editorContent) ?
          editorContent.map((block: any) => {
            if (block.type === 'dataAnalysis') {
              // For DataAnalysisBlock, preserve ALL custom properties
              const blockCopy = { ...block };

              // Make sure all required props exist with proper fallbacks
              blockCopy.props = {
                ...blockCopy.props,
                // Core props with fallbacks
                analysisType: blockCopy.props.analysisType || 'code',
                textAlignment: blockCopy.props.textAlignment || 'left',
                textColor: blockCopy.props.textColor || 'default',
                // Functional props for data analysis cell
                savedQuery: blockCopy.props.savedQuery || '',
                language: blockCopy.props.language || 'sql',
                cellId: blockCopy.props.cellId || '',
                datasetId: blockCopy.props.datasetId || 'ds1',
                // Result data props
                resultData: blockCopy.props.resultData || '[]',
                resultOutput: blockCopy.props.resultOutput || '',
                resultPlots: blockCopy.props.resultPlots || '[]',
                hasError: blockCopy.props.hasError || false,
                errorMessage: blockCopy.props.errorMessage || '',
                showGraphicWalker: blockCopy.props.showGraphicWalker || false
              };

              console.log('Processed dataAnalysis block:', blockCopy);
              return blockCopy;
            }
            return block;
          }) :
          editorContent;

        console.log('Replacing editor content with:', processedContent);

        // Add error handling for position-related errors
        try {
          // Ensure editor is ready and has valid state before replacing blocks
          if (editor.topLevelBlocks && Array.isArray(editor.topLevelBlocks)) {
            editor.replaceBlocks(editor.topLevelBlocks, processedContent);
          } else {
            // If topLevelBlocks is not ready, wait a bit and try again
            setTimeout(() => {
              if (editor.topLevelBlocks && Array.isArray(editor.topLevelBlocks)) {
                editor.replaceBlocks(editor.topLevelBlocks, processedContent);
              }
            }, 100);
          }
        } catch (positionError: any) {
          console.warn('Position error when replacing blocks, retrying...', positionError);
          // If we get a position error, try to reset the editor content more safely
          setTimeout(() => {
            try {
              editor.replaceBlocks(editor.topLevelBlocks, processedContent);
            } catch (retryError) {
              console.error('Failed to replace blocks after retry:', retryError);
              toast.error('Error loading editor content');
            }
          }, 200);
        }
      } catch (error) {
        console.error('Error processing editor content:', error);
        toast.error('Error loading editor content');
      }
    }
  }, [editorContent, editor]);


  const handlePublish = async () => {
    if (!noteId) return

    try {
      setIsLoading(true)
      const result = await publishNote(noteId)

      if (result.success) {
        setIsPublished(true)
        // @ts-ignore
        setPublicUrl(result.publicUrl)
        toast.success('Note published successfully')
      } else {
        toast.error(result.error || 'Failed to publish note')
      }
    } catch (error) {
      toast.error('Failed to publish note')
    } finally {
      setIsLoading(false)
    }
  }

  const importPublicNote = async (publicId: string) => {
    setIsLoading(true);
    try {
      // Fetch the public note
      const response = await fetch(`/api/public-notes/${publicId}`);
      if (!response.ok) throw new Error('Failed to fetch public note');

      const publicNote = await response.json();

      // Create a new note with the content from public note
      const result = await saveNote(
        '', // new note
        `${publicNote.title} (Merged)`,
        typeof publicNote.content === 'string'
          ? publicNote.content
          : JSON.stringify(publicNote.content),
        publicNote.coverImage,
        undefined, // No parent
        publicNote.publicId, // Pass the original publicId for reference
        true // Mark as merged
      );

      if (result.success && result.noteId) {
        toast.success('Note successfully merged');
        router.push(`/hr/workspace/note/${result.noteId}`);
      } else {
        throw new Error(result.error || 'Failed to import note');
      }
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Failed to import note');
      router.push('/hr/workspace/note');
    } finally {
      setIsLoading(false);
    }
  };

  const startCollaboration = async () => {
    if (!noteId) return;

    try {
      setIsLoading(true);

      // Generate a unique collaboration ID and link
      const result = await generateCollaborationLink(noteId);

      if (!result.success) {
        throw new Error(result.error || "Failed to generate collaboration link");
      }

      const collabId = result.collaborationId;
      console.log("Starting collaboration with ID:", collabId);

      if (!collabId) {
        throw new Error("No collaboration ID returned from server");
      }

      setCollaborationId(collabId);
      setIsCollaborating(true);

      // Update URL to include collaboration flag (without refreshing)
      const url = new URL(window.location.href);
      url.searchParams.set("collaborate", collabId);
      window.history.replaceState({}, "", url.toString());

      toast.success("Collaboration started");
    } catch (error) {
      console.error("Collaboration error:", error);
      toast.error("Failed to start collaboration");
    } finally {
      setIsLoading(false);
    }
  };

  const endCollaboration = () => {
    // Remove collaboration flag from URL
    const url = new URL(window.location.href);
    url.searchParams.delete("collaborate");
    window.history.replaceState({}, "", url.toString());

    setIsCollaborating(false);
    setCollaborationId(null);
    toast.success("Collaboration ended");
  };

  if (!mounted) return null;

  // Render skeleton for the title while loading
  const renderTitleSkeleton = () => (
    <div className="w-full">
      <Skeleton className="h-10 w-3/4" />
    </div>
  );

  // Render skeleton for the editor content while loading
  const renderEditorSkeleton = () => (
    <div className="space-y-4 w-full">
      <Skeleton className="h-6 w-full" />
      <Skeleton className="h-6 w-11/12" />
      <Skeleton className="h-6 w-3/4" />
      <Skeleton className="h-6 w-10/12" />
      <Skeleton className="h-6 w-5/6" />
      <Skeleton className="h-6 w-4/5" />
      <Skeleton className="h-6 w-3/5" />
      <Skeleton className="h-6 w-3/4" />
      <Skeleton className="h-6 w-2/3" />
    </div>
  );

  // Render skeleton for buttons while loading
  const renderButtonsSkeleton = () => (
    <div className="flex gap-2">
      <Skeleton className="h-9 w-24" />
      <Skeleton className="h-9 w-24" />
      <Skeleton className="h-9 w-24" />
    </div>
  );

  // Before rendering the component, ensure resolvedTheme is always a string
  const safeResolvedTheme = resolvedTheme || 'light'; // Provide a default value

  return (
    <CollaborationProvider
      roomId={collaborationId || `note-${noteId || "new"}`}
      isCollaborating={isCollaborating}
    >
      <CollaborationContent>
        <div className="h-full overflow-hidden">
        <CollaboratedEditorContent
          isCollaborating={isCollaborating}
          collaborationId={collaborationId}
          startCollaboration={startCollaboration}
          endCollaboration={endCollaboration}
          noteId={noteId}
          editor={editor}
          title={title}
          setTitle={setTitle}
          coverImage={coverImage}
          setCoverImage={setCoverImage}
          isLoading={isLoading}
          isPublished={isPublished}
          publicUrl={publicUrl}
          handleSave={handleSave}
          handlePublish={handlePublish}
          renderTitleSkeleton={renderTitleSkeleton}
          renderButtonsSkeleton={renderButtonsSkeleton}
          renderEditorSkeleton={renderEditorSkeleton}
          handleImageUpload={handleImageUpload}
          insertDataAnalysis={insertDataAnalysis}
          customDarkTheme={customDarkTheme}
          resolvedTheme={safeResolvedTheme}
          note={selectedNote}
          sidebarState={sidebarState}
        />
        </div>
      </CollaborationContent>
    </CollaborationProvider>
  );
}

interface CollaboratedEditorContentProps {
  isCollaborating: boolean;
  collaborationId: string | null;
  startCollaboration: () => Promise<void>;
  endCollaboration: () => void;
  noteId?: string;
  editor: any;
  title: string;
  setTitle: (title: string) => void;
  coverImage: string | null;
  setCoverImage: (url: string | null) => void;
  isLoading: boolean;
  isPublished: boolean;
  publicUrl: string | null;
  handleSave: () => Promise<void>;
  handlePublish: () => Promise<void>;
  renderTitleSkeleton: () => JSX.Element;
  renderButtonsSkeleton: () => JSX.Element;
  renderEditorSkeleton: () => JSX.Element;
  handleImageUpload: (file: File) => void;
  insertDataAnalysis: (editor: any) => any;
  customDarkTheme: any;
  resolvedTheme: string;
  sidebarState: 'open' | 'closed' | 'none';
  note: {
    id?: string;
    title?: string;
    content?: any;
    createdAt?: string | Date;
    updatedAt?: string | Date;
    [key: string]: any;
  } | null;
}

function CollaboratedEditorContent({
  isCollaborating,
  collaborationId,
  startCollaboration,
  endCollaboration,
  noteId,
  editor: nonCollabEditor,
  title,
  setTitle,
  coverImage,
  setCoverImage,
  isLoading,
  isPublished,
  publicUrl,
  handleSave,
  handlePublish,
  renderTitleSkeleton,
  renderButtonsSkeleton,
  renderEditorSkeleton,
  handleImageUpload,
  insertDataAnalysis,
  customDarkTheme,
  resolvedTheme,
  note,
  sidebarState,
}: CollaboratedEditorContentProps) {
  const { user, provider, doc, isInitializing } = useCollaboration();
  const [activeUsers, setActiveUsers] = useState<Array<{ name: string; color: string }>>([]);

  // Create editors outside of effects
  let editorToUse = nonCollabEditor;

  // Hook for collaborative editor
  const collaborativeEditor = useCreateBlockNote({
    collaboration: isCollaborating && provider && doc && !isInitializing ? {
          provider,
          fragment: doc.getXmlFragment("document-store"),
          user: {
            name: user.name,
            color: user.color,
          },
    } : undefined,
        schema: nonCollabEditor.schema,
        domAttributes: {
      editor: { class: 'bn-editor-no-scroll' }
    }
  });

  // Determine which editor to use
  editorToUse = isCollaborating && provider && doc && !isInitializing
    ? collaborativeEditor
    : nonCollabEditor;

  // Effect for tracking active users
  useEffect(() => {
    if (!provider || !isCollaborating) return;

    const updateUsers = () => {
      if (!provider?.awareness) return;

      try {
        const entries = Array.from(provider.awareness.getStates().entries());
        const usersData = entries
          .map(entry => {
            const [_, state] = entry as [number, any];
            if (!state?.user) return null;
            return {
              name: state.user.name as string,
              color: state.user.color as string
            };
          })
          .filter((user): user is { name: string; color: string } => user !== null);

        setActiveUsers(usersData);
    } catch (error) {
        console.error("Error processing users:", error);
      }
    };

    updateUsers();

    if (provider.awareness) {
      provider.awareness.on("change", updateUsers);
      return () => provider.awareness.off("change", updateUsers);
    }
  }, [provider, isCollaborating]);

  // Show loading state while initializing
  if (isCollaborating && isInitializing) {
    return (
      <div className="h-[calc(100vh-4rem)] flex flex-col items-center justify-center">
        <div className="space-y-4 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Initializing collaboration...</p>
          <Button
            variant="outline"
            onClick={endCollaboration}
            className="mt-4"
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  }

  // Use HeaderNote component
  return (
    <div className={`editor-container h-full ${
      sidebarState === 'open' ? 'sidebar-open' :
      sidebarState === 'closed' ? 'sidebar-closed' :
      'full-width'
    }`}>
      <HeaderNote
        title={title}
        setTitle={setTitle}
        noteId={noteId}
        collaborationId={collaborationId}
        isCollaborating={isCollaborating}
        startCollaboration={startCollaboration}
        endCollaboration={endCollaboration}
        handleSave={handleSave}
        handlePublish={handlePublish}
        handleImageUpload={handleImageUpload}
        isLoading={isLoading}
        isPublished={isPublished}
        publicUrl={publicUrl}
        renderTitleSkeleton={renderTitleSkeleton}
        renderButtonsSkeleton={renderButtonsSkeleton}
        activeUsers={activeUsers}
        createdAt={note?.createdAt ? new Date(note.createdAt) : undefined}
        updatedAt={note?.updatedAt ? new Date(note.updatedAt) : undefined}
      />

      <div className="blocknote-container w-full">
        <div className="blocknote-content-wrapper">
          {/* Cover Image Area */}
          {isLoading && coverImage ? (
            <div className="mb-6 rounded-lg overflow-hidden h-[200px]">
              <Skeleton className="w-full h-full" />
            </div>
          ) : coverImage && (
            <div className="mb-6 rounded-lg overflow-hidden h-[200px]">
              <img src={coverImage} alt="Cover" className="w-full h-full object-cover" />
            </div>
          )}

          {/* Editor content area */}
          <div className="prose prose-sm dark:prose-invert max-w-none w-full">
            {isLoading ? (
              renderEditorSkeleton()
            ) : (
              <BlockNoteView
                editor={editorToUse}
                theme={resolvedTheme === "dark" ? customDarkTheme : "light"}
                slashMenu={false}
                className="w-full max-w-full"
              >
                <SuggestionMenuController
                  triggerCharacter={"/"}
                  getItems={async (query) =>
                    filterSuggestionItems(
                      [
                        ...getDefaultReactSlashMenuItems(editorToUse),
                        insertDataAnalysis(editorToUse)
                      ],
                      query
                    )
                  }
                />
              </BlockNoteView>
            )}
          </div>
        </div>
      </div>

      <input
        id="imageUpload"
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => e.target.files && handleImageUpload(e.target.files[0])}
        disabled={isLoading}
      />
    </div>
  );
}