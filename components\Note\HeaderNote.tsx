"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Globe2, Upload, Save } from "lucide-react";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import CollaborationUI from "./CollaborationUI";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { format, formatDistanceToNow } from "date-fns";
import { Calendar, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

interface HeaderNoteProps {
  title: string;
  setTitle: (title: string) => void;
  noteId?: string;
  collaborationId?: string | null;
  isCollaborating: boolean;
  startCollaboration: () => Promise<void>;
  endCollaboration: () => void;
  handleSave: () => Promise<void>;
  handlePublish: () => Promise<void>;
  handleImageUpload: (file: File) => void;
  isLoading: boolean;
  isPublished: boolean;
  publicUrl: string | null;
  renderTitleSkeleton: () => JSX.Element;
  renderButtonsSkeleton: () => JSX.Element;
  activeUsers: Array<{ name: string; color: string }>;
  createdAt?: Date;
  updatedAt?: Date;
}

export function HeaderNote({
  title,
  setTitle,
  noteId,
  collaborationId,
  isCollaborating,
  startCollaboration,
  endCollaboration,
  handleSave,
  handlePublish,
  handleImageUpload,
  isLoading,
  isPublished,
  publicUrl,
  renderTitleSkeleton,
  renderButtonsSkeleton,
  activeUsers,
  createdAt,
  updatedAt,
}: HeaderNoteProps) {
  console.log("HeaderNote dates:", { createdAt, updatedAt });

  // Add state to track scroll position
  const [scrolled, setScrolled] = useState(false);

  // Add effect to detect scrolling
  useEffect(() => {
    const scrollContainer = document.querySelector('.blocknote-container');

    if (!scrollContainer) return;

    const handleScroll = () => {
      setScrolled(scrollContainer.scrollTop > 10);
    };

    // Initial check
    handleScroll();

    // Add event listener with proper handler reference
    scrollContainer.addEventListener('scroll', handleScroll);

    // Cleanup with the same handler reference
    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className={cn(
      "header-note",
      scrolled && "scrolled"
    )}>
      <div className="w-full mx-auto px-8">
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              {isLoading ? renderTitleSkeleton() : (
                <>
                  <Input
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="title-input border-none focus:outline-none bg-transparent"
                    placeholder="Untitled"
                    disabled={isLoading}
                  />
                  {isCollaborating && (
                    <div className="flex items-center gap-4">
                      <div className="flex -space-x-2">
                        {activeUsers.map((user, i) => (
                          <div
                            key={user.name}
                            className="w-8 h-8 rounded-full flex items-center justify-center text-xs text-white relative"
                            style={{ backgroundColor: user.color }}
                            title={user.name}
                          >
                            {user.name[0].toUpperCase()}
                            <div
                              className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full bg-green-500 border-2 border-white"
                              title="Online"
                            />
                          </div>
                        ))}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {activeUsers.length} {activeUsers.length === 1 ? 'user' : 'users'} connected
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
            {isLoading ? renderButtonsSkeleton() : (
              <div className="flex items-center gap-1">
                <CollaborationUI
                  noteId={noteId}
                  collaborationId={collaborationId}
                  isCollaborating={isCollaborating}
                  onStartCollaboration={startCollaboration}
                  onEndCollaboration={endCollaboration}
                  activeUsers={activeUsers}
                />

                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant={isPublished ? "default" : "outline"}
                      className="h-7 px-2 text-xs rounded-md font-medium"
                      disabled={isLoading}
                    >
                      <Globe2 className="h-3 w-3 mr-1" />
                      {isPublished ? 'Published' : 'Publish'}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Share your note</DialogTitle>
                      <DialogDescription>
                        Anyone with the link can view this note
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      {publicUrl && (
                        <div className="flex items-center gap-2">
                          <Input
                            value={`${window.location.origin}/p/${publicUrl}`}
                            readOnly
                            className="bg-muted"
                          />
                          <Button
                            onClick={() => {
                              navigator.clipboard.writeText(`${window.location.origin}/p/${publicUrl}`)
                              toast.success('Link copied to clipboard')
                            }}
                            variant="outline"
                          >
                            Copy
                          </Button>
                        </div>
                      )}
                      <Button
                        onClick={handlePublish}
                        className="w-full"
                        disabled={isLoading}
                      >
                        {isPublished ? 'Update public version' : 'Publish note'}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button
                  variant="outline"
                  className="h-7 px-2 text-xs rounded-md font-medium"
                  onClick={() => document.getElementById('imageUpload')?.click()}
                  disabled={isLoading}
                >
                  <Upload className="h-3 w-3 mr-1" />
                  Cover
                </Button>

                <Button
                  variant="outline"
                  className="h-7 px-2 text-xs rounded-md font-medium"
                  onClick={handleSave}
                  disabled={isLoading}
                >
                  <Save className="h-3 w-3 mr-1" />
                  Save
                </Button>
              </div>
            )}
          </div>

          {/* Date information */}
          {!isLoading && (createdAt || updatedAt) && (
            <div className="note-date-info flex items-center justify-between">
              <div className="flex items-center gap-3">
                {createdAt && (
                  <div className="flex items-center gap-1" title={format(createdAt, 'PPpp')}>
                    <Calendar className="h-3 w-3" />
                    <span>Created {format(createdAt, 'MMM d, yyyy')}</span>
                  </div>
                )}
                {updatedAt && (
                  <div className="flex items-center gap-1" title={format(updatedAt, 'PPpp')}>
                    <Clock className="h-3 w-3" />
                    <span>Updated {formatDistanceToNow(updatedAt, { addSuffix: true })}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}