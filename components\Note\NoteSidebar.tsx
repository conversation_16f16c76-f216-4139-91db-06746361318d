"use client"

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  PlusCircle, 
  Folder, 
  FileText, 
  ChevronRight, 
  ChevronDown, 
  Plus, 
  Trash2, 
  Search,
  MoreHorizontal,
  GripVertical,
  Globe2,
  GitMerge,
  Users
} from "lucide-react"
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Link from 'next/link'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertD<PERSON>ogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "sonner"

interface SidebarProps {
  notes: any[]
  onSelectNote: (note: any) => void
  onNewNote: (parentId?: string) => void
  onNewFolder: (folderName: string, parentId?: string) => void
  onDeleteNote: (noteId: string) => void
  isLoading?: boolean
}

const MIN_SIDEBAR_WIDTH = 240
const MAX_SIDEBAR_WIDTH = 480

const NoteSidebar: React.FC<SidebarProps> = ({ 
  notes, 
  onSelectNote, 
  onNewNote, 
  onNewFolder, 
  onDeleteNote,
  isLoading = false
}) => {
  const [width, setWidth] = useState(320)
  const [isResizing, setIsResizing] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')
  const [expandedFolders, setExpandedFolders] = useState<string[]>([])
  const [activeNote, setActiveNote] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const sidebarRef = useRef<HTMLDivElement>(null)
  const [itemToDelete, setItemToDelete] = useState<any | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return
      
      const newWidth = e.clientX
      if (newWidth >= MIN_SIDEBAR_WIDTH && newWidth <= MAX_SIDEBAR_WIDTH) {
        setWidth(newWidth)
      }
    }

    const handleMouseUp = () => {
      setIsResizing(false)
    }

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isResizing])

  const toggleFolder = (folderId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpandedFolders(prev => 
      prev.includes(folderId) 
        ? prev.filter(f => f !== folderId)
        : [...prev, folderId]
    )
  }

  const organizeNotes = (notes: any[], parentId: string | null = null): any[] => {
    return notes
      .filter(note => note.parentId === parentId)
      .sort((a, b) => {
        if (a.isFolder && !b.isFolder) return -1
        if (!a.isFolder && b.isFolder) return 1
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      })
  }

  const renderNoteTree = (parentId: string | null = null, level: number = 0, isLastChild: boolean = true) => {
    const filteredNotes = organizeNotes(notes, parentId).filter(note =>
      !searchQuery || note.title.toLowerCase().includes(searchQuery.toLowerCase())
    )

    return filteredNotes.map((note, index) => {
      const isLast = index === filteredNotes.length - 1
      
      // Parse metadata to check if the note is merged
      let isMerged = false;
      let mergedDate = null;
      let isCollaborative = false;
      try {
        if (note.metadata) {
          const metadata = typeof note.metadata === 'string'
            ? JSON.parse(note.metadata)
            : note.metadata;
          
          isMerged = !!metadata.mergedFromPublicId;
          mergedDate = metadata.mergedAt ? new Date(metadata.mergedAt) : null;
          isCollaborative = !!metadata.collaborationId;
        }
      } catch (e) {
        console.error('Error parsing note metadata for collaboration:', e);
      }
      
      return (
        <div key={note.id}>
          <div 
            className="group relative flex"
            style={{ paddingLeft: level > 0 ? `${(level * 12) + 12}px` : '12px' }}
          >
            {/* Tree Lines */}
            {level > 0 && (
              <>
                {/* Vertical line from parent */}
                <div 
                  className="absolute left-[12px] top-0 bottom-0 w-px bg-border"
                  style={{ 
                    left: `${level * 12}px`,
                    display: isLast ? 'none' : 'block'
                  }}
                />
                {/* Horizontal line to item */}
                <div 
                  className="absolute w-[12px] h-px bg-border"
                  style={{ 
                    left: `${level * 12}px`,
                    top: '12px'
                  }}
                />
              </>
            )}

            <div
              onClick={() => {
                if (!note.isFolder) {
                  onSelectNote(note)
                  setActiveNote(note.id)
                }
              }}
              className={cn(
                "flex items-center gap-2 px-2 py-1 text-sm hover:bg-secondary/50 rounded-sm cursor-pointer w-full",
                activeNote === note.id && "bg-secondary",
                note.isFolder && "font-medium"
              )}
            >
              <div 
                className="flex items-center gap-1 flex-1 min-w-0"
                onClick={(e) => note.isFolder && toggleFolder(note.id, e)}
              >
                {note.isFolder ? (
                  expandedFolders.includes(note.id) ? (
                    <ChevronDown className="h-4 w-4 shrink-0 text-muted-foreground" />
                  ) : (
                    <ChevronRight className="h-4 w-4 shrink-0 text-muted-foreground" />
                  )
                ) : (
                  <div className="w-4" />
                )}
                {note.isFolder ? (
                  <Folder className="h-4 w-4 shrink-0 text-muted-foreground" />
                ) : isCollaborative ? (
                  <Users className="h-4 w-4 shrink-0 text-blue-400" />
                ) : isMerged ? (
                  <GitMerge className="h-4 w-4 shrink-0 text-blue-500" />
                ) : (
                  <FileText className="h-4 w-4 shrink-0 text-muted-foreground" />
                )}
                <span className="truncate">{note.title}</span>
                
                {/* Show note dates as a tooltip on hover */}
                <div className="hidden group-hover:block absolute left-0 bottom-full mb-2 p-2 bg-popover border rounded shadow-sm text-xs z-50 w-48">
                  <div className="flex flex-col gap-1">
                    <div>
                      <span className="font-medium">Created:</span> {format(new Date(note.createdAt), 'MMM d, yyyy')}
                    </div>
                    <div>
                      <span className="font-medium">Updated:</span> {format(new Date(note.updatedAt), 'MMM d, yyyy')}
                    </div>
                    {isMerged && mergedDate && (
                      <div>
                        <span className="font-medium">Merged:</span> {format(mergedDate, 'MMM d, yyyy')}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    {note.isFolder && (
                      <>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onNewNote(note.id);
                        }}>
                          <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          const name = prompt('Enter folder name');
                          if (name) onNewFolder(name, note.id);
                        }}>
                          <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuItem 
                      onClick={(e) => handleDeleteItem(note, e)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-3.5 w-3.5" /> Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
          {note.isFolder && expandedFolders.includes(note.id) && (
            <div className="relative">
              {renderNoteTree(note.id, level + 1, isLast)}
            </div>
          )}
        </div>
      )
    })
  }

  // Add skeleton loading for note tree
  const renderSkeletonNoteTree = (count = 8, level = 0) => {
    return Array(count)
      .fill(0)
      .map((_, index) => (
        <div 
          key={`skeleton-${index}`}
          className="flex items-center gap-2 px-2 py-1"
          style={{ paddingLeft: level > 0 ? `${(level * 12) + 12}px` : '12px' }}
        >
          <div className="w-4 h-4" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className={`h-4 w-${Math.floor(Math.random() * 24) + 16}`} />
        </div>
      ))
  }

  // Add skeleton loading for published notes
  const renderSkeletonPublishedNotes = (count = 3) => {
    return (
      <div className="mb-4">
        <div className="flex items-center gap-2 px-2 py-1 text-sm">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-6 rounded-full" />
        </div>
        <div className="mt-1 space-y-0.5">
          {Array(count)
            .fill(0)
            .map((_, index) => (
              <div key={`pub-skeleton-${index}`} className="flex items-center gap-2 px-2 py-1">
                <Skeleton className="h-4 w-4" />
                <Skeleton className={`h-4 w-${Math.floor(Math.random() * 32) + 32}`} />
              </div>
            ))}
        </div>
        <Separator className="my-4" />
      </div>
    )
  }

  // Filter published notes - add check to ensure notes is an array
  const publishedNotes = Array.isArray(notes) 
    ? notes.filter(note => note.isPublished)
    : [];

  const handleDeleteItem = (item: any, e: React.MouseEvent) => {
    e.stopPropagation();
    setItemToDelete(item);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (!itemToDelete) return;
    
    toast.promise(
      Promise.resolve(onDeleteNote(itemToDelete.id)),
      {
        loading: `Deleting ${itemToDelete.isFolder ? 'folder' : 'note'}...`,
        success: `${itemToDelete.isFolder ? 'Folder' : 'Note'} deleted successfully`,
        error: 'Failed to delete'
      }
    );
    
    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  return (
    <div 
      ref={sidebarRef}
      className="relative flex flex-col h-[calc(100vh-4rem)] border-r bg-background/60 backdrop-blur-xl"
      style={{ width: `${width}px` }}
    >
      <div 
        className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-primary/10 transition-colors"
        onMouseDown={() => setIsResizing(true)}
      >
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 rounded-full hover:bg-primary/20" />
      </div>

      {/* Header */}
      <div className="p-4 flex-shrink-0">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Notes</h2>
          {isLoading ? (
            <Skeleton className="h-8 w-8 rounded-md" />
          ) : (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-secondary">
                <Plus className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => onNewNote()}>
                <FileText className="h-4 w-4 mr-2" />
                New Note
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                const name = prompt('Enter folder name')
                if (name) onNewFolder(name)
              }}>
                <Folder className="h-4 w-4 mr-2" />
                New Folder
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          )}
        </div>

        <div className="relative">
          {isLoading ? (
            <Skeleton className="h-9 w-full" />
          ) : (
            <>
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search notes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-9 bg-secondary/50"
          />
            </>
          )}
        </div>
      </div>

      {/* Note Tree */}
      <div className="flex-1 overflow-y-auto px-2 py-2">
        {isLoading ? (
          <>
            {/* Skeleton for published notes */}
            {renderSkeletonPublishedNotes()}
            
            {/* Skeleton for regular notes */}
            <div className="space-y-0.5">
              {renderSkeletonNoteTree(8, 0)}
              <div style={{ paddingLeft: '24px' }}>
                {renderSkeletonNoteTree(3, 1)}
              </div>
              {renderSkeletonNoteTree(5, 0)}
            </div>
          </>
        ) : (
          <>
        {/* Published Notes Section */}
        {publishedNotes.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center gap-2 px-2 py-1 text-sm font-medium text-muted-foreground">
              <Globe2 className="h-4 w-4" />
              <span>Published Notes</span>
              <span className="text-xs bg-primary/10 text-primary px-1.5 rounded-full">
                {publishedNotes.length}
              </span>
            </div>
            <div className="mt-1 space-y-0.5">
              {publishedNotes.map(note => (
                <div
                  key={note.id}
                  onClick={() => {
                    onSelectNote(note)
                    setActiveNote(note.id)
                  }}
                  className={cn(
                    "flex items-center gap-2 px-2 py-1 text-sm hover:bg-secondary/50 rounded-sm cursor-pointer",
                    activeNote === note.id && "bg-secondary"
                  )}
                >
                  <FileText className="h-4 w-4 shrink-0 text-muted-foreground" />
                  <span className="truncate flex-1">{note.title}</span>
                  <Link
                    // @ts-ignore
                    href={`/p/${note.publicId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="opacity-0 group-hover:opacity-100 text-muted-foreground hover:text-primary"
                    onClick={e => e.stopPropagation()}
                  >
                    <Globe2 className="h-3 w-3" />
                  </Link>
                </div>
              ))}
            </div>
            <Separator className="my-4" />
          </div>
        )}

        {/* Regular Notes Tree */}
        <div className="space-y-0.5">
          {renderNoteTree()}
        </div>
          </>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 text-xs text-muted-foreground bg-secondary/20">
        {isLoading ? (
          <Skeleton className="h-4 w-32" />
        ) : (
          <>{notes.length} note{notes.length !== 1 ? 's' : ''} • {publishedNotes.length} published</>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {itemToDelete?.isFolder 
                ? "This will delete the folder and all its contents. This action cannot be undone."
                : "This will permanently delete this note. This action cannot be undone."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default NoteSidebar