"use client"

import "@blocknote/core/fonts/inter.css"
import { BlockNoteView } from "@blocknote/mantine"
import "@blocknote/core/style.css"
import { useB<PERSON>Note, createReactBlockSpec } from "@blocknote/react"
import { defaultProps, BlockNoteSchema, defaultBlockSpecs } from "@blocknote/core"
import { useTheme } from "next-themes"
import { format } from "date-fns"
import { Calendar, Globe2, User, Database, BarChart3, LineChart, PieChart, AreaChart, TableIcon, ImageIcon, Maximize2, GitMerge, ArrowRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import { ChartVisualizer } from "@/components/ChartBuilder/ChartVisualizer"
import { GraphicWalkerVisualization } from "@/components/ChartBuilder/GraphicWalker"
import { useRouter } from "next/navigation"

const customDarkTheme = {
  colors: {
    editor: {
      text: 'hsl(0, 0%, 98%)',
      background: 'transparent',
    },
    menu: {
      text: 'hsl(0, 0%, 98%)',
      background: 'hsl(240, 3.7%, 15.9%)',
    },
    tooltip: {
      text: 'hsl(0, 0%, 98%)',
      background: 'hsl(240, 10%, 3.9%)',
    },
    hovered: {
      text: 'hsl(0, 0%, 98%)',
      background: 'hsl(240, 3.7%, 15.9%)',
    },
    selected: {
      text: 'hsl(240, 5.9%, 10%)',
      background: 'hsl(0, 0%, 98%)',
    },
    disabled: {
      text: 'hsl(240, 5%, 64.9%)',
      background: 'hsl(240, 3.7%, 15.9%)',
    },
    shadow: 'hsl(240, 3.7%, 15.9%)',
    border: 'hsl(240, 3.7%, 15.9%)',
  },
}

// Create a read-only version of DataAnalysisBlock for public view
const ReadOnlyDataAnalysisBlock = createReactBlockSpec(
  {
    type: "dataAnalysis",
    propSchema: {
      // Include default styling props
      textAlignment: defaultProps.textAlignment,
      textColor: defaultProps.textColor,
      analysisType: {
        default: "code",
        values: ["code", "table", "chart"],
      },
      // Store the Cell state
      savedQuery: {
        default: "",
      },
      language: {
        default: "sql",
      },
      cellId: {
        default: "",
      },
      datasetId: {
        default: "ds1",
      },
      // Store execution results
      resultData: {
        default: "[]",
      },
      resultOutput: {
        default: "",
      },
      resultPlots: {
        default: "[]",
      },
      hasError: {
        default: false,
      },
      errorMessage: {
        default: "",
      },
      // Add GraphicWalker flag
      showGraphicWalker: {
        default: false,
      },
      // Add chart configuration
      chartType: {
        default: "bar",
      },
      chartConfig: {
        default: "{}",
      },
      // Add viewMode to stored props
      viewMode: {
        default: "table",
      },
    },
    content: "none",
  },
  {
    render: (props) => {
      // Local state for chart type and view mode
      const [chartType, setChartType] = useState<'bar' | 'line' | 'pie' | 'area'>(
        (props.block.props.chartType as 'bar' | 'line' | 'pie' | 'area') || 'bar'
      );
      const [viewMode, setViewMode] = useState<'table' | 'chart' | 'output' | 'plots' | 'graphicwalker'>(
        (props.block.props.viewMode as any) || 'table'
      );
      
      // Extract the data from the block props
      let resultData: any[] = [];
      try {
        if (props.block.props.resultData) {
          resultData = JSON.parse(props.block.props.resultData);
        }
      } catch (error) {
        console.error("Failed to parse result data:", error);
      }
      
      // Format cell value for display
      const formatCellValue = (value: any): string => {
        if (value === null || value === undefined) return '-'
        if (typeof value === 'object') return JSON.stringify(value)
        if (typeof value === 'number') return value.toLocaleString()
        return String(value)
      }
      
      // Get columns for chart configuration
      const columns = resultData.length > 0 ? Object.keys(resultData[0]) : [];
      
      // Find numeric columns
      const numericColumns = resultData.length > 0 
        ? Object.keys(resultData[0]).filter(key => 
            typeof resultData[0][key] === 'number' || !isNaN(Number(resultData[0][key]))
          )
        : ['_index'];
      
      // Find category columns
      const categoryColumns = resultData.length > 0 
        ? Object.keys(resultData[0]).filter(key => 
            typeof resultData[0][key] === 'string'
          )
        : [];
      
      // Simple chart config
      const chartConfig = {
        xAxis: categoryColumns[0] || columns[0] || '_index',
        yAxis: numericColumns[0] || '_index',
        title: 'Data Visualization',
        description: 'Chart visualization',
        showLegend: true,
        showLabels: false,
        showGrid: true,
      };
      
      // Check if data is available
      const hasData = resultData && resultData.length > 0;
      const hasPlots = props.block.props.resultPlots && JSON.parse(props.block.props.resultPlots).length > 0;
      const plots = hasPlots ? JSON.parse(props.block.props.resultPlots) : [];

      // Create a simplified read-only view
      return (
        <Card className="w-full my-4 overflow-hidden border">
          <CardHeader className="py-2 px-4 bg-muted/20">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium">
                <Database className="h-4 w-4 inline mr-2" />
                Data Analysis ({props.block.props.language})
              </CardTitle>
            </div>
          </CardHeader>
          
          {/* Controls - Following QueryResult.tsx style */}
          <div className="flex flex-wrap items-center justify-between p-3 border-b bg-muted/50 gap-2">
            <span className="text-sm text-muted-foreground">
              {hasData ? `${resultData.length} rows` : ''}
              {props.block.props.resultOutput ? ' | Has output' : ''}
              {hasPlots ? ` | ${plots.length} plot${plots.length > 1 ? 's' : ''}` : ''}
            </span>
            <div className="flex flex-wrap gap-2">
              {props.block.props.hasError && (
                <span className="text-sm text-red-500 font-medium">
                  Execution Failed
                </span>
              )}
              {!props.block.props.hasError && (
                <span className="text-sm text-green-500 font-medium">
                  Execution Successful
                </span>
              )}
              
              {/* Table button */}
              {hasData && (
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <TableIcon className="h-4 w-4 mr-2" />
                  Table
                </Button>
              )}
              
              {/* Chart button */}
              {hasData && (
                <Button
                  variant={viewMode === 'chart' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('chart')}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Chart
                </Button>
              )}
              
              {/* Visual Explorer button */}
              {hasData && (
                <Button
                  variant={viewMode === 'graphicwalker' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('graphicwalker')}
                >
                  <Maximize2 className="h-4 w-4 mr-2" />
                  Visual Explorer
                </Button>
              )}
              
              {/* Output View button */}
              {props.block.props.resultOutput && (
                <Button
                  variant={viewMode === 'output' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('output')}
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Output
                </Button>
              )}
              
              {/* Plots View button */}
              {hasPlots && (
                <Button
                  variant={viewMode === 'plots' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('plots')}
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Plots
                </Button>
              )}
            </div>
          </div>
          
          {/* Chart Type Selector - Only visible when chart view is active */}
          {viewMode === 'chart' && (
            <div className="flex flex-wrap items-center gap-2 p-2 border-b bg-muted/20">
              <div className="flex items-center gap-1">
                <Button
                  variant={chartType === 'bar' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => setChartType('bar')}
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
                
                <Button
                  variant={chartType === 'line' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => setChartType('line')}
                >
                  <LineChart className="h-4 w-4" />
                </Button>
                
                <Button
                  variant={chartType === 'pie' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => setChartType('pie')}
                >
                  <PieChart className="h-4 w-4" />
                </Button>
                
                <Button
                  variant={chartType === 'area' ? 'default' : 'outline'}
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => setChartType('area')}
                >
                  <AreaChart className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          
          <CardContent className="p-0">
            {/* Show the code */}
            {props.block.props.savedQuery && (
              <div className="p-4">
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto whitespace-pre-wrap font-mono">
                    {props.block.props.savedQuery}
                  </pre>
                </div>
              </div>
            )}
            
            {/* Content Area - Following QueryResult.tsx style */}
            <div className="grid gap-4 p-4 grid-cols-1">
              {/* Charts Section - only show when explicitly selected */}
              {viewMode === 'chart' && (
                <div className="col-span-full border rounded-md">
                  <ChartVisualizer 
                    data={resultData} 
                    initialChartType={chartType}
                    chartConfig={chartConfig}
                    showConfig={true}
                  />
                </div>
              )}
              
              {/* Table View */}
              {viewMode === 'table' && hasData && (
                <div className="max-h-[500px] overflow-y-auto border rounded-md">
                  <div className="overflow-x-auto">
                    <div className="min-w-full">
                      <table className="w-full border-collapse table-auto">
                        <thead className="sticky top-0 bg-background border-b z-10">
                          <tr>
                            {Object.keys(resultData[0]).map((column) => (
                              <th
                                key={column}
                                className="p-3 text-left font-medium text-sm bg-muted/50 whitespace-nowrap"
                              >
                                {column}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {resultData.map((row, i) => (
                            <tr key={i} className="border-b hover:bg-muted/50">
                              {Object.keys(resultData[0]).map((column, j) => (
                                <td key={`${i}-${j}`} className="p-3 text-sm whitespace-nowrap">
                                  {formatCellValue(row[column])}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
              
              {/* GraphicWalker View - full width */}
              {viewMode === 'graphicwalker' && hasData && (
                <div className="col-span-full p-0 border rounded-md">
                  <GraphicWalkerVisualization 
                    data={resultData}
                    title="Interactive Data Explorer" 
                    onBack={() => setViewMode('table')}
                  />
                </div>
              )}
              
              {/* Output View */}
              {viewMode === 'output' && (props.block.props.resultOutput || props.block.props.hasError) && (
                <div className="max-h-[500px] overflow-y-auto border rounded-md">
                  <div className="p-4">
                    <pre className={cn(
                      "whitespace-pre-wrap p-4 rounded-md text-sm font-mono",
                      props.block.props.hasError ? "bg-red-50 dark:bg-red-950/20 text-red-600 dark:text-red-400" : "bg-muted"
                    )}>
                      {props.block.props.resultOutput || props.block.props.errorMessage}
                    </pre>
                  </div>
                </div>
              )}
              
              {/* Plots View */}
              {viewMode === 'plots' && hasPlots && (
                <div className="max-h-[500px] overflow-y-auto border rounded-md">
                  <div className="p-4 space-y-4">
                    {plots.map((plot: string, index: number) => {
                      // Ensure the plot data is properly formatted
                      const imageData = plot.startsWith('data:image') 
                        ? plot 
                        : `data:image/png;base64,${plot}`;
                      
                      return (
                        <div key={index} className="flex justify-center">
                          <img 
                            src={imageData}
                            alt={`Plot ${index + 1}`}
                            className="max-w-full h-auto rounded-lg shadow-lg"
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      );
    },
  }
);

export default function PublicNoteView({ note }: { note: any }) {
  const { resolvedTheme } = useTheme()
  const router = useRouter()
  
  // Create the schema with our custom blocks
  const schema = BlockNoteSchema.create({
    blockSpecs: {
      // Include default blocks
      ...defaultBlockSpecs,
      // Add our read-only data analysis block
      dataAnalysis: ReadOnlyDataAnalysisBlock,
    },
  });
  
  const editor = useBlockNote({
    schema,
    initialContent: 
      typeof note.content === "string" 
        ? JSON.parse(note.content)
        : note.content,
    // readonly: true
  })

  // Add this function to handle merging
  const handleMergeNote = () => {
    // Generate a URL with the note's public ID to import it
    router.push(`/hr/workspace/note?import=${note.publicId}`)
  }

  return (
    <div className="flex flex-col min-h-screen max-w-full">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        {/* Header Section */}
        <div className="mb-8">
          {note.coverImage && (
            <div className="mb-6 rounded-lg overflow-hidden h-[300px] relative">
              <img 
                src={note.coverImage} 
                alt="Cover" 
                className="w-full h-full object-cover transition-all duration-300 hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/10" />
            </div>
          )}

          {/* Title and Metadata */}
          <div className="py-6">
            <div className="flex justify-between items-start">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight">
                {note.title}
              </h1>
              
              {/* Add the Merge button */}
              <Button 
                onClick={handleMergeNote}
                className="flex items-center gap-2"
              >
                <GitMerge className="h-4 w-4" />
                Merge to My Notes
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Metadata */}
            <div className="flex flex-wrap gap-4 mt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{format(new Date(note.updatedAt || note.createdAt), 'MMMM d, yyyy')}</span>
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{note.author?.name || 'Anonymous'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe2 className="h-4 w-4" />
                <span>Public Note</span>
              </div>
            </div>
          </div>
        </div>

        {/* Note Content */}
        <div className="prose prose-sm md:prose-base dark:prose-invert max-w-none pb-16">
          <BlockNoteView
            editor={editor}
            theme={resolvedTheme === "dark" ? customDarkTheme : "light"}
            editable={false}
            slashMenu={false}
            formattingToolbar={false}
          />
        </div>
      </div>
    </div>
  )
}
