"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ResizableHandle, ResizablePanel } from "@/components/ui/resizable"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "sonner"
import { Node, Edge } from 'reactflow'
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"

interface SavedDiagram {
  id: string
  title: string
  content: {
    nodes: Node[]
    edges: Edge[]
  }
  createdAt: string
  isPublic?: boolean
  publicId?: string
}

interface SettingsSidebarProps {
  isOpen: boolean
  savedDiagrams: SavedDiagram[]
  isLoadingSaved: boolean
  onLoadDiagram: (diagram: SavedDiagram) => void
  defaultEdgeOptions: any
  setDefaultEdgeOptions: (options: any) => void
  isAnimatedEdges: boolean
  setIsAnimatedEdges: (value: boolean) => void
  showNodeBg: boolean
  setShowNodeBg: (value: boolean) => void
  onTogglePublic: (diagramId: string) => Promise<void>
}

const ShareCard = ({ diagram, onTogglePublic }: { 
  diagram: SavedDiagram
  onTogglePublic: (id: string) => Promise<void>
}) => {
  const publicUrl = diagram.publicId ? 
    `${window.location.origin}/workflow/${diagram.publicId}` : null;

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base">{diagram.title}</CardTitle>
            <CardDescription>
              {new Date(diagram.createdAt).toLocaleDateString()}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {diagram.isPublic ? 'Public' : 'Private'}
            </span>
            <Switch
              checked={diagram.isPublic}
              onCheckedChange={() => onTogglePublic(diagram.id)}
            />
          </div>
        </div>
      </CardHeader>
      {diagram.isPublic && publicUrl && (
        <CardContent>
          <div className="flex items-center gap-2">
            <Input
              value={publicUrl}
              readOnly
              className="text-sm font-mono"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                navigator.clipboard.writeText(publicUrl);
                toast.success('Link copied to clipboard');
              }}
            >
              Copy
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export function SettingsSidebar({
  isOpen,
  savedDiagrams,
  isLoadingSaved,
  onLoadDiagram,
  defaultEdgeOptions,
  setDefaultEdgeOptions,
  isAnimatedEdges,
  setIsAnimatedEdges,
  showNodeBg,
  setShowNodeBg,
  onTogglePublic,
}: SettingsSidebarProps) {
  if (!isOpen) return null

  return (
    <>
      <ResizableHandle withHandle />
      <ResizablePanel defaultSize={20} minSize={20} maxSize={40}>
        <div className="h-full flex flex-col bg-background border-l">
          <Tabs defaultValue="settings" className="flex-1">
            <TabsList className="w-full justify-start border-b px-4 pt-2">
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="saved">Saved Flows</TabsTrigger>
            </TabsList>
            
            <TabsContent value="settings" className="flex-1 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-6">
                  {/* Edge Settings */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Edge Settings</h4>
                    <div className="flex items-center justify-between">
                      <span>Animated Edges</span>
                      <Switch
                        checked={isAnimatedEdges}
                        onCheckedChange={setIsAnimatedEdges}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Edge Style</Label>
                      <Select
                        value={defaultEdgeOptions.type}
                        onValueChange={(value) => {
                          // @ts-ignore
                          setDefaultEdgeOptions(prev => ({
                            ...prev,
                            type: value
                          }))
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select edge style" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="smoothstep">Smooth Step</SelectItem>
                          <SelectItem value="custom">Bezier</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Node Settings */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Node Settings</h4>
                    <div className="flex items-center justify-between">
                      <span>Show Node Background</span>
                      <Switch
                        checked={showNodeBg}
                        onCheckedChange={setShowNodeBg}
                      />
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="saved" className="flex-1 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {isLoadingSaved ? (
                    <div className="flex items-center justify-center p-4">
                      <span className="animate-spin">⚡</span>
                    </div>
                  ) : savedDiagrams.length === 0 ? (
                    <div className="text-center text-muted-foreground p-4">
                      No saved diagrams found
                    </div>
                  ) : (
                    savedDiagrams.map((diagram) => (
                      <div key={diagram.id}>
                        <ShareCard
                          diagram={diagram}
                          onTogglePublic={onTogglePublic}
                        />
                        <Button 
                          variant="outline" 
                          className="w-full mt-2"
                          onClick={() => onLoadDiagram(diagram)}
                        >
                          Load Diagram
                        </Button>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
      </ResizablePanel>
    </>
  )
} 