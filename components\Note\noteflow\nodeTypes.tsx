"use client"

import { <PERSON>deP<PERSON>, <PERSON><PERSON>, Position } from 'reactflow'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { IconType } from 'react-icons'
import { SiReact } from 'react-icons/si'
import { EmployeeNode } from './nodes/EmployeeNode'
import { GroupNode } from './nodes/GroupNode'

interface NoteData {
  label: string;
  note?: string;
}

interface IconNodeData {
  label: string;
  icon: IconType;
}

interface ImageNodeData {
  label: string;
  imageUrl?: string;
}

export const nodeTypes = {
  square: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[60px] flex items-center justify-center shadow-md rounded-md border-2 border-gray-200 dark:border-gray-700">
      <Handle type="target" position={Position.Top} className="w-16 !bg-blue-300" />
      <div className="font-bold text-center dark:text-white text-black">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-blue-300" />
    </div>
  ),
  circle: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[100px] flex items-center justify-center shadow-md rounded-full border-2 border-gray-200 dark:border-gray-700">
      <Handle type="target" position={Position.Top} className="w-16 !bg-green-300" />
      <div className="font-bold text-center dark:text-white text-black">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-green-300" />
    </div>
  ),
  note: ({ data }: NodeProps<NoteData>) => (
    <div className="w-[300px] shadow-lg">
      <Card className="border-2 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="dark:text-white text-black">{data.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground whitespace-pre-wrap">
            {data.note}
          </p>
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  ),
  icon: ({ data }: NodeProps<IconNodeData>) => {
    const IconComponent = data.icon || SiReact;
    return (
      <div className="relative w-[100px] h-[100px] flex flex-col items-center justify-center 
                    bg-background/95 shadow-sm rounded-lg border border-border
                    hover:shadow-lg hover:border-primary transition-all duration-200">
        <Handle 
          type="target" 
          position={Position.Top} 
          className="!w-12 !bg-primary/30" 
        />
        <div className="flex flex-col items-center gap-2 p-2">
          <IconComponent 
            size={40}
            className="grayscale hover:grayscale-0 transition-all duration-300"
          />
          <div className="text-xs font-medium text-muted-foreground text-center truncate w-full">
            {data.label}
          </div>
        </div>
        <Handle 
          type="source" 
          position={Position.Bottom} 
          className="!w-12 !bg-primary/30" 
        />
      </div>
    );
  },
  image: ({ data }: NodeProps<ImageNodeData>) => (
    <div className="w-[300px] shadow-lg">
      <Card className="border-2 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="dark:text-white text-black">{data.label}</CardTitle>
        </CardHeader>
        <CardContent>
          {data.imageUrl && (
            <img 
              src={data.imageUrl} 
              alt={data.label}
              className="w-full h-auto rounded-md"
            />
          )}
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  ),
  employee: EmployeeNode,
  group: GroupNode,
} 