"use client"

import { <PERSON>de<PERSON><PERSON>, <PERSON><PERSON>, Position } from 'reactflow'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { User, Building2, Mail } from 'lucide-react'

export interface EmployeeNodeData {
  label: string;
  firstName?: string;
  lastName?: string;
  department?: string;
  position?: string;
  email?: string;
  imageUrl?: string;
  backgroundColor?: string;
}

export const EmployeeNode = ({ data }: NodeProps<EmployeeNodeData>) => {
  return (
    <div className="w-[300px] shadow-lg">
      <Card 
        className="border border-border"
        style={{ 
          backgroundColor: data.backgroundColor || 'var(--background)',
        }}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-background flex items-center justify-center">
              {data.imageUrl ? (
                <img 
                  src={data.imageUrl} 
                  alt={data.label}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-6 h-6 text-primary" />
              )}
            </div>
            <div>
              <CardTitle className="text-base text-foreground">
                {data.firstName} {data.lastName}
              </CardTitle>
              <p className="text-sm text-muted-foreground">{data.position}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <Building2 className="w-4 h-4 text-muted-foreground" />
              <span className="text-foreground">{data.department}</span>
            </div>
            {data.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <span className="text-foreground">{data.email}</span>
              </div>
            )}
          </div>
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  )
} 