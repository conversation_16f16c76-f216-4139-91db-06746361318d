"use client"

import React, { useState, useEffect, useRef } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import { Button } from '@/components/ui/button';
import { Download, Maximize2, Minimize2, Loader2, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, AlertCircle } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

interface PDFViewerProps {
  url: string;
  initialPage?: number;
  scale?: number;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  url,
  initialPage = 1,
  scale: initialScale = 1.0
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(initialScale);
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const downloadPDF = () => {
    const link = document.createElement('a');
    link.href = url;
    link.download = url.split('/').pop() || 'document.pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const loadPDF = async () => {
    try {
      setLoading(true);
      setError(null);

      // For authenticated requests, we need to include credentials
      const loadingTask = pdfjsLib.getDocument({
        url: url,
        withCredentials: true, // Include cookies for authentication
        httpHeaders: {
          // Add any additional headers if needed
        }
      });
      
      const pdf = await loadingTask.promise;
      
      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
      setCurrentPage(initialPage);
      
      await renderPage(pdf, initialPage, scale);
      setLoading(false);
    } catch (err: any) {
      console.error('Error loading PDF:', err);
      
      // Provide more specific error messages
      if (err.name === 'PasswordException') {
        setError('This PDF is password protected');
      } else if (err.name === 'InvalidPDFException') {
        setError('Invalid PDF file');
      } else if (err.name === 'MissingPDFException') {
        setError('PDF file not found');
      } else if (err.message?.includes('401') || err.message?.includes('403')) {
        setError('Access denied. Please check your authentication.');
      } else {
        setError('Failed to load PDF document. Please try again.');
      }
      
      setLoading(false);
    }
  };

  const renderPage = async (pdf: any, pageNum: number, currentScale: number) => {
    try {
      const page = await pdf.getPage(pageNum);
      const canvas = canvasRef.current;
      if (!canvas) return;

      const viewport = page.getViewport({ scale: currentScale });
      const context = canvas.getContext('2d');

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;
    } catch (err) {
      console.error('Error rendering page:', err);
      setError('Failed to render PDF page');
    }
  };

  const goToPage = async (pageNum: number) => {
    if (pageNum < 1 || pageNum > totalPages || !pdfDocument) return;
    
    setCurrentPage(pageNum);
    await renderPage(pdfDocument, pageNum, scale);
  };

  const changeScale = async (newScale: number) => {
    if (newScale < 0.5 || newScale > 3 || !pdfDocument) return;
    
    setScale(newScale);
    await renderPage(pdfDocument, currentPage, newScale);
  };

  useEffect(() => {
    if (url) {
      loadPDF();
    }
  }, [url]);

  const renderPDFViewer = () => {
    if (error) {
      return (
        <div className="flex items-center justify-center h-full p-4">
          <div className="text-center max-w-md">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button onClick={loadPDF} variant="outline" size="sm">
              Retry
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full">
        {/* Toolbar */}
        <div className="flex items-center justify-between p-3 border-b bg-muted/10">
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => goToPage(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-sm font-medium">
              Page {currentPage} of {totalPages}
            </span>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => goToPage(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => changeScale(scale - 0.25)}
              disabled={scale <= 0.5}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            
            <span className="text-sm font-medium min-w-[60px] text-center">
              {Math.round(scale * 100)}%
            </span>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => changeScale(scale + 0.25)}
              disabled={scale >= 3}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={downloadPDF}>
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={toggleFullScreen}>
              {isFullScreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* PDF Content */}
        <div className="flex-1 relative overflow-auto bg-gray-100 dark:bg-gray-900">
          <div className="flex justify-center p-4">
            <canvas
              ref={canvasRef}
              className="shadow-lg border border-border/50 bg-white"
            />
          </div>

          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">Loading PDF...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {isFullScreen ? (
        <Dialog open={isFullScreen} onOpenChange={setIsFullScreen}>
          <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden">
            {renderPDFViewer()}
          </DialogContent>
        </Dialog>
      ) : (
        renderPDFViewer()
      )}
    </>
  );
};

export default PDFViewer;
