"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import dynamic from 'next/dynamic';
import {
  FileText, File, Copy, AlertCircle, ChevronDown,
  Layers, X, Edit2, Save, FolderOpen, Database, Loader2,
  Trash2, Search, Filter, Eye, Upload, Grid, List
} from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Collapsible,
  CollapsibleContent,
} from "@/components/ui/collapsible";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Dynamically import the PDF viewer component to avoid SSR issues
const PDFViewer = dynamic(() => import('./PDFViewer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-[60vh]">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  )
});

interface ProcessedPage {
  pageNumber: number;
  text: string;
  isExpanded?: boolean;
}

interface ProcessedPDF {
  id: string;
  title: string;
  fileName: string;
  fileSize: number;
  pages: ProcessedPage[];
  metadata: any;
  isExpanded?: boolean;
  isEditingTitle?: boolean;
}

interface SavedPDF {
  id: string;
  fileName: string;
  fileSize: number;
  createdAt: string;
}



export default function PdfExtractor() {
  const [processedPDFs, setProcessedPDFs] = useState<ProcessedPDF[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  const progressInterval = useRef<NodeJS.Timeout>();
  const [savedPDFs, setSavedPDFs] = useState<SavedPDF[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [previewPDF, setPreviewPDF] = useState<{ fileName: string, pages: ProcessedPage[] } | null>(null);

  // PDF viewer state
  const [pdfFile, setPdfFile] = useState<string | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);

  const startProgressSimulation = () => {
    setProgress(0);
    let currentProgress = 0;
    progressInterval.current = setInterval(() => {
      currentProgress += Math.random() * 10;
      if (currentProgress > 90) {
        currentProgress = 90;
        clearInterval(progressInterval.current);
      }
      setProgress(currentProgress);
    }, 500);
  };

  const stopProgressSimulation = () => {
    clearInterval(progressInterval.current);
    setProgress(100);
  };

  const resetState = () => {
    setCurrentFile(null);
    setProgress(0);
    setError(null);
    clearInterval(progressInterval.current);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    for (const file of acceptedFiles) {
      if (!file.type.includes('pdf')) {
        toast.error(`${file.name} is not a PDF file`);
        continue;
      }

      setCurrentFile(file);
      setIsProcessing(true);
      startProgressSimulation();

      const formData = new FormData();
      formData.append('file', file);

      try {
        // Upload directly to the PDF documents API
        const response = await fetch('/api/pdf-documents', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        console.log('PDF upload response:', data);

        if (!response.ok || !data.success) {
          throw new Error(data.error || 'Failed to upload PDF');
        }

        stopProgressSimulation();

        // Create a simple PDF object with minimal information
        const newPDF: ProcessedPDF = {
          id: data.document.id,
          title: file.name.replace('.pdf', ''),
          fileName: file.name,
          fileSize: file.size,
          pages: [{ pageNumber: 1, text: 'PDF content is available for preview' }],
          metadata: data.metadata,
          isExpanded: false,
          isEditingTitle: false,
        };

        setProcessedPDFs(prev => [...prev, newPDF]);
        toast.success(`Successfully processed ${file.name}`);

        // Refresh the list of saved PDFs
        fetchSavedPDFs();
      } catch (err) {
        console.error('Error processing PDF:', err);
        toast.error(`Failed to process ${file.name}`);
      }
    }

    setIsProcessing(false);
    resetState();
  }, []);

  const togglePDFExpansion = (id: string) => {
    setProcessedPDFs(pdfs =>
      pdfs.map(pdf =>
        pdf.id === id
          ? { ...pdf, isExpanded: !pdf.isExpanded }
          : pdf
      )
    );
  };

  const togglePageExpansion = (pdfId: string, pageNumber: number) => {
    setProcessedPDFs(pdfs =>
      pdfs.map(pdf =>
        pdf.id === pdfId
          ? {
              ...pdf,
              pages: pdf.pages.map(page =>
        page.pageNumber === pageNumber
          ? { ...page, isExpanded: !page.isExpanded }
          : page
              )
            }
          : pdf
      )
    );
  };

  const toggleTitleEdit = (id: string) => {
    setProcessedPDFs(pdfs =>
      pdfs.map(pdf =>
        pdf.id === id
          ? { ...pdf, isEditingTitle: !pdf.isEditingTitle }
          : pdf
      )
    );
  };

  const updatePDFTitle = (id: string, newTitle: string) => {
    setProcessedPDFs(pdfs =>
      pdfs.map(pdf =>
        pdf.id === id
          ? { ...pdf, title: newTitle, isEditingTitle: false }
          : pdf
      )
    );
  };

  const removePDF = (id: string) => {
    setProcessedPDFs(pdfs => pdfs.filter(pdf => pdf.id !== id));
    toast.success('PDF removed');
  };

  const fetchSavedPDFs = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/pdf-documents');
      const data = await response.json();

      console.log('Fetched PDF documents:', data);

      if (data.success) {
        setSavedPDFs(data.documents || []);
      } else {
        console.error('Failed to fetch PDFs:', data.error);
        toast.error(data.error || 'Failed to fetch PDF documents');
      }
    } catch (error) {
      console.error('Error fetching PDF documents:', error);
      toast.error('Failed to fetch saved PDF documents');
    } finally {
      setIsLoading(false);
    }
  };

  const deletePDF = async (id: string) => {
    try {
      const response = await fetch(`/api/pdf-documents?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('PDF deleted successfully');
        fetchSavedPDFs(); // Refresh the list
        // Also remove from processed PDFs if it exists there
        setProcessedPDFs(pdfs => pdfs.filter(pdf => pdf.id !== id));
      } else {
        throw new Error('Failed to delete PDF');
      }
    } catch (error) {
      console.error('Error deleting PDF:', error);
      toast.error('Failed to delete PDF');
    }
  };

  const previewPDFContent = async (fileName: string) => {
    // First, check if we have the file in memory (from a recent upload)
    const existingPDF = processedPDFs.find(pdf => pdf.fileName === fileName);

    if (existingPDF) {
      // If we have the file in memory, show its pages in the text preview
      setPreviewPDF({
        fileName: existingPDF.fileName,
        pages: existingPDF.pages
      });
    } else {
      // For PDFs that are in the database but not in memory, show a placeholder
      const placeholderPages = [
        {
          pageNumber: 1,
          text: 'PDF content is available in the viewer.'
        }
      ];

      setPreviewPDF({
        fileName: fileName,
        pages: placeholderPages
      });
    }

    // Get the PDF URL
    const pdfUrl = getPdfUrl(fileName);

    // Set the PDF file URL for the viewer
    setPdfFile(pdfUrl);

    // Reset the page number to 1
    setPageNumber(1);

    // Open the preview dialog
    setIsPreviewOpen(true);
  };

  useEffect(() => {
    fetchSavedPDFs();
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
    },
    multiple: true,
    disabled: isProcessing,
  });

  // Create a function to get a PDF URL from a file name
const getPdfUrl = (fileName: string) => {
  // First try to get the PDF from the API
  return `/api/pdf-documents/file/${encodeURIComponent(fileName)}`;

  // If the above URL doesn't work, the PDFViewer component will fall back to the Mozilla example
};

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header and Upload Area */}
      <div className="flex flex-col md:flex-row gap-6 items-start">
        {/* Upload Area */}
        <div
          {...getRootProps()}
          id="pdf-dropzone"
          className={`
            flex-1 border-2 border-dashed rounded-xl p-8 transition-all
            ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/20'}
            ${isProcessing ? 'pointer-events-none opacity-50' : 'cursor-pointer hover:border-primary hover:bg-muted/10'}
          `}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center gap-4">
            {currentFile ? (
              <>
                <div className="bg-primary/10 p-4 rounded-full">
                  <File className="h-12 w-12 text-primary" />
                </div>
                <div className="text-center">
                  <p className="text-base font-medium">{currentFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(currentFile.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className="bg-muted/30 p-6 rounded-full">
                  <FolderOpen className="h-12 w-12 text-primary/70" />
                </div>
                <div className="text-center">
                  <p className="text-xl font-medium">
                    {isDragActive ? 'Drop your PDFs here' : 'Drag & drop your PDFs here'}
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    or click to browse your files
                  </p>
                </div>
                <div className="mt-2 flex flex-wrap justify-center gap-2">
                  <Badge variant="outline" className="text-xs">PDF files only</Badge>
                  <Badge variant="outline" className="text-xs">Max 10MB per file</Badge>
                </div>
              </>
            )}
          </div>

          {isProcessing && (
            <div className="space-y-3 mt-6">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  <span className="font-medium">Processing PDF...</span>
                </div>
                <span className="text-muted-foreground font-medium">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2 transition-all" />
            </div>
          )}

          {error && (
            <Alert variant="destructive" className="mt-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
      </div>

      {/* PDF Documents Section */}
      <div className="mt-12">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-semibold tracking-tight flex items-center gap-2">
              <FileText className="h-6 w-6 text-primary" />
              PDF Documents
            </h2>
            <p className="text-muted-foreground">
              Your library of uploaded PDF documents
            </p>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search PDFs..."
                className="pl-8 w-[200px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Sort by Date</DropdownMenuItem>
                <DropdownMenuItem>Sort by Name</DropdownMenuItem>
                <DropdownMenuItem>Sort by Size</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={viewMode === 'grid' ? "secondary" : "ghost"}
                size="sm"
                className="rounded-none"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? "secondary" : "ghost"}
                size="sm"
                className="rounded-none"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12 bg-muted/5 rounded-xl">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">Loading your documents...</p>
            </div>
          </div>
        ) : savedPDFs.length > 0 ? (
          <div>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {savedPDFs
                  .filter(pdf => pdf.fileName.toLowerCase().includes(searchTerm.toLowerCase()))
                  .map((pdf) => (
                    <div
                      key={pdf.id}
                      className="group relative bg-white dark:bg-muted/20 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all border border-border/50"
                    >
                      <div className="aspect-[4/3] bg-muted/30 flex items-center justify-center">
                        <FileText className="h-16 w-16 text-primary/50" />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium truncate" title={pdf.fileName}>
                          {pdf.fileName}
                        </h3>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant="secondary" className="text-xs">
                            {(pdf.fileSize / (1024 * 1024)).toFixed(2)} MB
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {new Date(pdf.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-3">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => previewPDFContent(pdf.fileName)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => deletePDF(pdf.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="space-y-2">
                {savedPDFs
                  .filter(pdf => pdf.fileName.toLowerCase().includes(searchTerm.toLowerCase()))
                  .map((pdf) => (
                    <div
                      key={pdf.id}
                      className="flex items-center justify-between p-4 rounded-lg border border-border/50 bg-white dark:bg-muted/20 hover:bg-muted/10 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className="bg-muted/30 p-3 rounded-lg">
                          <FileText className="h-8 w-8 text-primary/70" />
                        </div>
                        <div>
                          <h3 className="font-medium">{pdf.fileName}</h3>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {(pdf.fileSize / (1024 * 1024)).toFixed(2)} MB
                            </Badge>
                            <span>•</span>
                            <span>
                              Uploaded on {new Date(pdf.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => previewPDFContent(pdf.fileName)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deletePDF(pdf.id)}
                          className="text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-16 bg-muted/5 rounded-xl text-center">
            <div className="bg-muted/20 p-6 rounded-full mb-4">
              <Database className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="font-medium text-xl">No documents found</h3>
            <p className="text-sm text-muted-foreground max-w-md mt-2 mb-6">
              Upload a PDF document to start building your library. Your documents will appear here.
            </p>
            <Button onClick={() => document.getElementById('pdf-dropzone')?.click()}>
              <Upload className="h-4 w-4 mr-2" />
              Upload a PDF
            </Button>
          </div>
        )}
      </div>

      {/* PDF Preview Dialog */}
      <Dialog
        open={isPreviewOpen && !!previewPDF}
        onOpenChange={(open) => {
          if (!open) {
            setIsPreviewOpen(false);
            // Don't clear previewPDF immediately to avoid flickering if reopened
          }
        }}
      >
        <DialogContent className="max-w-5xl max-h-[90vh] p-0 overflow-hidden">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b">
              <DialogHeader className="p-0 space-y-1">
                <DialogTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  {previewPDF?.fileName}
                </DialogTitle>
              </DialogHeader>

              <Button
                variant="ghost"
                size="sm"
                className="h-8"
                onClick={() => {
                  setIsPreviewOpen(false);
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex flex-1 min-h-0">
              {/* PDF Content Area */}
              <div className="flex-1 overflow-hidden relative">
                {pdfFile ? (
                  <div className="h-full">
                    <PDFViewer
                      url={pdfFile}
                      initialPage={pageNumber}
                      scale={1.0}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex flex-col items-center gap-2">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <p className="text-sm text-muted-foreground">Loading PDF...</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Text Content Sidebar */}
              <div className="w-80 border-l bg-muted/5 overflow-hidden flex flex-col">
                <div className="p-3 border-b bg-muted/10 flex items-center">
                  <h3 className="text-sm font-medium">Document Text</h3>
                </div>
                <ScrollArea className="flex-1">
                  <div className="p-4 space-y-6">
                    {previewPDF?.pages.map((page) => (
                      <div
                        key={page.pageNumber}
                        className="space-y-2"
                        onClick={() => setPageNumber(page.pageNumber)}
                      >
                        <div className="flex items-center justify-between">
                          <h4 className="text-xs font-medium text-muted-foreground">Page {page.pageNumber}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              copyToClipboard(page.text);
                            }}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="text-xs bg-muted/20 p-3 rounded-md hover:bg-muted/30 transition-colors cursor-pointer">
                          <p className="line-clamp-6 whitespace-pre-wrap">{page.text}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>



      {/* Recently Processed PDFs */}
      {processedPDFs.length > 0 && (
        <div className="mt-12">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-semibold tracking-tight flex items-center gap-2">
                <FileText className="h-6 w-6 text-primary" />
                Recently Processed
              </h2>
              <p className="text-muted-foreground">
                PDFs you've just uploaded and processed
              </p>
            </div>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              {processedPDFs.map((pdf) => (
                <motion.div
                  key={pdf.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border rounded-xl overflow-hidden bg-white dark:bg-muted/20 shadow-sm"
                >
                  <Collapsible open={pdf.isExpanded} onOpenChange={() => togglePDFExpansion(pdf.id)}>
                    <div className="p-4 border-b">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="bg-primary/10 p-2 rounded-lg">
                            <FileText className="h-5 w-5 text-primary" />
                          </div>

                          {pdf.isEditingTitle ? (
                            <div className="flex items-center gap-2 flex-1">
                              <Input
                                defaultValue={pdf.title}
                                className="h-8"
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    updatePDFTitle(pdf.id, e.currentTarget.value);
                                  }
                                }}
                                onBlur={(e) => updatePDFTitle(pdf.id, e.target.value)}
                                autoFocus
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleTitleEdit(pdf.id)}
                              >
                                <Save className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <>
                              <h3 className="font-medium">{pdf.title}</h3>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-7 w-7 p-0"
                                onClick={() => toggleTitleEdit(pdf.id)}
                              >
                                <Edit2 className="h-3.5 w-3.5" />
                              </Button>
                            </>
                          )}
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {pdf.pages.length} pages
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {(pdf.fileSize / (1024 * 1024)).toFixed(2)} MB
                          </Badge>

                          <div className="flex items-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => togglePDFExpansion(pdf.id)}
                            >
                              <ChevronDown
                                className={`h-4 w-4 transition-transform duration-200 ${pdf.isExpanded ? 'rotate-180' : ''}`}
                              />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                              onClick={() => removePDF(pdf.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <CollapsibleContent>
                      <div className="bg-muted/5 p-4">
                        <ScrollArea className="h-[400px]">
                          <div className="space-y-3">
                            {pdf.pages.map((page) => (
                              <div
                                key={page.pageNumber}
                                className="relative"
                              >
                                <Collapsible
                                  open={page.isExpanded}
                                  onOpenChange={() => togglePageExpansion(pdf.id, page.pageNumber)}
                                >
                                  <div
                                    className="flex items-center justify-between p-3 rounded-lg border border-border/50 bg-white dark:bg-muted/20 hover:bg-muted/10 transition-colors cursor-pointer"
                                    onClick={() => togglePageExpansion(pdf.id, page.pageNumber)}
                                  >
                                    <div className="flex items-center gap-2">
                                      <div className="bg-primary/10 p-1.5 rounded-md">
                                        <Layers className="w-3.5 h-3.5 text-primary/80" />
                                      </div>
                                      <span className="text-sm font-medium">Page {page.pageNumber}</span>
                                    </div>

                                    <div className="flex items-center gap-1">
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="h-7 w-7 p-0"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          copyToClipboard(page.text);
                                        }}
                                      >
                                        <Copy className="h-3.5 w-3.5" />
                                      </Button>
                                      <ChevronDown
                                        className={`h-4 w-4 transition-transform duration-200 ${page.isExpanded ? 'rotate-180' : ''}`}
                                      />
                                    </div>
                                  </div>

                                  <CollapsibleContent>
                                    <div className="mt-2 ml-8 p-3 rounded-lg bg-muted/10 text-sm whitespace-pre-wrap">
                                      {page.text}
                                    </div>
                                  </CollapsibleContent>
                                </Collapsible>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
      )}
    </div>
  );
}