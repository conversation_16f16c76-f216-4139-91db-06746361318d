"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Send,
  Trash2,
  Brain,
  User,
  Database,
  Loader2,
  MessageSquare,
  BarChart3,
  Image as ImageIcon,
  FileText,
  Sparkles,
  Gem,
  Settings,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Quote
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import AITextLoading from './AiTextLoading';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  datasetsAnalyzed?: Array<{ name: string; totalRows: number }>;
  sources?: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }>;
  images?: Array<{ name: string; url: string }>;
  model?: string;
  isLoading?: boolean;
}

interface GeminiModel {
  id: string;
  name: string;
  description: string;
  supportsImages: boolean;
}

interface Dataset {
  id: string;
  name: string;
  description?: string;
  data: any[];
  headers: string[];
}

interface SimpleAIChatInterfaceProps {
  datasets: Dataset[];
  selectedDatasets: string[];
  onDatasetSelectionChange: (datasetIds: string[]) => void;
}

const GEMINI_MODELS: GeminiModel[] = [
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    description: 'Most capable model with excellent reasoning and image analysis',
    supportsImages: true
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    description: 'Fast and efficient with good performance and image support',
    supportsImages: true
  },
  {
    id: 'gemini-1.0-pro',
    name: 'Gemini 1.0 Pro',
    description: 'Reliable and stable for general tasks',
    supportsImages: false
  }
];

const SimpleAIChatInterface: React.FC<SimpleAIChatInterfaceProps> = ({
  datasets,
  selectedDatasets,
  onDatasetSelectionChange
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('gemini-1.5-flash');
  const [uploadedImages, setUploadedImages] = useState<Array<{ name: string; url: string; file: File }>>([]);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [selectedReference, setSelectedReference] = useState<{ dataset: string; reference: string; type: string } | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const selectedModelData = GEMINI_MODELS.find(m => m.id === selectedModel);
    if (!selectedModelData?.supportsImages) {
      toast.error('Selected model does not support image analysis. Please choose Gemini 1.5 Pro or Flash.');
      return;
    }

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const url = e.target?.result as string;
          setUploadedImages(prev => [...prev, { name: file.name, url, file }]);
          toast.success(`Image "${file.name}" uploaded successfully`);
        };
        reader.readAsDataURL(file);
      } else {
        toast.error('Please upload only image files');
      }
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Remove uploaded image
  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if ((!input.trim() && uploadedImages.length === 0) || isLoading) return;

    if (selectedDatasets.length === 0 && uploadedImages.length === 0) {
      toast.error('Please select at least one dataset or upload an image to analyze');
      return;
    }

    const userMessage: Message = {
      role: 'user',
      content: input.trim() || 'Analyze the uploaded image(s)',
      timestamp: new Date(),
      images: uploadedImages.map(img => ({ name: img.name, url: img.url })),
      model: selectedModel
    };

    setMessages(prev => [...prev, userMessage]);

    // Add loading message with shimmer
    const loadingMessage: Message = {
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isLoading: true
    };
    setMessages(prev => [...prev, loadingMessage]);

    const currentInput = input.trim();
    const currentImages = [...uploadedImages];
    setInput('');
    setUploadedImages([]);
    setIsLoading(true);

    try {
      // Prepare images for API call
      const imageData = await Promise.all(
        currentImages.map(async (img) => {
          return {
            name: img.name,
            data: img.url.split(',')[1], // Remove data:image/...;base64, prefix
            mimeType: img.file.type
          };
        })
      );

      const response = await fetch('/api/simple-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput || 'Analyze the uploaded image(s)',
          selectedDatasets,
          conversationHistory: messages.slice(-6), // Last 6 messages for context
          model: selectedModel,
          images: imageData
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get AI response');
      }

      const assistantMessage: Message = {
        role: 'assistant',
        content: data.content,
        timestamp: new Date(),
        datasetsAnalyzed: data.datasetsAnalyzed,
        sources: data.sources || [],
        model: selectedModel,
        isLoading: false
      };

      // Replace the loading message with the actual response
      setMessages(prev => prev.map((msg, index) =>
        index === prev.length - 1 && msg.isLoading ? assistantMessage : msg
      ));

    } catch (error: any) {
      console.error('Error sending message:', error);
      toast.error(error.message || 'Failed to send message');
      
      const errorMessage: Message = {
        role: 'assistant',
        content: `Sorry, I encountered an error while analyzing your data: ${error.message}. ${error.message.includes('overloaded') ? 'The AI service is currently busy. Please try again in a moment.' : 'Please try again.'}`,
        timestamp: new Date(),
        isLoading: false
      };

      // Replace the loading message with the error message
      setMessages(prev => prev.map((msg, index) =>
        index === prev.length - 1 && msg.isLoading ? errorMessage : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    toast.success('Chat cleared');
  };

  const toggleDataset = (datasetId: string) => {
    const newSelection = selectedDatasets.includes(datasetId)
      ? selectedDatasets.filter(id => id !== datasetId)
      : [...selectedDatasets, datasetId];
    onDatasetSelectionChange(newSelection);
  };

  const selectedDatasetsInfo = datasets.filter(d => selectedDatasets.includes(d.id));
  const totalRows = selectedDatasetsInfo.reduce((sum, d) => sum + (d.data?.length || 0), 0);

  return (
    <div className="h-screen flex bg-background">
      {/* Sidebar */}
      <div className="w-80 border-r bg-card flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b">
          <div className="flex items-center gap-2 mb-3">
            <div className="p-1.5 bg-primary rounded-md">
              <Sparkles className="h-4 w-4 text-primary-foreground" />
            </div>
            <div>
              <h1 className="font-semibold">AI Data Assistant</h1>
              <p className="text-xs text-muted-foreground">Chat with your data</p>
            </div>
          </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                className="h-8"
              >
                <Settings className="h-4 w-4" />
                {isSettingsOpen ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />}
              </Button>

              {messages.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearChat}
                  className="h-8 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          </div>

          {/* Settings Panel */}
          {isSettingsOpen && (
            <div className="border-t bg-muted/50 p-3">
              <div className="space-y-3">
                <div>
                  <label className="text-xs font-medium mb-1 block">AI Model</label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {GEMINI_MODELS.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center gap-2">
                            <Gem className="h-3 w-3 text-amber-500" />
                            <div>
                              <div className="text-xs font-medium">{model.name}</div>
                              <div className="text-xs text-muted-foreground">{model.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-xs font-medium mb-1 block">Upload Images</label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={!GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages}
                    className="w-full h-8 text-xs"
                  >
                    <ImageIcon className="h-3 w-3 mr-1" />
                    Add Images
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Datasets Section */}
          <div className="flex-1 overflow-hidden">
            <div className="p-3 border-b">
              <h3 className="text-sm font-medium flex items-center gap-2">
                <Database className="h-4 w-4" />
                Datasets
              </h3>
            </div>
            <ScrollArea className="flex-1 p-3">
              {datasets.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-24 text-center">
                  <Database className="h-6 w-6 text-muted-foreground mb-1" />
                  <p className="text-xs text-muted-foreground">No datasets</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {datasets.map((dataset) => (
                    <div
                      key={dataset.id}
                      className={cn(
                        "p-2 border rounded-md cursor-pointer transition-colors text-xs",
                        selectedDatasets.includes(dataset.id)
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      )}
                      onClick={() => toggleDataset(dataset.id)}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <input
                          type="checkbox"
                          checked={selectedDatasets.includes(dataset.id)}
                          onChange={() => {}}
                          className="h-3 w-3"
                        />
                        <span className="font-medium truncate">{dataset.name}</span>
                      </div>
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <BarChart3 className="h-3 w-3" />
                        <span>{(dataset.data?.length || 0).toLocaleString()} rows</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Summary */}
          {selectedDatasets.length > 0 && (
            <div className="p-3 border-t bg-muted/30">
              <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Summary
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-muted-foreground">Datasets:</span>
                  <Badge variant="secondary" className="text-xs h-5">
                    {selectedDatasets.length}
                  </Badge>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-muted-foreground">Total Rows:</span>
                  <Badge variant="secondary" className="text-xs h-5">
                    {totalRows.toLocaleString()}
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages Area */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full p-4" ref={scrollAreaRef}>
                {messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
                    <div className="p-3 bg-muted rounded-full">
                      <MessageSquare className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div className="max-w-sm">
                      <h3 className="text-lg font-medium mb-2">Start analyzing your data</h3>
                      <p className="text-sm text-muted-foreground mb-3">
                        Select datasets or upload images and ask questions like:
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="p-2 bg-muted rounded-md">
                          <span>💼 "How many people are married?"</span>
                        </div>
                        <div className="p-2 bg-muted rounded-md">
                          <span>📊 "What's the average salary?"</span>
                        </div>
                        <div className="p-2 bg-muted rounded-md">
                          <span>📸 "Extract text from this image"</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {messages.map((message, index) => (
                      <div
                        key={index}
                        className={cn(
                          "flex gap-2",
                          message.role === 'user' ? "justify-end" : "justify-start"
                        )}
                      >
                        <div
                          className={cn(
                            "max-w-[80%] rounded-lg p-3",
                            message.role === 'user'
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          )}
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <div className={cn(
                              "p-1 rounded-full",
                              message.role === 'user'
                                ? "bg-primary-foreground/20"
                                : "bg-primary"
                            )}>
                              {message.role === 'user' ? (
                                <User className="h-3 w-3" />
                              ) : (
                                <Sparkles className="h-3 w-3 text-primary-foreground" />
                              )}
                            </div>
                            <span className="text-xs font-medium">
                              {message.role === 'user' ? 'You' : 'AI'}
                            </span>
                            {message.model && message.role === 'assistant' && (
                              <Badge variant="outline" className="text-xs h-4">
                                <Gem className="h-2 w-2 mr-1" />
                                {GEMINI_MODELS.find(m => m.id === message.model)?.name || message.model}
                              </Badge>
                            )}
                            <span className="text-xs ml-auto opacity-60">
                              {message.timestamp.toLocaleTimeString()}
                            </span>
                          </div>

                          {/* User Images */}
                          {message.images && message.images.length > 0 && (
                            <div className="mb-2">
                              <div className="flex flex-wrap gap-1">
                                {message.images.map((img, i) => (
                                  <div key={i} className="relative">
                                    <img
                                      src={img.url}
                                      alt={img.name}
                                      className="w-12 h-12 object-cover rounded border"
                                    />
                                    <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-0.5 rounded-b truncate">
                                      {img.name}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Content with shimmer loading */}
                          {message.isLoading ? (
                            <AITextLoading />
                          ) : (
                            <div className="prose prose-sm max-w-none dark:prose-invert">
                              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                {message.content}
                              </ReactMarkdown>
                            </div>
                          )}

                          {/* Compact Clickable Sources */}
                          {message.sources && message.sources.length > 0 && !message.isLoading && (
                            <div className="mt-2 pt-2 border-t">
                              <div className="flex items-center gap-1 mb-1">
                                <Quote className="h-3 w-3" />
                                <span className="text-xs font-medium">Sources</span>
                              </div>
                              <div className="flex flex-wrap gap-1">
                                {message.sources.map((source, i) => (
                                  <button
                                    key={i}
                                    onClick={() => setSelectedReference(source)}
                                    className="inline-flex items-center gap-1 px-2 py-1 bg-secondary hover:bg-secondary/80 rounded text-xs transition-colors"
                                  >
                                    <span className="w-4 h-4 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs">
                                      {i + 1}
                                    </span>
                                    <Database className="h-3 w-3" />
                                    <span className="truncate max-w-20">{source.dataset}</span>
                                  </button>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Compact Datasets Analyzed */}
                          {message.datasetsAnalyzed && message.datasetsAnalyzed.length > 0 && !message.isLoading && (
                            <div className="mt-2 pt-2 border-t">
                              <div className="flex items-center gap-1 mb-1">
                                <BarChart3 className="h-3 w-3" />
                                <span className="text-xs font-medium">Analyzed</span>
                              </div>
                              <div className="flex flex-wrap gap-1">
                                {message.datasetsAnalyzed.map((ds, i) => (
                                  <Badge key={i} variant="outline" className="text-xs h-5">
                                    <Database className="h-2 w-2 mr-1" />
                                    {ds.name} ({ds.totalRows.toLocaleString()})
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
            </ScrollArea>
          </div>

          {/* Fixed Input Area at Bottom */}
          <div className="border-t bg-card p-4">
                {/* Compact Images Preview */}
                {uploadedImages.length > 0 && (
                  <div className="mb-2 p-2 bg-card rounded-md border">
                    <div className="flex items-center gap-2 mb-2">
                      <ImageIcon className="h-3 w-3" />
                      <span className="text-xs font-medium">Images</span>
                      <Badge variant="secondary" className="text-xs h-4">
                        {uploadedImages.length}
                      </Badge>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {uploadedImages.map((img, i) => (
                        <div key={i} className="relative group">
                          <img
                            src={img.url}
                            alt={img.name}
                            className="w-12 h-12 object-cover rounded border"
                          />
                          <button
                            onClick={() => removeImage(i)}
                            className="absolute -top-1 -right-1 w-4 h-4 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </button>
                          <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-0.5 rounded-b truncate">
                            {img.name}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <div className="flex-1 relative">
                    <Input
                      ref={inputRef}
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={
                        selectedDatasets.length === 0 && uploadedImages.length === 0
                          ? "Select datasets or upload images first..."
                          : uploadedImages.length > 0
                          ? "Ask about your data or uploaded images..."
                          : "Ask a question about your data..."
                      }
                      disabled={isLoading || (selectedDatasets.length === 0 && uploadedImages.length === 0)}
                      className="pr-10 h-9"
                    />

                    {/* Image Upload Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={!GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages || isLoading}
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
                    >
                      <ImageIcon className="h-3 w-3" />
                    </Button>
                  </div>

                  <Button
                    onClick={handleSendMessage}
                    disabled={(!input.trim() && uploadedImages.length === 0) || isLoading || (selectedDatasets.length === 0 && uploadedImages.length === 0)}
                    className="h-9 px-4"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {selectedDatasets.length === 0 && uploadedImages.length === 0 && (
                  <div className="mt-2 p-2 bg-muted rounded text-center">
                    <p className="text-xs text-muted-foreground">
                      💡 Select datasets or upload images to start
                    </p>
                  </div>
                )}

                {uploadedImages.length > 0 && !GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages && (
                  <div className="mt-2 p-2 bg-destructive/10 rounded text-center">
                    <p className="text-xs text-destructive">
                      ⚠️ Switch to Gemini 1.5 Pro/Flash for image analysis
                    </p>
                  </div>
                )}
          </div>
        </div>
      </div>

      {/* Reference Details Dialog */}
      {selectedReference && (
        <Dialog open={true} onOpenChange={() => setSelectedReference(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Quote className="h-4 w-4" />
                Reference Details
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium">Dataset:</label>
                <div className="flex items-center gap-2 mt-1">
                  <Database className="h-4 w-4" />
                  <span className="text-sm">{selectedReference.dataset}</span>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Type:</label>
                <Badge variant="secondary" className="ml-2">
                  {selectedReference.type}
                </Badge>
              </div>
              <div>
                <label className="text-sm font-medium">Reference:</label>
                <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                  {selectedReference.reference}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default SimpleAIChatInterface;
