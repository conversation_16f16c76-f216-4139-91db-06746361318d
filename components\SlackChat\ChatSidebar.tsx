"use client";

import React from 'react';
import { Hash, Plus, MoreVertical, Settings, Bell, Search, Users, Star, Lock, Globe } from 'lucide-react';
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Channel {
  id: string;
  name: string;
  unreadCount: number;
  isPrivate?: boolean;
  isStarred?: boolean;
}

interface ChannelMember {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date;
  status?: string;
}

interface ChatSidebarProps {
  channels: Channel[];
  activeChannel: Channel | null;
  channelMembers: ChannelMember[];
  currentUser: {
    id: string;
    fullName: string;
    username: string;
    avatar: string;
  } | null;
  isLoadingChannels: boolean;
  onChannelClick: (channel: Channel) => void;
  onCreateChannel?: () => void;
}

export function ChatSidebar({
  channels,
  activeChannel,
  channelMembers,
  currentUser,
  isLoadingChannels,
  onChannelClick,
  onCreateChannel
}: ChatSidebarProps) {
  const [searchQuery, setSearchQuery] = React.useState('');

  const filteredChannels = channels.filter(channel =>
    channel.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    return `${Math.floor(diffInSeconds / 86400)}d`;
  };

  return (
    <div className="w-64 bg-slate-50 border-r flex flex-col h-full">
      {/* Workspace Header */}
      <div className="p-4 border-b bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">HR</span>
            </div>
            <div>
              <h1 className="font-semibold text-sm">HR Atlas</h1>
              <div className="flex items-center gap-1 text-xs text-slate-500">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{currentUser?.fullName}</span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Workspace Settings
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Users className="h-4 w-4 mr-2" />
                Invite People
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search */}
      <div className="p-3 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search channels..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9 h-8 text-sm"
          />
        </div>
      </div>

      {/* Channels Section */}
      <div className="flex-1 overflow-hidden">
        <div className="p-3">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xs font-semibold text-slate-600 uppercase tracking-wide">
              Channels
            </h2>
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-5 w-5" 
              onClick={onCreateChannel}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
          
          <ScrollArea className="h-48">
            <div className="space-y-0.5">
              {isLoadingChannels ? (
                <div className="space-y-2">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="flex items-center gap-2 px-2 py-1.5">
                      <div className="w-4 h-4 bg-slate-200 rounded animate-pulse" />
                      <div className="h-3 bg-slate-200 rounded animate-pulse flex-1" />
                    </div>
                  ))}
                </div>
              ) : filteredChannels.length > 0 ? (
                filteredChannels.map(channel => (
                  <button
                    key={channel.id}
                    onClick={() => onChannelClick(channel)}
                    className={cn(
                      "w-full flex items-center gap-2 px-2 py-1.5 rounded-md text-sm transition-colors group",
                      activeChannel?.id === channel.id 
                        ? "bg-blue-100 text-blue-700 font-medium" 
                        : "text-slate-700 hover:bg-slate-100"
                    )}
                  >
                    {channel.isPrivate ? (
                      <Lock className="h-3.5 w-3.5 text-slate-400" />
                    ) : (
                      <Hash className="h-3.5 w-3.5 text-slate-400" />
                    )}
                    <span className="truncate flex-1 text-left">{channel.name}</span>
                    <div className="flex items-center gap-1">
                      {channel.isStarred && (
                        <Star className="h-3 w-3 text-yellow-500 fill-current" />
                      )}
                      {channel.unreadCount > 0 && (
                        <Badge variant="destructive" className="h-4 text-xs px-1.5">
                          {channel.unreadCount > 99 ? '99+' : channel.unreadCount}
                        </Badge>
                      )}
                    </div>
                  </button>
                ))
              ) : (
                <div className="text-center py-4 text-slate-400 text-xs">
                  {searchQuery ? 'No channels found' : 'No channels available'}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        <Separator />

        {/* Direct Messages */}
        <div className="p-3">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xs font-semibold text-slate-600 uppercase tracking-wide">
              Direct Messages
            </h2>
            <Button variant="ghost" size="icon" className="h-5 w-5">
              <Plus className="h-3 w-3" />
            </Button>
          </div>
          
          <ScrollArea className="h-32">
            <div className="space-y-0.5">
              {channelMembers.slice(0, 5).map(member => (
                <button
                  key={member.id}
                  className="w-full flex items-center gap-2 px-2 py-1.5 hover:bg-slate-100 rounded-md transition-colors text-sm"
                >
                  <div className="relative">
                    <Avatar className="h-5 w-5">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs bg-slate-200">
                        {member.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className={cn(
                      "absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 rounded-full border border-white",
                      member.isOnline ? "bg-green-400" : "bg-slate-300"
                    )} />
                  </div>
                  <div className="flex-1 min-w-0 text-left">
                    <span className="text-slate-700 truncate block text-xs">
                      {member.name}
                    </span>
                    {member.status && (
                      <span className="text-xs text-slate-400 truncate block">
                        {member.status}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </ScrollArea>
        </div>

        <Separator />

        {/* Members Section */}
        <div className="p-3 flex-1">
          <h2 className="text-xs font-semibold text-slate-600 uppercase tracking-wide mb-2">
            Members ({channelMembers.length})
          </h2>
          
          <ScrollArea className="h-full max-h-40">
            <div className="space-y-0.5">
              {channelMembers.map(member => (
                <div 
                  key={member.id}
                  className="flex items-center gap-2 px-2 py-1.5 hover:bg-slate-100 rounded-md transition-colors"
                >
                  <div className="relative">
                    <Avatar className="h-5 w-5">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs bg-slate-200">
                        {member.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className={cn(
                      "absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 rounded-full border border-white",
                      member.isOnline ? "bg-green-400" : "bg-slate-300"
                    )} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="text-xs text-slate-700 truncate block">
                      {member.name}
                    </span>
                    {!member.isOnline && member.lastSeen && (
                      <span className="text-xs text-slate-400 truncate block">
                        {getRelativeTime(member.lastSeen.toISOString())}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
}
