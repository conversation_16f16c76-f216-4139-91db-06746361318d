import { cn } from "@/lib/utils";

interface ConnectionStatusProps {
  isConnected: boolean;
}

export function ConnectionStatus({ isConnected }: ConnectionStatusProps) {
  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1.5">
        <div className={cn(
          "h-2 w-2 rounded-full transition-colors duration-200",
          isConnected 
            ? "bg-green-500" 
            : "bg-green-500/30"
        )} />
        <span className="text-xs text-muted-foreground">
          {isConnected ? "Connected" : "Connecting..."}
        </span>
      </div>
    </div>
  );
} 