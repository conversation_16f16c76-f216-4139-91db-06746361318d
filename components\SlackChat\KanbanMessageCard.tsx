"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { 
  Kanban, 
  Plus, 
  Calendar, 
  User, 
  Clock, 
  CheckCircle, 
  Circle, 
  AlertCircle,
  MoreHorizontal,
  ExternalLink
} from 'lucide-react';
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface Task {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'todo' | 'inprogress' | 'done';
  assignee: {
    id: string;
    name: string;
    avatar: string;
  };
  dueDate: Date;
  progress?: number;
}

interface KanbanMessageCardProps {
  tasks?: Task[];
  projectName?: string;
  channelName?: string;
  compact?: boolean;
}

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Update user authentication',
    description: 'Implement OAuth 2.0 for better security',
    priority: 'high',
    status: 'inprogress',
    assignee: {
      id: '1',
      name: 'Alice Johnson',
      avatar: '/placeholder.svg?height=32&width=32'
    },
    dueDate: new Date(Date.now() + 86400000 * 3),
    progress: 65
  },
  {
    id: '2',
    title: 'Design new dashboard',
    description: 'Create responsive dashboard layout',
    priority: 'medium',
    status: 'todo',
    assignee: {
      id: '2',
      name: 'Bob Smith',
      avatar: '/placeholder.svg?height=32&width=32'
    },
    dueDate: new Date(Date.now() + 86400000 * 7),
    progress: 0
  },
  {
    id: '3',
    title: 'Fix login bug',
    description: 'Resolve authentication timeout issue',
    priority: 'high',
    status: 'done',
    assignee: {
      id: '3',
      name: 'Carol Davis',
      avatar: '/placeholder.svg?height=32&width=32'
    },
    dueDate: new Date(Date.now() - 86400000),
    progress: 100
  }
];

export function KanbanMessageCard({ 
  tasks = mockTasks, 
  projectName = "HR Atlas Project",
  channelName = "development",
  compact = false 
}: KanbanMessageCardProps) {
  const [showFullBoard, setShowFullBoard] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'done': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'inprogress': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'todo': return <Circle className="h-4 w-4 text-gray-400" />;
      default: return <Circle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'done': return 'bg-green-50 border-green-200';
      case 'inprogress': return 'bg-blue-50 border-blue-200';
      case 'todo': return 'bg-gray-50 border-gray-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const formatDueDate = (date: Date) => {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return 'Due tomorrow';
    return `Due in ${diffDays} days`;
  };

  const completedTasks = tasks.filter(task => task.status === 'done').length;
  const totalTasks = tasks.length;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  if (compact) {
    return (
      <Card className="max-w-md border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Kanban className="h-4 w-4 text-blue-500" />
              <CardTitle className="text-sm">{projectName}</CardTitle>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <ExternalLink className="h-3 w-3" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl h-[80vh]">
                <DialogHeader>
                  <DialogTitle>Project Board - {projectName}</DialogTitle>
                </DialogHeader>
                <div className="h-full overflow-hidden">
                  {/* Full Kanban Board would go here */}
                  <div className="text-center py-8 text-muted-foreground">
                    Full Kanban Board View
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span>{completedTasks}/{totalTasks} completed</span>
            <Progress value={progressPercentage} className="flex-1 h-2" />
            <span>{Math.round(progressPercentage)}%</span>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {tasks.slice(0, 3).map((task) => (
              <div key={task.id} className={cn(
                "flex items-center gap-2 p-2 rounded-md border text-xs",
                getStatusColor(task.status)
              )}>
                {getStatusIcon(task.status)}
                <span className="flex-1 truncate">{task.title}</span>
                <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                  {task.priority}
                </Badge>
              </div>
            ))}
            {tasks.length > 3 && (
              <div className="text-xs text-muted-foreground text-center py-1">
                +{tasks.length - 3} more tasks
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Kanban className="h-5 w-5 text-blue-500" />
            <CardTitle>{projectName}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              #{channelName}
            </Badge>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Board
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-6xl h-[90vh]">
                <DialogHeader>
                  <DialogTitle>Project Board - {projectName}</DialogTitle>
                </DialogHeader>
                <div className="h-full overflow-hidden">
                  {/* Full Kanban Board would go here */}
                  <div className="text-center py-8 text-muted-foreground">
                    Full Kanban Board View - Integrate with KanbanBoard component
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            <span>{completedTasks}/{totalTasks} completed</span>
          </div>
          <Progress value={progressPercentage} className="flex-1 h-2" />
          <span className="font-medium">{Math.round(progressPercentage)}%</span>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* To Do */}
          <div>
            <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
              <Circle className="h-4 w-4 text-gray-400" />
              To Do ({tasks.filter(t => t.status === 'todo').length})
            </h4>
            <div className="space-y-2">
              {tasks.filter(task => task.status === 'todo').map((task) => (
                <Card key={task.id} className="p-3">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <h5 className="font-medium text-sm">{task.title}</h5>
                      <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                        {task.priority}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">{task.description}</p>
                    <div className="flex items-center justify-between">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={task.assignee.avatar} />
                        <AvatarFallback className="text-xs">
                          {task.assignee.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {formatDueDate(task.dueDate)}
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* In Progress */}
          <div>
            <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-500" />
              In Progress ({tasks.filter(t => t.status === 'inprogress').length})
            </h4>
            <div className="space-y-2">
              {tasks.filter(task => task.status === 'inprogress').map((task) => (
                <Card key={task.id} className="p-3 border-l-4 border-l-blue-500">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <h5 className="font-medium text-sm">{task.title}</h5>
                      <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                        {task.priority}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">{task.description}</p>
                    {task.progress !== undefined && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>Progress</span>
                          <span>{task.progress}%</span>
                        </div>
                        <Progress value={task.progress} className="h-1" />
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={task.assignee.avatar} />
                        <AvatarFallback className="text-xs">
                          {task.assignee.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {formatDueDate(task.dueDate)}
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Done */}
          <div>
            <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              Done ({tasks.filter(t => t.status === 'done').length})
            </h4>
            <div className="space-y-2">
              {tasks.filter(task => task.status === 'done').map((task) => (
                <Card key={task.id} className="p-3 bg-green-50 border-green-200">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <h5 className="font-medium text-sm line-through text-muted-foreground">{task.title}</h5>
                      <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-200">
                        Completed
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={task.assignee.avatar} />
                        <AvatarFallback className="text-xs">
                          {task.assignee.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
