"use client";

import { useState, useRef, useCallback } from "react";
import { Upload, FileText, Image, FileSpreadsheet, Send, X, Paperclip, Plus, Smile } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useUploadThing } from "@/lib/uploadthing";
import { toast } from "sonner";
import { Progress } from "@/components/ui/progress";

interface SlackFileUploadProps {
  onFileSelect: (fileUrl: string, fileType?: string) => void;
  onCancel: () => void;
  isUploading?: boolean;
  onMessageSubmit: (e: React.FormEvent, fileUrl?: string) => void;
  message: string;
  onMessageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

const UPLOADTHING_URL = "https://uploadthing.com/f/";

export function SlackFileUpload({
  onFileSelect,
  onCancel,
  isUploading: externalIsUploading,
  onMessageSubmit,
  message,
  onMessageChange,
  placeholder
}: SlackFileUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { startUpload, isUploading } = useUploadThing("messageAttachment", {
    onClientUploadComplete: (res) => {
      if (res?.[0]) {
        onFileSelect(res[0].url, selectedFiles[0]?.type || "");
        toast.success("File uploaded successfully!");
        resetUpload();
      }
    },
    onUploadError: (error) => {
      toast.error(`Error uploading file: ${error.message}`);
      resetUpload();
    },
    onUploadProgress: (progress) => {
      setUploadProgress(progress);
    },
  });

  const resetUpload = () => {
    setSelectedFiles([]);
    setPreviews([]);
    setUploadProgress(0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedFiles.length > 0) {
      try {
        const result = await startUpload(selectedFiles);
        if (result?.[0]) {
          const fileUrl = `${UPLOADTHING_URL}${result[0].key}`;
          onMessageSubmit(e, fileUrl);
        }
      } catch (error) {
        toast.error("Failed to upload file");
      }
    } else {
      onMessageSubmit(e);
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (files: File[]) => {
    // Limit to 5 files for better UX
    const limitedFiles = files.slice(0, 5);
    setSelectedFiles(limitedFiles);
    
    // Generate previews for images
    const newPreviews: string[] = [];
    limitedFiles.forEach((file, index) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          newPreviews[index] = reader.result as string;
          setPreviews([...newPreviews]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    const newPreviews = previews.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    setPreviews(newPreviews);
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-6 w-6 text-blue-500" />;
    } else if (file.type === 'application/pdf') {
      return <FileText className="h-6 w-6 text-red-500" />;
    } else if (file.type === 'text/csv' || file.type.includes('spreadsheet')) {
      return <FileSpreadsheet className="h-6 w-6 text-green-500" />;
    }
    return <FileText className="h-6 w-6 text-slate-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="border-t bg-card">
      {/* File Upload Area */}
      <div className="p-3 border-b">
        {selectedFiles.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-white">
                {selectedFiles.length} file{selectedFiles.length > 1 ? 's' : ''} selected
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={resetUpload}
                className="text-slate-400 hover:text-white hover:bg-slate-700"
              >
                Clear all
              </Button>
            </div>
            
            {/* Upload Progress */}
            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-slate-300">Uploading...</span>
                  <span className="text-slate-300">{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="h-2" />
              </div>
            )}
            
            {/* File List */}
            <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
              {selectedFiles.map((file, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-slate-700 rounded-lg border border-slate-600">
                  {/* File Icon or Image Preview */}
                  <div className="flex-shrink-0">
                    {file.type.startsWith('image/') && previews[index] ? (
                      <div className="w-12 h-12 rounded-lg overflow-hidden bg-slate-600">
                        <img
                          src={previews[index]}
                          alt={file.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-lg bg-slate-600 flex items-center justify-center">
                        {getFileIcon(file)}
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-white truncate">{file.name}</p>
                    <p className="text-xs text-slate-400">
                      {formatFileSize(file.size)} • {file.type.split('/')[1]?.toUpperCase() || 'FILE'}
                    </p>
                  </div>

                  {/* Remove Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-slate-400 hover:text-white hover:bg-slate-600"
                    onClick={() => removeFile(index)}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div
            className={cn(
              "rounded-lg border-2 border-dashed p-8 transition-colors cursor-pointer",
              dragActive ? "border-blue-500 bg-blue-900/20" : "border-slate-600 hover:border-slate-500",
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="flex flex-col items-center gap-3 text-center">
              <div className="w-12 h-12 rounded-full bg-slate-700 flex items-center justify-center">
                <Upload className="h-6 w-6 text-slate-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-white">
                  Drop files here or click to upload
                </p>
                <p className="text-xs text-slate-400 mt-1">
                  Support: Images, PDF, CSV, Excel files (max 10MB each)
                </p>
              </div>
            </div>
          </div>
        )}
        
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept="image/*,.pdf,.csv,.xlsx,.xls"
          onChange={handleChange}
          multiple
        />
      </div>

      {/* Message Input */}
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex items-end gap-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 p-3 border border-slate-600 rounded-lg bg-slate-700 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-slate-400 hover:text-white hover:bg-slate-600"
                onClick={() => fileInputRef.current?.click()}
              >
                <Plus className="h-4 w-4" />
              </Button>

              <Input
                type="text"
                placeholder={placeholder || "Add a message..."}
                value={message}
                onChange={onMessageChange}
                className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 bg-transparent text-white placeholder:text-slate-400"
                disabled={isUploading}
              />

              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-slate-400 hover:text-white hover:bg-slate-600"
              >
                <Smile className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isUploading}
              className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isUploading || (selectedFiles.length === 0 && !message.trim())}
              className="min-w-[80px] bg-green-600 hover:bg-green-700"
            >
              {isUploading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Sending...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Send className="h-4 w-4" />
                  <span>Send</span>
                </div>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
