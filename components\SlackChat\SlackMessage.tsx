"use client";

import { useState, useEffect, useCallback } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, Trash2, MoreHorizontal, Reply, Download, ExternalLink, FileText, Image as ImageIcon, FileSpreadsheet, Play, Pause } from "lucide-react";
import { cn } from "@/lib/utils";
import { ChatMessage } from "@/types/index";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { KanbanMessageCard } from "./KanbanMessageCard";
import Image from "next/image";

interface SlackMessageProps {
  message: ChatMessage;
  channelId: string;
  isCurrentUser: boolean;
  userColors: Record<string, string>;
  onDelete: (messageId: string) => void;
  onReply: (messageId: string) => void;
  onReaction: (messageId: string, emoji: string) => void;
  replyTo?: {
    id: string;
    content: string;
    user: string;
  };
  readBy?: string[];
  showAvatar?: boolean;
  isGrouped?: boolean;
  previousMessage?: ChatMessage | null;
}

const UPLOADTHING_URL = "https://uploadthing.com/f/";
const emojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];

export function SlackMessage({
  message,
  channelId,
  isCurrentUser,
  userColors,
  onDelete,
  onReply,
  onReaction,
  replyTo,
  readBy = [],
  showAvatar = true,
  isGrouped = false,
  previousMessage = null,
}: SlackMessageProps) {
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [previewError, setPreviewError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const userName = message.user || 'Anonymous';
  const userInitials = userName.substring(0, 2).toUpperCase();

  // Check if this message should be grouped with the previous one
  const shouldGroup = previousMessage &&
    previousMessage.userId === message.userId &&
    !replyTo &&
    !previousMessage.replyTo &&
    (new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime()) < 300000; // 5 minutes

  // Check if message contains kanban board reference
  const isKanbanMessage = message.content.includes('/kanban') || message.content.includes('/board') || message.content.includes('/project');
  const isCompactKanban = message.content.includes('/kanban-compact') || message.content.includes('/board-compact');

  const fetchAndCreateBlob = useCallback(async (url: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      setBlobUrl(blobUrl);
      return blobUrl;
    } catch (error) {
      console.error('Error creating blob URL:', error);
      setPreviewError(true);
      return null;
    }
  }, []);

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  const getFileIcon = (fileType: string, fileName: string) => {
    if (fileType?.startsWith('image/')) {
      return <ImageIcon className="h-5 w-5 text-blue-500" />;
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (fileType?.includes('spreadsheet') || fileName?.endsWith('.csv') || fileName?.endsWith('.xlsx')) {
      return <FileSpreadsheet className="h-5 w-5 text-green-500" />;
    }
    return <FileText className="h-5 w-5 text-slate-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderFilePreview = () => {
    const fileUrl = message.fileUrl || (message.fileKey ? `${UPLOADTHING_URL}${message.fileKey}` : null);
    if (!fileUrl) return null;

    const fileName = message.fileName || fileUrl.split('/').pop() || 'File';
    const fileType = message.fileType || '';

    // Image preview in card
    if (fileType.startsWith('image/')) {
      return (
        <div className="mt-1">
          <div className="bg-card rounded-lg border overflow-hidden max-w-xs">
            <div
              className="relative cursor-pointer group"
              onClick={() => setShowImagePreview(true)}
            >
              <Image
                src={fileUrl}
                alt={fileName}
                width={250}
                height={150}
                className="object-cover hover:opacity-90 transition-opacity w-full h-auto max-h-40"
                unoptimized
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                <div className="bg-black/50 rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity">
                  <ExternalLink className="h-4 w-4 text-white" />
                </div>
              </div>
            </div>
            <div className="p-2">
              <p className="text-xs font-medium truncate">{fileName}</p>
              <div className="flex items-center justify-between mt-1">
                <p className="text-xs text-muted-foreground">Image</p>
                <div className="flex gap-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        onClick={() => window.open(fileUrl, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Open image</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = fileUrl;
                          link.download = fileName;
                          link.click();
                        }}
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Download</TooltipContent>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>

          <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>
            <DialogContent className="max-w-6xl h-[90vh] p-0 bg-black">
              <div className="relative w-full h-full bg-black flex items-center justify-center">
                <Image
                  src={fileUrl}
                  alt={fileName}
                  fill
                  className="object-contain"
                  unoptimized
                  quality={100}
                  priority
                />
                <div className="absolute top-4 left-4 bg-black/70 text-white text-sm px-3 py-2 rounded">
                  {fileName}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      );
    }

    // File attachment in card
    return (
      <div className="mt-1">
        <div className="bg-card rounded-lg border p-2 hover:bg-accent/50 transition-colors max-w-xs">
          <div className="flex items-center gap-2">
            {getFileIcon(fileType, fileName)}
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium truncate">{fileName}</p>
              <p className="text-xs text-muted-foreground">
                {fileType.split('/')[1]?.toUpperCase() || 'FILE'}
              </p>
            </div>
            <div className="flex gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => window.open(fileUrl, '_blank')}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Open file</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = fileUrl;
                      link.download = fileName;
                      link.click();
                    }}
                  >
                    <Download className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Download</TooltipContent>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        "group relative px-2 py-1 transition-all duration-200 ease-in-out",
        isCurrentUser ? "flex justify-end" : "flex justify-start",
        shouldGroup ? "py-0.5" : "py-1"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={cn(
        "max-w-[75%] flex",
        isCurrentUser ? "flex-row-reverse" : "flex-row"
      )}>
        {/* Avatar */}
        {!shouldGroup && (
          <Avatar className={cn(
            "h-6 w-6 mt-0.5 flex-shrink-0",
            isCurrentUser ? "ml-2" : "mr-2"
          )}>
            <AvatarImage src={message.avatar} alt={userName} />
            <AvatarFallback className="text-xs">
              {userInitials}
            </AvatarFallback>
          </Avatar>
        )}

        {/* Message Bubble */}
        <div className={cn(
          "relative",
          shouldGroup && isCurrentUser && "mr-8",
          shouldGroup && !isCurrentUser && "ml-8"
        )}>
          {/* Reply indicator - Facebook style */}
          {replyTo && (
            <div className={cn(
              "mb-1 p-2 rounded-lg border-l-4 bg-muted border-border",
              isCurrentUser ? "bg-muted" : "bg-muted/50"
            )}>
              <div className="flex items-center gap-2 mb-1">
                <Reply className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs font-medium">{replyTo.user}</span>
              </div>
              <p className="text-xs text-muted-foreground truncate">{replyTo.content}</p>
            </div>
          )}

          {/* Message Header - Only show for non-grouped messages */}
          {!shouldGroup && !isCurrentUser && (
            <div className="flex items-baseline gap-2 mb-1">
              <span className="font-medium text-sm">{userName}</span>
              <span className="text-xs text-muted-foreground">
                {formatMessageTime(message.timestamp)}
              </span>
            </div>
          )}

          {/* Message Content Bubble */}
          <div className={cn(
            "rounded-2xl px-3 py-2 relative",
            isCurrentUser
              ? "bg-primary text-primary-foreground rounded-br-md"
              : "bg-muted text-foreground rounded-bl-md"
          )}>
            {/* Message Text */}
            {message.content && !isKanbanMessage && (
              <div className="text-sm leading-relaxed">
                {message.content}
              </div>
            )}

            {/* Kanban Board Card */}
            {isKanbanMessage && (
              <div className="my-2">
                <KanbanMessageCard
                  compact={isCompactKanban}
                  projectName={`Project for #${message.channelId}`}
                  channelName={message.channelId}
                />
                {message.content && (
                  <div className="text-sm text-slate-400 mt-2 italic">
                    {message.content.replace(/\/(kanban|board|project)(-compact)?/g, '').trim()}
                  </div>
                )}
              </div>
            )}

            {/* File Attachment */}
            {(message.fileKey || message.fileUrl) && renderFilePreview()}

            {/* Reactions */}
            {message.reactions && message.reactions.length > 0 && (
              <div className="flex items-center gap-1 mt-1">
                {message.reactions.map((reaction: any, index: number) => (
                  <button
                    key={index}
                    onClick={() => onReaction(message.id, reaction.emoji)}
                    className="flex items-center gap-1 px-2 py-1 rounded-full text-xs bg-muted hover:bg-accent border transition-colors"
                  >
                    <span>{reaction.emoji}</span>
                    <span className="font-medium">{reaction.count}</span>
                  </button>
                ))}
              </div>
            )}

            {/* Timestamp for current user messages */}
            {isCurrentUser && !shouldGroup && (
              <div className="text-xs text-muted-foreground mt-1 text-right">
                {formatMessageTime(message.timestamp)}
              </div>
            )}
          </div>

          {/* Message Actions */}
          {isHovered && (
            <div className={cn(
              "absolute top-0 bg-card border rounded-lg shadow-lg flex items-center animate-in fade-in-0 duration-200",
              isCurrentUser ? "left-0 -translate-x-full mr-2" : "right-0 translate-x-full ml-2"
            )}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setShowReactions(!showReactions)}
                  >
                    <span className="text-sm">😊</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add reaction</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => onReply(message.id)}
                  >
                    <Reply className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Reply</TooltipContent>
              </Tooltip>

              {isCurrentUser && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-destructive hover:text-destructive"
                      onClick={() => onDelete(message.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Delete message</TooltipContent>
                </Tooltip>
              )}

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setShowActions(!showActions)}
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>More actions</TooltipContent>
              </Tooltip>
            </div>
          )}

          {/* Emoji Picker */}
          {showReactions && (
            <div className={cn(
              "absolute top-6 bg-card border rounded-lg shadow-lg p-2 flex gap-1 z-10",
              isCurrentUser ? "left-0" : "right-0"
            )}>
              {emojis.map(emoji => (
                <button
                  key={emoji}
                  onClick={() => {
                    onReaction(message.id, emoji);
                    setShowReactions(false);
                  }}
                  className="p-1 hover:bg-accent rounded transition-colors"
                >
                  {emoji}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Read receipts for current user messages */}
        {isCurrentUser && readBy && readBy.length > 0 && (
          <div className="flex justify-end mt-1 mr-8">
            <div className="flex items-center gap-1">
              {readBy.slice(0, 3).map((reader, i) => (
                <div key={i} className="w-3 h-3 rounded-full bg-muted text-xs flex items-center justify-center">
                  ✓
                </div>
              ))}
              {readBy.length > 3 && (
                <span className="text-xs text-muted-foreground">+{readBy.length - 3}</span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
