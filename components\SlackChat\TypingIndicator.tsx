"use client";

import { cn } from "@/lib/utils";

interface TypingIndicatorProps {
  userName: string;
}

export function TypingIndicator({ userName }: TypingIndicatorProps) {
  return (
    <div className="flex items-center gap-2 px-4 py-1 text-sm text-muted-foreground">
      <div className="flex items-center gap-1">
        <div className="flex space-x-1">
          <div className="h-1.5 w-1.5 rounded-full bg-green-500 animate-bounce [animation-delay:-0.3s]" />
          <div className="h-1.5 w-1.5 rounded-full bg-green-500 animate-bounce [animation-delay:-0.15s]" />
          <div className="h-1.5 w-1.5 rounded-full bg-green-500 animate-bounce" />
        </div>
        <span className="ml-2">{userName} is typing...</span>
      </div>
    </div>
  );
} 