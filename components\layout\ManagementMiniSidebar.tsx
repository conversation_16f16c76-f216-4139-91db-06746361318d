"use client"

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useParams } from "next/navigation";
import { X, UserCog, MessageSquare, FileSpreadsheet, Users, FileText, PartyPopper, Paperclip, ChevronRight, ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useSidebar } from "@/components/ui/sidebar";

const managementRoutes = [
  { path: "/hr/management/profile", name: "Profile", icon: UserCog },
  { path: "/hr/workspace", name: "Chat", icon: MessageSquare },
  { path: "/hr/management/workspace/note", name: "Notes", icon: FileSpreadsheet },
];

interface ManagementMiniSidebarProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const ManagementMiniSidebar = ({ isOpen, setIsOpen }: ManagementMiniSidebarProps) => {
  const [pageType, setPageType] = useState<'management' | 'employee' | null>(null);
  const pathname = usePathname();
  const params = useParams();
  const employeeId = params.id;
  const { state: mainSidebarState } = useSidebar();
  const isMainSidebarExpanded = mainSidebarState === "expanded";

  const employeeRoutes = [
    { path: `/hr/employee/${employeeId}`, name: "Details", icon: Users },
    { path: `/hr/employee/${employeeId}/doc`, name: "Documents", icon: FileText },
    { path: `/hr/employee/${employeeId}/calc`, name: "Calculations", icon: PartyPopper },
    { path: `/hr/employee/${employeeId}/attestation`, name: "Attestation", icon: Paperclip },
  ];

  // Check if screen is mobile
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  useEffect(() => {
    const isEmployeePage = /^\/hr\/employee\/[^/]+/.test(pathname);
    const isManagementPage = pathname.startsWith('/hr/management');

    if (isEmployeePage) {
      setPageType('employee');
    } else if (isManagementPage) {
      setPageType('management');
    } else {
      setIsOpen(false);
      setPageType(null);
    }

    // Close sidebar on mobile
    if (isMobile) {
      setIsOpen(false);
    }
  }, [pathname, setIsOpen, isMobile]);

  // Manage body class for sidebar state without affecting scroll
  useEffect(() => {
    if (isOpen) {
      document.documentElement.classList.add('sidebar-open');
      document.body.classList.add('sidebar-open');
    } else {
      document.documentElement.classList.remove('sidebar-open');
      document.body.classList.remove('sidebar-open');
    }

    // Cleanup on unmount
    return () => {
      document.documentElement.classList.remove('sidebar-open');
      document.body.classList.remove('sidebar-open');
    };
  }, [isOpen]);

  if (!pageType || isMobile) return null;

  const routes = pageType === 'management' ? managementRoutes : employeeRoutes;

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {/* Overlay when sidebar is open - no click handler */}
      <div
        className={cn(
          "employee-mini-sidebar-overlay",
          isOpen && "open"
        )}
      />

      {/* Toggle button - only way to open/close sidebar */}
      <button
        onClick={toggleSidebar}
        className={cn(
          "employee-mini-sidebar-toggle",
          isOpen && "sidebar-open",
          isMainSidebarExpanded && "main-sidebar-expanded"
        )}
        style={{
          left: isMainSidebarExpanded
            ? (isOpen ? "28rem" : "19.5rem")
            : (isOpen ? "16rem" : "7.5rem")
        }}
        aria-label="Toggle sidebar"
      >
        {isOpen ? (
          <ChevronLeft className="h-3 w-3 text-muted-foreground hover:text-foreground transition-colors" />
        ) : (
          <ChevronRight className="h-3 w-3 text-muted-foreground hover:text-foreground transition-colors" />
        )}
      </button>

      {/* Sidebar */}
      <aside
        className={cn(
          "employee-mini-sidebar bg-background border-r shadow-md overflow-hidden",
          isOpen && "open",
          isMainSidebarExpanded && "main-sidebar-expanded"
        )}
        style={{
          left: isMainSidebarExpanded ? "16rem" : "4rem"
        }}
      >
        <div className="flex flex-col h-full overflow-hidden">
          <div className="h-12 border-b flex items-center px-3 overflow-hidden">
            {isOpen ? (
              <h2 className="text-xs font-semibold truncate">
                {pageType === 'management' ? 'Management' : 'Employee Details'}
              </h2>
            ) : null}
          </div>

          <nav className="flex-1 overflow-y-auto overflow-x-hidden p-2 scrollbar-thin">
            <ul className="space-y-1 overflow-hidden">
              {routes.map(({ path, name, icon: Icon }) => (
                <li key={path}>
                  {isOpen ? (
                    <Link
                      href={path}
                      className={cn(
                        "flex items-center gap-2 px-2 py-1.5 text-xs rounded-md text-muted-foreground overflow-hidden",
                        "hover:text-foreground hover:bg-accent/50 transition-colors",
                        pathname === path && "bg-accent/50 text-foreground font-medium"
                      )}
                    >
                      <Icon className="h-3.5 w-3.5 shrink-0" />
                      <span className="truncate max-w-[120px]">{name}</span>
                    </Link>
                  ) : (
                    <TooltipProvider>
                      <Tooltip delayDuration={100}>
                        <TooltipTrigger asChild>
                          <Link
                            href={path}
                            className={cn(
                              "flex items-center justify-center p-2 my-0.5 mx-auto rounded-md text-muted-foreground",
                              "hover:text-foreground hover:bg-accent/50 transition-colors",
                              pathname === path && "bg-accent/50 text-foreground font-medium"
                            )}
                          >
                            <Icon className="h-3.5 w-3.5 text-foreground/80" />
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <p className="text-xs">{name}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </aside>
    </>
  );
};

export default ManagementMiniSidebar;