"use client"

import * as React from "react"
// import { useSidebar } from "@/components/ui/sidebar"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

export function TeamSwitcher() {
  const { isMobile } = useSidebar()
  // const collapsed = sidebar.collapsible === "icon" && sidebar.isOpen === false
  const appName = "LoopFlow"
  const firstLetter = appName.charAt(0)

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
          {isMobile ? (
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/80 text-primary-foreground text-sm font-medium">
              {firstLetter}
            </div>
          ) : (
            <>
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/80 text-primary-foreground text-sm font-medium mr-2 shrink-0">
                {firstLetter}
              </div>
              <span className="text-base font-semibold">{appName}</span>
            </>
          )}
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
