'use client';

import * as React from "react";
import { cn } from "@/lib/utils";
import { Input } from "./input";

export interface InputWithPrefixProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'prefix'> {
  prefix?: React.ReactNode;
}

const InputWithPrefix = React.forwardRef<HTMLInputElement, InputWithPrefixProps>(
  ({ className, prefix, ...props }, ref) => {
    return (
      <div className="relative flex items-center w-full">
        {prefix && (
          <div className="absolute left-3 flex items-center pointer-events-none">
            {prefix}
          </div>
        )}
        <Input
          ref={ref}
          className={cn(
            prefix && "pl-10",
            className
          )}
          {...props}
        />
      </div>
    );
  }
);

InputWithPrefix.displayName = "InputWithPrefix";

export { InputWithPrefix };


