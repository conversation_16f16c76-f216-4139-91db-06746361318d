"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  <PERSON>older, 
  FileText, 
  ArrowLeft, 
  Clock, 
  User2,
  ChevronRight,
  FolderOpen
} from "lucide-react"
import Link from "next/link"
import { format } from "date-fns"
import { motion, AnimatePresence } from "framer-motion"

interface Note {
  id: string;
  title: string;
  content: string;
  coverImage: string | null;
  createdAt: Date;
  updatedAt: Date;
  isFolder: boolean;
  parentId: string | null;
}

interface FolderBrowserProps {
  notes: Note[];
  onClose: () => void;
  initialFolderId?: string;
}

export function FolderBrowser({ notes, onClose, initialFolderId }: FolderBrowserProps) {
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(initialFolderId || null);
  const [breadcrumb, setBreadcrumb] = useState<{ id: string; title: string }[]>([]);

  // Get current folder
  const currentFolder = currentFolderId ? notes.find(n => n.id === currentFolderId && n.isFolder) : null;

  // Get items in current folder
  const currentItems = notes.filter(note => note.parentId === currentFolderId);
  const folders = currentItems.filter(item => item.isFolder);
  const files = currentItems.filter(item => !item.isFolder);

  // Navigation functions
  const navigateToFolder = (folderId: string, folderTitle: string) => {
    if (currentFolder) {
      setBreadcrumb(prev => [...prev, { id: currentFolder.id, title: currentFolder.title }]);
    }
    setCurrentFolderId(folderId);
  };

  const navigateBack = () => {
    if (breadcrumb.length > 0) {
      const lastFolder = breadcrumb[breadcrumb.length - 1];
      setBreadcrumb(prev => prev.slice(0, -1));
      setCurrentFolderId(lastFolder.id);
    } else {
      setCurrentFolderId(null);
    }
  };

  const navigateToRoot = () => {
    setCurrentFolderId(null);
    setBreadcrumb([]);
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <Card 
        className="w-full max-w-4xl max-h-[80vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="w-5 h-5 text-blue-600" />
                {currentFolder ? currentFolder.title : "All Folders"}
              </CardTitle>
              <CardDescription>
                Browse your notes and folders
              </CardDescription>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
          
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={navigateToRoot}
              className="h-6 px-2"
            >
              Root
            </Button>
            {breadcrumb.map((crumb, index) => (
              <React.Fragment key={crumb.id}>
                <ChevronRight className="w-3 h-3" />
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => {
                    setBreadcrumb(prev => prev.slice(0, index + 1));
                    setCurrentFolderId(crumb.id);
                  }}
                  className="h-6 px-2"
                >
                  {crumb.title}
                </Button>
              </React.Fragment>
            ))}
            {currentFolder && (
              <>
                <ChevronRight className="w-3 h-3" />
                <span className="font-medium">{currentFolder.title}</span>
              </>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Back button */}
          {(currentFolderId || breadcrumb.length > 0) && (
            <Button 
              variant="outline" 
              onClick={navigateBack}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          )}

          <div className="space-y-6">
            {/* Folders */}
            {folders.length > 0 && (
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide flex items-center">
                  <Folder className="w-4 h-4 mr-2" />
                  Folders ({folders.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {folders.map((folder) => {
                    const filesInFolder = notes.filter(n => n.parentId === folder.id && !n.isFolder);
                    
                    return (
                      <Card 
                        key={folder.id}
                        className="cursor-pointer hover:shadow-lg transition-all group border-border/50 hover:scale-[1.02] bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20"
                        onClick={() => navigateToFolder(folder.id, folder.title)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center space-x-2">
                            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                              <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <CardTitle className="text-base line-clamp-1 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                                {folder.title}
                              </CardTitle>
                              <p className="text-xs text-muted-foreground mt-1">
                                {filesInFolder.length} file{filesInFolder.length !== 1 ? 's' : ''}
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Files */}
            {files.length > 0 && (
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Files ({files.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {files.map((file) => (
                    <Link key={file.id} href={`/hr/workspace/note/${file.id}`}>
                      <Card className="cursor-pointer hover:shadow-lg transition-all group border-border/50 hover:scale-[1.02]">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base line-clamp-1 group-hover:text-primary">
                              {file.title}
                            </CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-2 text-xs text-muted-foreground">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {format(new Date(file.createdAt), 'MMM dd, yyyy')}
                            </div>
                            <div className="flex items-center">
                              <User2 className="w-3 h-3 mr-1" />
                              You
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Empty state */}
            {folders.length === 0 && files.length === 0 && (
              <div className="text-center py-12">
                <Folder className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                  {currentFolder ? "Empty Folder" : "No Folders Found"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {currentFolder 
                    ? "This folder doesn't contain any files or subfolders yet."
                    : "Create your first folder to organize your notes."
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
