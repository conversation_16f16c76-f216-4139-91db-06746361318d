# ChartBuilder API Endpoint Fix

## 🐛 **Issue Identified**
**Error**: "Execution Failed - 400: Unsupported language" when running SQL code

**Root Cause**: SQL queries were being sent to the wrong API endpoint (`/api/execute` instead of `/api/query`)

## 🔧 **Fix Applied**

### 1. **Corrected SQL Execution Route**
**File**: `components/ChartBuilder/chartbuilderlogic/useCellExecution.ts`

**Before**:
```typescript
case 'sql':
  endpoint = '/api/execute';  // ❌ Wrong endpoint
  payload = {
    code,
    language: 'sql'
    // ...
  };
```

**After**:
```typescript
case 'sql':
  endpoint = '/api/query';  // ✅ Correct endpoint
  payload = {
    query: code.trim(),  // ✅ Correct parameter name
    datasets: cellDatasets.map(ds => ({
      id: ds.id,
      name: ds.name,
      data: ds.data
    })),
  };
```

### 2. **Enhanced Error Handling**
**File**: `app/api/execute/route.ts`

Added clear error messages when S<PERSON> is sent to the wrong endpoint:

```typescript
// Check if this is a SQL query sent to the wrong endpoint
if (language === 'sql') {
  return NextResponse.json(
    { error: 'SQL queries should be sent to /api/query endpoint, not /api/execute' },
    { status: 400 }
  );
}

// Only support Python execution in this endpoint
if (language !== 'python') {
  return NextResponse.json(
    { error: `Unsupported language: ${language}. This endpoint only supports Python execution.` },
    { status: 400 }
  );
}
```

## 📋 **API Endpoint Mapping**

| Language   | Correct Endpoint | Purpose |
|------------|------------------|---------|
| **SQL**    | `/api/query`     | Server-side SQL execution using AlasQL |
| **Python** | `/api/execute`   | Forwards to Python backend server |
| **JavaScript** | `/api/execute-js` | Client-side JavaScript execution |

## 🔍 **Verification**

### SQL Execution Flow:
1. **Frontend**: Cell component sends SQL query to `/api/query`
2. **Backend**: `/api/query` route processes the query using AlasQL
3. **Response**: Returns `{ data, output, showGraphicWalker, executionTime }`

### Python Execution Flow:
1. **Frontend**: Cell component sends Python code to `/api/execute`
2. **Middleware**: `/api/execute` forwards to Python backend
3. **Backend**: Python backend executes code and returns results
4. **Response**: Returns `{ data, output, plots }`

## ✅ **Expected Behavior After Fix**

### SQL Queries:
- ✅ Execute successfully using actual dataset names
- ✅ Support JOINs, UNIONs, and complex queries
- ✅ Return proper data format for visualization
- ✅ Show clear error messages for SQL syntax errors

### Python Code:
- ✅ Execute successfully with dataset variables
- ✅ Support matplotlib plots and data analysis
- ✅ Access datasets using actual names
- ✅ Return proper data format and plots

## 🧪 **Testing**

To verify the fix works:

1. **Test SQL Execution**:
   ```sql
   -- This should now work correctly
   SELECT * FROM employees LIMIT 5;
   
   -- Join multiple datasets
   SELECT e.name, s.amount 
   FROM employees e 
   JOIN sales s ON e.id = s.employee_id;
   ```

2. **Test Python Execution**:
   ```python
   # This should continue working
   print(employees.head())
   
   # Create a simple plot
   import matplotlib.pyplot as plt
   plt.plot([1, 2, 3, 4])
   result = get_plot()
   ```

3. **Test Error Handling**:
   - SQL with syntax errors should show clear AlasQL error messages
   - Python with errors should show Python traceback
   - Sending SQL to `/api/execute` should show helpful redirect message

## 📝 **Files Modified**

1. **`components/ChartBuilder/chartbuilderlogic/useCellExecution.ts`**
   - Fixed SQL execution to use `/api/query` endpoint
   - Updated payload format for SQL queries

2. **`app/api/execute/route.ts`**
   - Added validation to reject SQL queries with helpful error message
   - Enhanced error handling for unsupported languages

## 🎯 **Result**

SQL queries in ChartBuilder now execute correctly without the "Unsupported language" error. The system properly routes different languages to their appropriate execution endpoints, providing a much better user experience.

## 🔄 **Backward Compatibility**

This fix maintains full backward compatibility:
- Existing Python code continues to work unchanged
- JavaScript execution remains unaffected
- SQL queries now work as expected
- All existing API contracts are preserved
