# ChartBuilder Multiple Datasets - Final Test Script

## Quick Verification Tests

Use these exact scripts to verify that multiple dataset functionality is working correctly.

## Test 1: SQL UNION ALL (Copy and paste this exactly)

**Select 2+ datasets in a SQL cell and run:**

```sql
-- Test 1: Basic UNION ALL with standardized table names
SELECT 'Dataset 1' as source, COUNT(*) as row_count FROM dataset1
UNION ALL
SELECT 'Dataset 2' as source, COUNT(*) as row_count FROM dataset2;
```

**Expected Result:** Should show row counts from both datasets without errors.

## Test 2: SQL Column Selection (Copy and paste this exactly)

```sql
-- Test 2: Select specific columns from multiple datasets
-- Take 2 columns from dataset1, 3 columns from dataset2
SELECT employee_id, name FROM dataset1
UNION ALL
SELECT employee_id, name FROM dataset2;
```

**If you need different column counts:**
```sql
-- Different column counts - pad with NULL
SELECT 
  employee_id, 
  name,
  NULL as department
FROM dataset1
UNION ALL
SELECT 
  employee_id, 
  name,
  department
FROM dataset2;
```

## Test 3: SQL JOIN (Copy and paste this exactly)

```sql
-- Test 3: JOIN multiple datasets
SELECT 
  d1.employee_id,
  d1.name,
  d2.department,
  d2.salary
FROM dataset1 d1
JOIN dataset2 d2 ON d1.employee_id = d2.employee_id
LIMIT 10;
```

## Test 4: Python Multiple Dataset Loading (Copy and paste this exactly)

**Select 2+ datasets in a Python cell and run:**

```python
# Test 4: Load multiple datasets
show_datasets()  # See what's available

# Load datasets using standardized names
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

print(f"Dataset 1: {df1.shape[0]} rows, {df1.shape[1]} columns")
print(f"Dataset 2: {df2.shape[0]} rows, {df2.shape[1]} columns")

print(f"Dataset 1 columns: {df1.columns.tolist()}")
print(f"Dataset 2 columns: {df2.columns.tolist()}")

result = df1.head(3)
```

## Test 5: Python Column Selection (Copy and paste this exactly)

```python
# Test 5: Select specific columns from multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Take 2 columns from df1
df1_cols = df1.iloc[:, :2]  # First 2 columns
print(f"Selected from Dataset 1: {df1_cols.columns.tolist()}")

# Take 5 columns from df2 (or all if less than 5)
df2_cols = df2.iloc[:, :5]  # First 5 columns
print(f"Selected from Dataset 2: {df2_cols.columns.tolist()}")

# Combine them
combined = pd.concat([
    df1_cols.head(3).assign(source='Dataset1'),
    df2_cols.head(3).assign(source='Dataset2')
], ignore_index=True)

result = combined
```

## Test 6: Python Merge (Copy and paste this exactly)

```python
# Test 6: Merge datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Find common columns
common_cols = set(df1.columns) & set(df2.columns)
print(f"Common columns: {list(common_cols)}")

# Try to merge on the first common column
if common_cols:
    merge_col = list(common_cols)[0]
    print(f"Merging on: {merge_col}")
    
    merged = pd.merge(df1, df2, on=merge_col, how='inner')
    print(f"Merged dataset: {merged.shape}")
    result = merged.head()
else:
    print("No common columns found - showing concatenated data")
    result = pd.concat([df1.head(2), df2.head(2)], keys=['Dataset1', 'Dataset2'])
```

## Test 7: Debug Information (Copy and paste this exactly)

**SQL Debug:**
```sql
-- Test 7a: Check table availability
SELECT 'dataset1' as table_name, COUNT(*) as rows FROM dataset1
UNION ALL
SELECT 'dataset2' as table_name, COUNT(*) as rows FROM dataset2
UNION ALL
SELECT 'dataset3' as table_name, COUNT(*) as rows FROM dataset3;
```

**Python Debug:**
```python
# Test 7b: Check dataset availability
print("=== DATASET DEBUG INFO ===")
show_datasets()

print("\n=== LOADING TEST ===")
for i in range(1, 6):
    try:
        df = pd.read_csv(f'dataset{i}.csv')
        print(f"✓ dataset{i}.csv: {df.shape} rows/cols")
        print(f"  Columns: {df.columns.tolist()[:5]}...")  # First 5 columns
    except FileNotFoundError:
        print(f"✗ dataset{i}.csv: Not available")
        break
    except Exception as e:
        print(f"✗ dataset{i}.csv: Error - {e}")
        break

print("\n=== FIRST DATASET SAMPLE ===")
try:
    df1 = pd.read_csv('dataset1.csv')
    result = df1.head(2)
except Exception as e:
    result = f"Error loading dataset1: {e}"
```

## Expected Results Summary

### ✅ All tests should pass with:

1. **SQL UNION ALL**: Combines data from multiple tables
2. **SQL Column Selection**: Can select different columns from different tables
3. **SQL JOIN**: Can join tables on common columns
4. **Python Loading**: Can load multiple datasets with `pd.read_csv()`
5. **Python Column Selection**: Can select specific columns from each dataset
6. **Python Merge**: Can merge datasets on common columns
7. **Debug Info**: Shows available tables/datasets clearly

### ❌ If any test fails:

1. **"Table not found"**: Check dataset selection in cell
2. **"File not found"**: Run `show_datasets()` to see available files
3. **"Column not found"**: Check column names with debug script
4. **Empty results**: Verify datasets have data and common columns exist

## Troubleshooting Commands

**If SQL fails:**
```sql
-- Check what tables exist
SELECT 'test' as status, 'Tables should be dataset1, dataset2, etc.' as info;
```

**If Python fails:**
```python
# Check what's available
show_datasets()
print("If you see files listed above, use those exact names")
```

## Success Criteria

- ✅ Can select multiple datasets in UI
- ✅ SQL queries work with `dataset1`, `dataset2` table names
- ✅ UNION ALL combines data correctly
- ✅ JOINs work on common columns
- ✅ Python can load datasets with `pd.read_csv('dataset1.csv')`
- ✅ Can select specific columns from each dataset
- ✅ Can merge/concatenate datasets in Python
- ✅ Error messages are helpful when something goes wrong

Run these tests in order. If all pass, your multiple dataset functionality is working correctly!
