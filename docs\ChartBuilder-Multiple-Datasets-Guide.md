# ChartBuilder Multiple Datasets Guide

## Overview

The ChartBuilder component supports working with multiple datasets simultaneously in both SQL and Python environments. This guide explains how to select, load, and work with multiple datasets effectively.

## Dataset Selection

### UI Selection
1. Click the **Database icon** in any cell's toolbar
2. Use **checkboxes** to select multiple datasets
3. Selected datasets will be available in that specific cell
4. Each cell can have its own dataset selection

### Dataset Information Display
- Single dataset: Shows dataset name
- Multiple datasets: Shows count (e.g., "3 datasets")
- No selection: Shows "Select Datasets"

## SQL Usage with Multiple Datasets

### Table Naming Convention
When you select multiple datasets, they become available as tables using their actual dataset names (converted to safe SQL identifiers):

- Dataset names are converted to lowercase
- File extensions are removed (e.g., `.csv`, `.xlsx`)
- Special characters are replaced with underscores
- Examples:
  - "Employee Data.csv" → `employee_data`
  - "Sales-2024.xlsx" → `sales_2024`
  - "HR Analytics" → `hr_analytics`

### Basic Syntax
```sql
-- Query single dataset (use actual dataset name)
SELECT * FROM employee_data LIMIT 5;

-- Join multiple datasets
SELECT
  a.employee_id,
  a.name,
  b.department,
  b.salary
FROM employee_data a
JOIN payroll_data b ON a.employee_id = b.employee_id;

-- Union data from different datasets
SELECT month, sales FROM sales_2023
UNION ALL
SELECT month, sales FROM sales_2024;

-- Complex analysis across datasets
SELECT
  e.department,
  COUNT(*) AS employee_count,
  AVG(p.salary) AS avg_salary,
  SUM(s.sales) AS total_sales
FROM employee_data e
JOIN payroll_data p ON e.employee_id = p.employee_id
JOIN sales_data s ON e.employee_id = s.employee_id
GROUP BY e.department
ORDER BY total_sales DESC;
```

### Advanced SQL Operations

#### Cross-Dataset Comparisons
```sql
-- Compare metrics across datasets
SELECT
  'Employee Data' as source,
  COUNT(*) as record_count,
  AVG(salary) as avg_salary
FROM employee_data
UNION ALL
SELECT
  'Payroll Data' as source,
  COUNT(*) as record_count,
  AVG(salary) as avg_salary
FROM payroll_data;
```

#### Data Quality Checks
```sql
-- Find common records between datasets
SELECT e.employee_id, e.name
FROM employee_data e
INNER JOIN payroll_data p ON e.employee_id = p.employee_id;

-- Find records only in employee_data
SELECT e.employee_id, e.name
FROM employee_data e
LEFT JOIN payroll_data p ON e.employee_id = p.employee_id
WHERE p.employee_id IS NULL;
```

#### Table Information
```sql
-- Check available tables and their structure
-- Note: This works in client-side AlasQL execution
SELECT 'employee_data' as table_name, COUNT(*) as row_count FROM employee_data
UNION ALL
SELECT 'payroll_data' as table_name, COUNT(*) as row_count FROM payroll_data;
```

### Determining Table Names
To find out the exact table names for your selected datasets:

1. **Check the dataset names in the UI**: The table name will be the dataset name converted to lowercase with special characters replaced by underscores
2. **Use a simple query**: Try `SELECT * FROM [expected_name] LIMIT 1;` to test if the table exists
3. **Common conversions**:
   - Spaces become underscores: "Employee Data" → `employee_data`
   - Hyphens become underscores: "sales-data" → `sales_data`
   - File extensions are removed: "data.csv" → `data`
   - Special characters are removed: "data@2024!" → `data_2024_`

## Python Usage with Multiple Datasets

### Dataset Loading
Selected datasets are automatically available as virtual CSV files:

```python
# Check what datasets are available
show_datasets()  # Shows exact filenames to use

# Load datasets using pandas
df1 = pd.read_csv('dataset1.csv')  # First selected dataset
df2 = pd.read_csv('dataset2.csv')  # Second selected dataset
df3 = pd.read_csv('dataset3.csv')  # Third selected dataset

# Alternative: Load by dataset name (case-insensitive)
df_employees = pd.read_csv('employees.csv')
df_payroll = pd.read_csv('payroll.csv')
```

### Dataset Information
```python
# Get basic info about loaded datasets
print(f"df1 shape: {df1.shape}")
print(f"df1 columns: {df1.columns.tolist()}")

print(f"df2 shape: {df2.shape}")
print(f"df2 columns: {df2.columns.tolist()}")

# Find common columns
common_cols = set(df1.columns) & set(df2.columns)
print(f"Common columns: {list(common_cols)}")
```

### Data Merging and Concatenation

#### Inner/Outer Joins
```python
# Merge on common column
if 'employee_id' in df1.columns and 'employee_id' in df2.columns:
    merged = pd.merge(df1, df2, on='employee_id', how='inner')
    print(f"Merged dataset shape: {merged.shape}")
    result = merged.head()

# Merge with different join types
left_join = pd.merge(df1, df2, on='employee_id', how='left')
outer_join = pd.merge(df1, df2, on='employee_id', how='outer')
```

#### Concatenation
```python
# Vertical concatenation (stacking datasets)
combined = pd.concat([df1, df2], ignore_index=True)
print(f"Combined dataset shape: {combined.shape}")

# Horizontal concatenation (side by side)
side_by_side = pd.concat([df1, df2], axis=1)

# Concatenation with labels
labeled = pd.concat([df1, df2], keys=['Dataset1', 'Dataset2'])
result = labeled.head(10)
```

### Comparative Analysis

#### Statistical Comparisons
```python
# Compare basic statistics
comparison = pd.DataFrame({
    'Dataset1': df1.describe().loc['mean'],
    'Dataset2': df2.describe().loc['mean']
})
result = comparison
```

#### Data Quality Assessment
```python
# Compare data quality metrics
quality_comparison = pd.DataFrame({
    'Metric': ['Total Rows', 'Total Columns', 'Missing Values', 'Duplicate Rows'],
    'Dataset1': [
        len(df1),
        len(df1.columns),
        df1.isnull().sum().sum(),
        df1.duplicated().sum()
    ],
    'Dataset2': [
        len(df2),
        len(df2.columns),
        df2.isnull().sum().sum(),
        df2.duplicated().sum()
    ]
})
result = quality_comparison
```

### Visualization with Multiple Datasets

#### Comparison Plots
```python
# Compare distributions
column_to_compare = 'salary'  # Change to your column name

if column_to_compare in df1.columns and column_to_compare in df2.columns:
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.hist(df1[column_to_compare].dropna(), bins=20, alpha=0.7, label='Dataset 1')
    plt.title(f'{column_to_compare} - Dataset 1')
    plt.xlabel(column_to_compare)
    plt.ylabel('Frequency')
    
    plt.subplot(1, 2, 2)
    plt.hist(df2[column_to_compare].dropna(), bins=20, alpha=0.7, color='orange', label='Dataset 2')
    plt.title(f'{column_to_compare} - Dataset 2')
    plt.xlabel(column_to_compare)
    plt.ylabel('Frequency')
    
    plt.tight_layout()
    result = get_plot()
```

#### Overlay Plots
```python
# Overlay histograms for comparison
plt.figure(figsize=(10, 6))
plt.hist(df1[column_to_compare].dropna(), bins=20, alpha=0.5, label='Dataset 1')
plt.hist(df2[column_to_compare].dropna(), bins=20, alpha=0.5, label='Dataset 2')
plt.title(f'{column_to_compare} Comparison')
plt.xlabel(column_to_compare)
plt.ylabel('Frequency')
plt.legend()
plt.tight_layout()
result = get_plot()
```

## Best Practices

### Dataset Selection
- Select only the datasets you need for your analysis
- Use descriptive names when uploading datasets
- Verify dataset compatibility before complex operations

### Performance Tips
- Limit the number of datasets when possible
- Use appropriate join types (inner vs outer)
- Filter data early in your analysis pipeline
- Use `LIMIT` in SQL for large datasets during exploration

### Error Handling
```python
# Check if datasets exist before using
try:
    df1 = pd.read_csv('dataset1.csv')
    df2 = pd.read_csv('dataset2.csv')
    print("✓ Both datasets loaded successfully")
except FileNotFoundError as e:
    print(f"❌ Dataset not found: {e}")
    show_datasets()  # Show available datasets
```

### Memory Management
```python
# Check memory usage
print(f"df1 memory usage: {df1.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print(f"df2 memory usage: {df2.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Free memory when done
del df1, df2
```

## Common Use Cases

### 1. Employee Data Analysis
```python
# Load employee and payroll data
employees = pd.read_csv('employees.csv')
payroll = pd.read_csv('payroll.csv')

# Merge for comprehensive analysis
employee_payroll = pd.merge(employees, payroll, on='employee_id')
result = employee_payroll.groupby('department')['salary'].agg(['mean', 'count'])
```

### 2. Time Series Comparison
```python
# Compare sales data across different periods
sales_2023 = pd.read_csv('sales_2023.csv')
sales_2024 = pd.read_csv('sales_2024.csv')

# Add year column and combine
sales_2023['year'] = 2023
sales_2024['year'] = 2024
combined_sales = pd.concat([sales_2023, sales_2024])

# Plot comparison
plt.figure(figsize=(12, 6))
for year in [2023, 2024]:
    year_data = combined_sales[combined_sales['year'] == year]
    plt.plot(year_data['month'], year_data['sales'], label=f'{year}', marker='o')

plt.title('Sales Comparison by Year')
plt.xlabel('Month')
plt.ylabel('Sales')
plt.legend()
plt.grid(True)
plt.tight_layout()
result = get_plot()
```

### 3. Data Validation
```sql
-- Check data consistency across datasets
SELECT
  'employee_data' as source,
  COUNT(DISTINCT employee_id) as unique_employees,
  MIN(hire_date) as earliest_hire,
  MAX(hire_date) as latest_hire
FROM employee_data
UNION ALL
SELECT
  'payroll_data' as source,
  COUNT(DISTINCT employee_id) as unique_employees,
  MIN(hire_date) as earliest_hire,
  MAX(hire_date) as latest_hire
FROM payroll_data;
```

## Troubleshooting

### Common Issues

1. **Dataset not found**: Use `show_datasets()` to see available files
2. **Column mismatch**: Check column names with `df.columns.tolist()`
3. **Memory errors**: Process datasets in chunks or filter early
4. **Join failures**: Verify common columns exist and have compatible data types

### Debug Commands
```python
# Debug dataset availability
show_datasets()

# Check dataset structure
print("Dataset 1 info:")
print(df1.info())
print("\nDataset 2 info:")
print(df2.info())

# Verify join keys
print("Common columns:", set(df1.columns) & set(df2.columns))
```

## Testing Multiple Dataset Functionality

### Quick Test - SQL
```sql
-- Test with multiple datasets selected
-- This should show data from your first selected dataset
SELECT * FROM employee_data LIMIT 3;

-- Test joining (if you have compatible datasets)
SELECT COUNT(*) as total_records
FROM employee_data e
JOIN payroll_data p ON e.employee_id = p.employee_id;
```

### Quick Test - Python
```python
# Check what datasets are available
show_datasets()

# Load and inspect multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

print(f"Dataset 1: {df1.shape[0]} rows, {df1.shape[1]} columns")
print(f"Dataset 2: {df2.shape[0]} rows, {df2.shape[1]} columns")

# Show first few rows of each
print("\nDataset 1 sample:")
print(df1.head(2))

print("\nDataset 2 sample:")
result = df2.head(2)
```

### Verification Steps
1. **Select multiple datasets** using the database icon in a cell
2. **Run a simple query** to verify datasets are loaded
3. **Check column names** to understand data structure
4. **Test joins** on common columns if applicable
5. **Verify data types** and handle any inconsistencies

## Advanced Features

### Dynamic Dataset Loading
```python
# Load datasets dynamically based on availability
available = []
for i in range(1, 6):  # Check for up to 5 datasets
    try:
        df = pd.read_csv(f'dataset{i}.csv')
        available.append((f'df{i}', df))
        print(f"✓ Loaded dataset{i}.csv: {df.shape}")
    except FileNotFoundError:
        break

print(f"Total datasets loaded: {len(available)}")

# Work with all available datasets
if len(available) >= 2:
    df1, df2 = available[0][1], available[1][1]
    # Perform analysis...
    result = pd.concat([df1.head(2), df2.head(2)], keys=['Dataset1', 'Dataset2'])
```

### Error Handling Best Practices
```python
# Robust dataset loading with error handling
def load_datasets():
    datasets = {}
    try:
        show_datasets()  # Show what's available

        # Try to load common dataset patterns
        patterns = ['dataset1.csv', 'dataset2.csv', 'employees.csv', 'sales.csv']

        for pattern in patterns:
            try:
                df = pd.read_csv(pattern)
                datasets[pattern.replace('.csv', '')] = df
                print(f"✓ Loaded {pattern}: {df.shape}")
            except FileNotFoundError:
                continue

    except Exception as e:
        print(f"Error loading datasets: {e}")

    return datasets

# Use the function
datasets = load_datasets()
if 'dataset1' in datasets:
    result = datasets['dataset1'].head()
```

This guide provides comprehensive coverage of working with multiple datasets in the ChartBuilder. For specific use cases or advanced scenarios, refer to the pandas and SQL documentation.
