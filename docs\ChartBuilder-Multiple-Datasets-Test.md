# ChartBuilder Multiple Datasets Test Guide

## Test Setup

This document provides step-by-step tests to verify that multiple dataset functionality is working correctly in the ChartBuilder.

## Prerequisites

1. Have at least 2 datasets uploaded to your database
2. Access to the ChartBuilder component
3. Datasets should have some common columns for join testing

## Test 1: Dataset Selection UI

### Steps:
1. Open ChartBuilder
2. Create a new cell (or use existing cell)
3. Click the **Database icon** in the cell toolbar
4. Verify you can see multiple datasets in the dropdown
5. Select 2-3 datasets using checkboxes
6. Verify the button text updates to show count (e.g., "3 datasets")

### Expected Result:
- ✅ Multiple datasets can be selected
- ✅ UI shows correct count
- ✅ Selected datasets are remembered for that cell

## Test 2: SQL Multiple Dataset Query

### Steps:
1. Select 2+ datasets in a SQL cell
2. Run this test query (using standardized table names):

```sql
-- Test basic table access with standardized names
SELECT 'Dataset 1' as source, COUNT(*) as rows FROM dataset1
UNION ALL
SELECT 'Dataset 2' as source, COUNT(*) as rows FROM dataset2;
```

**Alternative using actual dataset names:**
```sql
-- If your datasets are named "employees" and "payroll"
SELECT 'Employees' as source, COUNT(*) as rows FROM employees
UNION ALL
SELECT 'Payroll' as source, COUNT(*) as rows FROM payroll;
```

3. Verify results show data from both tables

### Expected Result:
- ✅ Query executes successfully
- ✅ Results show row counts from both datasets
- ✅ No "table not found" errors
- ✅ UNION ALL combines results properly

## Test 3: SQL Join Operation

### Steps:
1. Ensure datasets have a common column (e.g., employee_id)
2. Run this join query using standardized table names:

```sql
-- Join using standardized table names
SELECT
  d1.employee_id,
  d1.name,
  d2.salary
FROM dataset1 d1
JOIN dataset2 d2 ON d1.employee_id = d2.employee_id
LIMIT 10;
```

**Alternative with actual dataset names:**
```sql
-- If your datasets are named appropriately
SELECT
  e.employee_id,
  e.name,
  p.salary
FROM employees e
JOIN payroll p ON e.employee_id = p.employee_id
LIMIT 10;
```

### Expected Result:
- ✅ Join executes successfully
- ✅ Results show combined data from both tables
- ✅ Data is properly matched on join key

## Test 4: Python Multiple Dataset Loading

### Steps:
1. Select 2+ datasets in a Python cell
2. Run this test code:

```python
# Check available datasets
show_datasets()

# Load multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

print(f"Dataset 1: {df1.shape[0]} rows, {df1.shape[1]} columns")
print(f"Dataset 2: {df2.shape[0]} rows, {df2.shape[1]} columns")

# Show column names
print(f"Dataset 1 columns: {df1.columns.tolist()}")
print(f"Dataset 2 columns: {df2.columns.tolist()}")

result = pd.concat([df1.head(2), df2.head(2)], keys=['Dataset1', 'Dataset2'])
```

### Expected Result:
- ✅ `show_datasets()` displays available files
- ✅ Both datasets load successfully
- ✅ Shape information is correct
- ✅ Result shows combined data

## Test 5: Python Dataset Merging

### Steps:
1. Use datasets with common columns
2. Run this merge test:

```python
# Load datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Find common columns
common_cols = set(df1.columns) & set(df2.columns)
print(f"Common columns: {list(common_cols)}")

# Attempt merge if common column exists
if 'employee_id' in common_cols:  # Replace with your common column
    merged = pd.merge(df1, df2, on='employee_id', how='inner')
    print(f"Merged dataset shape: {merged.shape}")
    result = merged.head()
else:
    print("No suitable common column found for merging")
    result = df1.head()
```

### Expected Result:
- ✅ Common columns are identified
- ✅ Merge operation succeeds (if common columns exist)
- ✅ Merged data is displayed correctly

## Test 6: Error Handling

### Steps:
1. Try to access a non-existent dataset:

```python
try:
    df_nonexistent = pd.read_csv('nonexistent.csv')
except FileNotFoundError as e:
    print("✓ Proper error handling for missing dataset")
    print(f"Error message: {e}")
    
# Show what's actually available
show_datasets()
```

### Expected Result:
- ✅ Clear error message for missing datasets
- ✅ `show_datasets()` provides helpful information
- ✅ Execution continues after error

## Test 7: Performance with Multiple Datasets

### Steps:
1. Select 3+ datasets
2. Run a performance test:

```python
import time

start_time = time.time()

# Load all datasets
datasets = {}
for i in range(1, 4):  # Test with 3 datasets
    try:
        df = pd.read_csv(f'dataset{i}.csv')
        datasets[f'df{i}'] = df
        print(f"Loaded dataset{i}: {df.shape}")
    except FileNotFoundError:
        print(f"Dataset{i} not available")

load_time = time.time() - start_time
print(f"Total loading time: {load_time:.2f} seconds")

# Simple analysis
total_rows = sum(df.shape[0] for df in datasets.values())
print(f"Total rows across all datasets: {total_rows}")

result = f"Loaded {len(datasets)} datasets with {total_rows} total rows in {load_time:.2f}s"
```

### Expected Result:
- ✅ Multiple datasets load within reasonable time
- ✅ Memory usage is acceptable
- ✅ Performance metrics are displayed

## Test 8: Cross-Language Consistency

### Steps:
1. Run SQL query to get row count:

```sql
SELECT COUNT(*) as sql_count FROM employee_data;
```

2. In a new Python cell with same dataset selected:

```python
df = pd.read_csv('employee_data.csv')  # Use actual dataset name
python_count = len(df)
print(f"Python count: {python_count}")
result = f"Row count: {python_count}"
```

### Expected Result:
- ✅ SQL and Python return same row count
- ✅ Data consistency across languages

## Troubleshooting Common Issues

### Issue: "Table not found" in SQL
**Solution:** Check dataset names and ensure they're converted properly:
- Spaces → underscores
- Special characters → underscores  
- Lowercase conversion
- File extensions removed

### Issue: "File not found" in Python
**Solution:** 
1. Run `show_datasets()` to see available files
2. Use exact filenames shown
3. Check dataset selection in cell

### Issue: Join fails in SQL
**Solution:**
1. Verify common columns exist
2. Check data types match
3. Use `DESCRIBE` or sample queries to inspect structure

### Issue: Memory errors with large datasets
**Solution:**
1. Use `LIMIT` in SQL queries
2. Use `df.head()` or `df.sample()` in Python
3. Process datasets in chunks

## Success Criteria

All tests should pass with:
- ✅ Multiple datasets can be selected and used
- ✅ SQL queries work with proper table names
- ✅ Python can load datasets via `pd.read_csv()`
- ✅ Joins and merges work correctly
- ✅ Error handling is informative
- ✅ Performance is acceptable
- ✅ Data consistency across languages

## Reporting Issues

If any test fails, please report:
1. Which test failed
2. Error messages received
3. Browser console errors
4. Dataset information (names, sizes)
5. Steps to reproduce

This ensures the multiple dataset functionality works reliably across all supported scenarios.
