# ChartBuilder Standard Syntax Guide

This guide explains the naming conventions and syntax used in ChartBuilder for SQL and Python code execution.

## Dataset Naming Convention

When you select datasets in ChartBuilder, they are made available using their **actual names** for better readability and AI assistance:

### SQL
- **Actual dataset names** - e.g., `employees`, `sales`, `customers` (based on your uploaded dataset names)
- **Fallback names** - `dataset1`, `dataset2`, `dataset3` (for compatibility)

### Python
- **Actual variable names** - e.g., `employees`, `sales`, `customers` (based on your uploaded dataset names)
- **Primary dataset** - `df` (always points to your first selected dataset)
- **Fallback names** - `df1`, `df2`, `df3` (for compatibility)

## SQL Examples

### Basic Query
```sql
-- Get first 5 rows from first dataset
SELECT * FROM dataset1 LIMIT 5;
```

### Join Multiple Datasets
```sql
-- Join two datasets
SELECT 
  d1.id,
  d1.name,
  d2.category,
  d2.value
FROM dataset1 d1
JOIN dataset2 d2 ON d1.id = d2.id;
```

### Union Data
```sql
-- Combine data from multiple datasets
SELECT name, value FROM dataset1
UNION ALL
SELECT name, value FROM dataset2;
```

### Aggregation Across Datasets
```sql
-- Complex analysis
SELECT 
  d1.category,
  COUNT(*) AS record_count,
  AVG(d1.value) AS avg_value,
  SUM(d2.amount) AS total_amount
FROM dataset1 d1
JOIN dataset2 d2 ON d1.id = d2.id
GROUP BY d1.category
ORDER BY total_amount DESC;
```

## Python Examples

### Method 1: Direct Variables (Recommended)
```python
# df is automatically loaded with your first selected dataset
print(f"Dataset shape: {df.shape}")
print(f"Columns: {df.columns.tolist()}")

# Display first 5 rows
result = df.head()

# Work with multiple datasets
if 'df2' in locals():
    print(f"Second dataset shape: {df2.shape}")
    # Merge datasets
    merged = pd.merge(df, df2, on='id', how='inner')
    result = merged.head()
```

### Method 2: Using pd.read_csv()
```python
# Load datasets using standard file names
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Check available datasets
show_datasets()  # Shows all available dataset files
```

### Data Analysis Examples
```python
# Basic analysis
print(f"Dataset info:")
print(f"Shape: {df.shape}")
print(f"Columns: {df.columns.tolist()}")
print(f"Data types:\n{df.dtypes}")

# Summary statistics
result = df.describe()

# Missing values
print(f"Missing values:\n{df.isnull().sum()}")
```

### Visualization Examples
```python
import matplotlib.pyplot as plt
import seaborn as sns

# Simple histogram
plt.figure(figsize=(10, 6))
plt.hist(df['column_name'], bins=20)
plt.title('Distribution of Column')
plt.xlabel('Value')
plt.ylabel('Frequency')
result = get_plot()  # This captures the plot for display

# Correlation heatmap
plt.figure(figsize=(12, 8))
correlation_matrix = df.select_dtypes(include=['number']).corr()
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('Correlation Matrix')
result = get_plot()
```

## Available Libraries

### Python
- **pandas** as pd
- **numpy** as np  
- **matplotlib.pyplot** as plt
- **seaborn** as sns
- **os**, **sys**, **io**, **json**, **datetime**, **re**

### Helper Functions
- **show_datasets()** - Shows available dataset files
- **get_plot()** - Captures matplotlib plots for display
- **describe()** - Enhanced describe function

## Best Practices

1. **Always select datasets first** using the database icon in each cell
2. **Use standard naming** (dataset1, dataset2 for SQL; df, df1, df2 for Python)
3. **Check dataset structure** before writing complex queries
4. **Use meaningful aliases** in SQL joins for readability
5. **Handle missing data** appropriately in Python analysis

## AI Assistant Compatibility

This standard syntax is designed to work seamlessly with AI coding assistants:

- **Predictable naming** makes it easy for AI to suggest correct table/variable names
- **Standard pandas/SQL syntax** ensures AI suggestions are accurate
- **Consistent patterns** help AI understand your intent quickly

## AI Assistant (Gemini-Powered)

ChartBuilder includes a built-in AI assistant powered by Google's Gemini AI that can generate code based on your selected datasets.

### How to Use
1. **Select datasets** using the database icon in your cell
2. **Click the "AI Assistant" button** in the cell controls
3. **Describe what you want to do** with your data
4. **Review and use the generated code**

### Example Prompts
- "Show the top 10 employees by salary"
- "Create a chart showing sales trends over time"
- "Find the average revenue by department"
- "Join employees and sales data to show performance"

### Benefits
- **Context-aware**: Knows your exact dataset names and structure
- **Language-specific**: Generates appropriate SQL or Python code
- **Best practices**: Follows standard syntax and conventions
- **Ready to run**: Generated code works immediately with your data

## Interactive Features

### GraphicWalker Integration
Add this comment to any SQL query to open the interactive visual explorer:
```sql
--#graphicwalker
SELECT * FROM your_dataset_name;
```

This will open a drag-and-drop interface for creating charts and visualizations.
