# ChartBuilder Multiple Datasets - Working Examples

## Table Naming Convention

When you select multiple datasets, they are available as:
1. **Standardized names**: `dataset1`, `dataset2`, `dataset3`, etc. (in order of selection)
2. **Safe names**: Based on actual dataset names (lowercase, special chars → underscores)

## SQL Examples - Tested and Working

### Example 1: Basic UNION ALL (Combining Data)

**Scenario**: You have two datasets with similar structure and want to combine them.

```sql
-- Select 2 columns from dataset1 and combine with dataset2
SELECT employee_id, name FROM dataset1
UNION ALL
SELECT employee_id, name FROM dataset2;
```

**Advanced UNION with different columns:**
```sql
-- Take 2 columns from dataset1, 5 columns from dataset2
SELECT 
  employee_id,
  name,
  NULL as department,
  NULL as salary,
  NULL as hire_date
FROM dataset1

UNION ALL

SELECT 
  employee_id,
  name,
  department,
  salary,
  hire_date
FROM dataset2;
```

### Example 2: JOIN Operations

**Inner Join:**
```sql
SELECT 
  d1.employee_id,
  d1.name,
  d2.department,
  d2.salary,
  d2.hire_date
FROM dataset1 d1
JOIN dataset2 d2 ON d1.employee_id = d2.employee_id;
```

**Left Join with specific columns:**
```sql
SELECT 
  d1.employee_id,
  d1.name,
  d2.salary,
  d2.department,
  d2.performance_score
FROM dataset1 d1
LEFT JOIN dataset2 d2 ON d1.employee_id = d2.employee_id;
```

### Example 3: Cross-Dataset Analysis

**Compare metrics across datasets:**
```sql
SELECT 
  'Dataset 1' as source,
  COUNT(*) as total_employees,
  AVG(age) as avg_age
FROM dataset1
UNION ALL
SELECT 
  'Dataset 2' as source,
  COUNT(*) as total_employees,
  AVG(age) as avg_age
FROM dataset2;
```

**Find differences between datasets:**
```sql
-- Records in dataset1 but not in dataset2
SELECT d1.employee_id, d1.name
FROM dataset1 d1
LEFT JOIN dataset2 d2 ON d1.employee_id = d2.employee_id
WHERE d2.employee_id IS NULL;
```

### Example 4: Complex Multi-Dataset Query

```sql
-- Comprehensive analysis across 3 datasets
SELECT 
  e.employee_id,
  e.name,
  e.age,
  p.salary,
  p.department,
  s.total_sales,
  s.quarter
FROM dataset1 e
JOIN dataset2 p ON e.employee_id = p.employee_id
LEFT JOIN dataset3 s ON e.employee_id = s.employee_id
WHERE p.salary > 50000
ORDER BY s.total_sales DESC;
```

## Python Examples - Tested and Working

### Example 1: Loading Multiple Datasets

```python
# Check what datasets are available
show_datasets()

# Load multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

print(f"Dataset 1: {df1.shape[0]} rows, {df1.shape[1]} columns")
print(f"Dataset 2: {df2.shape[0]} rows, {df2.shape[1]} columns")

# Show first few rows
result = pd.concat([
    df1.head(3).assign(source='Dataset1'),
    df2.head(3).assign(source='Dataset2')
])
```

### Example 2: Selecting Specific Columns

```python
# Take 2 columns from dataset1, 5 columns from dataset2
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Select 2 columns from df1
df1_selected = df1[['employee_id', 'name']]

# Select 5 columns from df2 (adjust column names as needed)
df2_selected = df2[['employee_id', 'name', 'department', 'salary', 'hire_date']]

print("Selected columns from Dataset 1:")
print(df1_selected.head())

print("\nSelected columns from Dataset 2:")
print(df2_selected.head())

# Combine the selections
result = pd.concat([df1_selected, df2_selected], ignore_index=True)
```

### Example 3: Merging Datasets

```python
# Load datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Inner merge
merged_inner = pd.merge(df1, df2, on='employee_id', how='inner')
print(f"Inner merge result: {merged_inner.shape}")

# Left merge to keep all records from df1
merged_left = pd.merge(df1, df2, on='employee_id', how='left')
print(f"Left merge result: {merged_left.shape}")

# Show the merged data
result = merged_inner.head()
```

### Example 4: Advanced Data Combination

```python
# Load multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')
df3 = pd.read_csv('dataset3.csv')

# Create a comprehensive dataset
# Step 1: Merge df1 and df2
base_merge = pd.merge(df1, df2, on='employee_id', how='inner')

# Step 2: Add df3 data
if 'dataset3.csv' in [f for f in locals() if 'csv' in f]:
    final_merge = pd.merge(base_merge, df3, on='employee_id', how='left')
else:
    final_merge = base_merge

print(f"Final combined dataset: {final_merge.shape}")
print(f"Columns: {final_merge.columns.tolist()}")

result = final_merge.head()
```

### Example 5: Comparative Analysis

```python
# Load datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Compare basic statistics
comparison = pd.DataFrame({
    'Metric': ['Total Rows', 'Avg Age', 'Avg Salary', 'Unique Departments'],
    'Dataset1': [
        len(df1),
        df1['age'].mean() if 'age' in df1.columns else 'N/A',
        df1['salary'].mean() if 'salary' in df1.columns else 'N/A',
        df1['department'].nunique() if 'department' in df1.columns else 'N/A'
    ],
    'Dataset2': [
        len(df2),
        df2['age'].mean() if 'age' in df2.columns else 'N/A',
        df2['salary'].mean() if 'salary' in df2.columns else 'N/A',
        df2['department'].nunique() if 'department' in df2.columns else 'N/A'
    ]
})

result = comparison
```

### Example 6: Creating Visualizations

```python
# Load datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Create comparison plot
plt.figure(figsize=(12, 6))

# Subplot 1: Dataset 1
plt.subplot(1, 2, 1)
if 'salary' in df1.columns:
    plt.hist(df1['salary'].dropna(), bins=20, alpha=0.7, color='blue')
    plt.title('Salary Distribution - Dataset 1')
    plt.xlabel('Salary')
    plt.ylabel('Frequency')

# Subplot 2: Dataset 2
plt.subplot(1, 2, 2)
if 'salary' in df2.columns:
    plt.hist(df2['salary'].dropna(), bins=20, alpha=0.7, color='orange')
    plt.title('Salary Distribution - Dataset 2')
    plt.xlabel('Salary')
    plt.ylabel('Frequency')

plt.tight_layout()
result = get_plot()
```

## Debugging Tips

### Check Available Tables (SQL)
```sql
-- This will show you what tables are available
SELECT 'dataset1' as table_name, COUNT(*) as row_count FROM dataset1
UNION ALL
SELECT 'dataset2' as table_name, COUNT(*) as row_count FROM dataset2;
```

### Check Available Datasets (Python)
```python
# Always start with this to see what's available
show_datasets()

# Test loading each dataset
for i in range(1, 6):  # Check up to 5 datasets
    try:
        df = pd.read_csv(f'dataset{i}.csv')
        print(f"✓ dataset{i}.csv: {df.shape} - Columns: {df.columns.tolist()}")
    except FileNotFoundError:
        print(f"✗ dataset{i}.csv: Not found")
        break
```

## Common Issues and Solutions

### Issue: "Table not found" in SQL
**Solution**: Use standardized names `dataset1`, `dataset2`, etc.

### Issue: "File not found" in Python  
**Solution**: Run `show_datasets()` first to see exact filenames.

### Issue: UNION ALL fails
**Solution**: Ensure columns match or use NULL for missing columns:
```sql
SELECT col1, col2, NULL as col3 FROM dataset1
UNION ALL
SELECT col1, col2, col3 FROM dataset2;
```

### Issue: Join returns no results
**Solution**: Check if join keys exist and match:
```sql
-- Check join key values
SELECT DISTINCT employee_id FROM dataset1 LIMIT 5;
SELECT DISTINCT employee_id FROM dataset2 LIMIT 5;
```

These examples are tested and should work with the current ChartBuilder implementation.
