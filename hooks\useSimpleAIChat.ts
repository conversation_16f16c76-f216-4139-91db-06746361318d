import { useState, useEffect } from 'react';
import { toast } from 'sonner';

export interface Dataset {
  id: string;
  name: string;
  description?: string;
  data: any[];
  headers: string[];
  fileType: string;
  createdAt: string;
}

export const useSimpleAIChat = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch datasets on mount
  useEffect(() => {
    fetchDatasets();
  }, []);

  const fetchDatasets = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/datasets');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch datasets: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && Array.isArray(data.datasets)) {
        setDatasets(data.datasets);
        console.log(`Loaded ${data.datasets.length} datasets`);
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error: any) {
      console.error('Error fetching datasets:', error);
      setError(error.message || 'Failed to fetch datasets');
      toast.error('Failed to load datasets');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDatasetSelectionChange = (datasetIds: string[]) => {
    setSelectedDatasets(datasetIds);
    
    if (datasetIds.length > 0) {
      const selectedNames = datasets
        .filter(d => datasetIds.includes(d.id))
        .map(d => d.name);
      
      console.log('Selected datasets:', selectedNames);
    }
  };

  const refreshDatasets = () => {
    fetchDatasets();
  };

  return {
    datasets,
    selectedDatasets,
    isLoading,
    error,
    handleDatasetSelectionChange,
    refreshDatasets
  };
};
