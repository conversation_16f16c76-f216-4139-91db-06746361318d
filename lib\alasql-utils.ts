// Using direct import of the alasql core
import alasql from 'alasql/dist/alasql.min.js';

// Type definitions for alasql
interface AlasqlTable {
  data: any[];
}

interface AlasqlTables {
  [tableName: string]: AlasqlTable;
}

interface Alasql {
  (sql: string, params?: any[]): any;
  tables: AlasqlTables;
}

// Cast alasql to our interface
const alasqlTyped = alasql as unknown as Alasql;

/**
 * Sets up a table with data in AlasQL
 * @param tableName The name of the table
 * @param data Array of objects to load
 */
export function setupTable(tableName: string, data: any[]): void {
  try {
    // Drop table if it exists to ensure clean state
    try {
      alasqlTyped(`DROP TABLE IF EXISTS ${tableName}`);
    } catch (e) {
      // Ignore errors if table doesn't exist
    }

    // Create the table
    alasqlTyped(`CREATE TABLE ${tableName}`);

    // Set the data directly
    alasqlTyped.tables[tableName] = { data: [...data] };

    console.log(`✓ Table '${tableName}' created with ${data.length} rows`);
  } catch (error) {
    console.error(`Error setting up table '${tableName}':`, error);
    throw error;
  }
}

/**
 * Execute a SQL query using AlasQL
 * @param query The SQL query to execute
 * @param params Optional parameters for the query
 * @returns The result of the query
 */
export function executeQuery(query: string, params?: any[]): any[] {
  try {
    console.log(`Executing SQL: ${query}`);
    const result = alasqlTyped(query, params);
    console.log(`✓ Query executed successfully, returned ${Array.isArray(result) ? result.length : 1} rows`);
    return result;
  } catch (error) {
    console.error('AlasQL error:', error);
    console.error('Query was:', query);
    throw error;
  }
}

/**
 * List all available tables in AlasQL
 * @returns Array of table names
 */
export function listTables(): string[] {
  try {
    return Object.keys(alasqlTyped.tables);
  } catch (error) {
    console.error('Error listing tables:', error);
    return [];
  }
}

/**
 * Get table info including row count and columns
 * @param tableName The name of the table
 * @returns Table information
 */
export function getTableInfo(tableName: string): { rowCount: number; columns: string[] } | null {
  try {
    const table = alasqlTyped.tables[tableName];
    if (!table || !table.data) {
      return null;
    }

    const rowCount = table.data.length;
    const columns = rowCount > 0 ? Object.keys(table.data[0]) : [];

    return { rowCount, columns };
  } catch (error) {
    console.error(`Error getting info for table '${tableName}':`, error);
    return null;
  }
}