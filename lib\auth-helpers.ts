import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import prisma from '@/lib/db'

/**
 * Helper function to get the authenticated user from the database using Clerk ID
 * Returns { user, error } where error is a NextResponse if there's an issue
 */
export async function getAuthenticatedUser(): Promise<{ user: any; error?: NextResponse }> {
  const { userId: clerkUserId } = auth()

  if (!clerkUserId) {
    return { user: null, error: NextResponse.json({ error: 'Unauthorized' }, { status: 401 }) }
  }

  // Find the user record using Clerk ID
  const user = await prisma.user.findUnique({
    where: { clerkId: clerkUserId }
  })

  if (!user) {
    return { user: null, error: NextResponse.json({ error: 'User not found' }, { status: 404 }) }
  }

  return { user }
}

/**
 * Helper function to verify workspace ownership
 * Returns the workspace if user owns it, throws error response otherwise
 */
export async function verifyWorkspaceOwnership(workspaceId: string, userId: string) {
  const workspace = await prisma.workspace.findFirst({
    where: {
      id: workspaceId,
      userId
    }
  })

  if (!workspace) {
    throw NextResponse.json({ error: 'Workspace not found' }, { status: 404 })
  }

  return workspace
}

/**
 * Helper function to verify notebook ownership through workspace
 * Returns the notebook if user owns it, throws error response otherwise
 */
export async function verifyNotebookOwnership(notebookId: string, workspaceId: string, userId: string) {
  const notebook = await prisma.notebook.findFirst({
    where: {
      id: notebookId,
      workspaceId,
      workspace: {
        userId
      }
    }
  })

  if (!notebook) {
    throw NextResponse.json({ error: 'Notebook not found' }, { status: 404 })
  }

  return notebook
}

/**
 * Helper function to verify dashboard ownership through workspace
 * Returns the dashboard if user owns it, throws error response otherwise
 */
export async function verifyDashboardOwnership(dashboardId: string, workspaceId: string, userId: string) {
  const dashboard = await prisma.dashboard.findFirst({
    where: {
      id: dashboardId,
      workspaceId,
      workspace: {
        userId
      }
    }
  })

  if (!dashboard) {
    throw NextResponse.json({ error: 'Dashboard not found' }, { status: 404 })
  }

  return dashboard
}
