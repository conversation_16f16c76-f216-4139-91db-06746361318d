// A simple store to keep track of cell results for use in the dashboard

import { SavedChart, DashboardItem, TableItem, PythonPlotItem } from '@/components/ChartBuilder/DashboardSection/types';

// Define the types of results we want to store
interface CellResultsStore {
  tables: TableItem[];
  plots: PythonPlotItem[];
  charts: SavedChart[];
}

// Initialize the store with empty arrays
const store: CellResultsStore = {
  tables: [],
  plots: [],
  charts: []
};

// Add a table result
export function addTableResult(table: TableItem): void {
  // Check if a table with the same ID already exists
  const existingIndex = store.tables.findIndex(t => t.id === table.id);

  if (existingIndex >= 0) {
    // Update existing table
    store.tables[existingIndex] = table;
  } else {
    // Add new table
    store.tables.push(table);
  }
}

// Add a plot result
export function addPlotResult(plot: PythonPlotItem): void {
  // Check if a plot with the same ID already exists
  const existingIndex = store.plots.findIndex(p => p.id === plot.id);

  if (existingIndex >= 0) {
    // Update existing plot
    store.plots[existingIndex] = plot;
  } else {
    // Add new plot
    store.plots.push(plot);
  }
}

// Add a chart result
export function addChartResult(chart: SavedChart): void {
  // Check if a chart with the same ID already exists
  const existingIndex = store.charts.findIndex(c => c.id === chart.id);

  if (existingIndex >= 0) {
    // Update existing chart
    store.charts[existingIndex] = chart;
  } else {
    // Add new chart
    store.charts.push(chart);
  }
}

// Get all tables
export function getTables(): TableItem[] {
  return [...store.tables];
}

// Get all plots
export function getPlots(): PythonPlotItem[] {
  return [...store.plots];
}

// Get all charts
export function getCharts(): SavedChart[] {
  return [...store.charts];
}

// Get all results
export function getAllResults(): CellResultsStore {
  return {
    tables: [...store.tables],
    plots: [...store.plots],
    charts: [...store.charts]
  };
}

// Clear all results
export function clearResults(): void {
  store.tables = [];
  store.plots = [];
  store.charts = [];
}

// Remove a specific result by ID and type
export function removeResult(id: string, type: 'table' | 'plot' | 'chart'): void {
  switch (type) {
    case 'table':
      store.tables = store.tables.filter(t => t.id !== id);
      break;
    case 'plot':
      store.plots = store.plots.filter(p => p.id !== id);
      break;
    case 'chart':
      store.charts = store.charts.filter(c => c.id !== id);
      break;
  }
}
