import { auth, currentUser } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { Prisma } from "@prisma/client";

const MAX_RETRIES = 3;
const DELAY_MS = 1000;

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function ensureUserInDatabase(retryCount = 0) {
  try {
    const { userId: clerkId } = auth();
    const user = await currentUser();

    if (!clerkId || !user) {
      throw new Error("Not authenticated");
    }

    // First try to find the user
    let dbUser = await prisma.user.findUnique({
      where: { clerkId }
    });

    // If user doesn't exist, create them with a transaction
    if (!dbUser) {
      try {
        dbUser = await prisma.$transaction(async (prisma) => {
          // Double-check inside transaction to avoid race conditions
          const existingUser = await prisma.user.findUnique({
            where: { clerkId }
          });

          if (existingUser) {
            return existingUser;
          }

          return await prisma.user.create({
            data: {
              clerkId,
              email: user.emailAddresses[0].emailAddress,
              name: `${user.firstName} ${user.lastName}`.trim(),
            }
          });
        });
      } catch (e) {
        if (e instanceof Prisma.PrismaClientKnownRequestError && retryCount < MAX_RETRIES) {
          // Wait before retrying
          await delay(DELAY_MS * (retryCount + 1));
          return ensureUserInDatabase(retryCount + 1);
        }
        throw e;
      }
    }

    return dbUser;
  } catch (error) {
    console.error("Error ensuring user in database:", error);
    throw new Error("Failed to ensure user in database");
  }
}