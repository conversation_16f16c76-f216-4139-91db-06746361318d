export interface PDFChunk {
  id: string;
  fileName: string;
  content: string;
  pageNumber: number;
  chunkIndex: number;
  createdAt: string;
  vectorId?: string;
}


export async function processChunks(chunks: PDFChunk[]) {
  try {
    // Validate chunks
    if (!Array.isArray(chunks)) {
      throw new Error('Invalid chunks format');
    }

    // Sort chunks by page number and chunk index
    const sortedChunks = chunks.sort((a, b) => {
      if (a.pageNumber === b.pageNumber) {
        return a.chunkIndex - b.chunkIndex;
      }
      return a.pageNumber - b.pageNumber;
    });

    // Format chunks for context
    return sortedChunks.map(chunk => `
PDF: ${chunk.fileName} (Page ${chunk.pageNumber})
Content:
${chunk.content}
`).join('\n---\n');

  } catch (error) {
    console.error('Error processing PDF chunks:', error);
    throw new Error('Failed to process PDF chunks');
  }
}

export function validatePDFChunk(chunk: PDFChunk): boolean {
  return (
    typeof chunk.fileName === 'string' &&
    typeof chunk.content === 'string' &&
    typeof chunk.pageNumber === 'number' &&
    chunk.content.trim().length > 0
  );
} 