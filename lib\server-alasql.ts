// This file is specifically for server-side usage

// Import only core functionality - avoiding the FS modules
// Direct dynamic import to avoid React Native dependency resolution during build
const alasql = require('alasql/dist/alasql.min');

/**
 * Sets up a table with data in AlasQL for server-side use
 * @param tableName The name of the table
 * @param data Array of objects to load
 */
export function setupServerTable(tableName: string, data: any[]): void {
  // Create the table if it doesn't exist
  alasql(`CREATE TABLE IF NOT EXISTS ${tableName}`);
  
  // Clear existing data
  alasql(`DELETE FROM ${tableName}`);
  
  // Set the data directly - use type casting to avoid TS errors
  (alasql as any).tables[tableName] = { data: [...data] };
}

/**
 * Execute a SQL query using AlasQL on the server
 * @param query The SQL query to execute
 * @param params Optional parameters for the query
 * @returns The result of the query
 */
export function executeServerQuery(query: string, params?: any[]): any[] {
  try {
    return alasql(query, params);
  } catch (error) {
    console.error('Server AlasQL error:', error);
    throw error;
  }
}

/**
 * Executes a calculation SQL query (no FROM clause)
 * @param query The calculation query
 * @returns The calculation result
 */
export function executeCalculation(query: string): any[] {
  const strippedQuery = query.replace(/^select\s+/i, '');
  return alasql(`SELECT ${strippedQuery}`);
} 