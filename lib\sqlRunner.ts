import alasql from 'alasql';

export class SQLRunner {
  static async executeOffline(query: string, datasets: any[]) {
    // Initialize alasql with the datasets
    datasets.forEach((dataset, index) => {
      const tableName = `dataset${index}`;
      // @ts-ignore
      alasql.tables[tableName] = dataset.data;
    });

    try {
      const result = alasql(query);
      return {
        data: result,
        output: `Query executed successfully in offline mode. Returned ${result.length} rows.`
      };
    } catch (error) {
      // @ts-ignore
      throw new Error(`SQL Error: ${error.message}`);
    }
  }
}