// This file helps with module resolution issues
const Module = require('module');
const originalRequire = Module.prototype.require;

// Override require to handle missing native modules
Module.prototype.require = function(path) {
  try {
    return originalRequire.apply(this, arguments);
  } catch (err) {
    if (
      err.code === 'MODULE_NOT_FOUND' &&
      (path === 'react-native-fs' || 
       path === 'react-native-fetch-blob' ||
       path === 'fs' ||
       path === 'child_process' ||
       path === 'crypto')
    ) {
      console.warn(`Module "${path}" not available in this environment, returning empty mock`);
      // Return a mock implementation
      return {}; 
    }
    throw err;
  }
};

module.exports = {}; // Export empty object 