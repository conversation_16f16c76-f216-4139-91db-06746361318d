// Simple test script to verify ChartBuilder functionality
// Run with: node test-chartbuilder.js

const fetch = require('node-fetch');

const FRONTEND_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:8000';

async function testHealthEndpoints() {
  console.log('🔍 Testing health endpoints...');
  
  try {
    // Test backend health
    const backendHealth = await fetch(`${BACKEND_URL}/health`);
    if (backendHealth.ok) {
      const data = await backendHealth.json();
      console.log('✅ Backend health:', data);
    } else {
      console.log('❌ Backend health check failed:', backendHealth.status);
    }
  } catch (error) {
    console.log('❌ Backend not reachable:', error.message);
  }

  try {
    // Test frontend health (basic page load)
    const frontendHealth = await fetch(`${FRONTEND_URL}/api/ping`);
    if (frontendHealth.ok) {
      console.log('✅ Frontend API reachable');
    } else {
      console.log('❌ Frontend API check failed:', frontendHealth.status);
    }
  } catch (error) {
    console.log('❌ Frontend not reachable:', error.message);
  }
}

async function testDatasetAPI() {
  console.log('\n🔍 Testing dataset API...');
  
  try {
    // Test datasets endpoint (without auth - should return 401)
    const datasetsResponse = await fetch(`${FRONTEND_URL}/api/datasets`);
    console.log('📊 Datasets API status:', datasetsResponse.status);
    
    if (datasetsResponse.status === 401) {
      console.log('✅ Authentication required (expected)');
    } else if (datasetsResponse.status === 200) {
      const data = await datasetsResponse.json();
      console.log('✅ Datasets API response:', data);
    } else {
      console.log('❌ Unexpected status:', datasetsResponse.status);
    }
  } catch (error) {
    console.log('❌ Dataset API test failed:', error.message);
  }
}

async function testPythonExecution() {
  console.log('\n🔍 Testing Python execution...');
  
  try {
    // Test Python execution endpoint (without auth - should return 401)
    const pythonResponse = await fetch(`${FRONTEND_URL}/api/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: 'print("Hello from Python!")',
        language: 'python',
        datasets: []
      })
    });
    
    console.log('🐍 Python execution API status:', pythonResponse.status);
    
    if (pythonResponse.status === 401) {
      console.log('✅ Authentication required (expected)');
    } else if (pythonResponse.status === 200) {
      const data = await pythonResponse.json();
      console.log('✅ Python execution response:', data);
    } else {
      console.log('❌ Unexpected status:', pythonResponse.status);
    }
  } catch (error) {
    console.log('❌ Python execution test failed:', error.message);
  }
}

async function testBackendDirectly() {
  console.log('\n🔍 Testing backend directly...');
  
  try {
    // Test backend Python execution directly
    const backendResponse = await fetch(`${BACKEND_URL}/api/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'test-token'
      },
      body: JSON.stringify({
        query: 'print("Hello from backend!")\nresult = "Backend is working"',
        language: 'python',
        datasets: []
      })
    });
    
    console.log('🔧 Backend execution status:', backendResponse.status);
    
    if (backendResponse.ok) {
      const data = await backendResponse.json();
      console.log('✅ Backend execution response:', data);
    } else {
      const errorText = await backendResponse.text();
      console.log('❌ Backend execution failed:', errorText);
    }
  } catch (error) {
    console.log('❌ Backend direct test failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting ChartBuilder functionality tests...\n');
  
  await testHealthEndpoints();
  await testDatasetAPI();
  await testPythonExecution();
  await testBackendDirectly();
  
  console.log('\n📋 Test Summary:');
  console.log('- Health endpoints: Check if both frontend and backend are running');
  console.log('- Dataset API: Should require authentication (401 expected)');
  console.log('- Python execution: Should require authentication (401 expected)');
  console.log('- Backend direct: Should work with test token');
  console.log('\n💡 To test with real data:');
  console.log('1. Start the development server: npm run dev');
  console.log('2. Start the Python backend: cd backend && python main.py');
  console.log('3. Sign in to the application');
  console.log('4. Upload some datasets');
  console.log('5. Try the ChartBuilder with SQL/Python code');
  console.log('\n📚 Standard syntax examples:');
  console.log('SQL: SELECT * FROM dataset1 LIMIT 5;');
  console.log('Python: print(df.head()) # df is your first dataset');
}

// Run the tests
runTests().catch(console.error);
