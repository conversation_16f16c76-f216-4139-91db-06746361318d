{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "downlevelIteration": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "typeRoots": ["./node_modules/@types", "./types"], "allowSyntheticDefaultImports": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}