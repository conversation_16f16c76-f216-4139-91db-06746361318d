import { Attendance as PrismaAttendance, Employee as PrismaEmployee } from '@prisma/client';

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER'
}

export enum Department {
  HR = 'HR',
  IT = 'IT',
  FINANCE = 'FINANCE',
  MARKETING = 'MARKETING',
  OPERATIONS = 'OPERATIONS'
}

export enum EmploymentType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT'
}

export type Attendance = PrismaAttendance;
// @ts-ignore
export interface Employee extends Omit<PrismaEmployee, 'dateNaissance' | 'dateDebut' | 'genre' | 'departement' | 'typeEmploi' | 'dateExpirationPermis' | 'dateFinContrat'> {
  dateNaissance: Date | string;
  dateDebut: Date | string;
  genre: Gender;
  departement: Department;
  typeEmploi: EmploymentType;
  dateExpirationPermis?: Date | string | null;
  dateFinContrat?: Date | string | null;
  salaire: number;
  matricule?: string; // This field is not in the DB schema
  nationalite?: string;
  numeroPasseport?: string;
  permisTravaill?: string;
  situationFamiliale?: 'celibataire' | 'marie' | 'divorce' | 'veuf';
  nombreEnfants?: number;
  niveauEducation?: string;
  diplomes?: string[];
  languesParlees?: string[];
  formationsContinues?: string[];
  salaireBrut?: number;
  salaireNet?: number;
  tauxIR?: number;
  tauxCNSS?: number;
  tauxAMO?: number;
  banque?: string;
  agenceBancaire?: string;
  periodeEssai?: number;
  congesPayes?: number;
  congesMaladie?: number;
  congesMaternite?: number;
  contratType?: string;
  estLocataire?: boolean;
  possedeAppartement?: boolean;
  utiliseTransport?: boolean;
  typeTransport?: string;
  distanceDomicileTravail?: number;
  numeroCIMR?: string;
  groupeSanguin?: string;
  zoneResidence?: string;
  modePaiement?: string;
  echelonSalaire?: string;
  modeTravaill?: 'remote' | 'hybrid' | 'office';
}

export interface PayrollItem {
  id: string;
  label: string;
  value: number;
  formula: string;
  category: 'earning' | 'deduction' | 'calculated';
  description?: string;
  taux?: number;
  base?: number;
  nombre?: number;
}

export interface PaySlipData {
  employee: Employee;
  payrollItems: PayrollItem[];
  totals: {
    totalGains: number;
    totalDeductions: number;
    netAPayer: number;
  };
  periodStart: Date;
  periodEnd: Date;
  baseSalary: number;
  overtime: number;
  transportation: number;
  familyAllowance: number;
  otherAllowances: number;
  grossSalary: number;
  cnss: number;
  cimr: number;
  mutuelle: number;
  ir: number;
  irPercentage: number;
  salaryAdvance: number;
  netSalary: number;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  isFolder: boolean;
  parentId: string | null;
  createdAt: Date;
  updatedAt: Date;
  children?: Note[];
}
