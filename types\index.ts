// this is for note publish
export interface Note {
    id: string
    title: string
    content: string
    coverImage?: string | null
    isFolder: boolean
    isPublished?: boolean
    publishedAt?: Date | null
    publicId?: string | null
    userId: string
    parentId?: string | null
    createdAt: Date
    updatedAt: Date
    author?: {
      name: string
    }
    isSelected?: boolean;
  }

export type AIModel = 'cohere' | 'gemini' | 'together-mixtral' | 'together-llama';

export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  tokenUsage?: {
    totalTokens: number;
    promptTokens?: number;
    completionTokens?: number;
  };
}


export interface ChatContext {
  selectedNotes: Note[];
  userMessage: string;
}

export interface DialogFooter {
  className?: string;
  children: React.ReactNode;
}

export interface Dataset {
  id: string;
  name: string;
  data: any[];
  columns?: Array<{ name: string; type: string }>;
  headers?: string[];
  fileType: string;
  createdAt: Date;
  description?: string;
  vectorId?: string;
  embedding?: boolean;
  embeddingModel?: string;
  folderId?: string;
}

// Import PDFChunk from pdf.ts instead of redefining it here
import { PDFChunk } from './pdf';
export type { PDFChunk };

export interface ChatMessage {
  id: string;
  content: string;
  fileUrl?: string;
  fileName?: string;
  fileType?: string;
  fileKey?: string;
  user: string;
  userId: string;
  channelId: string;
  avatar: string;
  timestamp: string;
  reactions: Array<{
    emoji: string;
    count: number;
    users: string[];
  }>;
  replyTo?: {
    id: string;
    content: string;
    user: string;
  };
  readBy?: string[];
}

