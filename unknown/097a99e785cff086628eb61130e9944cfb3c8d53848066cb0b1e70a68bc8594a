"use client";

import { useState, useMemo, useEffect } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Dialog } from "../ui/dialog";
import { TableCell } from './TableComponents/TableCell';
import { TableControls } from './TableComponents/TableControls';
import { TableDialogs } from './TableComponents/TableDialogs';
import { TablePagination } from './TableComponents/TablePagination';
import { ColumnBarChart } from './TableComponents/ColumnBarChart';
import { format, parseISO } from "date-fns";

interface TableData {
  [key: string]: string | number | boolean;
}

interface EditHistoryItem {
  type: 'edit' | 'deleteRow' | 'deleteColumn';
  row: number | null;
  column: string | null;
  oldValue: any | null;
  newValue: any | null;
  timestamp: Date;
}

interface Version {
  id: string;
  versionNumber: number;
  changes: EditHistoryItem[];
  createdAt: Date;
  user: {
    name: string;
  };
}

interface TableTabProps {
  data: TableData[];
  headers: string[];
  datasetId?: string;
  onMaximize?: () => void;
  onSave?: (updatedData: TableData[]) => void;
}

const formatDate = (dateValue: any) => {
  try {
    if (!dateValue) return 'N/A';
    if (typeof dateValue === 'string') {
      return format(parseISO(dateValue), 'MMM d, yyyy HH:mm');
    }
    if (typeof dateValue === 'number') {
      return format(new Date(dateValue), 'MMM d, yyyy HH:mm');
    }
    if (dateValue instanceof Date) {
      return format(dateValue, 'MMM d, yyyy HH:mm');
    }
    return 'Invalid date';
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

export function TableTab<TData extends TableData>({ data: initialData, headers, datasetId, onSave }: TableTabProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState("");
  const [rowSelection, setRowSelection] = useState({});
  const [editHistory, setEditHistory] = useState<Array<EditHistoryItem>>([]);
  const [undoHistory, setUndoHistory] = useState<Array<EditHistoryItem>>([]);
  const [redoHistory, setRedoHistory] = useState<Array<EditHistoryItem>>([]);
  const [data, setData] = useState(initialData);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showChangesPreview, setShowChangesPreview] = useState(false);
  const [showAddColumnDialog, setShowAddColumnDialog] = useState(false);
  const [newColumnName, setNewColumnName] = useState("");
  const [versions, setVersions] = useState<Version[]>([]);
  const [showVersionHistory, setShowVersionHistory] = useState(false);

  useEffect(() => {
    fetchVersionHistory();
  }, [datasetId]);

  const fetchVersionHistory = async () => {
    if (!datasetId) return;
    try {
      const response = await fetch(`/api/datasets?datasetId=${datasetId}`);
      const result = await response.json();
      if (result.success) {
        setVersions(result.versions || []);
      } else {
        setVersions([]);
      }
    } catch (error) {
      console.error('Error fetching version history:', error);
      setVersions([]);
    }
  };

  const handleAddRow = () => {
    const newRow: TableData = {};
    headers.forEach(header => {
      newRow[header] = "";
    });
    setData([...data, newRow]);
    toast.success("New row added");
  };

  const handleAddColumn = () => {
    if (!newColumnName) return;
    const updatedHeaders = [...headers, newColumnName];
    const updatedData = data.map(row => ({
      ...row,
      [newColumnName]: ""
    }));
    setColumnVisibility(prev => ({
      ...prev,
      [newColumnName]: true
    }));
    setData(updatedData);
    headers.push(newColumnName);
    setShowAddColumnDialog(false);
    setNewColumnName("");
    toast.success("New column added");
  };

  const handleSaveChanges = async () => {
    if (!datasetId) {
      toast.error('Dataset ID is required');
      return;
    }
    if (editHistory.length === 0) {
      toast.error('No changes to save');
      return;
    }
    try {
      const savePromise = fetch('/api/datasets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          datasetId,
          changes: editHistory,
          newData: data,
          headers,
        })
      }).then(async (response) => {
        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error || 'Failed to save changes');
        }
        return result;
      });
      await toast.promise(savePromise, {
        loading: 'Saving changes...',
        success: () => {
          setEditHistory([]);
          fetchVersionHistory();
          return 'Changes saved successfully';
        },
        error: 'Failed to save changes'
      });
      localStorage.setItem(`dataset-${datasetId}`, JSON.stringify({
        data,
        headers,
        lastSaved: new Date().toISOString()
      }));
      if (onSave) {
        onSave(data);
      }
    } catch (error) {
      console.error('Error saving changes:', error);
    }
  };

  const handleUndo = () => {
    if (editHistory.length === 0) return;

    const lastEdit = editHistory[editHistory.length - 1];
    setEditHistory(prev => prev.slice(0, -1));
    setRedoHistory(prev => [...prev, lastEdit]);

    // Revert the change
    if (lastEdit.type === 'edit' && lastEdit.row !== null && lastEdit.column) {
      const updatedData = [...data];
      updatedData[lastEdit.row] = {
        ...updatedData[lastEdit.row],
        [lastEdit.column]: lastEdit.oldValue
      };
      setData(updatedData);
      toast.success('Change undone');
    }
  };

  const handleRedo = () => {
    if (redoHistory.length === 0) return;

    const lastRedo = redoHistory[redoHistory.length - 1];
    setRedoHistory(prev => prev.slice(0, -1));
    setEditHistory(prev => [...prev, lastRedo]);

    // Reapply the change
    if (lastRedo.type === 'edit' && lastRedo.row !== null && lastRedo.column) {
      const updatedData = [...data];
      updatedData[lastRedo.row] = {
        ...updatedData[lastRedo.row],
        [lastRedo.column]: lastRedo.newValue
      };
      setData(updatedData);
      toast.success('Change redone');
    }
  };

  const handleCellEdit = (rowIndex: number, column: string, oldValue: any, newValue: any) => {
    const change: EditHistoryItem = {
      type: 'edit',
      row: rowIndex,
      column,
      oldValue,
      newValue,
      timestamp: new Date()
    };

    setEditHistory(prev => [...prev, change]);
    setRedoHistory([]); // Clear redo history on new edit

    const updatedData = [...data];
    updatedData[rowIndex] = {
      ...updatedData[rowIndex],
      [column]: newValue
    };

    setData(updatedData);
    toast.success('Value updated', { duration: 2000 });
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
        if (e.shiftKey) {
          handleRedo();
        } else {
          handleUndo();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [editHistory, redoHistory]);

  const renderChangeContent = (change: EditHistoryItem) => {
    switch (change.type) {
      case 'deleteRow':
        return change.row !== null ? (
          <div className="text-red-500">
            Deleted row {change.row + 1}
          </div>
        ) : null;
      case 'deleteColumn':
        return change.column ? (
          <div className="text-red-500">
            Deleted column "{change.column}"
          </div>
        ) : null;
      case 'edit':
        return change.row !== null && change.column ? (
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">
              {`Row ${change.row + 1}, ${change.column}:`}
            </span>
            <span className="line-through text-red-500">
              {change.oldValue?.toString() || 'empty'}
            </span>
            <span className="text-muted-foreground">→</span>
            <span className="text-green-500">
              {change.newValue?.toString() || 'empty'}
            </span>
          </div>
        ) : null;
      default:
        return null;
    }
  };

  const table = useReactTable<TData>({
    data: data as TData[],
    columns: useMemo(() => {
      return headers.map((header) => ({
        id: String(header), // Ensure header is a string and used as ID
        accessorKey: String(header), // Ensure accessor key is a string
        header: ({ column }) => (
          <div className="flex flex-col items-center gap-1">
            <div className="w-full px-2 pt-1">
              <ColumnBarChart
                data={data}
                accessor={String(header)} // Ensure accessor is a string
                width={90}
                height={20}
              />
            </div>
            <div className="flex items-center gap-2 w-full">
              <button
                className="-ml-2 h-6 text-xs data-[state=sorted]:text-primary truncate max-w-[250px]"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                title={String(header)}
              >
                {String(header)}
              </button>
            </div>
          </div>
        ),
        cell: ({ row, column }) => {
          const value = row.getValue(String(header));
          return (
            <TableCell
              row={row}
              column={column}
              value={value}
              onEdit={(newValue) => handleCellEdit(row.index, String(header), value, newValue)}
              onDeleteRow={(index) => {
                const updatedData = [...data];
                updatedData.splice(index, 1);
                setData(updatedData);
                toast.success('Row deleted');
              }}
              onDuplicateRow={(index) => {
                const newRow = { ...row.original };
                const updatedData = [...data];
                updatedData.splice(index + 1, 0, newRow);
                setData(updatedData);
                toast.success('Row duplicated');
              }}
            />
          );
        },
      }));
    }, [headers, data]),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  const renderTableContent = () => (
    <div className="relative w-full h-full">
      <div
        className={cn(
          "overflow-auto",
          "scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent",
          "transition-colors duration-200",
          "h-full"
        )}
        style={{
          width: '100%',
          height: 'calc(100vh - 8rem)'
        }}
      >
        <table className="w-full border-collapse table-auto text-xs" style={{ tableLayout: 'auto' }}>
          <thead className="sticky top-0 z-30">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className={cn(
                      "px-2 text-left align-middle font-medium text-xs whitespace-nowrap",
                      "bg-muted dark:bg-muted text-muted-foreground dark:text-muted-foreground",
                      "border-b border-r border-border/50 last:border-r-0",
                      "hover:bg-accent dark:hover:bg-accent transition-colors",
                      "group select-none"
                    )}
                    style={{ minWidth: '100px', maxWidth: '300px', height: 'auto', paddingTop: '0', paddingBottom: '4px' }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row, i) => (
              <tr
                key={row.id}
                className={cn(
                  "relative",
                  i % 2 === 0 ? "bg-background" : "bg-muted/5",
                  "hover:bg-muted/10 transition-colors"
                )}
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className={cn(
                      "p-0 border-b border-r border-border/50 last:border-r-0 h-7 whitespace-nowrap",
                      "group-hover:bg-muted/10"
                    )}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full min-h-[calc(100vh-2rem)] w-full">
      <div className="shadow-sm bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
        <TableControls
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          handleAddRow={handleAddRow}
          handleAddColumn={handleAddColumn}
          setShowVersionHistory={setShowVersionHistory}
          versions={versions}
          editHistory={editHistory}
          setShowChangesPreview={setShowChangesPreview}
          handleSaveChanges={handleSaveChanges}
          rowOperation={{ type: '', loading: false }}
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={editHistory.length > 0}
          canRedo={redoHistory.length > 0}
        />
      </div>

      <div className="rounded-md w-full bg-background shadow-sm flex-grow overflow-hidden">
        {renderTableContent()}
      </div>

      <div className="border-t shadow-sm bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
        <TablePagination table={table} />
      </div>

      <TableDialogs
        showAddColumnDialog={showAddColumnDialog}
        setShowAddColumnDialog={setShowAddColumnDialog}
        newColumnName={newColumnName}
        setNewColumnName={setNewColumnName}
        handleAddColumn={handleAddColumn}
        showVersionHistory={showVersionHistory}
        setShowVersionHistory={setShowVersionHistory}
        versions={versions}
        formatDate={formatDate}
        renderChangeContent={renderChangeContent}
        showChangesPreview={showChangesPreview}
        setShowChangesPreview={setShowChangesPreview}
        editHistory={editHistory}
      />
    </div>
  );
}
