'use server';

import { auth } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { nanoid } from "nanoid";

// Type for metadata object
interface NoteMetadata {
  collaborationId?: string;
  collaborationStartedAt?: string;
  collaborators?: string[];
  mergedFromPublicId?: string;
  mergedAt?: string;
  [key: string]: any; // Allow other properties
}

// Function to generate a unique collaboration ID for a note
export async function generateCollaborationLink(noteId: string): Promise<{ 
  success: boolean; 
  error?: string; 
  collaborationId?: string;
  collaborationLink?: string;
}> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    // Find the note
    const note = await prisma.note.findUnique({
      where: { id: noteId, userId: dbUser.id },
    });

    if (!note) {
      return { success: false, error: "Note not found" };
    }

    // Check if metadata exists and has a collaboration ID
    let collaborationId: string;
    let metadata: any = {};
    
    if (note.metadata) {
      try {
        metadata = JSON.parse(note.metadata as string);
        if (metadata.collaborationId) {
          collaborationId = metadata.collaborationId;
        } else {
          collaborationId = `collab-${nanoid(8)}`;
          metadata.collaborationId = collaborationId;
          metadata.collaborationStartedAt = new Date().toISOString();
        }
      } catch (e) {
        collaborationId = `collab-${nanoid(8)}`;
        metadata = {
          collaborationId,
          collaborationStartedAt: new Date().toISOString()
        };
      }
    } else {
      collaborationId = `collab-${nanoid(8)}`;
      metadata = {
        collaborationId,
        collaborationStartedAt: new Date().toISOString()
      };
    }

    // Update the note with collaboration metadata
    await prisma.note.update({
      where: { id: noteId },
      data: {
        metadata: JSON.stringify(metadata),
      },
    });
    
    // Generate a collaboration link
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const collaborationLink = `${baseUrl}/hr/workspace/note/${noteId}?collaborate=${collaborationId}`;
    
    return { 
      success: true, 
      collaborationId, 
      collaborationLink 
    };
  } catch (error) {
    console.error("Error generating collaboration link:", error);
    return { success: false, error: "Failed to generate collaboration link" };
  }
}

// Function to share a note with another user
export async function shareNote(noteId: string, email: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId: clerkUserId } = auth();

    if (!clerkUserId) {
      return { success: false, error: "Authentication required" };
    }

    const dbUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      return { success: false, error: "User not found in database" };
    }

    // Find the note
    const note = await prisma.note.findUnique({
      where: { id: noteId, userId: dbUser.id },
    });

    if (!note) {
      return { success: false, error: "Note not found" };
    }

    // Find the user to share with
    const userToShare = await prisma.user.findUnique({
      where: { email },
      select: { clerkId: true }
    });

    if (!userToShare) {
      return { success: false, error: "User not found" };
    }

    // Parse the metadata
    let metadata: NoteMetadata = {};
    if (note.metadata) {
      try {
        metadata = JSON.parse(note.metadata as string) as NoteMetadata;
      } catch (e) {
        console.error("Failed to parse metadata", e);
        metadata = {};
      }
    }

    // Initialize or update collaborators list
    if (!metadata.collaborators) {
      metadata.collaborators = [];
    }

    // Add the user to collaborators if not already present
    if (!metadata.collaborators.includes(userToShare.clerkId)) {
      metadata.collaborators.push(userToShare.clerkId);
    }

    // Save the updated metadata
    await prisma.note.update({
      where: { id: noteId },
      data: {
        metadata: JSON.stringify(metadata),
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error sharing note:", error);
    return { success: false, error: "Failed to share note" };
  }
} 