'use client'

import { useState, useRef, useEffect } from 'react'
import { ChartVisualizer } from './ChartVisualizer'
import { X, Maximize2, MinusCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ExpandableChartProps {
  isOpen: boolean
  onClose: () => void
  data: any[]
  chartType: 'line' | 'bar' | 'pie' | 'area'
  config: any
  title: string
}

export function ExpandableChart({
  isOpen,
  onClose,
  data,
  chartType,
  config,
  title
}: ExpandableChartProps) {
  const [isMaximized, setIsMaximized] = useState(false)
  const modalRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose()
    }
    window.addEventListener('keydown', handleEsc)
    return () => window.removeEventListener('keydown', handleEsc)
  }, [onClose])
  
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }
    return () => { document.body.style.overflow = 'auto' }
  }, [isOpen])
  
  if (!isOpen) return null
  
  return (
    <div 
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
      onClick={onClose}
    >
      <div 
        ref={modalRef}
        className={`bg-background rounded-lg shadow-xl overflow-hidden relative transition-all duration-300 ease-in-out ${
          isMaximized ? 'w-[95vw] h-[95vh]' : 'w-[80vw] h-[80vh]'
        }`}
        onClick={e => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="font-medium text-lg">{title}</h3>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMaximized(!isMaximized)}
            >
              {isMaximized ? <MinusCircle className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="p-4 h-[calc(100%-60px)]">
          <ChartVisualizer 
            data={data} 
            initialChartType={chartType}
            chartConfig={{
              ...config,
              showConfig: false,
              fontSize: 'normal',
              title: title,
              // Ensure chart fills the modal
              height: "100%",
              width: "100%",
              preserveAspectRatio: true
            }}
            showConfig={false}
            fullHeight={true}
          />
        </div>
      </div>
    </div>
  )
} 