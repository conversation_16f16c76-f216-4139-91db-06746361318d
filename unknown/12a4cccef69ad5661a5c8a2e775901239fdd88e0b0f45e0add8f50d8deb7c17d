"use client"

import * as React from "react"
import Link from "next/link"
import {
  <PERSON>older,
  Forward,
  MoreHorizontal,
  Trash2,
  type LucideIcon,
} from "lucide-react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { Separator } from "../ui/separator"

interface Project {
  name: string
  url: string
  icon: LucideIcon | React.ComponentType<any>
  badge?: number
  onClick?: () => void
  dropdown?: React.ReactNode
}

export function NavProjects({ projects }: { projects: Project[] }) {
  const { isMobile } = useSidebar()
  
  return (
    <>
      <Separator />
      <SidebarGroupLabel>Projects</SidebarGroupLabel>
      <SidebarMenu>
        {projects.map((project) => (
          <SidebarMenuItem key={project.name}>
            {project.dropdown ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="sm"
                    className="relative data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <span className="relative">
                      <project.icon className="size-4 shrink-0" />
                      {project.badge && (
                        <span className="absolute -top-2 -right-2 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
                          {project.badge}
                        </span>
                      )}
                    </span>
                    <span>{project.name}</span>
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-48 rounded-lg"
                  side={isMobile ? "bottom" : "right"}
                  align={isMobile ? "end" : "start"}
                >
                  <DropdownMenuLabel>Recent Messages</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    {project.dropdown}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link href={project.url} onClick={project.onClick}>
                <SidebarMenuButton size="sm">
                  <project.icon className="size-4 shrink-0" />
                  <span>{project.name}</span>
                </SidebarMenuButton>
              </Link>
            )}
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </>
  )
}
