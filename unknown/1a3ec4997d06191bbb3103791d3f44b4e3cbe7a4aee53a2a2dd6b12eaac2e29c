import { getEmployeeById } from '@/actions/actions';
import { notFound, redirect } from 'next/navigation';
import EmployeeProfileClient from '@/components/Empoyees/EmployeeProfileClient';
import { Employee } from '@prisma/client';
import { auth } from '@clerk/nextjs/server';

export default async function EmployeeProfilePage({ params }: { params: { id: string } }) {
  const employee = await getEmployeeById(params.id);
  const { userId } = auth();

  if (!employee) {
    notFound();
  }

  if (!userId) {
      redirect('/sign-in');
  }
  
  
  return <EmployeeProfileClient employee={employee as Employee} />;
}
