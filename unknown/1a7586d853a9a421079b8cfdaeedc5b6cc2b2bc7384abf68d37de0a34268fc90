import { Metadata } from "next";
import PdfExtractor from "@/components/PdfExtractor";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "RAG Book",
  description: "Upload and analyze PDF documents using RAG.",
};

export default function RagBookPage() {
  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>PDF Document Analysis</CardTitle>
          <CardDescription>
            Upload a PDF document to extract and analyze its contents using RAG (Retrieval Augmented Generation).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PdfExtractor />
        </CardContent>
      </Card>
    </div>
  );
}
