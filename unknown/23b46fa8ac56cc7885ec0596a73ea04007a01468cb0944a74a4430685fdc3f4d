"use server"

import { pusherServer } from "@/lib/pusher";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const { userId } = auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const data = await req.text();
    const [socketId, channelName] = data.split(":");

    const authResponse = pusherServer.authorizeChannel(socketId, channelName, {
      user_id: userId,
    });

    return NextResponse.json(authResponse);
  } catch (error) {
    console.error("Pusher Auth Error:", error);
    return new NextResponse("Error", { status: 500 });
  }
} 