'use client';

import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { cn } from '@/lib/utils';


interface MermaidProps {
  chart: string;
}

const Mermaid: React.FC<MermaidProps> = ({ chart }) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const renderChart = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Initialize mermaid with specific config
        mermaid.initialize({
          startOnLoad: true,
          theme: 'default',
          securityLevel: 'loose',
          themeVariables: {
            fontFamily: 'inherit',
          },
          logLevel: 'error',
        });

        if (elementRef.current) {
          // Clear previous content
          elementRef.current.innerHTML = '';
          
          // Generate unique ID for this chart
          const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`;
          
          // Render new chart
          const { svg } = await mermaid.render(id, chart);
          if (elementRef.current) {
            elementRef.current.innerHTML = svg;
          }
        }
      } catch (err) {
        console.error('Mermaid rendering error:', err);
        setError('Failed to render diagram. Please check the syntax.');
      } finally {
        setIsLoading(false);
      }
    };

    renderChart();
  }, [chart]);

  if (error) {
    return (
      <div className="my-4 p-4 rounded-md bg-destructive/10 text-destructive">
        {error}
      </div>
    );
  }

  return (
    <div className="my-4 overflow-x-auto">
      {isLoading && (
        <div className="flex justify-center items-center h-32 bg-muted/20 rounded-md animate-pulse">
          <div className="space-y-2">
            <div className="h-2 w-48 bg-muted rounded"></div>
            <div className="h-2 w-40 bg-muted rounded"></div>
            <div className="h-2 w-44 bg-muted rounded"></div>
          </div>
        </div>
      )}
      <div 
        ref={elementRef} 
        className={cn(
          "flex justify-center",
          isLoading && "hidden"
        )} 
      />
    </div>
  );
};

export default Mermaid;
