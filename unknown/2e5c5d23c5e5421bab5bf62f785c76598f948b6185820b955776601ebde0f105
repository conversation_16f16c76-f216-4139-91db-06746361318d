import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { Toaster } from 'sonner';
import AuthProvider from '@/components/AuthProvider';
import { Footer, Navbar } from "@/components/landing";
import "@/styles/blocknote.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Loopflow",
  description: "Empowering businesses to communicate effortlessly with their data",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={inter.className}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
          >
            <AuthProvider />
            <Toaster position="bottom-right" />
            <div id="home" className="absolute inset-0 h-full mt-[63px" />
              <main className="mx-auto w-full z-40 relative">
                  {children}
              </main>
          </ThemeProvider>
        </body>
      </html>    
    </ClerkProvider>
  );
}
