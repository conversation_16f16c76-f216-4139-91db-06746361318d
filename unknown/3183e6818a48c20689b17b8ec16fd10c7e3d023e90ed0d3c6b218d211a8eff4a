"use client"

import { useEffect, useState, RefObject } from "react"

interface ResizeObserverEntry {
  contentRect: DOMRectReadOnly
}

interface ObserverRect {
  width: number
  height: number
}

export function useResizeObserver(ref: RefObject<HTMLElement>): ObserverRect {
  const [dimensions, setDimensions] = useState<ObserverRect>({
    width: 800,
    height: 400
  })

  useEffect(() => {
    const observeTarget = ref.current
    if (!observeTarget) return

    const resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
      entries.forEach((entry) => {
        setDimensions({
          width: entry.contentRect.width,
          height: entry.contentRect.height
        })
      })
    })

    resizeObserver.observe(observeTarget)

    return () => {
      resizeObserver.unobserve(observeTarget)
    }
  }, [ref])

  return dimensions
} 