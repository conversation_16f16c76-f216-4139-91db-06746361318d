import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { currentUser } from '@clerk/nextjs/server';
import { Hand, Plus } from "lucide-react";
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

const ClientDashboard = dynamic(() => import('@/components/EmployeeDashboard/ClientDashboard'), { ssr: false });

export default async function Dashboard() {
  const { userId } = auth();

  if (!userId) {
    redirect('/sign-in');
  }

  const user = await currentUser();

  return (
    <div className="flex-1 flex flex-col">
      <div className="w-full max-w-7xl mx-auto px-6 py-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-4xl font-bold">
            Welcome, <span className="text-blue-500">{user?.firstName} <Hand size={24} className="inline-block" /></span>
          </h1>
          <Link href="/hr/Invoice">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Employee
            </Button>
          </Link>
        </div>
      </div>

      <div className="">
        <Suspense fallback={
          <div className="w-full h-[400px] flex items-center justify-center">
            <div className="animate-pulse text-muted-foreground">
              Loading dashboard...
            </div>
          </div>
        }>
          <ClientDashboard />
        </Suspense>
      </div>
    </div>
  );
}
