'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeProps,
  Handle,
  Position,
  EdgeTypes,
  getBezierPath,
  getSmoothStepPath,
  getMarkerEnd,
  MarkerType,
  Panel,
  useReactFlow,
  ReactFlowProvider,
} from 'reactflow'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Database, Globe, Square, Circle, Triangle, Diamond, Trash2, Download, Settings2, Image as ImageIcon, Monitor, Copy, User, Building2, Mail, Users } from 'lucide-react'
import 'reactflow/dist/style.css'
import { toPng } from 'html-to-image'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useTheme } from "next-themes"
import { ColorPicker } from "@/components/ui/color-picker"
import { toast } from "sonner"
import { fetchMetadata } from "@/utils/fetchMetadata"
import { IconType } from 'react-icons'
import { FaBeer, FaCoffee, FaApple } from 'react-icons/fa' // Example icons
import { 
  SiReact, SiMongodb, SiAmazon, SiMysql, SiPostgresql, 
  SiNestjs, SiNextdotjs, SiVuedotjs, SiAngular, 
  SiNodedotjs, SiExpress, SiPython, SiJavascript, SiTypescript,
  SiHtml5, SiCss3, SiTailwindcss, SiBootstrap,
  SiDocker, SiKubernetes, SiJenkins, SiGit,
  SiAmazons3, SiAmazonec2, SiAwslambda, SiAmazonrds,
  SiRedis, SiElasticsearch, SiGraphql, SiApollographql,
  SiFirebase, SiSupabase, SiVercel, SiNetlify,
  SiLinux, SiUbuntu, SiDebian, SiCentos,
  SiNginx, SiApache, SiPhp,
  SiSwift, SiKotlin, SiFlutter, SiDart,
  SiAndroidstudio, SiXcode, SiVisualstudiocode, SiWebstorm, SiGooglecloud, SiDigitalocean, SiHeroku,
   SiSwagger, SiGrafana, SiKibana,
   SiChakraui, SiRadixui, SiStorybook,
  SiVite, SiWebpack,
  SiPrisma, SiSequelize,
  SiCypress, SiPlaywright,
  SiStripe, SiAuth0, SiPassport,
  SiRedux,
  SiLaravel, SiDjango, SiRubyonrails, SiSpring,
  SiGo, SiRust, SiCplusplus, SiCsharp,
   SiRabbitmq, SiApachepulsar,
  SiTerraform, SiAnsible, SiPacker,
  SiGithubactions, SiGitlab, SiCircleci, SiBitbucket,
  SiSolidity, SiWeb3Dotjs, SiIpfs,
  SiMaterialdesign,
  SiMongoose,
  SiTypeform,
  SiAzuredevops,
  SiVitess,
  SiPostman,
  SiVitest,
  SiJest,
  SiRecoil,
  SiZulip,
  SiJirasoftware,
  SiMqtt,
  SiHyper, 
} from 'react-icons/si'
import { FaServer, FaDatabase, FaCloud, FaCode } from 'react-icons/fa'
import { ResizablePanel, ResizablePanelGroup, ResizableHandle } from "@/components/ui/resizable"
import { ChevronRight, ChevronLeft } from "lucide-react"
import { FaJava } from 'react-icons/fa6'
import { Layers } from "lucide-react"
import { MdOutlineAutoFixHigh } from 'react-icons/md'
import { saveDiagram, getSavedDiagrams, toggleDiagramPublic } from "@/app/actions/diagram"
import { SettingsSidebar } from "./SettingsSidebar"
import { EmployeeNodeData } from './nodes/EmployeeNode'
import { GroupNodeData } from './nodes/GroupNode'

interface CustomEdgeProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: Position;
  targetPosition: Position;
  style?: React.CSSProperties;
  data?: any;
  markerEnd?: string;
}

const CustomEdge: React.FC<CustomEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <path
      id={id}
      style={style}
      className="react-flow__edge-path"
      d={edgePath}
      markerEnd={markerEnd}
    />
  );
};

const SmoothStepEdge: React.FC<CustomEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}) => {
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <path
      id={id}
      style={style}
      className="react-flow__edge-path"
      d={edgePath}
      markerEnd={markerEnd}
    />
  );
};

const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
  smoothstep: SmoothStepEdge,
};

interface NoteData {
  label: string;
  note?: string;
  style?: React.CSSProperties;
}

interface WebNodeData {
  label: string;
  url?: string;
  imageUrl?: string;
}

interface ImageNodeData {
  label: string;
  imageUrl?: string;
}

interface IconNodeData {
  label: string;
  icon: IconType;
}

const nodeTypes = {
  square: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[60px] flex items-center justify-center shadow-md rounded-md border-2 border-gray-200 dark:border-gray-700">
      <Handle type="target" position={Position.Top} className="w-16 !bg-blue-300" />
      <div className="font-bold text-center dark:text-white text-black">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-blue-300" />
    </div>
  ),
  circle: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[100px] flex items-center justify-center shadow-md rounded-full border-2 border-gray-200 dark:border-gray-700" 
         style={{ backgroundColor: data.style?.backgroundColor || 'white' }}>
      <Handle type="target" position={Position.Top} className="w-16 !bg-green-300" />
      <div className="font-bold text-center dark:text-white text-black">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-green-300" />
    </div>
  ),
  triangle: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[100px] flex items-center justify-center shadow-md border-2 border-gray-200" 
         style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }}>
      <Handle type="target" position={Position.Top} className="w-16 !bg-yellow-300" />
      <div className="font-bold text-center mt-8">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-yellow-300" />
    </div>
  ),
  diamond: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[100px] flex items-center justify-center shadow-md border-2 border-gray-200" 
         style={{ clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)' }}>
      <Handle type="target" position={Position.Top} className="w-16 !bg-purple-300" />
      <div className="font-bold text-center">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-purple-300" />
    </div>
  ),
  database: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[100px] flex flex-col items-center justify-center shadow-md rounded-md border-2 border-gray-200">
      <Handle type="target" position={Position.Top} className="w-16 !bg-pink-300" />
      <Database className="w-8 h-8 text-pink-500" />
      <div className="font-bold text-center mt-2">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-pink-300" />
    </div>
  ),
  http: ({ data }: NodeProps) => (
    <div className="w-[100px] h-[100px] flex flex-col items-center justify-center shadow-md rounded-md border-2 border-gray-200">
      <Handle type="target" position={Position.Top} className="w-16 !bg-orange-300" />
      <Globe className="w-8 h-8 text-orange-500" />
      <div className="font-bold text-center mt-2">{data.label}</div>
      <Handle type="source" position={Position.Bottom} className="w-16 !bg-orange-300" />
    </div>
  ),
  note: ({ data }: NodeProps<NoteData>) => (
    <div className="w-[300px] shadow-lg">
      <Card className="border-2 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="dark:text-white text-black">{data.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground whitespace-pre-wrap">
            {data.note}
          </p>
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  ),
  web: ({ data }: NodeProps<WebNodeData>) => (
    <div className="w-[300px] shadow-lg">
      <Card className="border-2 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="dark:text-white text-black">{data.label}</CardTitle>
          {data.url && (
            <CardDescription>
              <a href={data.url} target="_blank" rel="noopener noreferrer" 
                 className="text-blue-500 hover:underline">
                {data.url}
              </a>
            </CardDescription>
          )}
        </CardHeader>
        <CardContent>
          {data.imageUrl && (
            <img 
              src={data.imageUrl} 
              alt={data.label}
              className="w-full h-auto rounded-md"
            />
          )}
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  ),
  image: ({ data }: NodeProps<ImageNodeData>) => (
    <div className="w-[300px] shadow-lg">
      <Card className="border-2 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="dark:text-white text-black">{data.label}</CardTitle>
        </CardHeader>
        <CardContent>
          {data.imageUrl && (
            <img 
              src={data.imageUrl} 
              alt={data.label}
              className="w-full h-auto rounded-md"
            />
          )}
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  ),
  icon: ({ data }: NodeProps<IconNodeData>) => {
    const IconComponent = data.icon;
    return (
      <div className="relative w-[100px] h-[100px] flex flex-col items-center justify-center 
                      bg-background/95 shadow-sm rounded-lg border border-border
                      hover:shadow-lg hover:border-primary transition-all duration-200">
        <Handle 
          type="target" 
          position={Position.Top} 
          className="!w-12 !bg-primary/30" 
        />
        <div className="flex flex-col items-center gap-2 p-2">
          {IconComponent && (
            <IconComponent 
              size={40}
              className="grayscale hover:grayscale-0 transition-all duration-300"
            />
          )}
          <div className="text-xs font-medium text-muted-foreground text-center truncate w-full">
            {data.label}
          </div>
        </div>
        <Handle 
          type="source" 
          position={Position.Bottom} 
          className="!w-12 !bg-primary/30" 
        />
      </div>
    );
  },
  employee: ({ data }: NodeProps<EmployeeNodeData>) => (
    <div className="w-[300px] shadow-lg">
      <Card className="border-2 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
              {data.imageUrl ? (
                <img 
                  src={data.imageUrl} 
                  alt={data.label}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-6 h-6 text-primary" />
              )}
            </div>
            <div>
              <CardTitle className="text-base dark:text-white text-black">
                {data.firstName} {data.lastName}
              </CardTitle>
              <p className="text-sm text-muted-foreground">{data.position}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <Building2 className="w-4 h-4 text-muted-foreground" />
              <span>{data.department}</span>
            </div>
            {data.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <span>{data.email}</span>
              </div>
            )}
          </div>
        </CardContent>
        <Handle type="target" position={Position.Top} className="w-16" />
        <Handle type="source" position={Position.Bottom} className="w-16" />
      </Card>
    </div>
  ),

  group: ({ data }: NodeProps<GroupNodeData>) => (
    <div className="min-w-[400px] min-h-[300px] resize">
      <Card className={`w-full h-full border-2 border-dashed border-border/50 ${data.groupColor || 'bg-gray-50/50 dark:bg-gray-950/20'} 
        transition-colors duration-200 backdrop-blur-sm p-4 relative group`}>
        <div className="absolute top-2 left-4 flex items-center gap-2 opacity-50 group-hover:opacity-100 transition-opacity">
          <Users className="w-4 h-4 text-foreground/70" />
          <span className="text-sm font-medium text-foreground/70">
            {data.department || 'Department'}
          </span>
        </div>
        <Handle type="target" position={Position.Top} className="w-16 !bg-foreground/20" />
        <Handle type="source" position={Position.Bottom} className="w-16 !bg-foreground/20" />
      </Card>
    </div>
  ),
}

const nodeShapes = [
  { type: 'square', icon: Square, label: 'Square' },
  { type: 'circle', icon: Circle, label: 'Circle' },
  { type: 'triangle', icon: Triangle, label: 'Triangle' },
  { type: 'diamond', icon: Diamond, label: 'Diamond' },
  { type: 'database', icon: Database, label: 'Database' },
  { type: 'http', icon: Globe, label: 'HTTP' },
  { type: 'note', icon: Settings2, label: 'Note Card' },
  { type: 'web', icon: Globe, label: 'Web Page' },
  { type: 'image', icon: ImageIcon, label: 'Image' },
  { type: 'employee', icon: User, label: 'Employee' },
  { type: 'group', icon: Users, label: 'Team Group' },
]

// Replace the existing iconMap with this new categorized version
const techStackIcons = {
  frontend: {
    label: 'Frontend',
    icons: {
      React: SiReact,
      Vue: SiVuedotjs,
      Angular: SiAngular,
      NextJS: SiNextdotjs,
      HTML5: SiHtml5,
      CSS3: SiCss3,
      Tailwind: SiTailwindcss,
      Bootstrap: SiBootstrap,
      MaterialUI: SiMaterialdesign,
      ChakraUI: SiChakraui,
      RadixUI: SiRadixui,
      Storybook: SiStorybook,
    }
  },
  backend: {
    label: 'Backend',
    icons: {
      NodeJS: SiNodedotjs,
      Express: SiExpress,
      NestJS: SiNestjs,
      Python: SiPython,
      Java: FaJava,
      PHP: SiPhp,
      Laravel: SiLaravel,
      Django: SiDjango,
      Rails: SiRubyonrails,
      Spring: SiSpring,
      Go: SiGo,
      Rust: SiRust,
    }
  },
  database: {
    label: 'Database & ORM',
    icons: {
      MongoDB: SiMongodb,
      MySQL: SiMysql,
      PostgreSQL: SiPostgresql,
      Redis: SiRedis,
      Elasticsearch: SiElasticsearch,
      Prisma: SiPrisma,
      Sequelize: SiSequelize,
      TypeORM: SiTypeform,
      Mongoose: SiMongoose,
    }
  },
  cloud: {
    label: 'Cloud Services',
    icons: {
      AWS: SiAmazon,
      Azure: SiAzuredevops,
      GCP: SiGooglecloud,
      DigitalOcean: SiDigitalocean,
      Heroku: SiHeroku,
      S3: SiAmazons3,
      EC2: SiAmazonec2,
      Lambda: SiAwslambda,
      RDS: SiAmazonrds,
    }
  },
  devops: {
    label: 'DevOps & CI/CD',
    icons: {
      Docker: SiDocker,
      Kubernetes: SiKubernetes,
      Jenkins: SiJenkins,
      Git: SiGit,
      GithubActions: SiGithubactions,
      GitLab: SiGitlab,
      CircleCI: SiCircleci,
      Terraform: SiTerraform,
      Ansible: SiAnsible,
    }
  },
  testing: {
    label: 'Testing',
    icons: {
      Vitest: SiVitest,
      Jest: SiJest,
      Cypress: SiCypress,
      Playwright: SiPlaywright,
    }
  },
  tools: {
    label: 'Tools & APIs',
    icons: {
      Postman: SiPostman,
      Swagger: SiSwagger,
      Grafana: SiGrafana,
      Kibana: SiKibana,
      VSCode: SiVisualstudiocode,
      WebStorm: SiWebstorm,
      Nginx: SiNginx,
      Apache: SiApache,
    }
  },
  state: {
    label: 'State Management',
    icons: {
      Redux: SiRedux,
      Zustand: SiZulip,
      Jotai: SiJirasoftware,
      Recoil: SiRecoil,
    }
  },
  auth: {
    label: 'Authentication',
    icons: {
      Auth0: SiAuth0,
      OAuth: MdOutlineAutoFixHigh,
      Passport: SiPassport,
      Stripe: SiStripe,
    }
  },
  messaging: {
    label: 'Message Queues',
    icons: {
      Kafka: Monitor,
      RabbitMQ: SiRabbitmq,
      Pulsar: SiApachepulsar,
      ZeroMQ: SiMqtt,
    }
  },
  web3: {
    label: 'Web3 & Blockchain',
    icons: {
      Solidity: SiSolidity,
      Web3: SiWeb3Dotjs,
      IPFS: SiIpfs,
      Hyperledger: SiHyper,
    }
  },
}

// Update the iconMap to use the flattened version of techStackIcons
const iconMap: Record<string, IconType> = Object.entries(techStackIcons).reduce((acc, [_, category]) => {
  return { ...acc, ...category.icons }
}, {})

// Update the IconSearchDialog component
const IconSearchDialog = ({ 
  isOpen, 
  onClose, 
  onSelect,
  position,
  existingNode = null,
}: { 
  isOpen: boolean;
  onClose: () => void;
  onSelect: (icon: any, name: string, nodeId?: string) => void;
  position: { x: number; y: number };
  existingNode?: Node | null;
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  // Get all categories
  const categories = [
    { id: 'all', label: 'All' },
    ...Object.entries(techStackIcons).map(([id, { label }]) => ({ id, label }))];

  // Filter icons based on category and search term
  const filteredIcons = Object.entries(techStackIcons).flatMap(([category, { icons }]) => 
    Object.entries(icons)
      .filter(([name]) => 
        name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        (selectedCategory === 'all' || category === selectedCategory)
      )
      .map(([name, icon]) => ({ name, icon, category }))
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] p-0 gap-0">
        <div className="flex h-[600px]">
          {/* Categories Sidebar */}
          <div className="w-48 border-r p-2 space-y-1">
            {categories.map(({ id, label }) => (
              <Button
                key={id}
                variant={selectedCategory === id ? "secondary" : "ghost"}
                className="w-full justify-start text-sm"
                onClick={() => setSelectedCategory(id)}
              >
                {label}
              </Button>
            ))}
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            <div className="p-4 border-b">
              <DialogHeader>
                <DialogTitle>{existingNode ? 'Change Icon' : 'Select Tech Stack Icon'}</DialogTitle>
                <DialogDescription>
                  Search and select an icon for your node
                </DialogDescription>
              </DialogHeader>
              <Input
                placeholder="Search icons..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>

            {/* Icons Grid */}
            <div className="flex-1 overflow-auto p-4">
              <div className="grid grid-cols-6 gap-2">
                {filteredIcons.map(({ name, icon: Icon }) => (
                  <Button
                    key={name}
                    variant="outline"
                    className="h-20 p-2 flex flex-col items-center justify-center gap-2 hover:border-primary group"
                    onClick={() => onSelect(Icon, name, existingNode?.id)}
                  >
                    <Icon className="w-8 h-8 grayscale group-hover:grayscale-0 transition-all duration-200" />
                    <span className="text-xs text-center truncate w-full">
                      {name}
                    </span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Add this new component for AI prompt
const AIDiagramGenerator = ({ 
  onGenerate 
}: { 
  onGenerate: (data: { nodes: Node[]; edges: Edge[] }) => void 
}) => {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleGenerate = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.details || 'Failed to generate diagram');
      }

      if (data.error) {
        throw new Error(data.details || data.error);
      }

      // Validate the response data
      if (!data.nodes || !data.edges) {
        throw new Error('Invalid diagram data received');
      }

      // Process nodes to ensure icon components are properly set
      const processedNodes = data.nodes.map((node: any) => {
        if (node.type === 'icon' && node.data?.icon) {
          // Get the icon component from techStackIcons
          const iconComponent = Object.values(techStackIcons).reduce((found: IconType | null, category) => {
            if (found) return found;
            const icons = category.icons as Record<string, IconType>;
            return icons[node.data.icon] || null;
          }, null as IconType | null);

          return {
            ...node,
            data: {
              ...node.data,
              icon: iconComponent || SiReact // Fallback to React icon if not found
            }
          };
        }
        return node;
      });

      onGenerate({
        nodes: processedNodes,
        edges: data.edges
      });
      toast.success('Diagram generated successfully');
    } catch (error) {
      console.error('Generation error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate diagram');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Describe your diagram</Label>
        <Textarea
          placeholder="E.g., Create a system architecture diagram for a social media application with user authentication, database, and API endpoints"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="h-[200px]"
        />
      </div>
      <Button 
        onClick={handleGenerate} 
        disabled={!prompt || isLoading}
        className="w-full"
      >
        {isLoading ? (
          <>
            <span className="mr-2">Generating...</span>
            <span className="animate-spin">⚡</span>
          </>
        ) : (
          <>
            <span className="mr-2">Generate Diagram</span>
            <MdOutlineAutoFixHigh className="h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  );
};

// Add this new component for the save dialog
const SaveDiagramDialog = ({
  isOpen,
  onClose,
  onSave,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSave: (title: string) => Promise<void>;
}) => {
  const [title, setTitle] = useState('')
  const [isSaving, setIsSaving] = useState(false)

  const handleSave = async () => {
    if (!title.trim()) {
      toast.error('Title is required')
      return
    }

    setIsSaving(true)
    await onSave(title)
    setIsSaving(false)
    setTitle('')
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Save Diagram</DialogTitle>
          <DialogDescription>
            Enter a title for your diagram
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="diagram-title" className="text-right">
              Title
            </Label>
            <Input
              id="diagram-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="My Awesome Diagram"
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <span className="mr-2">Saving...</span>
                <span className="animate-spin">⚡</span>
              </>
            ) : (
              <>
                <span className="mr-2">Save Diagram</span>
                <Download className="h-4 w-4" />
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface SavedDiagram {
  id: string;
  title: string;
  content: {
    nodes: any[];
    edges: any[];
  };
  createdAt: string;
}

function Flow() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [nodeName, setNodeName] = useState('')
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [noteText, setNoteText] = useState('')
  const [isAnimatedEdges, setIsAnimatedEdges] = useState(false)
  const [showNodeBg, setShowNodeBg] = useState(true)
  const reactFlowInstance = useReactFlow()
  const { theme } = useTheme()
  const [nodeColor, setNodeColor] = useState('#000000')
  const [webUrl, setWebUrl] = useState('')
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [iconType, setIconType] = useState<IconType>(FaBeer)
  const [searchTerm, setSearchTerm] = useState('')
  const [isJsonDialogOpen, setIsJsonDialogOpen] = useState(false)
  const [isSettingsPanelOpen, setIsSettingsPanelOpen] = useState(false)
  const [isIconSearchOpen, setIsIconSearchOpen] = useState(false);
  const [dropPosition, setDropPosition] = useState({ x: 0, y: 0 });
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false)
  const [savedDiagrams, setSavedDiagrams] = useState<SavedDiagram[]>([])
  const [isLoadingSaved, setIsLoadingSaved] = useState(false)
  const [employeeData, setEmployeeData] = useState({
    firstName: '',
    lastName: '',
    department: '',
    position: '',
    email: '',
  })
  const [teamData, setTeamData] = useState({
    teamName: '',
    department: '',
    description: '',
    teamColor: '',
  });
  const [groupData, setGroupData] = useState({
    department: '',
    groupColor: '',
  });

  const [defaultEdgeOptions, setDefaultEdgeOptions] = useState<{
    type: 'smoothstep' | 'custom';
    style: {
      strokeWidth: number;
      stroke: string;
      strokeDasharray?: number;
      animation?: string;
    };
    markerEnd: {
      type: MarkerType;
      color: string;
    };
  }>({
    type: 'smoothstep',
    style: {
      strokeWidth: 2,
      stroke: '#b1b1b7',
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#b1b1b7',
    },
  });

   // Update the animated edges effect
   useEffect(() => {
    setDefaultEdgeOptions(prev => ({
      ...prev,
      style: {
        ...prev.style,
        ...(isAnimatedEdges ? {
          strokeDasharray: 5,
          animation: 'dashdraw 1s linear infinite',
        } : {
          strokeDasharray: undefined,
          animation: undefined,
        })
      }
    }));
  }, [isAnimatedEdges]);

  const onConnect = useCallback(
    (params: Connection | Edge) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  )

  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      if (!reactFlowWrapper.current) return;

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (!type) return;

      const position = {
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      };

      if (type === 'tech-stack') {
        setDropPosition(position);
        setIsIconSearchOpen(true);
        return;
      }

      if (type === 'employee') {
        const newNode: Node<EmployeeNodeData> = {
          id: `${type}-${Date.now()}`,
          type,
          position: reactFlowInstance.project(position),
          data: { 
            label: 'New Employee',
            firstName: '',
            lastName: '',
            department: '',
            position: '',
            email: '',
          }
        };
        setNodes((nds) => [...nds, newNode]);
        setSelectedNode(newNode);
        setIsDialogOpen(true);
        return;
      }

      if (type === 'group') {
        const newNode: Node<GroupNodeData> = {
          id: `${type}-${Date.now()}`,
          type,
          position: reactFlowInstance.project(position),
          data: { 
            label: 'New Group',
            department: 'Department',
          },
          style: { 
            width: 400, 
            height: 300,
          }
        };
        setNodes((nds) => [...nds, newNode]);
        setSelectedNode(newNode);
        setIsDialogOpen(true);
        return;
      }

      // Handle other node types
      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position: reactFlowInstance.project(position),
        data: { 
          label: `New ${type}`,
          className: 'dark:text-white text-black'
        }
      };

      setNodes((nds) => [...nds, newNode]);
    },
    [reactFlowInstance, setNodes]
  );

  const onNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    if (node.type === 'icon') {
      setSelectedNode(node);
      setIsIconSearchOpen(true);
    } else {
      setSelectedNode(node);
      setNodeName(node.data.label);
      setIsDialogOpen(true);
    }
  }, []);

  const fetchWebPreview = async (url: string) => {
    try {
      const metadata = await fetchMetadata(url)
      return metadata.image || null
    } catch (error) {
      console.error('Error fetching metadata:', error)
      toast.error('Failed to fetch webpage metadata')
      return null
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImageFile(file)
      const reader = new FileReader()
      reader.onload = () => {
        const imageUrl = reader.result as string
        if (selectedNode) {
          setNodes((nds) =>
            nds.map((node) => {
              if (node.id === selectedNode.id) {
                return {
                  ...node,
                  data: { ...node.data, imageUrl }
                }
              }
              return node
            })
          )
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const updateNodeName = useCallback(async () => {
    if (selectedNode) {
      let updatedData: any = {
        ...selectedNode.data,
        label: nodeName,
        note: selectedNode.type === 'note' ? noteText : undefined,
      }

      if (selectedNode.type === 'employee') {
        updatedData = {
          ...updatedData,
          ...employeeData,
          backgroundColor: nodeColor,
          label: `${employeeData.firstName} ${employeeData.lastName}`
        }
      }

      if (selectedNode.type === 'group') {
        updatedData = {
          ...updatedData,
          ...groupData,
          groupColor: nodeColor,
        }
      }

      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === selectedNode.id) {
            return {
              ...node,
              data: updatedData,
            }
          }
          return node
        })
      )
      toast.success('Node updated successfully')
    }
    setIsDialogOpen(false)
  }, [selectedNode, nodeName, noteText, nodeColor, employeeData, groupData, setNodes])

  const deleteNode = useCallback(() => {
    setNodes((nds) => nds.filter((node) => !node.selected))
    setEdges((eds) => eds.filter((edge) => !edge.selected))
  }, [setNodes, setEdges])

  const animatedEdgeStyle = {
    strokeDasharray: 5,
    animation: 'dashdraw 1s linear infinite',
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const styleSheet = document.createElement('style');
      styleSheet.textContent = `
        @keyframes dashdraw {
          from {
            stroke-dashoffset: 10;
          }
        }
      `;
      document.head.appendChild(styleSheet);

      return () => {
        document.head.removeChild(styleSheet);
      };
    }
  }, []);

  const saveAsImage = useCallback(() => {
    if (reactFlowWrapper.current === null) return

    toPng(reactFlowWrapper.current, {
      filter: (node) => {
        const excludeClasses = ['react-flow__panel', 'react-flow__controls', 'react-flow__minimap']
        return !excludeClasses.some(className => node.classList?.contains(className))
      }
    })
      .then((dataUrl) => {
        const link = document.createElement('a')
        link.download = 'workflow.png'
        link.href = dataUrl
        link.click()
      })
  }, [])

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
    setNodeName(node.data.label)
    setNoteText(node.data.note || '')
    setIsDialogOpen(true)
  }, [])

  const filteredNodeShapes = nodeShapes.filter((shape) =>
    shape.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getFormattedJson = () => {
    return JSON.stringify({ nodes, edges }, null, 2)
  }

  const handleIconSelect = useCallback((Icon: any, name: string, nodeId?: string) => {
    if (nodeId) {
      // Update existing node
      setNodes((nds) => nds.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              label: name,
              icon: Icon,
            }
          };
        }
        return node;
      }));
      toast.success('Icon updated successfully');
    } else {
      // Create new node with proper position calculation
      const newNode: Node = {
        id: `icon-${Date.now()}`,
        type: 'icon',
        position: reactFlowInstance.project({
          x: dropPosition.x,
          y: dropPosition.y,
        }),
        data: {
          label: name,
          icon: Icon,
        }
      };
      
      setNodes((nds) => [...nds, newNode]);
      toast.success('Icon added successfully');
    }
    setIsIconSearchOpen(false);
  }, [dropPosition, setNodes, reactFlowInstance]);

  const handleSaveDiagram = async (title: string) => {
    try {
      // Serialize nodes before saving
      const serializedNodes = nodes.map(node => ({
        ...node,
        data: {
          ...node.data,
          // Convert icon component to string name
          icon: node.type === 'icon' ? 
            Object.entries(techStackIcons).reduce((iconName, [category, { icons }]) => {
              const foundIcon = Object.entries(icons).find(([_, Icon]) => 
                Icon === node.data.icon
              );
              return foundIcon ? foundIcon[0] : iconName;
            }, '') : 
            undefined
        }
      }));

      const result = await saveDiagram({
        title,
        nodes: serializedNodes,
        edges,
      });

      if (result.success) {
        toast.success('Diagram saved successfully');
        setIsSaveDialogOpen(false);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Save error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save diagram');
    }
  };

  const fetchSavedDiagrams = useCallback(async () => {
    try {
      setIsLoadingSaved(true)
      const result = await getSavedDiagrams()
      if (result.success) {
        // @ts-ignore
        setSavedDiagrams(result.data)
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      toast.error('Failed to fetch saved diagrams')
    } finally {
      setIsLoadingSaved(false)
    }
  }, [])

  useEffect(() => {
    if (isSettingsPanelOpen) {
      fetchSavedDiagrams()
    }
  }, [isSettingsPanelOpen, fetchSavedDiagrams])

  const loadSavedDiagram = useCallback((diagram: SavedDiagram) => {
    try {
      const { nodes: savedNodes, edges: savedEdges } = diagram.content

      // Process nodes to restore icon components
      const processedNodes = savedNodes.map((node: any) => {
        if (node.type === 'icon' && node.data?.icon) {
          // Find the icon component from techStackIcons
          const iconComponent = Object.values(techStackIcons).reduce((found: any, category) => {
            if (found) return found;
            // @ts-ignore
            return category.icons[node.data.icon] || found;
          }, null);

          return {
            ...node,
            data: {
              ...node.data,
              icon: iconComponent || SiReact // Fallback to React icon if not found
            }
          };
        }
        return node;
      });

      setNodes(processedNodes)
      setEdges(savedEdges)
      toast.success('Diagram loaded successfully')
    } catch (error) {
      console.error('Load error:', error)
      toast.error('Failed to load diagram')
    }
  }, [setNodes, setEdges])

  // Add this function in the Flow component
  const handleTogglePublic = useCallback(async (diagramId: string) => {
    try {
      const result = await toggleDiagramPublic(diagramId);
      
      if (result.success) {
        // Refresh the diagrams list
        await fetchSavedDiagrams();
        // @ts-ignore
        if (result.data.isPublic) {
          toast.success('Diagram is now public');
          // Copy the public URL to clipboard
          if (result.publicUrl) {
            const fullUrl = `${window.location.origin}${result.publicUrl}`;
            await navigator.clipboard.writeText(fullUrl);
            toast.success('Public URL copied to clipboard');
          }
        } else {
          toast.success('Diagram is now private');
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Toggle public error:', error);
      toast.error('Failed to toggle diagram visibility');
    }
  }, [fetchSavedDiagrams]);

  return (
    <div className="h-screen w-full flex flex-col">
      <div className="flex-1 flex min-h-0">
        <div className="w-20 border-r bg-background flex flex-col overflow-hidden">
          <ScrollArea className="flex-1">
            <div className="p-2 grid grid-cols-1 gap-2">
              {filteredNodeShapes.map((shape) => (
                <TooltipProvider key={shape.type}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        draggable
                        onDragStart={(event) => event.dataTransfer.setData('application/reactflow', shape.type)}
                        className="flex flex-col items-center p-2 rounded-md border cursor-move hover:bg-accent"
                      >
                        <shape.icon className="w-6 h-6" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{shape.label}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      draggable
                      onDragStart={(event) => {
                        event.dataTransfer.setData('application/reactflow', 'tech-stack');
                      }}
                      className="flex flex-col items-center p-2 rounded-md border cursor-move hover:bg-accent"
                    >
                      <Layers className="w-6 h-6" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Tech Stack</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </ScrollArea>
        </div>
        <ResizablePanelGroup direction="horizontal" className="flex-1">
          <ResizablePanel defaultSize={80} minSize={60}>
            <div className="h-full relative" ref={reactFlowWrapper}>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onDragOver={onDragOver}
                onDrop={onDrop}
                onNodeClick={onNodeClick}
                onNodeDoubleClick={onNodeDoubleClick}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                defaultEdgeOptions={defaultEdgeOptions}
                className="absolute inset-0"
                fitView
              >
                <Controls className="absolute top-2 left-2" />
                <Background />
                <Panel position="top-right" className="space-x-2">
                  <Button onClick={() => setIsSaveDialogOpen(true)} size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                  <Button onClick={saveAsImage} size="sm">
                    <ImageIcon className="w-4 h-4 mr-2" />
                    Save as Image
                  </Button>
                  <Button onClick={() => setIsJsonDialogOpen(true)} size="sm">
                    <Settings2 className="w-4 h-4 mr-2" />
                    View JSON
                  </Button>
                  <Button 
                    onClick={() => setIsSettingsPanelOpen(!isSettingsPanelOpen)}
                    size="sm"
                    variant="outline"
                  >
                    {isSettingsPanelOpen ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                  </Button>
                </Panel>
              </ReactFlow>
            </div>
          </ResizablePanel>

          <SettingsSidebar
            isOpen={isSettingsPanelOpen}
            savedDiagrams={savedDiagrams}
            isLoadingSaved={isLoadingSaved}
            onLoadDiagram={loadSavedDiagram}
            defaultEdgeOptions={defaultEdgeOptions}
            setDefaultEdgeOptions={setDefaultEdgeOptions}
            isAnimatedEdges={isAnimatedEdges}
            setIsAnimatedEdges={setIsAnimatedEdges}
            showNodeBg={showNodeBg}
            setShowNodeBg={setShowNodeBg}
            onTogglePublic={handleTogglePublic}
          />
        </ResizablePanelGroup>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Node Properties</DialogTitle>
            <DialogDescription>Configure the node properties.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="node-name" className="text-right">Name</Label>
              <Input
                id="node-name"
                value={nodeName}
                onChange={(e) => setNodeName(e.target.value)}
                className="col-span-3"
              />
            </div>
            {selectedNode?.type === 'note' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="note-text" className="text-right">Note</Label>
                <Textarea
                  id="note-text"
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  className="col-span-3"
                  rows={4}
                />
              </div>
            )}

            {selectedNode?.type === 'web' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="web-url" className="text-right">URL</Label>
                <Input
                  id="web-url"
                  value={webUrl}
                  onChange={(e) => setWebUrl(e.target.value)}
                  placeholder="https://example.com"
                  className="col-span-3"
                />
              </div>
            )}

            {selectedNode?.type === 'image' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="image-upload" className="text-right">Upload Image</Label>
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="col-span-3"
                />
              </div>
            )}

            {selectedNode?.type === 'employee' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="first-name" className="text-right">First Name</Label>
                  <Input
                    id="first-name"
                    value={employeeData.firstName}
                    onChange={(e) => setEmployeeData(prev => ({ ...prev, firstName: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="last-name" className="text-right">Last Name</Label>
                  <Input
                    id="last-name"
                    value={employeeData.lastName}
                    onChange={(e) => setEmployeeData(prev => ({ ...prev, lastName: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="department" className="text-right">Department</Label>
                  <Input
                    id="department"
                    value={employeeData.department}
                    onChange={(e) => setEmployeeData(prev => ({ ...prev, department: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="position" className="text-right">Position</Label>
                  <Input
                    id="position"
                    value={employeeData.position}
                    onChange={(e) => setEmployeeData(prev => ({ ...prev, position: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={employeeData.email}
                    onChange={(e) => setEmployeeData(prev => ({ ...prev, email: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
              </>
            )}

            {selectedNode?.type === 'team' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="team-name" className="text-right">Team Name</Label>
                  <Input
                    id="team-name"
                    value={teamData.teamName}
                    onChange={(e) => setTeamData(prev => ({ ...prev, teamName: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="department" className="text-right">Department</Label>
                  <Input
                    id="department"
                    value={teamData.department}
                    onChange={(e) => setTeamData(prev => ({ ...prev, department: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">Description</Label>
                  <Textarea
                    id="description"
                    value={teamData.description}
                    onChange={(e) => setTeamData(prev => ({ ...prev, description: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="team-color" className="text-right">Custom Color</Label>
                  <div className="col-span-3">
                    <ColorPicker
                      color={teamData.teamColor}
                      onChange={(color) => setTeamData(prev => ({ ...prev, teamColor: color }))}
                    />
                  </div>
                </div>
              </>
            )}

            {selectedNode?.type === 'group' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="department" className="text-right">Department</Label>
                  <Input
                    id="department"
                    value={groupData.department}
                    onChange={(e) => setGroupData(prev => ({ ...prev, department: e.target.value }))}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="group-color" className="text-right">Group Color</Label>
                  <div className="col-span-3">
                    <ColorPicker
                      color={groupData.groupColor}
                      onChange={(color) => setGroupData(prev => ({ ...prev, groupColor: color }))}
                    />
                  </div>
                </div>
              </>
            )}

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Color</Label>
              <div className="col-span-3">
                <ColorPicker
                  color={nodeColor}
                  onChange={setNodeColor}
                />
              </div>
            </div>
          </div>
          <DialogFooter className="flex justify-between">
            <Button
              variant="destructive"
              onClick={() => {
                if (selectedNode) {
                  setNodes((nds) => nds.filter((n) => n.id !== selectedNode.id))
                  setIsDialogOpen(false)
                  toast.success('Node deleted successfully')
                }
              }}
            >
              Delete Node
            </Button>
            <Button onClick={updateNodeName}>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isJsonDialogOpen} onOpenChange={setIsJsonDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] flex flex-col p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle>Workflow Configuration</DialogTitle>
            <DialogDescription>
              View, export, or generate diagrams using AI
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="json" className="flex-1">
            <div className="px-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="json">JSON View</TabsTrigger>
                <TabsTrigger value="ai">AI Generator</TabsTrigger>
              </TabsList>
            </div>
            <div className="flex-1 p-6 pt-2">
              <TabsContent value="json" className="mt-0 h-full">
                <div className="space-y-4 h-full flex flex-col">
                  <div className="flex-1 min-h-0">
                    <ScrollArea className="h-[60vh] w-full rounded-md border">
                      <div className="p-4">
                        <pre className="text-sm whitespace-pre-wrap break-words">
                          {getFormattedJson()}
                        </pre>
                      </div>
                    </ScrollArea>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        const jsonString = getFormattedJson();
                        const blob = new Blob([jsonString], { type: 'application/json' });
                        const href = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = href;
                        link.download = 'workflow.json';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        toast.success('JSON exported successfully');
                      }}
                      className="flex-1"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export JSON
                    </Button>
                    <Button
                      onClick={() => {
                        try {
                          navigator.clipboard.writeText(getFormattedJson());
                          toast.success('JSON copied to clipboard');
                        } catch (error) {
                          toast.error('Failed to copy JSON');
                        }
                      }}
                      variant="outline"
                      className="flex-1"
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      Copy JSON
                    </Button>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="ai" className="mt-0">
                <AIDiagramGenerator 
                  onGenerate={(data) => {
                    setNodes(data.nodes);
                    setEdges(data.edges);
                    setIsJsonDialogOpen(false);
                    toast.success('Diagram generated successfully');
                  }} 
                />
              </TabsContent>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>

      <IconSearchDialog
        isOpen={isIconSearchOpen}
        onClose={() => {
          setIsIconSearchOpen(false);
          setSelectedNode(null);
        }}
        onSelect={handleIconSelect}
        position={dropPosition}
        existingNode={selectedNode}
      />

      <SaveDiagramDialog
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
        onSave={handleSaveDiagram}
      />
    </div>
  )
}

export default function EnhancedWorkflowEditor() {
  return (
    <ReactFlowProvider>
      <Flow />
    </ReactFlowProvider>
  )
}