import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";

// You'll need to set up your Y-Sweet server with an appropriate secret
// This endpoint serves as an authentication layer for Y-Sweet
export async function POST(req: NextRequest) {
  try {
    // Get the current user from Clerk
    const user = await currentUser();
    
    // You must be logged in to use this API
    if (!user) {
      return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Get the data from the request
    const data = await req.json();
    const { docId } = data;
    
    if (!docId) {
      return new NextResponse(JSON.stringify({ error: "Missing docId" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check if this is a valid docId format (collab-XXXXXXXX)
    const isValidCollabId = docId.startsWith('collab-') || docId.startsWith('note-');
    if (!isValidCollabId) {
      return new NextResponse(JSON.stringify({ error: "Invalid docId format" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Generate token with detailed user information
    const token = {
      docId,
      userId: user.id,
      userName: user.firstName || user.username || user.emailAddresses?.[0]?.emailAddress?.split('@')?.[0] || "Anonymous",
      color: getUserColor(user.id),
      // Add profile image if available
      profileImageUrl: user.imageUrl,
      // Add last active timestamp
      lastActive: Date.now(),
      expires: Date.now() + 1000 * 60 * 60 * 24, // 24 hours
    };

    console.log("Generated collaboration token:", token);

    return new NextResponse(JSON.stringify({ token }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    console.error("Y-Sweet auth error:", error);
    return new NextResponse(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Function to generate consistent colors from user ID
function getUserColor(userId: string) {
  const colors = [
    "#FFC107", "#FF5722", "#9C27B0", "#3F51B5", "#03A9F4", 
    "#009688", "#8BC34A", "#FF9800", "#607D8B", "#E91E63"
  ];
  
  // Generate a deterministic hash from the userId
  const hash = userId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  
  return colors[Math.abs(hash) % colors.length];
}

// Simple function to generate a token
// In a real implementation, you'd use Y-Sweet's authentication mechanism
function generateYSweetToken(userId: string, docId: string) {
  const header = { alg: "HS256", typ: "JWT" };
  const payload = {
    sub: userId,
    docId: docId,
    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24, // 24 hours
  };
  
  // This is a placeholder - you'd use a proper JWT library and your Y-Sweet secret
  return Buffer.from(
    JSON.stringify({ header, payload })
  ).toString("base64");
} 