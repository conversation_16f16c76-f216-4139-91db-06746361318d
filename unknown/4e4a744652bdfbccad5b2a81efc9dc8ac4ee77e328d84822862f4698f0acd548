:root {
  --square-bg: rgb(239 246 255);
  --circle-bg: rgb(240 253 244);
  --triangle-bg: rgb(254 249 195);
  --diamond-bg: rgb(250 245 255);
  --database-bg: rgb(253 242 248);
  --http-bg: rgb(255 247 237);
}

.react-flow__edge-path {
  transition: stroke 0.3s;
}

.react-flow__node {
  transition: all 0.3s;
}

/* Dark mode background colors */
[data-theme='dark'] {
  --square-bg: rgb(30 58 138);
  --circle-bg: rgb(20 83 45);
  --triangle-bg: rgb(113 63 18);
  --diamond-bg: rgb(88 28 135);
  --database-bg: rgb(131 24 67);
  --http-bg: rgb(124 45 18);
}

.react-flow__handle {
  opacity: 0;
  transition: opacity 0.3s;
}

.react-flow__node:hover .react-flow__handle {
  opacity: 1;
}

/* Hide dots in background */
.react-flow__background {
  display: none;
} 