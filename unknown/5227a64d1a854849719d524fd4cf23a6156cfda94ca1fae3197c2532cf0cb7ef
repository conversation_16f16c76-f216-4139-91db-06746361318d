/* Create this new file for cursor styling */
.bn-cursor {
  position: absolute;
  border-left: 2px solid;
  border-top: 2px solid;
  border-bottom: 2px solid;
  margin-left: -1px;
  box-sizing: border-box;
  transform-origin: left;
  transition: opacity 0.3s ease;
}

.bn-cursor-caret {
  margin-left: -0.5px;
  height: 1.2em;
  border-left-width: 2px;
}

.bn-cursor-label {
  position: absolute;
  top: -1.4em;
  left: -1px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  padding: 0px 5px;
  white-space: nowrap;
  color: white;
  opacity: 0.85;
}

.bn-cursor-label:hover {
  opacity: 1;
}

.bn-cursor.inactive {
  opacity: 0.5;
}

/* Custom styling for collaborative cursors */
.bn-container .bn-selection-area {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

.bn-cursor-caret {
  position: absolute;
  width: 2px;
  height: 17px;
  z-index: 10;
}

.bn-cursor-name-label {
  position: absolute;
  top: -16px;
  left: 0;
  font-size: 10px;
  padding: 2px 4px;
  white-space: nowrap;
  color: white;
  border-radius: 3px;
  user-select: none;
  z-index: 11;
}

/* Hide cursor labels when inactive */
.bn-cursor.inactive .bn-cursor-name-label {
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Show cursor labels on hover */
.bn-cursor.inactive:hover .bn-cursor-name-label {
  opacity: 1;
}

/* Increase the z-index of the cursor container to ensure it stays above other content */
.bn-collaboration-cursor-container {
  z-index: 100;
}

/* BlockNote cursor styles */
.bn-container {
  position: relative;
}

/* Cursor styles */
.bn-collaborationCursor {
  position: absolute;
  pointer-events: none;
  z-index: 100;
}

.bn-collaborationCursor__caret {
  position: absolute;
  width: 2px;
  height: 17px;
}

.bn-collaborationCursor__selectionRect {
  position: absolute;
  pointer-events: none;
  opacity: 0.2;
}

.bn-collaborationCursor__label {
  position: absolute;
  top: -16px;
  left: 0;
  font-size: 10px;
  padding: 0px 4px;
  white-space: nowrap;
  color: white;
  border-radius: 3px;
  user-select: none;
}

/* Inactive cursor styling */
.bn-collaborationCursor--inactive .bn-collaborationCursor__label {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bn-collaborationCursor--inactive:hover .bn-collaborationCursor__label {
  opacity: 1;
}

/* Additional styles */
.bn-editor {
  position: relative;
}

.bn-container .ProseMirror {
  position: relative;
} 