"use client";

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { storeEmployeeDocument, getEmployeeDocuments, deleteEmployeeDocument } from '@/actions/employeedoc';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner';
import { FileIcon, Trash2Icon } from 'lucide-react';

export default function DocPage() {
  const { id } = useParams();
  const [file, setFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const [documents, setDocuments] = useState<any[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDocuments();
  }, [id]);

  const fetchDocuments = async () => {
    setIsLoading(true);
    try {
      const docs = await getEmployeeDocuments(id as string);
      setDocuments(docs);
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileName', file.name);
      formData.append('description', description);
      formData.append('employeeId', id as string);

      const result = await storeEmployeeDocument(formData);
      if (result.success) {
        setFile(null);
        setDescription('');
        fetchDocuments();
        toast.success('Document uploaded successfully');
      } else {
        throw new Error(result.error || 'Failed to upload document');
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload document');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (documentId: string) => {
    const promise = deleteEmployeeDocument(documentId);

    toast.promise(promise, {
      loading: 'Deleting document...',
      success: (data) => {
        if (data.success) {
          fetchDocuments();
          return 'Document deleted successfully';
        } else {
          throw new Error(data.error || 'Failed to delete document');
        }
      },
      error: 'Failed to delete document',
    });
  };

  return (
    <div className="ml-20 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Upload Employee Document</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="file" className="block text-sm font-medium text-gray-700">
                Select PDF File
              </label>
              <Input
                type="file"
                id="file"
                accept=".pdf"
                onChange={handleFileChange}
                className="mt-1"
              />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mt-1"
              />
            </div>
            <Button type="submit" disabled={!file || isUploading}>
              {isUploading ? 'Uploading...' : 'Upload Document'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Employee Documents</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>Loading documents...</p>
          ) : documents.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>File Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Uploaded At</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {documents.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell className="flex items-center">
                      <FileIcon className="mr-2" />
                      {doc.fileName}
                    </TableCell>
                    <TableCell>{doc.description}</TableCell>
                    <TableCell>{new Date(doc.uploadedAt).toLocaleString()}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" onClick={() => handleDelete(doc.id)}>
                        <Trash2Icon className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p>No documents uploaded yet.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
