"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface ChangelogItem {
  date: string;
  version: string;
  title: string;
  description: string;
  type: 'feature' | 'improvement' | 'bugfix' | 'security' | 'deprecation';
  details?: string[];
  author?: string;
  breaking?: boolean;
}

const changelogData: ChangelogItem[] = [
  {
    date: '2024-11-23',
    version: '1.1.0',
    title: 'Major Platform Update',
    description: 'Enhanced user interface with new dashboard features and improved performance.',
    type: 'feature',
    breaking: true,
    details: [
      'Completely redesigned dashboard interface',
      'Added real-time collaboration features',
      'Improved data visualization components',
      'New keyboard shortcuts for power users'
    ],
    author: 'Development Team'
  },
  {
    date: '2024-06-10',
    version: '0.1.5',
    title: 'Security Enhancements',
    description: 'Critical security updates and performance improvements.',
    type: 'security',
    details: [
      'Updated authentication system',
      'Enhanced data encryption',
      'Fixed potential XSS vulnerabilities'
    ],
    author: 'Security Team'
  },
  {
    date: '2024-01-05',
    version: '0.1.0',
    title: 'Performance Optimization',
    description: 'Major performance improvements and bug fixes.',
    type: 'improvement',
    details: [
      'Reduced page load time by 50%',
      'Optimized database queries',
      'Improved caching mechanism'
    ],
    author: 'Performance Team'
  }
];

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

const getTypeColor = (type: ChangelogItem['type']) => {
  switch (type) {
    case 'feature':
      return 'bg-emerald-500 text-emerald-500 hover:bg-emerald-600';
    case 'improvement':
      return 'bg-blue-500 text-blue-500 hover:bg-blue-600';
    case 'bugfix':
      return 'bg-red-500 text-red-500 hover:bg-red-600';
    case 'security':
      return 'bg-yellow-500 text-yellow-500 hover:bg-yellow-600';
    case 'deprecation':
      return 'bg-purple-500 text-purple-500 hover:bg-purple-600';
    default:
      return 'bg-slate-500 text-slate-500 hover:bg-slate-600';
  }
};

const getTypePulseColor = (type: ChangelogItem['type']) => {
  switch (type) {
    case 'feature':
      return 'rgba(16, 185, 129, 0.25)'; // emerald
    case 'improvement':
      return 'rgba(59, 130, 246, 0.25)'; // blue
    case 'bugfix':
      return 'rgba(239, 68, 68, 0.25)'; // red
    case 'security':
      return 'rgba(234, 179, 8, 0.25)';  // yellow
    case 'deprecation':
      return 'rgba(168, 85, 247, 0.25)'; // purple
    default:
      return 'rgba(100, 116, 139, 0.25)'; // slate
  }
};

const Changelog: React.FC = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedType, setSelectedType] = React.useState('all');

  const filteredChangelog = changelogData.filter(change => {
    const matchesSearch = change.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         change.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = selectedType === 'all' || change.type === selectedType;
    return matchesSearch && matchesType;
  });

  return (
    <div className="max-w-4xl mx-auto py-16 px-4">
      <div className="space-y-8">
        <div className="flex flex-col items-center text-center space-y-4">
          <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl">
            Changelog
          </h1>
          <p className="text-muted-foreground max-w-[85%] leading-normal sm:text-lg sm:leading-7">
            Keep track of all the updates and improvements we make to the platform
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search updates..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Tabs defaultValue="all" className="w-full sm:w-auto">
            <TabsList>
              <TabsTrigger value="all" onClick={() => setSelectedType('all')}>All</TabsTrigger>
              <TabsTrigger value="feature" onClick={() => setSelectedType('feature')}>Features</TabsTrigger>
              <TabsTrigger value="security" onClick={() => setSelectedType('security')}>Security</TabsTrigger>
              <TabsTrigger value="improvement" onClick={() => setSelectedType('improvement')}>Improvements</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
          className="space-y-8"
        >
          {filteredChangelog.map((change, index) => (
            <motion.div
              key={index}
              variants={item}
              className="relative pl-8 pb-8 border-l-2 border-border last:border-l-0"
            >
              <div className="absolute left-[-9px] top-0">
                <motion.div
                  className={`w-4 h-4 rounded-full ${getTypeColor(change.type)} border-[3px] border-background relative`}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.2 }}
                >
                  {index === 0 && (
                    <>
                      <span 
                        className="absolute inline-flex h-[200%] w-[200%] -left-[50%] -top-[50%] rounded-full"
                        style={{ 
                          backgroundColor: getTypePulseColor(change.type),
                          animation: 'ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite'
                        }} 
                      />
                      <span 
                        className="absolute inline-flex h-[300%] w-[300%] -left-[100%] -top-[100%] rounded-full"
                        style={{ 
                          backgroundColor: getTypePulseColor(change.type),
                          animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                          opacity: 0.35
                        }} 
                      />
                    </>
                  )}
                </motion.div>
              </div>

              <Card>
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between flex-wrap gap-2">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{change.date}</p>
                      <h3 className="text-xl font-semibold">{change.title}</h3>
                    </div>
                    <div className="flex items-center gap-2">
                      {change.breaking && (
                        <Badge variant="destructive">Breaking Change</Badge>
                      )}
                      <Badge variant="outline">v{change.version}</Badge>
                      <Badge className={`${getTypeColor(change.type)} bg-opacity-10`}>
                        {change.type}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">{change.description}</p>
                  {change.details && (
                    <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground">
                      {change.details.map((detail, i) => (
                        <li key={i}>{detail}</li>
                      ))}
                    </ul>
                  )}
                </CardContent>
                {change.author && (
                  <CardFooter>
                    <p className="text-sm text-muted-foreground">
                      Released by {change.author}
                    </p>
                  </CardFooter>
                )}
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default Changelog;