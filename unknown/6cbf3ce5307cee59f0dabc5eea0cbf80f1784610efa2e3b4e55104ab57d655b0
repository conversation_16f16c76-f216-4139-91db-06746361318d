'use client'

import { useState, useRef, useEffect } from 'react'
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Palette } from "lucide-react"
import { cn } from "@/lib/utils"

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
}

// Predefined color palette
const PRESET_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57',
  '#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5',
  '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50'
]

const PREDEFINED_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57',
  '#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5',
  '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50',
  '#8bc34a', '#cddc39', '#ffeb3b', '#ffc107', '#ff9800'
]

export function ColorPicker({ value, onChange }: ColorPickerProps) {
  const [color, setColor] = useState(value)
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Update color when value prop changes
  useEffect(() => {
    setColor(value)
  }, [value])
  
  // Handle color change
  const handleColorChange = (newColor: string) => {
    setColor(newColor)
    onChange(newColor)
  }
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 gap-1 pl-3 pr-2"
        >
          <div 
            className="h-4 w-4 rounded-sm mr-1" 
            style={{ backgroundColor: color }}
          />
          <span className="text-xs truncate max-w-[60px]">{color}</span>
          <Palette className="h-4 w-4 ml-auto" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3">
        <div className="space-y-3">
          <div>
            <p className="text-sm font-medium mb-2">Select Color</p>
            <div className="grid grid-cols-5 gap-2 mb-3">
              {PREDEFINED_COLORS.map((colorOption, i) => (
                <button
                  key={i}
                  className={cn(
                    "w-6 h-6 rounded-md border border-muted",
                    value === colorOption && "ring-2 ring-primary ring-offset-1"
                  )}
                  style={{ backgroundColor: colorOption }}
                  onClick={() => handleColorChange(colorOption)}
                  title={colorOption}
                />
              ))}
            </div>
          </div>
          <div>
            <p className="text-sm font-medium mb-2">Custom Color</p>
            <div className="flex items-center gap-2">
              <Input
                ref={inputRef}
                type="text"
                value={color}
                onChange={(e) => handleColorChange(e.target.value)}
                className="h-8"
                placeholder="#RRGGBB"
              />
              <input
                type="color"
                value={color}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-8 h-8 p-0 border rounded cursor-pointer"
                aria-label="Pick a color"
              />
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
} 