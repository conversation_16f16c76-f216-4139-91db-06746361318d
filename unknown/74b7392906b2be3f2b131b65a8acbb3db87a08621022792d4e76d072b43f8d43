export type EventType = 'meeting' | 'training' | 'leave';
export type ItemType = 'event' | 'note' | 'workflow' | 'activity';
export type ActivityType = 'task' | 'comment' | 'notification';

export interface BaseCalendarItem {
  id: string;
  title: string;
  date: Date;
  description?: string;
}

export interface CalendarEvent extends BaseCalendarItem {
  priority: string;
  type: 'event';
  itemType: EventType;
  attendees: string[];
  reminder: boolean;
  reminderTime: string;
}

export interface CalendarNote extends BaseCalendarItem {
  type: 'note';
  content: string | Record<string, unknown>;
  isPublished: boolean;
}

export interface CalendarWorkflow extends BaseCalendarItem {
  type: 'workflow';
  isPublic: boolean;
  nodes: any[];
  edges: any[];
}

export interface CalendarActivity extends BaseCalendarItem {
  type: 'activity';
  activityType: ActivityType;
}

export type CalendarItem = 
  | CalendarEvent 
  | CalendarNote 
  | CalendarWorkflow 
  | CalendarActivity;

export interface Holiday {
  date: Date;
  name: string;
  type: 'fixed' | 'islamic';
}

export interface CalendarResponse {
  success: boolean;
  data?: CalendarItem[];
  error?: string;
}