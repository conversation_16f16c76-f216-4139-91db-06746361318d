const fs = require('fs');
const path = require('path');

// Read the source file
const alasqlSource = fs.readFileSync(
  path.join(__dirname, '../node_modules/alasql/dist/alasql.min.js'),
  'utf8'
);

// Remove any requires/imports that cause problems
const modifiedSource = alasqlSource
  .replace(/require\(['"]react-native-fs['"]\)/g, '{}')
  .replace(/require\(['"]react-native-fetch-blob['"]\)/g, '{}')
  .replace(/require\(['"]fs['"]\)/g, '{}')
  .replace(/require\(['"]crypto['"]\)/g, '{}')
  .replace(/require\(['"]child_process['"]\)/g, '{}');

// Write to a custom file
const outputPath = path.join(__dirname, '../lib/alasql.browser.js');
fs.writeFileSync(outputPath, modifiedSource);

console.log(`Built browser-compatible alasql at ${outputPath}`); 