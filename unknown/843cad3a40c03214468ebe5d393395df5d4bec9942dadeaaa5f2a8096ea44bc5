'use server';

import prisma from '@/lib/db'
import { ensureUserInDatabase } from '@/lib/ensure-user'

export async function fetchSankeyData() {
  try {
    const user = await ensureUserInDatabase()
    if (!user) {
      throw new Error('User not found')
    }

    const employees = await prisma.employee.findMany({
      where: {
        userId: user.id,
      },
      select: {
        id: true,
        departement: true,
        poste: true,
        salaire: true,
      },
    })

    return employees
  } catch (error) {
    console.error('Error fetching Sankey data:', error)
    return []
  }
}


