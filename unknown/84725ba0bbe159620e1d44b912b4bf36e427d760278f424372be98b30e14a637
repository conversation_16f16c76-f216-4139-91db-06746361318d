"use client"

import { useEffect } from "react";
import Link from "next/link";
import { Menu, LayoutDashboard, FileBarChart, Settings, Users } from "lucide-react";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface BilanMiniSidebarProps {
  isSidebarExpanded: boolean;
  setIsSidebarExpanded: (value: boolean) => void;
}

const BilanMiniSidebar = ({ isSidebarExpanded, setIsSidebarExpanded }: BilanMiniSidebarProps) => {
  const pathname = usePathname();

  const sidebarLinks = [
    { icon: LayoutDashboard, label: 'Bilan Social', href: '/hr/bilan' },
    { icon: FileBarChart, label: 'Bilan Paie', href: '/hr/bilan/bilanpaie' },
    { icon: Users, label: 'Employees', href: '#' },
    { icon: Settings, label: 'Settings', href: '#' },
  ];

  return (
    <div className={`fixed h-full bg-background border-r transition-all duration-300 z-10 
      ${isSidebarExpanded ? 'w-44' : 'w-14'}`}> {/* Reduced width */}
      <div className="flex flex-col p-1 space-y-1"> {/* Reduced padding and spacing */}
        <button 
          onClick={() => setIsSidebarExpanded(!isSidebarExpanded)}
          className="p-1.5 hover:bg-accent rounded-lg transition-colors" 
        >
          <Menu className="h-4 w-4" /> {/* Reduced icon size */}
        </button>

        <nav className="space-y-0.5"> {/* Reduced spacing */}
          {sidebarLinks.map((link) => (
            <TooltipProvider key={link.label}>
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <Link
                    href={link.href}
                    className={cn(
                      "flex items-center p-1.5 hover:bg-accent rounded-lg transition-colors", // Reduced padding
                      pathname === link.href && "bg-accent"
                    )}
                  >
                    <link.icon className="h-3.5 w-3.5" /> {/* Reduced icon size */}
                    {isSidebarExpanded && (
                      <span className="ml-1.5 text-xs">{link.label}</span>
                    )
                    }
                  </Link>
                </TooltipTrigger>
                {!isSidebarExpanded && (
                  <TooltipContent side="right" className="border">
                    <p>{link.label}</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default BilanMiniSidebar;
