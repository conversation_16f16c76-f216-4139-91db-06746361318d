/**
 * Direct CSS Overrides for React Grid Layout
 *
 * This file contains direct CSS overrides that will force react-grid-layout
 * to use absolute positioning instead of transforms, fixing the issue with
 * elements not following the cursor during drag operations.
 */

/* Force absolute positioning for all grid items */
.react-grid-item {
  position: absolute !important;
  transform: none !important;
  transition: none !important;
  -ms-transform: none !important;
  -webkit-transform: none !important;
  touch-action: none !important;
  z-index: 1;
}

/* Reset all transform properties during drag */
.react-grid-item.react-draggable-dragging {
  transform: none !important;
  transition: none !important;
  -ms-transform: none !important;
  -webkit-transform: none !important;
  z-index: 9999 !important;
  cursor: grabbing !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Force exact positioning during drag operations */
.react-draggable-dragging {
  position: absolute !important;
  left: var(--drag-x) !important;
  top: var(--drag-y) !important;
  pointer-events: auto !important;
}

/* Ensure the grid container has proper height */
.react-grid-layout {
  overflow: visible !important;
  min-height: calc(100vh - 120px) !important; /* Use viewport-based height */
  height: auto !important; /* Allow it to grow as needed */
  width: 100% !important; /* Use full width */
  max-width: 100% !important;
  margin: 0 !important;
}

/* Override the parent container height */
.dashboard-wrapper {
  min-height: 100vh !important;
  height: auto !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

/* Fix the layout specifically */
.layout {
  min-height: calc(100vh - 120px) !important;
  height: auto !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
}

/* Add customizations for modern bento grid-like layout */
[data-view-mode="grid"] .react-grid-layout {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-gap: 12px;
  grid-auto-rows: minmax(60px, auto);
}

/* Fix for drag cursors - ensure proper grabbing */
body[data-dashboard-dragging="true"] {
  cursor: grabbing !important;
}

body[data-dashboard-resizing="true"] {
  cursor: se-resize !important;
}

/* Ensure better placeholder visualization */
.react-grid-placeholder {
  background-color: rgba(var(--primary-rgb), 0.1) !important;
  border: 2px dashed rgba(var(--primary-rgb), 0.3) !important;
  border-radius: 8px !important;
  z-index: 0 !important;
}

/* Ensure dashboard container has proper height */
.dashboard-container {
  min-height: calc(100vh - 120px) !important;
  height: auto !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 16px !important;
  padding-bottom: 200px !important; /* Add padding at bottom for more space */
  overflow-x: hidden !important;
}

/* Improve drag handle visibility and interaction */
.draggable-handle {
  height: 20px !important;
  background-color: rgba(0, 0, 0, 0.03) !important;
  cursor: grab !important;
  opacity: 0 !important;
  transition: opacity 0.2s ease, background-color 0.2s ease;
  z-index: 10 !important;
}

.draggable-handle:hover,
.react-grid-item:hover .draggable-handle {
  background-color: rgba(0, 0, 0, 0.08) !important;
  opacity: 1 !important;
}

.draggable-handle:active {
  cursor: grabbing !important;
  background-color: rgba(0, 0, 0, 0.12) !important;
}

/* Make sure non-draggable elements don't initiate drags */
.react-grid-item .non-draggable,
.react-grid-item button,
.react-grid-item [role="button"],
.react-grid-item a,
.react-grid-item input,
.react-grid-item textarea {
  pointer-events: auto !important;
  touch-action: auto !important;
  z-index: 20 !important;
  position: relative;
}

/* Styles for rich text cards */
.rich-text-card {
  position: relative !important;
  overflow: visible !important;
  border: 1px solid transparent !important;
  transition: border-color 0.2s ease;
}

.rich-text-card:hover {
  border-color: rgba(0, 0, 0, 0.1) !important;
}

/* Make rich text cards more minimal */
.rich-text-card .card-header {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.rich-text-card:hover .card-header {
  opacity: 1;
}

/* Fix specific issues with chart drag */
[data-item-type="chart"] {
  cursor: grab !important;
}

[data-item-type="chart"].react-draggable-dragging {
  cursor: grabbing !important;
  will-change: transform, left, top;
}

/* Improved resize handle appearance and interaction */
.react-resizable-handle {
  z-index: 10 !important;
  cursor: se-resize !important;
  width: 20px !important;
  height: 20px !important;
  right: 0 !important;
  bottom: 0 !important;
  opacity: 0 !important;
  transition: opacity 0.2s ease;
  background-image: none !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.react-grid-item:hover .react-resizable-handle {
  opacity: 1 !important;
}

.react-resizable-handle::after {
  content: "";
  width: 8px;
  height: 8px;
  border-right: 2px solid rgba(0, 0, 0, 0.5);
  border-bottom: 2px solid rgba(0, 0, 0, 0.5);
  transform: rotate(45deg);
}

/* Ensure empty dashboard has proper height */
.h-\[400px\] {
  min-height: 600px !important;
}

/* Specific fix for the initial jump when starting drag */
.react-draggable {
  position: absolute !important;
  cursor: grab !important;
}

.react-draggable:active {
  cursor: grabbing !important;
}

/* Fix for card jitter during drag */
.react-grid-item.cssTransforms {
  position: absolute !important;
  transform: none !important;
  transition: none !important;
}

/* Fix the vertical space for the GridLayout children */
.react-grid-layout > div {
  margin-bottom: 16px !important;
}

/* Force full width for all parent containers */
.layout.grid-view, .layout.list-view {
  width: 100% !important;
  max-width: 100% !important;
}

/* Ensure main content area uses full width */
.main-content,
main,
.container,
.mx-auto {
  max-width: 100% !important;
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Fix resize handle interaction */
.react-resizable-handle {
  z-index: 10 !important;
  cursor: se-resize !important;
  width: 20px !important;
  height: 20px !important;
  right: 0 !important;
  bottom: 0 !important;
  opacity: 0 !important;
  transition: opacity 0.2s ease;
  background-image: none !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.react-grid-item:hover .react-resizable-handle {
  opacity: 1 !important;
}

.react-resizable-handle::after {
  content: "";
  width: 8px;
  height: 8px;
  border-right: 2px solid rgba(0, 0, 0, 0.5);
  border-bottom: 2px solid rgba(0, 0, 0, 0.5);
  transform: rotate(45deg);
}

/* Add grid snapping guidelines */
.grid-guideline {
  position: absolute;
  background-color: rgba(var(--primary-rgb), 0.3);
  z-index: 1000;
  pointer-events: none;
}

.grid-guideline-horizontal {
  height: 1px;
  left: 0;
  right: 0;
  width: 100%;
}

.grid-guideline-vertical {
  width: 1px;
  top: 0;
  bottom: 0;
}

/* Fix resize flickering */
.react-resizable {
  position: relative;
  overflow: visible !important;
}

/* Ensure text doesn't get selected during drag */
.react-grid-layout * {
  user-select: none !important;
}

/* Fix for card jumping when starting drag */
.dashboard-item-container {
  transform: none !important;
}

/* Fix for rich text card editing not updating correctly */
[data-is-editing="true"] {
  z-index: 1000 !important;
  border-color: hsl(var(--primary)) !important;
}

/* Force cursor grabbing during drag */
body.dragging {
  cursor: grabbing !important;
}

/* Make chart cards more precise when dragging */
[data-item-type="chart"].react-draggable-dragging {
  cursor: grabbing !important;
  pointer-events: auto !important;
  touch-action: none !important;
}

/* Ensure no border in view mode */
.no-border {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

/* Rich text content should be clearly distinguishable but without borders in view mode */
.rich-text-content {
  outline: none !important;
  background-color: transparent !important;
}

/* Ensure editing content doesn't trigger dragging */
.editing-content {
  cursor: text !important;
  user-select: text !important;
  touch-action: auto !important;
  pointer-events: auto !important;
}

/* Specific non-drag elements */
.non-draggable, 
button,
[role="button"],
a,
input,
textarea,
.react-resizable-handle {
  pointer-events: auto !important;
  touch-action: auto !important;
  user-select: auto !important;
  -webkit-user-drag: none !important;
  cursor: pointer !important;
  z-index: 11 !important;
  position: relative !important;
}

/* Extra specificity for buttons */
.rich-text-card button,
.rich-text-card .non-draggable {
  pointer-events: auto !important;
  z-index: 100 !important;
} 