import { useState, useEffect } from 'react';
import { AgentSetModel } from '@/components/AgentSetRag/ModelSelector';

// Define types for AgentSet API
export type AgentSetNamespace = {
  id: string;
  name: string;
  slug: string;
  organizationId: string;
  createdAt: string;
  embeddingConfig: {
    provider: string;
    model: string;
    apiKey: string;
  };
  vectorStoreConfig: {
    provider: string;
    apiKey: string;
    indexHost: string;
  };
};

export type AgentSetSearchResult = {
  id: string;
  score: number;
  text: string;
  relationships?: Record<string, any>;
  metadata?: {
    file_directory?: string;
    filename?: string;
    filetype?: string;
    link_texts?: any[];
    link_urls?: any[];
    languages?: any[];
    sequence_number?: number;
    [key: string]: any;
  };
};

export type AgentSetSearchParams = {
  query: string;
  topK?: number;
  rerank?: boolean;
  rerankLimit?: number;
  filter?: Record<string, any>;
  minScore?: number;
  includeRelationships?: boolean;
  includeMetadata?: boolean;
};

export type AgentSetChatMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string;
};

// Hook for interacting with AgentSet API
export const useAgentSetRag = () => {
  const [namespaces, setNamespaces] = useState<AgentSetNamespace[]>([]);
  const [selectedNamespace, setSelectedNamespace] = useState<string | null>(null);
  const [searchResults, setSearchResults] = useState<AgentSetSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<AgentSetChatMessage[]>([]);
  const [selectedModel, setSelectedModel] = useState<AgentSetModel>('command-r-plus');
  const [isReasoningMode, setIsReasoningMode] = useState(false);
  const [maxTokens, setMaxTokens] = useState(2048);
  const [config, setConfig] = useState<{ apiUrl: string; hasApiKey: boolean }>({
    apiUrl: '/api/agentset/proxy',
    hasApiKey: false
  });

  // Fetch configuration on mount
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/agentset/config');
        if (response.ok) {
          const data = await response.json();
          setConfig({
            apiUrl: '/api/agentset/proxy', // Always use our proxy
            hasApiKey: data.hasApiKey
          });

          if (!data.hasApiKey) {
            setError('AgentSet API key is not configured. Please add it to your environment variables.');
          }
        }
      } catch (err) {
        console.error('Error fetching config:', err);
      }
    };

    fetchConfig();
  }, []);

  // Fetch list of namespaces
  const fetchNamespaces = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!config.hasApiKey) {
        throw new Error('AgentSet API key is not configured');
      }

      const response = await fetch(`${config.apiUrl}?path=/v1/namespace`);

      if (!response.ok) {
        throw new Error(`Failed to fetch namespaces: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setNamespaces(data.data);
        // Auto-select the first namespace if none is selected
        if (!selectedNamespace && data.data.length > 0) {
          setSelectedNamespace(data.data[0].id);
        }
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch namespaces');
      console.error('Error fetching namespaces:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Search in a namespace
  const searchNamespace = async (namespaceId: string, params: AgentSetSearchParams) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!config.hasApiKey) {
        throw new Error('AgentSet API key is not configured');
      }

      const response = await fetch(`${config.apiUrl}?path=/v1/namespace/${namespaceId}/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error(`Search failed: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setSearchResults(data.data);
        return data.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: any) {
      setError(err.message || 'Search failed');
      console.error('Error searching namespace:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Add a message to the chat
  const addMessage = (message: AgentSetChatMessage) => {
    setChatMessages(prev => [...prev, message]);
  };

  // Clear chat messages
  const clearChat = () => {
    setChatMessages([]);
  };

  // Send a message and get a response using RAG
  const sendMessage = async (message: string) => {
    if (!selectedNamespace) {
      setError('No namespace selected');
      return;
    }

    // Add user message to chat
    addMessage({ role: 'user', content: message });

    setIsLoading(true);

    try {
      if (!config.hasApiKey) {
        throw new Error('AgentSet API key is not configured');
      }

      // Use our dedicated AgentSet chat API endpoint
      const response = await fetch('/api/agentset/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [
            ...chatMessages.filter(msg => msg.role !== 'system'), // Remove any previous system messages
            { role: 'user', content: message }
          ],
          namespaceId: selectedNamespace,
          model: selectedModel,
          isReasoningMode: isReasoningMode,
          maxTokens: maxTokens,
          temperature: 0.3 // Lower temperature for more factual responses
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('AgentSet chat API error:', errorText);
        throw new Error(`Chat request failed: ${response.status}`);
      }

      // Handle JSON response from Cohere
      const responseData = await response.json();

      if (responseData.error) {
        throw new Error(responseData.error);
      }

      // Add the assistant response to chat
      addMessage({
        role: 'assistant',
        content: responseData.content
      });

    } catch (err: any) {
      setError(err.message || 'Failed to send message');
      console.error('Error sending message:', err);

      // Add error message to chat
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    namespaces,
    selectedNamespace,
    searchResults,
    isLoading,
    error,
    chatMessages,
    selectedModel,
    isReasoningMode,
    maxTokens,
    fetchNamespaces,
    searchNamespace,
    setSelectedNamespace,
    setSelectedModel,
    setIsReasoningMode,
    setMaxTokens,
    addMessage,
    clearChat,
    sendMessage
  };
};

export default useAgentSetRag;
