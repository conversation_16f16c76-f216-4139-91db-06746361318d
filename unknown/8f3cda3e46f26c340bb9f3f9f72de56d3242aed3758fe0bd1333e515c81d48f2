import { getPublicNote } from "@/actions/actions"
import NotFound from "@/app/hr/employee/[id]/not-found"
import PublicNoteView from "@/components/Note/PublicNoteView"
import { Metadata } from 'next'
import { Suspense } from "react"

interface PageProps {
  params: { 
    publicId: string 
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const note = await getPublicNote(params.publicId)
  
  if (!note) {
    return {
      title: 'Note not found',
    }
  }

  return {
    title: `${note.title} | HRatlas`,
    description: note.title,
    openGraph: {
      title: note.title,
      description: "Shared note from HRatlas",
      images: note.coverImage ? [note.coverImage] : [],
    },
  }
}

export default async function PublicNotePage({
  params: { publicId }
}: PageProps) {
  const note = await getPublicNote(publicId)
  
  if (!note) {
    return <NotFound />
  }

  return (
    <main className="min-h-screen bg-background">
      <Suspense fallback={<Loading />}>
        <PublicNoteView note={note} />
      </Suspense>
    </main>
  )
}

// Create a loading state
function Loading() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="w-full max-w-5xl px-4">
        {/* Updated Skeleton loading animation */}
        <div className="w-full bg-muted">
          <div className="max-w-5xl mx-auto px-4">
            <div className="aspect-[3/1] bg-muted animate-pulse rounded-lg my-4" />
          </div>
        </div>
        <div className="space-y-4 mt-8">
          <div className="h-12 bg-muted animate-pulse rounded w-3/4" />
          <div className="h-4 bg-muted animate-pulse rounded w-1/4" />
          <div className="space-y-2 pt-8">
            {[1,2,3].map(i => (
              <div key={i} className="h-4 bg-muted animate-pulse rounded w-full" />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

