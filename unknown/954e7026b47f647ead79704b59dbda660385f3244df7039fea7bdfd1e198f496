"use client";

import { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
// import { DatasetTableButton } from './DatasetTableButton';

interface Dataset {
  id: string;
  name: string;
  data: any[];
  headers: string[];
  createdAt: string;
}

interface ViewDatasetProps {
  onDatasetSelect: (dataset: Dataset | null) => void;
  title: string;
}

export default function ViewDataset({ onDatasetSelect, title }: ViewDatasetProps) {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDatasets();
  }, []);

  const fetchDatasets = async () => {
    try {
      const response = await fetch('/api/datasets');
      const data = await response.json();
      
      if (data.success) {
        // @ts-ignore
        const validDatasets = data.datasets.filter(d => d.id && d.name);
       
        setDatasets(validDatasets);
      } else {
        toast.error('Failed to load datasets');
      }
    } catch (error) {
      console.error('Error fetching datasets:', error);
      toast.error('Failed to load datasets');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDatasetSelect = (id: string) => {
    const dataset = datasets.find(d => d.id === id);
    
    setSelectedDataset(dataset || null);
    onDatasetSelect(dataset || null);
  };

  return (
    <div className="mb-6">
      <h1 className="text-2xl font-bold mb-4">{title}</h1>
      <div className="flex items-center gap-4">
        {isLoading ? (
          <div className="text-muted-foreground">Loading datasets...</div>
        ) : datasets.length > 0 ? (
          <>
            <Select onValueChange={handleDatasetSelect}>
              <SelectTrigger className="w-[300px]">
                <SelectValue placeholder="Select a dataset" />
              </SelectTrigger>
              <SelectContent>
                {datasets.map((dataset) => (
                  dataset.id && (
                    <SelectItem key={dataset.id} value={dataset.id}>
                      {dataset.name || 'Unnamed Dataset'}
                    </SelectItem>
                  )
                ))}
              </SelectContent>
            </Select>
            {/* {selectedDataset && (
              <DatasetTableButton 
                data={selectedDataset.data} 
                title={`${selectedDataset.name} - Table View`}
              />
            )} */}
          </>
        ) : (
          <div className="text-muted-foreground">No datasets available</div>
        )}
      </div>
    </div>
  );
} 