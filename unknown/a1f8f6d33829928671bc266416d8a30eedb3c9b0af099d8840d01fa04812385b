/**
 * Drag Performance Optimizations
 * 
 * This file provides critical fixes for drag and drop performance
 * to prevent freezing during drag operations.
 */

/* Force hardware acceleration for dragged elements */
[data-dnd-draggable-dragging="true"] {
  transform: translate3d(0, 0, 0) !important;
  will-change: transform !important;
  transition: none !important;
  animation: none !important;
  pointer-events: none !important;
  z-index: 9999 !important;
}

/* Optimize performance during drag by disabling transitions */
.dragging-active * {
  transition: none !important;
  animation: none !important;
}

/* Ensure smooth cursor following */
.dragging-active [data-draggable="true"] {
  position: relative;
  z-index: 50;
}

/* Reduce visual effects during drag to improve performance */
.dragging-active .react-grid-item:not(.react-draggable-dragging) {
  pointer-events: none;
}

/* Disable pointer events on non-dragged elements to improve performance */
.dragging-active *:not([data-dnd-draggable-dragging="true"]) {
  pointer-events: none;
}

/* Re-enable pointer events for drop zones */
.dragging-active [data-droppable="true"] {
  pointer-events: auto !important;
}
