"use client"

import React, { useEffect, useState } from 'react';
import { getAttendanceByEmployeeId } from '@/actions/actions';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { format, subYears, addYears, startOfYear, endOfYear, eachMonthOfInterval, isSameYear, startOfMonth, endOfMonth, addDays, eachWeekOfInterval, isWeekend, eachDayOfInterval, isSameMonth } from "date-fns";
import { ChevronLeft, ChevronRight, TrendingUp } from 'lucide-react';
import { Area, AreaChart, Bar, BarChart, CartesianGrid, LabelList, XAxis } from "recharts";
import { Button } from "@/components/ui/button";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from "sonner";

interface AttendanceHistoryBarProps {
  employeeId: string;
  employeeName: string;
}

interface ChartDataItem {
  name: string;
  workdays: number;
  Present: number;
  Absent: number;
  Late: number;
  absentDays: string[];
  lateDays: string[];
  dailyStats?: {
    total: number;
    working: number;
    weekend: number;
  };
}

const chartConfig = {
  Present: {
    label: "Present",
    color: "hsl(216, 100%, 50%)", // Changed to blue
  },
  Late: {
    label: "Late",
    color: "hsl(32, 100%, 50%)", // Better orange
  },
  Absent: {
    label: "Absent",
    color: "hsl(0, 100%, 50%)", // Brighter red
  },
} satisfies ChartConfig;

// Add type for workdays filter function
const isNonWeekend = (date: Date): boolean => {
  const day = date.getDay();
  return day !== 0 && day !== 6;
};

const AttendanceHistoryBar: React.FC<AttendanceHistoryBarProps> = ({ employeeId, employeeName }) => {
  const [currentYear, setCurrentYear] = useState(new Date());
  const [chartData, setChartData] = useState<ChartDataItem[]>([]);

  const handlePreviousYear = () => {
    setCurrentYear(prevDate => subYears(prevDate, 1));
  };

  const handleNextYear = () => {
    setCurrentYear(prevDate => addYears(prevDate, 1));
  };

  // Replace old query with optimized version
  const { data: attendances = [] } = useQuery({
    queryKey: ['attendances', employeeId],
    queryFn: async () => {
      try {
        const data = await getAttendanceByEmployeeId(employeeId);
        return data;
      } catch (error) {
        toast.error('Failed to fetch attendance data');
        return [];
      }
    },
    refetchOnWindowFocus: true,
    refetchInterval: 1000,
    // Add these options to improve loading behavior
    staleTime: 30000, // Consider data fresh for 30 seconds
    // cacheTime: 3600000, 
  });

  // Remove the addMutation as it's not being used in this component
  // Remove references to setIsDialogOpen and onAttendanceChange

  useEffect(() => {
    const firstDayOfYear = startOfYear(currentYear);
    const lastDayOfYear = endOfYear(currentYear);
    const monthsInYear = eachMonthOfInterval({ start: firstDayOfYear, end: lastDayOfYear });

    const newChartData: ChartDataItem[] = monthsInYear.map(month => {
      const monthStart = startOfMonth(month);
      const monthEnd = endOfMonth(month);
      const allDays = eachDayOfInterval({ start: monthStart, end: monthEnd });
      const workdays = allDays.filter(isNonWeekend);
      const workdaysCount = workdays.length;

      // Ensure we're comparing dates properly
      const monthAttendances = attendances.filter(a => {
        const attendanceDate = new Date(a.date);
        return isSameMonth(attendanceDate, month) && isSameYear(attendanceDate, month);
      });

      // Count absences and lates
      const absentDays = monthAttendances.filter(a => a.status.toLowerCase() === 'absent');
      const lateDays = monthAttendances.filter(a => a.status.toLowerCase() === 'late');
      
      const absentCount = absentDays.length;
      const lateCount = lateDays.length;
      // Present count is working days minus absences and lates
      const presentCount = Math.max(0, workdaysCount - (absentCount + lateCount));

      return {
        name: format(month, 'MMM'),
        workdays: workdaysCount,
        Present: presentCount,
        Absent: absentCount,
        Late: lateCount,
        absentDays: absentDays.map(a => format(new Date(a.date), 'MMM d')),
        lateDays: lateDays.map(a => format(new Date(a.date), 'MMM d')),
        dailyStats: {
          total: allDays.length,
          working: workdaysCount,
          weekend: allDays.length - workdaysCount
        }
      };
    });

    setChartData(newChartData);
  }, [attendances, currentYear]);
  const currentMonthData = chartData.length > 0 ? chartData[chartData.length - 1] : null;

  const calculatePercentage = (value: number, total: number): string => {
    if (!total || total === 0) return '0%';
    return `${Math.round((value / total) * 100)}%`;
  };

  return (
    <div className="grid gap-2">
      <Card className="w-full">
        <CardHeader className="py-2 px-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-sm">Attendance Overview</CardTitle>
              <CardDescription className="text-xs">
                {format(currentYear, 'MMMM yyyy')}
              </CardDescription>
            </div>
            <div className="flex items-center gap-1">
              <Button onClick={handlePreviousYear} variant="outline" size="icon" className="h-6 w-6">
                <ChevronLeft className="h-3 w-3" />
              </Button>
              <Button onClick={handleNextYear} variant="outline" size="icon" className="h-6 w-6">
                <ChevronRight className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-2">
          <ChartContainer config={chartConfig}>
            <AreaChart
              data={chartData}
              margin={{ top: 2, right: 2, bottom: 0, left: 2 }}
              className="h-[50px] w-full"
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.1} />
              <XAxis 
                dataKey="name" 
                tickLine={false}
                axisLine={false}
                dy={2}
                tick={{ fontSize: 10 }}
                height={20}
              />
              <ChartTooltip 
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload as ChartDataItem;
                    return (
                      <div className="bg-background/95 px-2 py-1 rounded-lg shadow-lg border text-[10px]">
                        <p className="font-medium">{data.name}</p>
                        <p className="text-blue-500">Present: {data.Present}/{data.workdays}</p>
                        <p className="text-orange-500">Late: {data.Late}</p>
                        <p className="text-red-500">Absent: {data.Absent}</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Area
                type="monotone"
                dataKey="Late"
                stroke="hsl(32, 100%, 50%)"
                fill="hsl(32, 100%, 50%)"
                fillOpacity={0.15}
                strokeWidth={1}
              />
              <Area
                type="monotone"
                dataKey="Absent"
                stroke="hsl(0, 100%, 50%)"
                fill="hsl(0, 100%, 50%)"
                fillOpacity={0.15}
                strokeWidth={1}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="pt-0 px-2 pb-2">
          {currentMonthData && (
            <div className="w-full space-y-2">
              <div className="grid grid-cols-3 gap-1">
                <Card className="p-1.5 bg-blue-50 dark:bg-blue-950/50">
                  <div className="text-blue-600 dark:text-blue-400">
                    <div className="text-base font-semibold">{currentMonthData.Present}</div>
                    <div className="text-[10px]">Present</div>
                    <div className="text-[8px] mt-0.5">
                      {calculatePercentage(currentMonthData.Present, currentMonthData.workdays)}
                    </div>
                  </div>
                </Card>
                <Card className="p-1.5 bg-orange-50 dark:bg-orange-950/50">
                  <div className="text-orange-600 dark:text-orange-400">
                    <div className="text-base font-semibold">{currentMonthData.Late}</div>
                    <div className="text-[10px]">Late</div>
                    <div className="text-[8px] mt-0.5">
                      {calculatePercentage(currentMonthData.Late, currentMonthData.workdays)}
                    </div>
                  </div>
                </Card>
                <Card className="p-1.5 bg-red-50 dark:bg-red-950/50">
                  <div className="text-red-600 dark:text-red-400">
                    <div className="text-base font-semibold">{currentMonthData.Absent}</div>
                    <div className="text-[10px]">Absent</div>
                    <div className="text-[8px] mt-0.5">
                      {calculatePercentage(currentMonthData.Absent, currentMonthData.workdays)}
                    </div>
                  </div>
                </Card>
              </div>

              <div className="grid grid-cols-3 gap-1 text-[10px] text-muted-foreground">
                <div>Total: {currentMonthData.dailyStats?.total}</div>
                <div>Working: {currentMonthData.dailyStats?.working}</div>
                <div>Weekend: {currentMonthData.dailyStats?.weekend}</div>
              </div>

              {(currentMonthData.absentDays.length > 0 || currentMonthData.lateDays.length > 0) && (
                <div className="text-[10px] space-y-0.5">
                  {currentMonthData.absentDays.length > 0 && (
                    <div className="text-red-500">
                      Absent: {currentMonthData.absentDays.join(', ')}
                    </div>
                  )}
                  {currentMonthData.lateDays.length > 0 && (
                    <div className="text-orange-500">
                      Late: {currentMonthData.lateDays.join(', ')}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

// Wrap the component with QueryClientProvider if not already wrapped by parent
export default function WrappedAttendanceHistoryBar(props: AttendanceHistoryBarProps) {
  return (
    <QueryClientProvider client={new QueryClient()}>
      <>
        <AttendanceHistoryBar {...props} />
        {/* No need for explicit Toaster as it should be in your root layout */}
      </>
    </QueryClientProvider>
  );
}