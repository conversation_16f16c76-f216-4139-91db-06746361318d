'use client'

import React from 'react';
import { motion } from "framer-motion";
import { Database, Eye, GitBranch, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { cn } from "@/lib/utils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { formatBytes, formatNumber, formatDate, calculateDataSize } from './utils';

interface DatasetInfoProps {
  savedDatasets: any[];
  allVersions: Record<string, any[]>;
  onDatasetSelect: (dataset: any) => void;
  onDeleteDataset: (datasetId: string) => void;
  onShowVersionHistory: (dataset: any) => void;
  loadVersionData: (datasetId: string, versionNumber: number) => void;
}

export const DatasetInfo: React.FC<DatasetInfoProps> = ({
  savedDatasets,
  allVersions,
  onDatasetSelect,
  onDeleteDataset,
  onShowVersionHistory,
  loadVersionData
}) => {
  if (!savedDatasets.length) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4"
    >
      <div className="rounded-lg border bg-card overflow-hidden text-sm">
        <div className="grid grid-cols-[2.5fr_1.5fr_2.5fr_auto] items-center gap-4 p-4 bg-muted/50">
          <div className="font-medium text-sm text-muted-foreground">Dataset</div>
          <div className="font-medium text-sm text-muted-foreground">Info</div>
          <div className="font-medium text-sm text-muted-foreground">Version Timeline</div>
          <div className="font-medium text-sm text-muted-foreground text-right">Actions</div>
        </div>

        <div className="max-h-[400px] overflow-auto">
          {savedDatasets.map((dataset, index) => {
            // Filter versions for this specific dataset
            const datasetVersions = allVersions[dataset.id] || [];

            // Add debug output if there are no versions showing
            if (datasetVersions.length === 0) {
              console.log(`No versions found for dataset ${dataset.id}. All versions:`, allVersions);
            }

            return (
              <motion.div
                key={dataset.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  "grid grid-cols-[2.5fr_1.5fr_2.5fr_auto] items-center gap-4 p-4",
                  "hover:bg-muted/50 relative group border-t"
                )}
              >
                {/* Dataset Info - More spacious */}
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5
                                flex items-center justify-center">
                    <Database className="h-5 w-5 text-primary" />
                  </div>
                  <div className="min-w-0">
                    <p className="font-medium truncate text-base">{dataset.name}</p>
                    <p className="text-sm text-muted-foreground truncate">
                      {formatDate(dataset.createdAt)}
                    </p>
                  </div>
                </div>

                {/* Stats - Larger text */}
                <div className="flex flex-col gap-2 text-sm">
                  <span className="text-muted-foreground">
                    {formatNumber(dataset.data.length)} rows × {dataset.headers.length} cols
                  </span>
                  <span className="text-muted-foreground">
                    {formatBytes(calculateDataSize(dataset.data))}
                  </span>
                </div>

                {/* Version Timeline - Wider spacing */}
                <div className="relative px-4">
                  <div className="absolute inset-y-0 h-1 bg-gradient-to-r from-primary/20 via-primary/5 to-transparent
                                rounded-full top-1/2 -translate-y-1/2"
                       style={{ width: `${Math.min(100, datasetVersions.length * 20)}%` }} />
                  <div className="relative flex items-center gap-1 min-h-[20px]">
                    {datasetVersions.length > 0 ? (
                      datasetVersions.map((version) => (
                        <div key={version.id} className="relative group/version">
                          <motion.button
                            onClick={() => loadVersionData(dataset.id, version.versionNumber)}
                            whileHover={{ scale: 1.2, y: -2 }}
                            className={cn(
                              "relative h-5 w-5 rounded-full flex items-center justify-center text-[10px] font-medium",
                              "transition-all duration-200",
                              version.changes.length > 0
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted text-muted-foreground"
                            )}
                          >
                            {version.versionNumber}
                            {version.changes.length > 0 && (
                              <span className="absolute -top-0.5 -right-0.5 h-2 w-2 rounded-full bg-green-500
                                             border border-background" />
                            )}
                          </motion.button>

                          {/* Fixed tooltip positioned relative to the button */}
                          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 opacity-0
                                         group-hover/version:opacity-100 transition-opacity z-50 pointer-events-none">
                            <div className="bg-popover border shadow-md text-popover-foreground text-xs rounded-md
                                          px-2 py-1 whitespace-nowrap">
                              <div className="font-medium">Version {version.versionNumber}</div>
                              <div className="text-[10px] text-muted-foreground">{formatDate(version.createdAt)}</div>
                              <div className="text-[10px] mt-1">
                                {version.changes.length} {version.changes.length === 1 ? 'change' : 'changes'}
                              </div>
                            </div>
                            <div className="absolute top-full left-1/2 -translate-x-1/2 -mt-1">
                              <div className="border-8 border-transparent border-t-popover" />
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <span className="text-xs text-muted-foreground italic px-2">No version history</span>
                    )}
                  </div>
                </div>

                {/* Actions - Larger buttons */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8"
                    onClick={() => onDatasetSelect(dataset)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8"
                    onClick={() => {
                      onDatasetSelect(dataset);
                      onShowVersionHistory(dataset);
                    }}
                  >
                    <GitBranch className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Dataset</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this dataset? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDeleteDataset(dataset.id)}
                          className="bg-destructive hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>

                {/* Animated border */}
                <motion.div
                  className="absolute bottom-0 left-0 h-[1px] bg-gradient-to-r from-primary/50 to-primary/0"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
              </motion.div>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
};

