declare module 'alasql/dist/alasql.min' {
    interface AlaSQLStatement {
        (sql: string, params?: any[]): any;
    }

    interface AlaSQLStatic extends AlaSQLStatement {
        promise(sql: string, params?: any[]): Promise<any>;
        tables: { [key: string]: any };
        options: {
            errorlog: boolean;
            valueof: boolean;
            dropifnotexists: boolean;
            datetimeformat: string;
            casesensitive: boolean;
        };
    }

    const alasql: AlaSQLStatic;
    export = alasql;
}
