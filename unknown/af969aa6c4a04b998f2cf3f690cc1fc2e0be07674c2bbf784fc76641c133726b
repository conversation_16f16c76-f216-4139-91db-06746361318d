"use client"

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'

import { toast } from 'sonner'
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { joinChannelByInvite } from '@/actions/chatActions'

export default function JoinChannel() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string
  const [isJoining, setIsJoining] = useState(false)
  const [userName, setUserName] = useState('')

  const handleJoinChannel = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!userName.trim()) {
      toast.error('Please enter your name')
      return
    }

    try {
      setIsJoining(true)
      const result = await joinChannelByInvite(token, userName)
      
      if (result.success) {
        toast.success(result.message || 'Successfully joined the channel!')
        router.push(`/hr/workspace/chats/${result.channelId}`)
      } else {
        toast.error(result.error || 'Failed to join channel')
        router.push('/hr/workspace')
      }
    } catch (error) {
      console.error('Error joining channel:', error);
      toast.error('An error occurred while joining the channel')
      router.push('/hr/workspace')
    } finally {
      setIsJoining(false)
    }
  }

  return (
    <div className="flex items-center justify-center h-screen">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Join Channel</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleJoinChannel} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="userName" className="text-sm font-medium">
                Your Name
              </label>
              <Input
                id="userName"
                placeholder="Enter your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                disabled={isJoining}
                required
              />
            </div>
            <Button 
              type="submit" 
              className="w-full"
              disabled={isJoining}
            >
              {isJoining ? 'Joining...' : 'Join Channel'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 