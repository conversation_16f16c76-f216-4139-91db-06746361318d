"use client"

import { NodeProps } from 'reactflow'
import { Users } from 'lucide-react'
import { useCallback, useState } from 'react'

export interface GroupNodeData {
  label: string;
  department?: string;
  groupColor?: string;
  width?: number;
  height?: number;
}

const defaultColors = {
  'tech': 'border-blue-500/50',
  'design': 'border-purple-500/50',
  'marketing': 'border-green-500/50',
  'sales': 'border-orange-500/50',
  'hr': 'border-pink-500/50',
  'finance': 'border-yellow-500/50',
  'default': 'border-gray-500/50'
}

export const GroupNode = ({ data }: NodeProps<GroupNodeData>) => {
  const [dimensions, setDimensions] = useState({
    width: data.width || 400,
    height: data.height || 300
  });

  const borderColor = data.groupColor ? `border-[${data.groupColor}]/50` : 
    defaultColors[data.department?.toLowerCase() as keyof typeof defaultColors] || defaultColors.default;

  const onResize = useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;
    const rect = target.closest('.group')?.getBoundingClientRect();
    if (!rect) return;
    
    const newWidth = Math.max(400, event.clientX - rect.left);
    const newHeight = Math.max(300, event.clientY - rect.top);
    setDimensions({ width: newWidth, height: newHeight });
  }, []);

  return (
    <div 
      style={{ 
        width: dimensions.width,
        height: dimensions.height,
      }}
      className="relative group"
    >
      {/* Transparent Container */}
      <div 
        className={`
          absolute inset-0
          rounded-xl
          border-[2px] border-dashed ${borderColor}
          bg-transparent
          transition-colors duration-200
          hover:border-opacity-75 border-opacity-40
        `}
      >
        {/* Department Label */}
        <div className="absolute -top-3 left-4 px-2 py-0.5 rounded-md bg-background/95 border border-border/50 shadow-sm">
          <div className="flex items-center gap-2">
            <Users className="w-3.5 h-3.5 text-muted-foreground" />
            <span className="text-xs font-medium text-muted-foreground">
              {data.department || 'Department'}
            </span>
          </div>
        </div>

        {/* Resize Handle */}
        <div 
          className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
          onMouseDown={(e) => {
            e.stopPropagation();
            const onMouseMove = (e: MouseEvent) => onResize(e);
            const onMouseUp = () => {
              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
            };
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          }}
        >
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            className="text-muted-foreground/50"
          >
            <path 
              d="M22 22L13 13M13 22L22 13" 
              stroke="currentColor" 
              strokeWidth="2"
            />
          </svg>
        </div>
      </div>
    </div>
  )
}