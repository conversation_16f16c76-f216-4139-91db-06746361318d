'use client'

import { useState, useEffect, useRef } from 'react';
import { ChartVisualizerProps } from './types';
import { EnhancedChartVisualizer } from './ChartSectionsConf/EnhancedChartVisualizer';

export function ResponsiveChartVisualizer(props: ChartVisualizerProps) {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Function to update container size
  const updateSize = () => {
    if (containerRef.current) {
      const { width, height } = containerRef.current.getBoundingClientRect();
      setContainerSize({ width, height });
    }
  };

  // Update size on mount and when the window resizes
  useEffect(() => {
    updateSize();
    
    // Create a ResizeObserver to detect container size changes
    const resizeObserver = new ResizeObserver(() => {
      updateSize();
    });
    
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    // Also listen for window resize events
    window.addEventListener('resize', updateSize);
    
    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', updateSize);
    };
  }, []);

  return (
    <div 
      ref={containerRef} 
      className="w-full h-full flex items-center justify-center"
      style={{ minHeight: props.fullHeight ? '100%' : '250px' }}
    >
      <div 
        className="w-full h-full"
        style={{ 
          maxWidth: containerSize.width,
          maxHeight: containerSize.height
        }}
      >
        <EnhancedChartVisualizer {...props} />
      </div>
    </div>
  );
}
