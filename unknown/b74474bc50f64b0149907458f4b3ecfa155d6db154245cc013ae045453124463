import { useEffect, useRef } from 'react';

export function useEventSource(url: string | null, onMessage: (event: MessageEvent) => void) {
  const eventSourceRef = useRef<EventSource | null>(null);

  useEffect(() => {
    if (!url) return;

    const connect = () => {
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      eventSource.onmessage = onMessage;

      eventSource.onerror = (error) => {
        console.error('EventSource error:', error);
        eventSource.close();
        // Attempt to reconnect after 5 seconds
        setTimeout(connect, 5000);
      };
    };

    connect();

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [url, onMessage]);

  return eventSourceRef.current;
} 