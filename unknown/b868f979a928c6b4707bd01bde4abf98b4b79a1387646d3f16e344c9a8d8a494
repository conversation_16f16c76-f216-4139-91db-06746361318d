.dashboard-canvas {
  --grid-color: rgba(100, 116, 139, 0.1);
  background-size: var(--grid-size) var(--grid-size);
  background-image:
    linear-gradient(to right, var(--grid-color) 1px, transparent 1px),
    linear-gradient(to bottom, var(--grid-color) 1px, transparent 1px);
  transition: all 0.3s ease;
}

.dashboard-canvas.edit-mode {
  --grid-color: rgba(100, 116, 139, 0.2);
  cursor: default;
}

.chart-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  z-index: 10;
}

/* Animation for new charts */
@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.chart-wrapper {
  min-width: 0;
  min-height: 0;
  animation: fadeIn 0.3s ease-out;
}

.resize-handle {
  position: absolute;
  background: transparent;
  z-index: 30;
  transition: background-color 0.2s ease;
}

.resize-handle:hover,
.resize-handle.active {
  background-color: rgba(var(--primary), 0.3);
}

.resize-handle.east, .resize-handle.e {
  top: 0;
  right: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
}

.resize-handle.south, .resize-handle.s {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  cursor: ns-resize;
}

.resize-handle.southeast, .resize-handle.se {
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: nwse-resize;
  border-radius: 0 0 4px 0;
}

.snap-indicator {
  position: absolute;
  background-color: #2563eb;
  z-index: 25;
  pointer-events: none;
}

.snap-indicator.vertical {
  width: 1px;
  height: 100%;
  top: 0;
}

.snap-indicator.horizontal {
  height: 1px;
  width: 100%;
  left: 0;
}

/* List view styles */
.list-view .react-grid-item {
  width: 100% !important;
  margin-bottom: 12px !important;
}

.list-view .react-grid-item .chart-card {
  transition: all 0.3s ease;
}

/* Print styles */
@media print {
  .dashboard-canvas {
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
  }

  .chart-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}
