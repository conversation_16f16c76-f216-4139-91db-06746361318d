# Python Data Analysis Examples
# These examples demonstrate how to use the enhanced Python capabilities in the ChartBuilder

# Basic Data Exploration
# ---------------------
# View the first few rows of the dataset
result = df.head()

# Get basic information about the dataset
result = df.info()

# Get statistical summary of numeric columns
result = df.describe()

# Get comprehensive dataset analysis
result = analyzer.analyze_dataset(df)

# Check available columns
print(f"Available columns: {available_columns}")

# Package Management
# -----------------
# List installed packages
result = pip_list()

# Show details about a specific package
result = pip_show('pandas')

# Install a package (use with caution)
# pip_install('scikit-learn')

# Data Visualization
# -----------------
# Create a simple bar chart
plt.figure(figsize=(10, 6))
plt.bar(df['department'], df['salary'])
plt.title('Salary by Department')
plt.xlabel('Department')
plt.ylabel('Salary')
plt.xticks(rotation=45)
plt.tight_layout()
result = get_plot()

# Create a histogram
plt.figure(figsize=(10, 6))
plt.hist(df['age'], bins=10, alpha=0.7)
plt.title('Age Distribution')
plt.xlabel('Age')
plt.ylabel('Frequency')
plt.grid(True, alpha=0.3)
result = get_plot()

# Create a scatter plot
plt.figure(figsize=(10, 6))
plt.scatter(df['age'], df['salary'])
plt.title('Age vs Salary')
plt.xlabel('Age')
plt.ylabel('Salary')
plt.grid(True, alpha=0.3)
result = get_plot()

# Create a pie chart
plt.figure(figsize=(10, 6))
df['department'].value_counts().plot.pie(autopct='%1.1f%%')
plt.title('Department Distribution')
plt.ylabel('')
result = get_plot()

# Using Seaborn for advanced visualizations
plt.figure(figsize=(12, 8))
sns.heatmap(df.corr(), annot=True, cmap='coolwarm')
plt.title('Correlation Matrix')
result = get_plot()

# Create a box plot
plt.figure(figsize=(12, 6))
sns.boxplot(x='department', y='salary', data=df)
plt.title('Salary Distribution by Department')
plt.xticks(rotation=45)
result = get_plot()

# Create a violin plot
plt.figure(figsize=(12, 6))
sns.violinplot(x='department', y='salary', data=df)
plt.title('Salary Distribution by Department')
plt.xticks(rotation=45)
result = get_plot()

# Create a pair plot
sns.pairplot(df[['age', 'salary', 'id']])
result = get_plot()

# Using Helper Functions
# ---------------------
# Plot a histogram using the helper function
result = plot_histogram('age', bins=15)

# Plot a boxplot using the helper function
result = plot_boxplot('salary')

# Plot a scatter plot using the helper function
result = plot_scatter('age', 'salary')

# Plot a correlation matrix using the helper function
result = plot_correlation()

# Plot a count plot using the helper function
result = plot_countplot('department')

# Plot a bar plot using the helper function
result = plot_barplot('department', 'salary')

# Plot a line plot using the helper function
result = plot_lineplot('id', 'salary')

# Advanced Data Analysis
# ---------------------
# Analyze a numeric column
result = analyzer.analyze_numeric(df, 'salary')

# Analyze a categorical column
result = analyzer.analyze_categorical(df, 'department')

# Detect outliers in a numeric column
result = analyzer.detect_outliers(df, 'salary', method='iqr')

# Group data and calculate statistics
result = df.groupby('department')['salary'].agg(['mean', 'median', 'std', 'min', 'max'])

# Create a pivot table
result = pd.pivot_table(df, values='salary', index='department', columns='id', aggfunc='mean')

# Multiple Plots in One Figure
# ---------------------------
# Create a figure with multiple subplots
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Plot 1: Bar chart
axes[0, 0].bar(df['department'], df['salary'])
axes[0, 0].set_title('Salary by Department')
axes[0, 0].set_xlabel('Department')
axes[0, 0].set_ylabel('Salary')
axes[0, 0].tick_params(axis='x', rotation=45)

# Plot 2: Histogram
axes[0, 1].hist(df['age'], bins=10)
axes[0, 1].set_title('Age Distribution')
axes[0, 1].set_xlabel('Age')
axes[0, 1].set_ylabel('Frequency')

# Plot 3: Scatter plot
axes[1, 0].scatter(df['age'], df['salary'])
axes[1, 0].set_title('Age vs Salary')
axes[1, 0].set_xlabel('Age')
axes[1, 0].set_ylabel('Salary')

# Plot 4: Pie chart
df['department'].value_counts().plot.pie(autopct='%1.1f%%', ax=axes[1, 1])
axes[1, 1].set_title('Department Distribution')
axes[1, 1].set_ylabel('')

plt.tight_layout()
result = get_plot()

# Using Dark Theme
# ---------------
# Set up dark theme for plots
PlotManager.setup_default_style(theme='dark')

# Create a plot with dark theme
plt.figure(figsize=(10, 6))
plt.bar(df['department'], df['salary'], color='skyblue')
plt.title('Salary by Department (Dark Theme)')
plt.xlabel('Department')
plt.ylabel('Salary')
plt.xticks(rotation=45)
plt.tight_layout()
result = get_plot()

# Reset to light theme
PlotManager.setup_default_style(theme='light')
