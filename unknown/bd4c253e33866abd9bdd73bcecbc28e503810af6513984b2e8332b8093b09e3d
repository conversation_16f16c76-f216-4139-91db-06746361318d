'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ColorPicker } from "./ColorPicker"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, X, Move } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

export interface Series {
  id: string
  field: string
  color: string
  label: string
  visible: boolean
}

interface MultiSeriesSelectorProps {
  availableFields: string[]
  selectedSeries: Series[]
  onChange: (series: Series[]) => void
  maxSeries?: number
}

// Chart color palette
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00C49F', '#FFBB28', '#FF8042', '#a4de6c', '#d0ed57'
];

export function MultiSeriesSelector({
  availableFields,
  selectedSeries,
  onChange,
  maxSeries = 5
}: MultiSeriesSelectorProps) {
  const [series, setSeries] = useState<Series[]>(selectedSeries)

  // Update parent component when series change
  useEffect(() => {
    onChange(series)
  }, [series, onChange])

  // Add a new series
  const addSeries = () => {
    if (series.length >= maxSeries) return

    // Find first available field not already used
    const usedFields = series.map(s => s.field)
    const availableField = availableFields.find(field => !usedFields.includes(field))
    
    if (!availableField) return

    const newSeries: Series = {
      id: `series-${Date.now()}`,
      field: availableField,
      color: COLORS[series.length % COLORS.length],
      label: availableField,
      visible: true
    }

    setSeries([...series, newSeries])
  }

  // Remove a series
  const removeSeries = (id: string) => {
    setSeries(series.filter(s => s.id !== id))
  }

  // Update a series
  const updateSeries = (id: string, updates: Partial<Series>) => {
    setSeries(series.map(s => s.id === id ? { ...s, ...updates } : s))
  }

  // Toggle visibility of a series
  const toggleVisibility = (id: string) => {
    setSeries(series.map(s => s.id === id ? { ...s, visible: !s.visible } : s))
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Data Series</h4>
        {series.length < maxSeries && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={addSeries}
            className="h-7 text-xs"
          >
            <Plus className="h-3.5 w-3.5 mr-1" />
            Add Series
          </Button>
        )}
      </div>

      {series.length === 0 ? (
        <div className="text-sm text-muted-foreground text-center py-2">
          No data series selected. Add one to begin.
        </div>
      ) : (
        <ScrollArea className="h-[200px] py-1">
          <div className="space-y-3 pr-4">
            {series.map((s) => (
              <div 
                key={s.id} 
                className="flex flex-col gap-2 p-2 border rounded-md"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={s.visible}
                      onCheckedChange={() => toggleVisibility(s.id)}
                      id={`series-${s.id}-visible`}
                    />
                    <Label 
                      htmlFor={`series-${s.id}-visible`}
                      className={s.visible ? "font-medium" : "text-muted-foreground"}
                    >
                      {s.label || s.field}
                    </Label>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => removeSeries(s.id)}
                  >
                    <X className="h-3.5 w-3.5" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <Label className="text-xs">Field</Label>
                    <select
                      value={s.field}
                      onChange={(e) => updateSeries(s.id, { field: e.target.value })}
                      className="w-full h-8 text-xs px-2 py-1 rounded-md border"
                    >
                      {availableFields.map(field => (
                        <option key={field} value={field}>{field}</option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs">Color</Label>
                    <ColorPicker
                      value={s.color}
                      onChange={(color) => updateSeries(s.id, { color })}
                    />
                  </div>
                </div>
                
                <div className="space-y-1">
                  <Label className="text-xs">Label</Label>
                  <Input
                    value={s.label}
                    onChange={(e) => updateSeries(s.id, { label: e.target.value })}
                    placeholder={s.field}
                    className="h-8 text-xs"
                  />
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      )}
    </div>
  )
} 