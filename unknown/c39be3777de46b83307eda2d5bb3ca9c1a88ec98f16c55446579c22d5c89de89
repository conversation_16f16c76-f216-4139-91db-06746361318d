"use client"

import React, { use<PERSON>emo, useRef, useEffect } from "react"
import { TrendingUp } from "lucide-react"
import * as echarts from "echarts"

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface DataOverviewProps {
  data: any[]
  headers: string[]
  title?: string
  description?: string
}

export function DataOverview({ data, headers, title = "Data Overview", description }: DataOverviewProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  // Generate overview data for visualization
  const { overviewData, numericColumns } = useMemo(() => {
    if (!data.length || !headers.length) return { overviewData: [], numericColumns: [] }
    
    // Only use numeric columns for the chart
    const numCols = headers.filter(header => {
      // Check if at least 50% of values in this column are numbers
      const numericCount = data.reduce((count, row) => {
        const value = row[header]
        return count + (typeof value === 'number' || !isNaN(Number(value)) ? 1 : 0)
      }, 0)
      return numericCount > data.length * 0.5
    }).slice(0, 4) // Limit to 4 columns for readability
    
    // For each numeric column, create a series of points
    const formattedData = numCols.map(column => {
      const series = data.slice(0, 20).map((row, index) => {
        const value = Number(row[column])
        return [index + 1, !isNaN(value) ? value : 0]
      })
      
      return {
        name: column,
        data: series,
        type: 'line',
        showSymbol: false,
        smooth: true,
        lineStyle: {
          width: 1.5,
        }
      }
    })
    
    return { 
      overviewData: formattedData,
      numericColumns: numCols
    }
  }, [data, headers])

  // Initialize and update chart
  useEffect(() => {
    if (!chartRef.current || !overviewData.length) return
    
    // Initialize chart
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }
    
    // Generate colors for the series
    const colors = overviewData.map((_, i) => {
      const colorIndex = (i % 6) + 1
      return `hsl(var(--chart-${colorIndex}))`
    })
    
    // Set chart options
    const option = {
      color: colors,
      grid: {
        top: 8,
        right: 8,
        bottom: 20,
        left: 40,
        containLabel: false
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params: any) => {
          let result = `Point ${params[0].value[0]}<br/>`
          params.forEach((param: any) => {
            result += `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:8px;height:8px;background-color:${param.color};"></span> ${param.seriesName}: ${param.value[1]}<br/>`
          })
          return result
        }
      },
      xAxis: {
        type: 'value',
        min: 1,
        max: 20,
        axisLabel: {
          fontSize: 8,
          formatter: (value: number) => `P${value}`
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 8
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            opacity: 0.3
          }
        }
      },
      series: overviewData
    }
    
    // Apply options
    chartInstance.current.setOption(option)
    
    // Resize handler
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    
    window.addEventListener('resize', handleResize)
    
    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = null
      }
    }
  }, [overviewData])

  // Return a placeholder message if there's no valid data
  if (!overviewData.length) {
    return (
      <Card>
        <CardHeader className="py-1 px-3">
          <CardTitle className="text-xs">{title}</CardTitle>
          <CardDescription className="text-xs">No numeric data available for visualization</CardDescription>
        </CardHeader>
        <CardContent className="py-2 flex items-center justify-center">
          <p className="text-xs text-muted-foreground">Upload data with numeric columns to see visualization</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border shadow-sm">
      <CardHeader className="py-1 px-3">
        <CardTitle className="text-xs">{title}</CardTitle>
        <CardDescription className="text-xs opacity-75">{description || `Showing trends across ${numericColumns.length} columns`}</CardDescription>
      </CardHeader>
      <CardContent className="p-1">
        {/* Super compact chart - just 80px in height */}
        <div ref={chartRef} className="w-full" style={{ height: "80px" }} />
      </CardContent>
      <CardFooter className="py-1 px-3 text-[10px] border-t text-muted-foreground">
        <div className="flex items-center gap-1">
          <TrendingUp className="h-3 w-3" />
          <span>Showing data points across {numericColumns.join(", ")}</span>
        </div>
      </CardFooter>
    </Card>
  )
} 