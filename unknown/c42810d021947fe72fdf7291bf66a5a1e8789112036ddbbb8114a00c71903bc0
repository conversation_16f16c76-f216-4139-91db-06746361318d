"use client";

import { useState } from 'react';
import <PERSON>ilanPaie from '@/components/BilanReport/BilanPaie';
import ViewDataset from '@/components/BilanReport/ViewDataset';
import { toast } from 'sonner';
import { Code } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { She<PERSON>, <PERSON>etTrigger, SheetTitle, SheetHeader, SheetContent } from '@/components/ui/sheet';
import BilanMiniSidebar from '@/components/layout/BilanMiniSidebar';

interface Dataset {
  id: string;
  name: string;
  data: any[];
  headers: string[];
  createdAt: string;
}

export default function BilanPaiePage() {
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);

  const handleDatasetSelect = (dataset: Dataset | null) => {
    console.log("Dataset received in page:", dataset); // Debug incoming dataset
    console.log("Dataset data structure:", dataset?.data); // Debug data structure
    setSelectedDataset(dataset);
  };

  const requiredJsonFormat = [
    {
      "Rubrique": "string",       // Code for the salary component (e.g., "001" for base salary)
      "Nom & Prénom": "string",   // Employee's full name
      "Libelle": "string",        // Description of the salary component (e.g., "Base Salary")
      "Total": "number",          // Total amount for the salary component
      "Jan-25": "number",         // Amount for January 2025
      "Feb-25": "number",         // Amount for February 2025
      "Mar-25": "number",         // Amount for March 2025
      "Apr-25": "number",         // Amount for April 2025
      "May-25": "number",         // Amount for May 2025
      "Jun-25": "number",         // Amount for June 2025
      "Jul-25": "number",         // Amount for July 2025
      "Aug-25": "number",         // Amount for August 2025
      "Sep-25": "number",         // Amount for September 2025
      "Oct-25": "number",         // Amount for October 2025
      "Nov-25": "number",         // Amount for November 2025
      "Dec-25": "number"          // Amount for December 2025
    }
  ]
 
  return (
    <div className="flex min-h-screen">
      <BilanMiniSidebar 
        isSidebarExpanded={isSidebarExpanded}
        setIsSidebarExpanded={setIsSidebarExpanded}
      />

      <div className={`flex-1 transition-margin duration-300 
        ${isSidebarExpanded ? 'ml-44' : 'ml-14'}`}>
        <div className="container mx-auto px-0.5 py-0.5"> {/* Reduced padding */}
          <div className="space-y-0.5"> {/* Minimal spacing */}
            <ViewDataset 
              onDatasetSelect={handleDatasetSelect}
              title="Payroll Analysis Dashboard"
            />

            <div className="flex justify-end -mt-2">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-1"> {/* Reduced size and gap */}
                    <Code className="h-3 w-3" /> {/* Reduced icon size */}
                    <span className="text-xs">Data Format</span> {/* Reduced text size */}
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[300px] sm:w-[400px]"> {/* Reduced width */}
                  <SheetHeader>
                    <SheetTitle className="text-sm">Required Data Format</SheetTitle>
                  </SheetHeader>
                  <div className="mt-4 h-[calc(100vh-6rem)] overflow-y-auto"> {/* Reduced margin and height */}
                    <pre className="text-xs text-muted-foreground"> {/* Reduced text size */}
                      {JSON.stringify(requiredJsonFormat, null, 2)}
                    </pre>
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            {selectedDataset && (
              <div className="mt-0.5"> {/* Minimal margin */}
                <BilanPaie data={selectedDataset.data} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
