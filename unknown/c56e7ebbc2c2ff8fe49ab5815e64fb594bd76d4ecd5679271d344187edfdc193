export type Priority = "low" | "medium" | "high";

export interface User {
    id: string
    name: string
    avatar: string
}

export interface Task {
    id: string
    title: string
    description: string
    priority: Priority
    assignee: User
    dueDate: Date
    commentsCount: number
    createdAt: Date
    type?: 'task' | 'bug' | 'feature'
    sprint?: 'none' | 'current' | 'backlog' | 'next'
}

export interface Column {
    id: string
    title: string
    tasks: Task[]
}
