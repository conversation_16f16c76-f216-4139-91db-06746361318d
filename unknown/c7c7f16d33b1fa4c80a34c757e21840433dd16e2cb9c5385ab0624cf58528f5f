"use client"

import React from 'react'
import React<PERSON>low, {
  Controls,
  Background,
  Node,
  Edge,
  ConnectionMode,
  NodeProps,
  Handle,
  Position,
  BackgroundVariant,
  ReactFlowProvider,
  Panel,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Define types for our node data
interface NodeData {
  label: string;
  content?: string;
  note?: string;
}

// Custom Node Types
const CustomNode = ({ data }: NodeProps<NodeData>) => (
  <Card className="min-w-[200px] border-2 shadow-lg hover:shadow-xl transition-shadow duration-200">
    <CardHeader className="p-4">
      <CardTitle className="text-base font-medium">{data.label}</CardTitle>
    </CardHeader>
    {(data.content || data.note) && (
      <CardContent className="p-4 pt-0">
        <p className="text-sm text-muted-foreground">{data.content || data.note}</p>
      </CardContent>
    )}
    <Handle type="target" position={Position.Top} className="!bg-primary/50 w-3 h-3" />
    <Handle type="source" position={Position.Bottom} className="!bg-primary/50 w-3 h-3" />
  </Card>
)

// Define custom node types
const nodeTypes = {
  custom: CustomNode,
}

interface PublicFlowProps {
  title: string;
  initialData: {
    nodes: Node<NodeData>[];
    edges: Edge[];
  }
}

function Flow({ title, initialData }: PublicFlowProps) {
  // Define edge options
  const defaultEdgeOptions = {
    animated: true,
    style: {
      stroke: 'hsl(var(--primary))',
      strokeWidth: 2,
    },
  }

  // Ensure nodes have the correct type
  const processedNodes = initialData.nodes.map(node => ({
    ...node,
    type: 'custom', // Set all nodes to use our custom type
  }))

  return (
    <div className="w-full h-full bg-background">
      <ReactFlow
        nodes={processedNodes}
        edges={initialData.edges}
        nodeTypes={nodeTypes}
        defaultEdgeOptions={defaultEdgeOptions}
        connectionMode={ConnectionMode.Loose}
        fitView
        fitViewOptions={{ padding: 0.2 }}
        minZoom={0.5}
        maxZoom={2}
        nodesDraggable={false}
        nodesConnectable={false}
        elementsSelectable={false}
        proOptions={{ hideAttribution: true }}
      >
        <Panel position="top-left" className="bg-background/95 p-4 rounded-lg shadow-lg border border-border">
          <h2 className="text-xl font-semibold text-foreground">{title}</h2>
        </Panel>
        <Background 
          variant={BackgroundVariant.Dots}
          gap={12}
          size={1}
          color="hsl(var(--muted-foreground))"
          className="opacity-20"
        />
        <Controls 
          className="bg-background border-border"
          showInteractive={false}
        />
      </ReactFlow>
    </div>
  )
}

// Wrap the component with ReactFlowProvider
export default function PublicFlow(props: PublicFlowProps) {
  return (
    <ReactFlowProvider>
      <Flow {...props} />
    </ReactFlowProvider>
  )
}