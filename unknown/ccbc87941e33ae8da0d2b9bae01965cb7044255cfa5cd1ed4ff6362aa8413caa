.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}

.react-grid-item.react-grid-placeholder {
  background: rgba(0, 0, 0, 0.1);
  border: 2px dashed rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.react-grid-item.resizing {
  opacity: 0.9;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 100;
  opacity: 0.9;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.react-grid-item > .react-resizable-handle::after {
  content: "";
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
}