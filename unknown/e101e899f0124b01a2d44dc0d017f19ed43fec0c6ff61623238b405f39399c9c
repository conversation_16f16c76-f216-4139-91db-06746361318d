"use client"

import { <PERSON><PERSON>, <PERSON>L<PERSON>, Plus, <PERSON>ting<PERSON>, X } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { generateInviteLink, getChannelMembers } from "@/actions/chatActions"
import { AvatarGroup } from "@/components/ui/avatar-group"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PiBellSimpleThin } from "react-icons/pi";
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"

interface Member {
  id: string
  name: string
  email: string
}

interface NavbarWorkspaceProps {
  channelId?: string
  inviteLink?: string
  notifications?: Array<{
    id: string
    title: string
    message: string
    time: string
  }>
  invitedBy?: {
    name: string
  }
}

export function NavbarWorkspace({
  channelId,
  inviteLink = "",
  notifications = [],
  invitedBy,
}: NavbarWorkspaceProps) {
  const router = useRouter()
  const [members, setMembers] = useState<Member[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [channelInviteLink, setChannelInviteLink] = useState(inviteLink)
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false)
  const [isGeneratingLink, setIsGeneratingLink] = useState(false)

  useEffect(() => {
    const loadData = async () => {
      if (channelId) {
        setIsLoading(true)
        try {
          const membersResult = await getChannelMembers(channelId)
          
          if (membersResult.success && membersResult.members) {
            console.log(`Fetched ${membersResult.members.length} members for channel ${channelId}:`, membersResult.members)
            setMembers(membersResult.members)
          } else {
            console.error('Failed to fetch members:', membersResult.error)
          }
          
          if (!inviteLink) {
            const linkResult = await generateInviteLink(channelId)
            if (linkResult.success && linkResult.inviteLink) {
              setChannelInviteLink(linkResult.inviteLink.split('/').pop() || '')
            }
          }
        } catch (error) {
          console.error('Error loading channel data:', error)
        } finally {
          setIsLoading(false)
        }
      }
    }
    
    loadData()
  }, [channelId, inviteLink])

  const copyInviteLink = async () => {
    const link = `${window.location.origin}/hr/workspace/chats/join/${channelInviteLink}`
    
    try {
      await navigator.clipboard.writeText(link)
      toast.success("Invite link copied to clipboard!")
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy link. Please try again.")
    }
  }

  const handleGenerateNewLink = async () => {
    if (!channelId) return
    
    setIsGeneratingLink(true)
    toast.promise(
      generateInviteLink(channelId).then(result => {
        setIsGeneratingLink(false)
        return result
      }).catch(error => {
        setIsGeneratingLink(false)
        throw error
      }),
      {
        loading: "Generating new invite link...",
        success: (result) => {
          if (result.success && result.inviteLink) {
            const newToken = result.inviteLink.split('/').pop() || ''
            setChannelInviteLink(newToken)
            return "New invite link generated!"
          }
          throw new Error("Failed to generate invite link")
        },
        error: "Failed to generate invite link"
      }
    )
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      "bg-[#F44336]", "bg-[#E91E63]", "bg-[#9C27B0]", "bg-[#673AB7]", 
      "bg-[#3F51B5]", "bg-[#2196F3]", "bg-[#03A9F4]", "bg-[#00BCD4]", 
      "bg-[#009688]", "bg-[#4CAF50]", "bg-[#8BC34A]", "bg-[#CDDC39]", 
      "bg-[#FFC107]", "bg-[#FF9800]", "bg-[#FF5722]", "bg-[#795548]", 
      "bg-[#607D8B]"
    ]

    if (!name) return colors[0]
    
    const hash = name.split('')
      .reduce((acc, char) => {
        const value = char.charCodeAt(0)
        return ((acc << 5) - acc) + value
      }, 0)

    const index = Math.abs(hash) % colors.length
    return colors[index]
  }

  const getFullInviteLink = () => {
    if (typeof window === 'undefined') {
      return '';
    }
    return `${window.location.origin}/hr/workspace/chats/join/${channelInviteLink}`;
  }

  const openInviteInNewTab = () => {
    window.open(getFullInviteLink(), '_blank')
  }

  const mockMoreMembers = () => {
    if (members.length <= 3) {
      const mockMembers = [
        ...members,
        { id: 'mock1', name: 'John Doe', email: '<EMAIL>' },
        { id: 'mock2', name: 'Jane Smith', email: '<EMAIL>' },
        { id: 'mock3', name: 'Bob Johnson', email: '<EMAIL>' },
        { id: 'mock4', name: 'Alice Brown', email: '<EMAIL>' },
      ];
      setMembers(mockMembers);
    }
  };

  return (
    <>
      <div className="fixed top-0 left-0 right-0 z-50 border-b bg-background/80 backdrop-blur-sm">
        <div className="flex h-12 items-center px-4 max-w-[1400px] mx-auto">
          <div className="flex flex-1 items-center justify-between">
            <div className="flex items-center space-x-3">
            </div>

            <div className="flex items-center space-x-4">
              {channelId && (isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="h-8 w-24 rounded-full bg-primary/5 animate-pulse"></div>
                  <div className="h-8 w-8 rounded-full bg-primary/10 animate-pulse"></div>
                  <div className="h-8 w-8 rounded-full bg-primary/10 animate-pulse"></div>
                </div>
              ) : members.length > 0 ? (
                <div className="flex items-center">
                  <div className="flex items-center mr-1">
                    <div className="rounded-full px-3 py-1 bg-primary/5 text-xs text-muted-foreground">
                      Members ({members.length})
                    </div>
                  </div>
                  
                  <AvatarGroup className="ml-2">
                    {members.slice(0, 3).map((member) => (
                      <TooltipProvider key={member.id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Avatar 
                              className={`h-8 w-8 border-2 border-background ${getAvatarColor(member.name)} cursor-pointer transition-transform hover:scale-110`}
                            >
                              <AvatarFallback className="text-background font-semibold">
                                {member.name ? member.name.charAt(0).toUpperCase() : '?'}
                              </AvatarFallback>
                            </Avatar>
                          </TooltipTrigger>
                          <TooltipContent className="bg-background/95 backdrop-blur border">
                            <div className="flex flex-col gap-1">
                              <span className="font-medium">{member.name || 'Unknown'}</span>
                              <span className="text-xs text-muted-foreground">{member.email || 'No email'}</span>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                    
                    {members.length > 3 && (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Avatar className="h-8 w-8 bg-gradient-to-br from-primary/20 to-primary/40 hover:from-primary/30 hover:to-primary/50 cursor-pointer transition-all duration-200 border-2 border-background group">
                            <AvatarFallback className="text-foreground font-medium text-xs group-hover:text-primary transition-colors">
                              +{members.length - 3}
                            </AvatarFallback>
                          </Avatar>
                        </PopoverTrigger>
                        <PopoverContent className="w-80 p-0 shadow-lg border-border/50" align="end">
                          <div className="p-4 border-b bg-muted/30">
                            <h4 className="text-sm font-semibold">All Members ({members.length})</h4>
                          </div>
                          <div className="max-h-80 overflow-auto p-2">
                            {members.map((member, index) => (
                              <div key={member.id} className="flex items-center gap-3 p-2 hover:bg-muted/50 rounded-md transition-colors">
                                <Avatar className={`h-8 w-8 ${getAvatarColor(member.name)}`}>
                                  <AvatarFallback className="text-background font-semibold">
                                    {member.name ? member.name.charAt(0).toUpperCase() : '?'}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex flex-col">
                                  <p className="text-sm font-medium leading-none">{member.name || 'Unknown'}</p>
                                  <p className="text-xs text-muted-foreground mt-1">{member.email || 'No email'}</p>
                                </div>
                                {index < 3 && (
                                  <span className="ml-auto text-xs text-muted-foreground px-2 py-0.5 bg-primary/5 rounded-full">
                                    Visible
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </PopoverContent>
                      </Popover>
                    )}
                  </AvatarGroup>

                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-3 gap-1.5 text-xs hover:bg-primary/5 transition-colors"
                    onClick={() => setIsInviteDialogOpen(true)}
                  >
                    <Plus className="h-3.5 w-3.5" />
                    Invite
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1.5 text-xs"
                  onClick={() => setIsInviteDialogOpen(true)}
                >
                  <Plus className="h-3.5 w-3.5" />
                  Invite Members
                </Button>
              ))}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="relative"
                  >
                    <PiBellSimpleThin className="h-4 w-4" />
                    {notifications.length > 0 && (
                      <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-orange-500 animate-pulse" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[300px]">
                  <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <DropdownMenuItem key={notification.id}>
                        <div className="flex flex-col space-y-1">
                          <span className="font-medium">{notification.title}</span>
                          <span className="text-sm text-muted-foreground">
                            {notification.message}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {notification.time}
                          </span>
                        </div>
                      </DropdownMenuItem>
                    ))
                  ) : (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      No new notifications
                    </div>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push("/hr/settings")}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Invite Link Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Invite People to Channel</DialogTitle>
            <DialogDescription>
              Share this link with people you want to invite to this channel
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2 mt-4">
            <div className="grid flex-1 gap-2">
              <Input
                readOnly
                value={getFullInviteLink()}
                className="font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground">
                This link will expire if a new one is generated
              </p>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    type="button" 
                    size="icon" 
                    variant="outline"
                    onClick={copyInviteLink}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Copy invite link</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    type="button" 
                    size="icon" 
                    variant="outline"
                    onClick={openInviteInNewTab}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Open in new tab</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <DialogFooter className="flex items-center justify-between sm:justify-between">
            <Button
              variant="outline"
              onClick={handleGenerateNewLink}
              disabled={isGeneratingLink}
            >
              {isGeneratingLink ? "Generating..." : "Generate New Link"}
            </Button>
            <Button
              type="button" 
              onClick={() => setIsInviteDialogOpen(false)}
            >
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}