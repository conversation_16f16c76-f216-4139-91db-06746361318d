"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import Image from 'next/image'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardFooter } from '@/components/ui/card'

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import GridPattern from '@/components/magicui/grid-pattern'
import { cn } from '@/lib/utils'


const OnboardingPage = () => {
  const [step, setStep] = useState(1)
  const [organizationName, setOrganizationName] = useState('')
  const [useCase, setUseCase] = useState('')
  const [collaborationType, setCollaborationType] = useState('')
  const router = useRouter()


  const totalSteps = 4

  const progress = (step / totalSteps) * 100


  const handleNext = () => {
    if (step === 1 && !organizationName) {
      toast.error('Please enter your organization name')
      return
    }
    if (step === 2 && !useCase) {
      toast.error('Please select a use case')
      return
    }
    if (step === 3 && useCase === 'collaboration' && !collaborationType) {
      toast.error('Please select a collaboration type')
      return
    }
    if (step < 4) {

      setStep(step + 1)
    } else {
      handleSubmit()
    }
  }


  const handleBack = () => {

    if (step > 1) {

      setStep(step - 1)

    }

  }


  const handleSubmit = async () => {
    toast.promise(
      new Promise((resolve) => {
        setTimeout(() => {
          resolve(true)
          router.push('/hr/dashboard')
        }, 2000)
      }),
      {
        loading: 'Setting up your organization...',
        success: 'Organization setup complete!',
        error: 'Failed to set up organization',
      }
    )
  }

  const stepContent = [

    {

      title: "Let's set up your organization",

      description: "Enter your organization's name to get started.",

      image: "/flow.png",

      content: (

                <Input
                  placeholder="Enter your organization name"
                  value={organizationName}
                  onChange={(e) => setOrganizationName(e.target.value)}
          className="mt-4"

        />

      ),

    },

    {

      title: "What will you use this for?",

      description: "Choose the primary purpose for your organization.",

      image: "/flow.png",

      content: (

        <RadioGroup value={useCase} onValueChange={setUseCase} className="mt-4 space-y-2">

                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="hr" id="hr" />
                    <Label htmlFor="hr">HR Management</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="collaboration" id="collaboration" />
                    <Label htmlFor="collaboration">Collaboration</Label>
                  </div>
                </RadioGroup>
      ),

    },

    {

      title: "What type of collaboration?",

      description: "Select the primary focus of your collaboration.",

      image: "/flow.png",

      content: (

        <RadioGroup value={collaborationType} onValueChange={setCollaborationType} className="mt-4 space-y-2">

                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="work" id="work" />
                    <Label htmlFor="work">Work</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="study" id="study" />
                    <Label htmlFor="study">Study</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="personal" id="personal" />
                    <Label htmlFor="personal">Personal</Label>
                  </div>
                </RadioGroup>
      ),

    },

    {

      title: "You're all set!",

      description: "Your organization is ready to go. Click 'Finish' to get started.",

      image: "/images/success.svg",

      content: null,

    },

  ]



  return (
    <>
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-full max-w-2xl shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Welcome to HR Payroll</h1>
            <Progress value={progress} className="w-1/3" />
          </div>
          <motion.div
            key={step}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="flex items-center space-x-8"
          >
            <div className="w-1/3">
              <Image
                src={stepContent[step - 1].image}
                alt="Step illustration"
                width={200}
                height={200}
                className="mx-auto"
              />
            </div>
            <div className="w-2/3 space-y-4">
              <h2 className="text-xl font-semibold">{stepContent[step - 1].title}</h2>
              <p className="">{stepContent[step - 1].description}</p>
              {stepContent[step - 1].content}
            </div>
          </motion.div>
        </CardContent>
        <CardFooter className="flex justify-between p-6">
          <Button onClick={handleBack} variant="outline" disabled={step === 1}>
            Back
          </Button>
          <Button onClick={handleNext}>
            {step < 4 ? 'Next' : 'Finish'}
          </Button>
        </CardFooter>
      </Card>
    </div>
    </>
  )
}

export default OnboardingPage
