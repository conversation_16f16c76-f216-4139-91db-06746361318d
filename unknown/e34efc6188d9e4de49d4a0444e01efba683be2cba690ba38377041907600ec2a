'use client'

import React, { useState } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Download, Filter, Copy } from 'lucide-react';

interface DataTableProps {
  data: any[];
  onSelectionChange?: (selectedRows: any[]) => void;
  maxHeight?: string;
}

export function DataTableVirtualized({ 
  data, 
  onSelectionChange,
  maxHeight = '400px'
}: DataTableProps) {
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState(data);
  
  const parentRef = React.useRef<HTMLDivElement>(null);
  const columns = data[0] ? Object.keys(data[0]) : [];

  const rowVirtualizer = useVirtualizer({
    count: filteredData.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 35,
    overscan: 10,
    scrollMargin: parentRef.current?.offsetTop ?? 0,
    getItemKey: (index) => `row-${index}`,
  });

  const columnVirtualizer = useVirtualizer({
    horizontal: true,
    count: columns.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 150,
    overscan: 3,
    scrollMargin: parentRef.current?.offsetLeft ?? 0,
    getItemKey: (index) => `col-${columns[index]}`,
  });

  const handleRowSelection = (index: number) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedRows(newSelected);
    onSelectionChange?.(Array.from(newSelected).map(idx => filteredData[idx]));
  };

  const handleSelectAll = () => {
    if (selectedRows.size === filteredData.length) {
      setSelectedRows(new Set());
      onSelectionChange?.([]);
    } else {
      setSelectedRows(new Set(filteredData.map((_, i) => i)));
      onSelectionChange?.(filteredData);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (!term) {
      setFilteredData(data);
      return;
    }
    
    const filtered = data.filter(row => 
      Object.values(row).some(value => 
        String(value).toLowerCase().includes(term.toLowerCase())
      )
    );
    setFilteredData(filtered);
  };

  const handleExport = () => {
    const selectedData = Array.from(selectedRows).map(idx => filteredData[idx]);
    const csv = columns.join(',') + '\n' +
      selectedData.map(row => 
        columns.map(col => JSON.stringify(row[col])).join(',')
      ).join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'exported_data.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Improve column width calculation
  const columnWidths = React.useMemo(() => {
    const widths: Record<string, number> = {};
    if (!data.length) return widths;

    columns.forEach(col => {
      // Get maximum content width
      const maxContent = Math.max(
        col.length,
        ...data.slice(0, 100).map(row => String(row[col]).length)
      );
      
      // Calculate width based on content
      const isNumeric = typeof data[0][col] === 'number';
      widths[col] = isNumeric 
        ? Math.min(120, Math.max(80, maxContent * 8))
        : Math.min(300, Math.max(100, maxContent * 8));
    });
    return widths;
  }, [columns, data]);

  // Calculate total width
  const totalWidth = React.useMemo(() => 
    Object.values(columnWidths).reduce((sum, width) => sum + width, 40), // 40px for checkbox
    [columnWidths]
  );

  return (
    <div className="space-y-4 max-w-full">
      <div className="flex items-center justify-between sticky top-0 bg-background z-10 p-2">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-64"
          />
          <Button
            variant="outline"
            onClick={handleSelectAll}
            className="whitespace-nowrap"
          >
            {selectedRows.size === filteredData.length ? 'Deselect All' : 'Select All'}
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleExport}
            disabled={selectedRows.size === 0}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Selected
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleExport()}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(JSON.stringify(Array.from(selectedRows).map(idx => filteredData[idx])))}>
                <Copy className="h-4 w-4 mr-2" />
                Copy to Clipboard
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div
        ref={parentRef}
        className="border rounded-lg overflow-auto"
        style={{ 
          height: maxHeight,
          position: 'relative',
          contain: 'strict',
        }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: `${totalWidth}px`,
            position: 'relative',
          }}
        >
          {/* Header */}
          <div className="sticky top-0 z-10 bg-background border-b">
            <Checkbox
              checked={selectedRows.size === filteredData.length}
              onCheckedChange={handleSelectAll}
              className="absolute left-2 top-2"
            />
            {columnVirtualizer.getVirtualItems().map((virtualColumn) => (
              <div
                key={virtualColumn.key}
                className="absolute px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                style={{
                  left: `${virtualColumn.start + 40}px`,
                  width: `${columnWidths[columns[virtualColumn.index]]}px`,
                  height: '35px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {columns[virtualColumn.index]}
              </div>
            ))}
          </div>

          {/* Rows */}
          {rowVirtualizer.getVirtualItems().map((virtualRow) => (
            <div
              key={virtualRow.key}
              data-index={virtualRow.index}
              className={`absolute w-full ${
                selectedRows.has(virtualRow.index)
                  ? 'bg-muted/50'
                  : 'hover:bg-muted/30'
              }`}
              style={{
                top: `${virtualRow.start}px`,
                height: `${virtualRow.size}px`,
                transform: `translateY(35px)`,
              }}
            >
              <Checkbox
                checked={selectedRows.has(virtualRow.index)}
                onCheckedChange={() => handleRowSelection(virtualRow.index)}
                className="absolute left-2 top-2"
              />
              {columnVirtualizer.getVirtualItems().map((virtualCol) => (
                <div
                  key={`${virtualRow.key}-${virtualCol.key}`}
                  className="absolute px-4 py-2"
                  style={{
                    left: `${virtualCol.start + 40}px`,
                    width: `${columnWidths[columns[virtualCol.index]]}px`,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {typeof filteredData[virtualRow.index][columns[virtualCol.index]] === 'number'
                    ? filteredData[virtualRow.index][columns[virtualCol.index]].toLocaleString()
                    : String(filteredData[virtualRow.index][columns[virtualCol.index]])
                  }
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        {selectedRows.size} of {filteredData.length} rows selected
      </div>
    </div>
  );
}
