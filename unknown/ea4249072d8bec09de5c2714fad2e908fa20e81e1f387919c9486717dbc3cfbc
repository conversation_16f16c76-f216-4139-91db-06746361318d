// For Line Chart
result = df.groupBy('month', {
  'salary': 'avg',
  'bonus': 'avg'
})

// For Bar Chart
result = df.groupBy('department', {
  'employees': 'count'
})

// For Pie Chart
result = df.value_counts('employment_status')

// For Area Chart
result = df.groupBy('year', {
  'total_salary': 'sum',
  'total_bonus': 'sum'
})


// 1. Salary Distribution Analysis
result = df.analyze('salary')

// 2. Department-wise Average Salary
result = df.groupBy('department', {
  'salary': 'avg',
  'employees': 'count'
})

// 3. Experience Level Analysis
result = df.groupBy('experience_years', {
  'salary': 'avg',
  'count': 'count'
})

// 4. Gender Pay Gap Analysis
result = df.groupBy('gender', {
  'salary': 'avg',
  'employees': 'count'
})

// 5. Performance Rating Distribution
result = df.value_counts('performance_rating')

// 6. High Performers Analysis
result = df.filter(row => row.performance_rating >= 4)

// 7. Salary Range Analysis
result = df.filter(row => row.salary >= 30000 && row.salary <= 50000)

// 8. Department and Position Analysis
result = df.groupBy(['department', 'position'], {
  'salary': 'avg',
  'employees': 'count'
})

// 9. Employee Demographics
result = df.groupBy('age_group', {
  'employees': 'count',
  'salary': 'avg'
})

// 10. Tenure Analysis
result = df.analyze('years_of_service')


// Import required dependencies are handled by the backend

// Basic Data Exploration
// Get basic statistics of all numeric columns
result = df.analyze('salary')

// For specific column analysis with multiple metrics
result = df.groupBy('department', {
  'salary': 'avg',
  'age': 'count'
})

// For value counts (frequency) of categorical columns
result = df.value_counts('department')

// For filtering data
result = df.filter(row => row.salary > 50000)

// For selecting specific columns
result = df.select(['name', 'department', 'salary'])

// For basic head/tail operations
result = df.head(10) // First 10 rows
// or
result = df.tail(10) // Last 10 rows

// For comparing specific values
result = df.compare('department', ['IT', 'HR'])

// Filter rows where Rubrique is one of the specific values (300, 775, 770)
// and sum their values
result = df.filter(row => [300, 775, 770].includes(Number(row.Rubrique)))

// Or for a more detailed analysis showing the sum and breakdown:
const filteredData = df.filter(row => [300, 775, 770].includes(Number(row.Rubrique)))
const total = filteredData.data.reduce((sum, row) => sum + Number(row.Rubrique), 0)

result = {
  data: [
    {
      breakdown: filteredData.data.map(row => ({
        Rubrique: row.Rubrique,
        "Nom & Prénom": row["Nom & Prénom"],
        Libelle: row.Libelle
      })),
      total_sum: total
    }
  ]
}

// Log the total for verification
console.log(`Total sum of Rubriques (300 + 775 + 770): ${total}`)

// Get totals grouped by Rubrique
result = df.filter(row => [300, 775, 770].includes(Number(row.Rubrique)))
           .data.reduce((acc, row) => {
  const rubrique = Number(row.Rubrique);
  if (!acc[rubrique]) {
    acc[rubrique] = {
      Rubrique: rubrique,
      Libelle: row.Libelle,
      total: 0
    };
  }
  
  // Sum all monthly values
  ['Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25', 'Jun-25', 
   'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25'].forEach(month => {
    acc[rubrique].total += Number(row[month]) || 0;
  });
  
  return acc;
}, {});

// Convert to array format for display
result = {
  data: Object.values(result),
  output: `Total analysis for Rubriques 300, 775, and 770`
};