'use client'

/**
 * BlockNoteRichTextCard Component
 *
 * A simplified rich text editor card for the dashboard using BlockNoteJS.
 */

import { useState, useEffect } from 'react';
import { TextItem } from './types';
import { Grip, Trash2, Edit, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';

// BlockNote imports
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";

import { useTheme } from "next-themes";
import { useBlockNote } from '@blocknote/react';

interface BlockNoteRichTextCardProps {
  textItem: TextItem;
  isEditMode: boolean;
  onUpdateText: (textId: string, updates: Partial<TextItem>) => void;
  onRemoveText: (textId: string) => void;
}

export function BlockNoteRichTextCard({ textItem, isEditMode, onUpdateText, onRemoveText }: BlockNoteRichTextCardProps) {
  // State for UI interactions
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [mounted, setMounted] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { resolvedTheme } = useTheme();

  // Initialize BlockNote editor
  const editor = useBlockNote({
    initialContent: textItem.content ?
      (() => {
        try {
          return JSON.parse(textItem.content);
        } catch (e) {
          return [{ type: "paragraph", content: "Click to edit rich text" }];
        }
      })() :
      [{ type: "paragraph", content: "Click to edit rich text" }],
    domAttributes: {
      editor: { class: 'bn-editor-no-scroll' }
    }
  });

  // Initialize once on mount
  useEffect(() => {
    setMounted(true);

    // If new item, enter edit mode after a brief delay
    if (textItem.isNew) {
      setTimeout(() => {
        setIsEditing(true);
      }, 100);
    }
  }, [textItem.isNew]);

  // Handle mouse enter/leave for hover effects
  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  // Handle double click to enter edit mode
  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode && !isEditing) {
      setIsEditing(true);
    }
  };

  // Handle edit button click
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (isEditMode && !isEditing) {
      setIsEditing(true);
    }
  };

  // Handle save button click
  const handleSave = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    try {
      // Get the current content from the editor
      const contentToSave = JSON.stringify(editor.topLevelBlocks);

      // Exit edit mode first
      setIsEditing(false);

      // Save the item with the content
      onUpdateText(textItem.id, {
        content: contentToSave,
        isNew: false,
        isRichText: true
      });

      toast.success('Rich text saved', { duration: 2000 });
    } catch (error) {
      console.error('Error saving rich text:', error);
      toast.error('Failed to save rich text');
      setIsEditing(false);
    }
  };

  // Cancel editing
  const handleCancel = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    setIsEditing(false);
  };

  // Handle rich text deletion
  const handleDelete = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    // Confirm deletion
    if (confirm('Are you sure you want to delete this text card?')) {
      onRemoveText(textItem.id);
      toast.success('Text card deleted');
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+Enter to save
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    }

    // Esc to cancel
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  if (!mounted) {
    return null;
  }

  // Custom theme for dark mode
  const customDarkTheme = {
    colors: {
      editor: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 10%, 3.9%)',
      },
      menu: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      tooltip: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 10%, 3.9%)',
      },
      hovered: {
        text: 'hsl(0, 0%, 98%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      selected: {
        text: 'hsl(240, 5.9%, 10%)',
        background: 'hsl(0, 0%, 98%)',
      },
      disabled: {
        text: 'hsl(240, 5%, 64.9%)',
        background: 'hsl(240, 3.7%, 15.9%)',
      },
      shadow: 'hsl(240, 3.7%, 15.9%)',
      border: 'hsl(240, 3.7%, 15.9%)',
    },
  };

  // Render the card with conditional content based on edit mode
  return (
    <Card
      className={`w-full h-full relative rich-text-card ${isEditMode ? '' : 'no-border'}`}
      style={{
        zIndex: isEditing ? 100 : isHovered ? 10 : 'auto',
        boxShadow: isEditing ? '0 4px 12px rgba(0, 0, 0, 0.1)' : 'none',
        backgroundColor: 'transparent',
        border: isEditMode ?
               (isEditing ? '1px solid hsl(var(--primary))' :
               (isHovered ? '1px solid rgba(0, 0, 0, 0.1)' : '1px solid transparent')) :
               'none',
        transition: 'border-color 0.2s ease, box-shadow 0.2s ease'
      }}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-item-id={textItem.id}
      data-is-editing={isEditing ? 'true' : 'false'}
      onKeyDown={handleKeyDown}
    >
      {/* Floating Action Buttons - Only visible in edit mode and not when editing */}
      {isEditMode && !isEditing && (
        <>
          {/* Delete Button */}
          <Button
            variant="destructive"
            size="icon"
            className="absolute -top-3 -right-3 h-6 w-6 rounded-full shadow-md z-50 non-draggable"
            onClick={handleDelete}
            onMouseDown={(e) => e.stopPropagation()}
            style={{ pointerEvents: 'auto' }}
            title="Delete card"
          >
            <Trash2 className="h-3 w-3" />
          </Button>

          {/* Edit Button */}
          <Button
            variant="default"
            size="icon"
            className="absolute -top-3 -right-10 h-6 w-6 rounded-full shadow-md z-50 non-draggable"
            onClick={handleEditClick}
            onMouseDown={(e) => e.stopPropagation()}
            style={{ pointerEvents: 'auto' }}
            title="Edit content"
          >
            <Edit className="h-3 w-3" />
          </Button>
        </>
      )}
      {/* Card Header with Controls - Always visible in edit mode */}
      {isEditMode && (
        <CardHeader
          className="flex flex-row items-center justify-between p-1 space-y-0 bg-primary/5 border-b card-header"
          style={{
            height: '24px',
            overflow: 'hidden',
            pointerEvents: 'none' // This makes the header not capture mouse events except for its children
          }}
        >
          <div
            className="flex items-center gap-1 draggable-handle"
            style={{ pointerEvents: 'auto' }}
          >
            <Grip className="h-3 w-3 text-primary/70" />
            <span className="text-xs font-medium text-primary/70">BlockNote Editor</span>
          </div>

          {/* Header controls removed in favor of floating buttons */}
        </CardHeader>
      )}

      {/* Card Content */}
      <CardContent
        className="p-0"
        style={{
          height: isEditMode ? 'calc(100% - 24px)' : '100%'
        }}
      >
        {isEditing ? (
          // Edit mode with BlockNote editor
          <div className="w-full h-full flex flex-col non-draggable">
            {/* Toolbar */}
            <div className="flex items-center justify-between p-1 border-b bg-muted/20">
              <div className="ml-auto flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs non-draggable"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="h-6 text-xs non-draggable"
                  onClick={handleSave}
                >
                  Done
                </Button>
              </div>
            </div>

            {/* BlockNote Editor */}
            <div className="w-full h-full p-1 non-draggable blocknote-container" style={{ minHeight: '100px' }}>
              <BlockNoteView
                editor={editor}
                theme={resolvedTheme === "dark" ? customDarkTheme : "light"}
              />
            </div>

            <div className="absolute bottom-3 right-3 bg-primary/10 text-primary text-xs px-2 py-1 rounded-sm">
              Ctrl+Enter to save, Esc to cancel
            </div>
          </div>
        ) : (
          // View-only mode
          <div
            className="w-full h-full p-3 rich-text-content blocknote-content-wrapper"
            style={{
              minHeight: '100px',
              backgroundColor: 'transparent'
            }}
          >
            <BlockNoteView
              editor={editor}
              theme={resolvedTheme === "dark" ? customDarkTheme : "light"}
            />
          </div>
        )}
      </CardContent>

      {/* Custom resize handle - Only shown when hovered or in edit mode */}
      {isEditMode && !isEditing && (
        <div
          className="absolute bottom-0 right-0 w-6 h-6 cursor-se-resize transition-opacity react-resizable-handle non-draggable"
          style={{
            zIndex: 5,
            opacity: isHovered ? 0.7 : 0,
            backgroundColor: 'rgba(var(--primary-rgb), 0.05)',
            borderTopLeftRadius: '4px',
            transition: 'opacity 0.2s ease, background-color 0.2s ease',
            boxShadow: '-1px -1px 2px rgba(0,0,0,0.05)'
          }}
        >
          <ChevronDown className="h-3 w-3 rotate-45 text-primary/70 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
      )}
    </Card>
  );
}
