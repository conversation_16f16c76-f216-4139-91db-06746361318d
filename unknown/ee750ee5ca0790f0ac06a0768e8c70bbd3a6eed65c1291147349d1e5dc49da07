import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

export const CustomDialog = ({ 
  isOpen, 
  onClose, 
  children,
  title = "Dataset View" 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
  children: React.ReactNode;
  title?: string;
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed inset-2 z-50 flex flex-col rounded-lg border bg-background shadow-lg"> {/* Reduced from inset-4 */}
        <div className="flex items-center justify-between p-1 border-b"> {/* Reduced from p-2 */}
          <h2 className="text-base font-semibold px-1">{title}</h2> {/* Reduced text size and added minimal padding */}
          <Button variant="ghost" size="sm" onClick={onClose} className="h-7 px-2"> {/* Reduced button size */}
            <X className="h-3 w-3" /> {/* Reduced icon size */}
          </Button>
        </div>
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </div>
    </div>
  );
};