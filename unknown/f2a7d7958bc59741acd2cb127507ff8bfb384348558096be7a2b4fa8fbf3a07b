'use server';

import { revalidatePath } from "next/cache";
import { auth } from "@clerk/nextjs/server";
import prisma from "@/lib/db";
import { GridFSBucket, MongoClient } from 'mongodb';
import { ObjectId } from 'mongodb';

const MAX_FILE_SIZE = 12 * 1024 * 1024; // 12MB

export async function storeEmployeeDocument(
  formData: FormData
): Promise<{ success: boolean; error?: string; documentId?: string }> {
  const { userId } = auth();

  if (!userId) {
    return { success: false, error: "You must be logged in to upload a document" };
  }

  try {
    const employeeId = formData.get('employeeId') as string;
    const file = formData.get('file') as Blob | null;
    const description = formData.get('description') as string;

    if (!file) {
      return { success: false, error: "No file provided" };
    }

    const employee = await prisma.employee.findUnique({
      where: { id: employeeId },
    });

    if (!employee) {
      return { success: false, error: "Employee not found" };
    }

    const fileName = formData.get('fileName') as string;
    const fileType = file.type;
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    let documentId: string;

    if (buffer.length <= MAX_FILE_SIZE) {
      // Store small files directly in the database
      const base64File = buffer.toString('base64');
      const document = await prisma.employeeDocument.create({
        data: {
          employeeId,
          fileName,
          fileType,
          fileContent: base64File,
          description,
        },
      });
      documentId = document.id;
    } else {
      // Use GridFS for larger files
      const client = new MongoClient(process.env.DATABASE_URL!);
      await client.connect();
      const db = client.db();
      const bucket = new GridFSBucket(db);

      const uploadStream = bucket.openUploadStream(fileName);
      await new Promise<void>((resolve, reject) => {
        uploadStream.write(buffer, (error) => {
          if (error) reject(error);
          else {
            uploadStream.end(() => {
              resolve();
            });
          }
        });
      });

      const document = await prisma.employeeDocument.create({
        data: {
          employeeId,
          fileName,
          fileType,
          fileContent: uploadStream.id.toString(),
          description,
          isGridFS: true,
        },
      });
      documentId = document.id;

      await client.close();
    }

    revalidatePath(`/hr/employee/${employeeId}/doc`);

    return { success: true, documentId };
  } catch (error) {
    console.error('Error in storeEmployeeDocument:', error);
    return { success: false, error: error instanceof Error ? error.message : "An unknown error occurred while storing the document" };
  }
}

export async function getEmployeeDocuments(employeeId: string): Promise<any[]> {
  const { userId } = auth();

  if (!userId) {
    throw new Error("You must be logged in to view documents");
  }

  try {
    const documents = await prisma.employeeDocument.findMany({
      where: { employeeId },
      orderBy: { uploadedAt: 'desc' },
    });

    return documents.map(doc => ({
      id: doc.id,
      fileName: doc.fileName,
      fileType: doc.fileType,
      uploadedAt: doc.uploadedAt,
      description: doc.description,
      isGridFS: doc.isGridFS,
    }));
  } catch (error) {
    console.error('Error in getEmployeeDocuments:', error);
    return []; // Return an empty array instead of throwing an error
  }
}

export async function deleteEmployeeDocument(documentId: string): Promise<{ success: boolean; error?: string }> {
  const { userId } = auth();

  if (!userId) {
    return { success: false, error: "You must be logged in to delete a document" };
  }

  try {
    const document = await prisma.employeeDocument.delete({
      where: { id: documentId },
    });

    if (document.isGridFS) {
      // Delete from GridFS
      const client = new MongoClient(process.env.DATABASE_URL!);
      await client.connect();
      const db = client.db();
      const bucket = new GridFSBucket(db);
      await bucket.delete(new ObjectId(document.fileContent));
      await client.close();
    }

    revalidatePath(`/hr/employee/${document.employeeId}/doc`);

    return { success: true };
  } catch (error) {
    console.error('Error in deleteEmployeeDocument:', error);
    return { success: false, error: "An error occurred while deleting the document" };
  }
}
