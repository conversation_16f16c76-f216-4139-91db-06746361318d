"use client";

import { formatDistanceT<PERSON><PERSON>ow } from "date-fns";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import {
  FileText,
  FolderKanban,
  Calendar,
  Settings,
  ChevronLeft,
  ChevronRight,
  Plus,
  Kanban,
} from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { PiFlowArrowBold } from "react-icons/pi";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from "@/components/ui/alert-dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";

interface WorkflowSidebarProps {
  workflows: any[];
  isLoading: boolean;
  onLoadWorkflow: (workflow: any) => void;
}

const navigationLinks = [
{ href: "/hr/workspace/ragbook", icon :Kanban,label: "RAG" },
  { href: "/hr/workspace/note", icon: FileText, label: "Notes" },
  { href: "/hr/workspace/callend", icon: Calendar, label: "Calendar" },
  // { href: "/hr/workspace/settings", icon: Settings, label: "Settings" },
];

export function WorkflowSidebar({ workflows, isLoading, onLoadWorkflow }: WorkflowSidebarProps) {
  const router = useRouter();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [workflowToLoad, setWorkflowToLoad] = useState<any>(null);
  const [sortBy, setSortBy] = useState<'date' | 'name'>('date');

  return (
    <div
      className={cn(
        "border-r bg-background transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex h-full flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          {!isCollapsed && <h2 className="text-lg font-semibold">Workflows</h2>}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="ml-auto"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        <ScrollArea className="flex-1 px-3 py-2">
          {/* Navigation Links */}
          <div className="space-y-1 mb-4">
            {navigationLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 text-sm rounded-md text-muted-foreground hover:text-foreground hover:bg-accent transition-colors",
                  !isCollapsed ? "justify-start" : "justify-center"
                )}
              >
                <link.icon className="h-4 w-4" />
                {!isCollapsed && <span>{link.label}</span>}
              </Link>
            ))}
          </div>

          {/* Saved Workflows */}
          {!isCollapsed && (
            <>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Saved Workflows</span>
                <Button variant="ghost" size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-4">
                <Input
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>

              <Select value={sortBy} onValueChange={(value: 'date' | 'name') => setSortBy(value)}>
                <SelectTrigger className="w-full mb-2">
                  <SelectValue placeholder="Sort by..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Latest First</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                </SelectContent>
              </Select>

              {isLoading ? (
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex flex-col gap-2 p-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-1">
                  {workflows
                    .sort((a, b) => {
                      if (sortBy === 'date') {
                        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                      }
                      return a.title.localeCompare(b.title);
                    })
                    .filter(w => 
                      w.title.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((workflow) => (
                      <button
                        key={workflow.id}
                        onClick={() => setWorkflowToLoad(workflow)}
                        className="w-full text-left p-2 rounded-md hover:bg-accent group"
                      >
                        <div className="flex items-center gap-2">
                          <PiFlowArrowBold className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium truncate">
                            {workflow.title}
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatDistanceToNow(new Date(workflow.createdAt))} ago
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          {workflow.tags?.map((tag: string) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </button>
                    ))}
                </div>
              )}
            </>
          )}
        </ScrollArea>
      </div>
      <AlertDialog open={!!workflowToLoad} onOpenChange={() => setWorkflowToLoad(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Load Workflow</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to load "{workflowToLoad?.title}"? This will replace your current workflow.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => {
              onLoadWorkflow(workflowToLoad);
              setWorkflowToLoad(null);
            }}>
              Load Workflow
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 