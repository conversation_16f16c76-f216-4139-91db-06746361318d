import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { GitBranch, Eye } from "lucide-react";

interface TableDialogsProps {
  showAddColumnDialog: boolean;
  setShowAddColumnDialog: (show: boolean) => void;
  newColumnName: string;
  setNewColumnName: (name: string) => void;
  handleAddColumn: () => void;
  showVersionHistory: boolean;
  setShowVersionHistory: (show: boolean) => void;
  versions: any[];
  formatDate: (date: any) => string;
  renderChangeContent: (change: any) => React.ReactNode;
  showChangesPreview: boolean;
  setShowChangesPreview: (show: boolean) => void;
  editHistory: any[];
}

export function TableDialogs({
  showAddColumnDialog,
  setShowAddColumnDialog,
  newColumnName,
  setNewColumnName,
  handleAddColumn,
  showVersionHistory,
  setShowVersionHistory,
  versions,
  formatDate,
  renderChangeContent,
  showChangesPreview,
  setShowChangesPreview,
  editHistory,
}: TableDialogsProps) {
  return (
    <>
      <Dialog open={showAddColumnDialog} onOpenChange={setShowAddColumnDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Add New Column</h4>
              <p className="text-sm text-muted-foreground">
                Enter the name for the new column
              </p>
            </div>
            <Input
              placeholder="Column name..."
              value={newColumnName}
              onChange={(e) => setNewColumnName(e.target.value)}
              className="text-sm"
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddColumnDialog(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleAddColumn}
                disabled={!newColumnName}
              >
                Add Column
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showVersionHistory} onOpenChange={setShowVersionHistory}>
        <DialogContent className="sm:max-w-[800px] h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Version History
            </DialogTitle>
            <DialogDescription>
              Track all changes made to this dataset
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full pr-4">
              <div className="space-y-6">
                {versions.map((version, index) => (
                  <div 
                    key={version.id}
                    className="relative pl-8 before:absolute before:left-3 before:top-0 before:h-full before:w-[2px] before:bg-muted last:before:h-1/2"
                  >
                    <div className="absolute left-0 top-1 h-6 w-6 rounded-full border bg-background flex items-center justify-center text-sm">
                      {version.versionNumber}
                    </div>
                    <Card>
                      <CardHeader className="py-3">
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                {version.user?.name || 'Unknown'}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                made changes
                              </span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {formatDate(version.createdAt)}
                            </div>
                          </div>
                          <Badge variant="outline">
                            {version.changes.length} changes
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {version.changes.map((change: any, i: number) => (
                            <div key={i} className="text-sm">
                              {renderChangeContent(change)}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showChangesPreview} onOpenChange={setShowChangesPreview}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Pending Changes
            </DialogTitle>
            <DialogDescription>
              Review changes before saving
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] mt-4">
            <div className="space-y-4">
              {editHistory.map((change, index) => (
                <Card key={index} className="p-4">
                  <div className="space-y-2">
                    {renderChangeContent(change)}
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default TableDialogs;