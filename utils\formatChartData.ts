interface ChartData {
  type: 'bar' | 'line' | 'pie';
  data: any[];
}

export function formatAIResponseForChart(aiResponse: string): ChartData | null {
  try {
    // Try to parse the AI response as JSON
    const data = JSON.parse(aiResponse);
    
    // Validate the required structure
    if (!data.type || !data.data || !Array.isArray(data.data)) {
      return null;
    }
    
    // Validate chart type
    if (!['bar', 'line', 'pie'].includes(data.type)) {
      return null;
    }
    
    // Validate data structure
    const isValidData = data.data.every((item: any) => 
      typeof item === 'object' && 
      'name' in item && 
      'value' in item
    );
    
    if (!isValidData) {
      return null;
    }
    
    return {
      type: data.type,
      data: data.data
    };
  } catch (error) {
    return null;
  }
}

// Example of expected AI response format:
/*
{
  "type": "bar",
  "data": [
    { "name": "Category 1", "value": 400 },
    { "name": "Category 2", "value": 300 },
    { "name": "Category 3", "value": 200 }
  ]
}
*/
