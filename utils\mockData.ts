/**
 * Mock data utilities for offline mode
 * Provides sample datasets for practicing with Python and SQL
 */

// Generate a mock employee dataset
export const generateEmployeeData = (count = 100) => {
  const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Product', 'Design'];
  const positions = ['Junior', 'Mid-level', 'Senior', 'Lead', 'Manager', 'Director'];
  const locations = ['New York', 'San Francisco', 'London', 'Berlin', 'Tokyo', 'Remote'];
  const statuses = ['Active', 'On Leave', 'Terminated', 'Sabbatical'];
  const genders = ['Male', 'Female', 'Non-Binary'];
  const contractTypes = ['Full-time', 'Part-time', 'Contract', 'Intern'];

  // Create a more structured dataset with realistic salary ranges by department and position
  const departmentSalaryRanges = {
    'Engineering': { base: 70000, range: 80000 },
    'Marketing': { base: 50000, range: 60000 },
    'Sales': { base: 45000, range: 75000 },
    'HR': { base: 45000, range: 55000 },
    'Finance': { base: 60000, range: 70000 },
    'Product': { base: 65000, range: 75000 },
    'Design': { base: 55000, range: 65000 }
  };

  const positionMultipliers = {
    'Junior': 0.7,
    'Mid-level': 1.0,
    'Senior': 1.3,
    'Lead': 1.5,
    'Manager': 1.8,
    'Director': 2.2
  };

  return Array.from({ length: count }, (_, i) => {
    const age = 22 + Math.floor(Math.random() * 40);
    const department = departments[Math.floor(Math.random() * departments.length)];
    const position = positions[Math.floor(Math.random() * positions.length)];
    const gender = genders[Math.floor(Math.random() * genders.length)];
    const contractType = contractTypes[Math.floor(Math.random() * contractTypes.length)];

    // Calculate a more realistic salary based on department and position
    // @ts-ignore
    const deptSalary = departmentSalaryRanges[department];
    // @ts-ignore
    const posMultiplier = positionMultipliers[position];
    const baseSalary = deptSalary.base + Math.floor(Math.random() * deptSalary.range);
    const salary = Math.floor(baseSalary * posMultiplier);

    const yearsOfService = Math.floor(Math.random() * 15);
    const location = locations[Math.floor(Math.random() * locations.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    // Calculate hire date based on years of service
    const hireDate = new Date();
    hireDate.setFullYear(hireDate.getFullYear() - yearsOfService);
    const hireDateStr = hireDate.toISOString().split('T')[0];

    // Add some new hires for testing
    const isNewHire = yearsOfService < 1;

    return {
      id: i + 1,
      name: `Employee ${i + 1}`,
      age,
      department,
      position,
      salary,
      yearsOfService,
      location,
      status,
      gender,
      contractType,
      isRemote: location === 'Remote',
      hireDate: hireDateStr,
      performanceScore: Math.floor(Math.random() * 5) + 1,
      isNewHire
    };
  });
};

// Generate a Moroccan-specific employee dataset for HR
export const generateMoroccanEmployeeData = (count = 100) => {
  const departments = ['Ingénierie', 'Marketing', 'Ventes', 'Ressources Humaines', 'Finance', 'Produit', 'Design'];
  const positions = ['Junior', 'Intermédiaire', 'Sénior', 'Chef d\'équipe', 'Manager', 'Directeur'];
  const cities = ['Casablanca', 'Rabat', 'Marrakech', 'Tanger', 'Fès', 'Agadir', 'Tétouan', 'Oujda', 'Remote'];
  const statuses = ['Actif', 'En congé', 'Fin de contrat', 'Congé sabbatique'];
  const genders = ['Homme', 'Femme'];
  const contractTypes = ['CDI', 'CDD', 'ANAPEC', 'Stage', 'Freelance'];

  // Salary ranges in MAD (Moroccan Dirham)
  const departmentSalaryRanges = {
    'Ingénierie': { base: 8000, range: 12000 },
    'Marketing': { base: 6000, range: 8000 },
    'Ventes': { base: 5000, range: 10000 },
    'Ressources Humaines': { base: 5500, range: 7000 },
    'Finance': { base: 7000, range: 9000 },
    'Produit': { base: 7500, range: 10000 },
    'Design': { base: 6500, range: 8500 }
  };

  const positionMultipliers = {
    'Junior': 0.7,
    'Intermédiaire': 1.0,
    'Sénior': 1.3,
    'Chef d\'équipe': 1.5,
    'Manager': 1.8,
    'Directeur': 2.2
  };

  // Common Moroccan names
  const firstNames = [
    'Mohammed', 'Youssef', 'Ahmed', 'Amine', 'Omar', 'Hamza', 'Ali', 'Mehdi', 'Samir', 'Karim',
    'Fatima', 'Aisha', 'Leila', 'Meryem', 'Nadia', 'Samira', 'Amina', 'Khadija', 'Hanane', 'Loubna'
  ];

  const lastNames = [
    'Alaoui', 'El Amrani', 'Benchekroun', 'Benhaddou', 'Benjelloun', 'Bennani', 'Berrada', 'Cherkaoui',
    'El Fassi', 'El Mansouri', 'Tazi', 'Idrissi', 'Saidi', 'Lahlou', 'Tahiri', 'Ouazzani'
  ];

  return Array.from({ length: count }, (_, i) => {
    const age = 22 + Math.floor(Math.random() * 40);
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const department = departments[Math.floor(Math.random() * departments.length)];
    const position = positions[Math.floor(Math.random() * positions.length)];
    const gender = genders[Math.floor(Math.random() * genders.length)];
    const contractType = contractTypes[Math.floor(Math.random() * contractTypes.length)];

    // Calculate a more realistic salary based on department and position in MAD
    // @ts-ignore
    const deptSalary = departmentSalaryRanges[department];
    // @ts-ignore
    const posMultiplier = positionMultipliers[position];
    const baseSalary = deptSalary.base + Math.floor(Math.random() * deptSalary.range);
    const salary = Math.floor(baseSalary * posMultiplier);

    const yearsOfService = Math.floor(Math.random() * 15);
    const city = cities[Math.floor(Math.random() * cities.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    // Calculate hire date based on years of service
    const hireDate = new Date();
    hireDate.setFullYear(hireDate.getFullYear() - yearsOfService);
    const hireDateStr = hireDate.toISOString().split('T')[0];

    // Social Security Numbers (CNSS) follow a specific format in Morocco
    const cnssNumber = `${Math.floor(1000000 + Math.random() * 9000000)}`;

    // Random CIMR (pension fund) number
    const cimrNumber = Math.random() < 0.7 ? `${Math.floor(100000 + Math.random() * 900000)}` : null;

    // Calculate taxes based on Moroccan tax brackets
    const calculateTax = (monthlySalary:any) => {
      const annualSalary = monthlySalary * 12;
      let taxAmount = 0;

      if (annualSalary <= 30000) {
        taxAmount = 0;
      } else if (annualSalary <= 50000) {
        taxAmount = (annualSalary - 30000) * 0.1;
      } else if (annualSalary <= 60000) {
        taxAmount = 2000 + (annualSalary - 50000) * 0.2;
      } else if (annualSalary <= 80000) {
        taxAmount = 4000 + (annualSalary - 60000) * 0.3;
      } else if (annualSalary <= 180000) {
        taxAmount = 10000 + (annualSalary - 80000) * 0.34;
      } else {
        taxAmount = 44000 + (annualSalary - 180000) * 0.38;
      }

      return Math.round(taxAmount / 12); // Monthly tax
    };

    // CNSS (social security) contribution - 4.48% of salary, capped at 6000 MAD
    const cnssContribution = Math.min(salary * 0.0448, 6000 * 0.0448);

    // AMO (health insurance) - 2.26% of salary
    const amoContribution = salary * 0.0226;

    // CIMR (pension) - optional, 6% of salary if enrolled
    const cimrContribution = cimrNumber ? salary * 0.06 : 0;

    // Calculate IR (income tax)
    const irTax = calculateTax(salary);

    // Net salary after deductions
    const netSalary = salary - cnssContribution - amoContribution - cimrContribution - irTax;

    return {
      id: i + 1,
      prenom: firstName,
      nom: lastName,
      age,
      departement: department,
      poste: position,
      salaire: salary,
      salaireNet: Math.round(netSalary),
      ir: Math.round(irTax),
      cnss: Math.round(cnssContribution),
      amo: Math.round(amoContribution),
      cimr: Math.round(cimrContribution),
      numeroCnss: cnssNumber,
      numeroCimr: cimrNumber,
      anciennete: yearsOfService,
      ville: city,
      statut: status,
      genre: gender,
      typeContrat: contractType,
      estRemote: city === 'Remote',
      dateDebut: hireDateStr,
      performance: Math.floor(Math.random() * 5) + 1,
      nouveau: yearsOfService < 1
    };
  });
};

// Define the type for attendance data
interface AttendanceRecord {
  id: string;
  employeeId: number;
  date: string;
  status: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  notes: string | null;
}

// Define the type for employee data
interface MoroccanEmployee {
  id: number;
  prenom: string;
  nom: string;
  [key: string]: any; // Allow other properties
}

// Generate Moroccan attendance data for employees
export const generateMoroccanAttendanceData = (employees: MoroccanEmployee[], days = 30) => {
  const attendanceStatuses = ['Présent', 'Absent', 'Congé', 'Maladie', 'Formation', 'Mission'];
  const statusWeights = [0.85, 0.03, 0.05, 0.03, 0.02, 0.02]; // Probabilities for each status

  const attendanceData: AttendanceRecord[] = [];
  const today = new Date();

  employees.forEach(employee => {
    // For the last 30 days
    for (let i = 0; i < days; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Skip weekends (Saturday = 6, Sunday = 0)
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        continue;
      }

      // Generate random status based on weights
      const random = Math.random();
      let cumulativeProbability = 0;
      let status = attendanceStatuses[0]; // Default status

      for (let j = 0; j < statusWeights.length; j++) {
        cumulativeProbability += statusWeights[j];
        if (random < cumulativeProbability) {
          status = attendanceStatuses[j];
          break;
        }
      }

      // Generate check-in and check-out times for present employees
      let checkInTime = null;
      let checkOutTime = null;

      if (status === 'Présent') {
        // Normal check-in between 8:00 and 9:30
        const checkInHour = 8 + Math.floor(Math.random() * 2);
        const checkInMinute = Math.floor(Math.random() * 60);
        const checkIn = new Date(date);
        checkIn.setHours(checkInHour, checkInMinute, 0);
        checkInTime = checkIn.toISOString();

        // Normal check-out between 17:00 and 19:00
        const checkOutHour = 17 + Math.floor(Math.random() * 2);
        const checkOutMinute = Math.floor(Math.random() * 60);
        const checkOut = new Date(date);
        checkOut.setHours(checkOutHour, checkOutMinute, 0);
        checkOutTime = checkOut.toISOString();
      }

      attendanceData.push({
        id: `att-${employee.id}-${date.toISOString().split('T')[0]}`,
        employeeId: employee.id,
        date: date.toISOString().split('T')[0],
        status,
        checkInTime,
        checkOutTime,
        notes: status !== 'Présent' ? `Employé ${status.toLowerCase()}` : null
      });
    }
  });

  return attendanceData;
};

// Generate a mock sales dataset
export const generateSalesData = (count = 100) => {
  const products = ['Laptop', 'Phone', 'Tablet', 'Monitor', 'Keyboard', 'Mouse', 'Headphones'];
  const categories = ['Electronics', 'Accessories', 'Software', 'Services'];
  const regions = ['North', 'South', 'East', 'West', 'Central'];
  const channels = ['Online', 'Retail', 'Direct', 'Partner'];

  return Array.from({ length: count }, (_, i) => {
    const product = products[Math.floor(Math.random() * products.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const quantity = Math.floor(Math.random() * 10) + 1;
    const unitPrice = 50 + Math.floor(Math.random() * 950);
    const totalAmount = quantity * unitPrice;
    const region = regions[Math.floor(Math.random() * regions.length)];
    const channel = channels[Math.floor(Math.random() * channels.length)];

    // Generate a random date within the last year
    const daysAgo = Math.floor(Math.random() * 365);
    const saleDate = new Date(Date.now() - (daysAgo * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];

    return {
      id: i + 1,
      product,
      category,
      quantity,
      unitPrice,
      totalAmount,
      region,
      channel,
      saleDate,
      customerId: Math.floor(Math.random() * 1000) + 1,
      discount: Math.random() < 0.3 ? Math.floor(Math.random() * 30) : 0,
      isPromotion: Math.random() < 0.2,
    };
  });
};

// Generate a mock financial dataset
export const generateFinancialData = (count = 100) => {
  const transactionTypes = ['Income', 'Expense', 'Transfer', 'Investment'];
  const categories = ['Salary', 'Rent', 'Utilities', 'Food', 'Transportation', 'Entertainment', 'Healthcare', 'Education'];
  const accounts = ['Checking', 'Savings', 'Credit Card', 'Investment'];

  return Array.from({ length: count }, (_, i) => {
    const transactionType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const amount = 10 + Math.floor(Math.random() * 1990);
    const account = accounts[Math.floor(Math.random() * accounts.length)];

    // Generate a random date within the last year
    const daysAgo = Math.floor(Math.random() * 365);
    const transactionDate = new Date(Date.now() - (daysAgo * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];

    return {
      id: i + 1,
      transactionType,
      category,
      amount,
      account,
      transactionDate,
      description: `Transaction ${i + 1}`,
      isRecurring: Math.random() < 0.3,
      isAutomated: Math.random() < 0.5,
      balance: 5000 + Math.floor(Math.random() * 10000),
    };
  });
};

// Define the type for payroll data
interface PayrollRecord {
  id: string;
  employeeId: number;
  employeeName: string;
  period: string;
  baseSalary: number;
  bonuses: number;
  overtime: number;
  grossSalary: number;
  ir: number;
  cnss: number;
  amo: number;
  cimr: number;
  advancePayment: number;
  loanRepayment: number;
  netSalary: number;
  paymentDate: string | null;
  paymentStatus: string;
  paymentMethod: string | null;
}

// Generate Moroccan payroll data for employees
export const generateMoroccanPayrollData = (employees: MoroccanEmployee[], months = 12) => {
  const payrollData: PayrollRecord[] = [];
  const today = new Date();

  employees.forEach(employee => {
    // Generate payroll data for the last X months
    for (let i = 0; i < months; i++) {
      const date = new Date(today);
      date.setMonth(date.getMonth() - i);

      // Base salary from employee record
      const baseSalary = employee.salaire;

      // Bonuses and overtime change slightly month to month
      const bonuses = Math.random() < 0.3 ? Math.round(baseSalary * (0.05 + Math.random() * 0.15)) : 0;
      const overtime = Math.random() < 0.4 ? Math.round(baseSalary * (0.02 + Math.random() * 0.08)) : 0;

      // Deductions remain mostly constant
      const cnssContribution = Math.min(baseSalary * 0.0448, 6000 * 0.0448);
      const amoContribution = baseSalary * 0.0226;
      const cimrContribution = employee.numeroCimr ? baseSalary * 0.06 : 0;

      // Calculate gross salary
      const grossSalary = baseSalary + bonuses + overtime;

      // Calculate IR (income tax) based on gross
      const calculateTax = (monthlySalary:any) => {
        const annualSalary = monthlySalary * 12;
        let taxAmount = 0;

        if (annualSalary <= 30000) {
          taxAmount = 0;
        } else if (annualSalary <= 50000) {
          taxAmount = (annualSalary - 30000) * 0.1;
        } else if (annualSalary <= 60000) {
          taxAmount = 2000 + (annualSalary - 50000) * 0.2;
        } else if (annualSalary <= 80000) {
          taxAmount = 4000 + (annualSalary - 60000) * 0.3;
        } else if (annualSalary <= 180000) {
          taxAmount = 10000 + (annualSalary - 80000) * 0.34;
        } else {
          taxAmount = 44000 + (annualSalary - 180000) * 0.38;
        }

        return Math.round(taxAmount / 12); // Monthly tax
      };

      const irTax = calculateTax(grossSalary);

      // Other potential deductions
      const advancePayment = Math.random() < 0.1 ? Math.round(baseSalary * 0.2) : 0;
      const loanRepayment = Math.random() < 0.15 ? 1000 + Math.floor(Math.random() * 2000) : 0;

      // Calculate net salary
      const netSalary = grossSalary - cnssContribution - amoContribution - cimrContribution - irTax - advancePayment - loanRepayment;

      // Payment status
      const isPaid = date.getMonth() < today.getMonth() || date.getFullYear() < today.getFullYear();

      // Payment date - typically 27th to 30th of month
      const paymentDate = new Date(date.getFullYear(), date.getMonth(), 27 + Math.floor(Math.random() * 4));

      payrollData.push({
        id: `pay-${employee.id}-${date.getFullYear()}-${date.getMonth() + 1}`,
        employeeId: employee.id,
        employeeName: `${employee.prenom} ${employee.nom}`,
        period: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        baseSalary: Math.round(baseSalary),
        bonuses: Math.round(bonuses),
        overtime: Math.round(overtime),
        grossSalary: Math.round(grossSalary),
        ir: Math.round(irTax),
        cnss: Math.round(cnssContribution),
        amo: Math.round(amoContribution),
        cimr: Math.round(cimrContribution),
        advancePayment,
        loanRepayment,
        netSalary: Math.round(netSalary),
        paymentDate: isPaid ? paymentDate.toISOString().split('T')[0] : null,
        paymentStatus: isPaid ? 'Payé' : 'En attente',
        paymentMethod: isPaid ? (Math.random() < 0.9 ? 'Virement bancaire' : 'Chèque') : null
      });
    }
  });

  return payrollData;
};

// Create mock Moroccan employees
const moroccanEmployees: MoroccanEmployee[] = generateMoroccanEmployeeData(100);
const moroccanAttendance: AttendanceRecord[] = generateMoroccanAttendanceData(moroccanEmployees.slice(0, 20), 60);
const moroccanPayroll: PayrollRecord[] = generateMoroccanPayrollData(moroccanEmployees.slice(0, 20), 12);

// Mock datasets available in offline mode
export const mockDatasets = [
  {
    id: 'moroccan-employees',
    name: 'Employés Marocains',
    description: 'Données des employés pour HR Atlas Maroc',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    rowCount: moroccanEmployees.length,
    columnCount: Object.keys(moroccanEmployees[0]).length,
    sizeInBytes: 0,
    tags: ['RH', 'Employés', 'Maroc'],
    data: moroccanEmployees,
  },
  {
    id: 'moroccan-attendance',
    name: 'Présence Employés',
    description: 'Données de présence des employés',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    rowCount: moroccanAttendance.length,
    columnCount: Object.keys(moroccanAttendance[0]).length,
    sizeInBytes: 0,
    tags: ['RH', 'Présence', 'Maroc'],
    data: moroccanAttendance,
  },
  {
    id: 'moroccan-payroll',
    name: 'Paie Employés',
    description: 'Données de paie des employés marocains',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    rowCount: moroccanPayroll.length,
    columnCount: Object.keys(moroccanPayroll[0]).length,
    sizeInBytes: 0,
    tags: ['RH', 'Paie', 'Maroc'],
    data: moroccanPayroll,
  },
  {
    id: 'mock-employees',
    name: 'Mock Employees',
    description: 'Sample employee data for HR analytics',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    rowCount: 100,
    columnCount: 11,
    sizeInBytes: 0,
    tags: ['HR', 'Employees', 'Mock'],
    data: generateEmployeeData(),
  },
  {
    id: 'mock-sales',
    name: 'Mock Sales',
    description: 'Sample sales data for business analytics',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    rowCount: 100,
    columnCount: 12,
    sizeInBytes: 0,
    tags: ['Sales', 'Business', 'Mock'],
    data: generateSalesData(),
  },
  {
    id: 'mock-financial',
    name: 'Mock Financial',
    description: 'Sample financial data for financial analysis',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    rowCount: 100,
    columnCount: 10,
    sizeInBytes: 0,
    tags: ['Finance', 'Transactions', 'Mock'],
    data: generateFinancialData(),
  },
];

// Function to get a mock dataset by ID
export const getMockDataset = (id: string) => {
  return mockDatasets.find(dataset => dataset.id === id);
};

// Function to get all mock datasets
export const getAllMockDatasets = () => {
  return mockDatasets;
};
